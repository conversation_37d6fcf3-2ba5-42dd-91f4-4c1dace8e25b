//开屏页
import 'dart:convert';
import 'dart:io';

import 'package:UrNovel/Util/Common/Animation/lottie_animation_view.dart';
import 'package:UrNovel/Util/Common/CommonManager/common_manager.dart';
import 'package:UrNovel/Util/DataReportManager/event_name_config.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:get/get.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

import '../../NativeBridge/native_bridge.dart';
import '../../Util/DataReportManager/event_report_manager.dart';
import '../../Util/Extensions/colorUtil.dart';
import '../../Util/NetWorkManager/net_work_manager.dart';
import '../../Util/SharedPreferences/shared_preferences.dart';
import '../../Util/SheetAndAlter/toast.dart';
import '../../Util/device.dart';
import '../../Util/logUtil.dart';
import '../../Util/stringUtil.dart';
import '../Model/login_model.dart';
import '../Model/login_way_model.dart';
import '../Model/user_model.dart';
import '../ViewModel/ViewModel.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  late bool isLoginIng;
  late bool isAgreeTermsAndPrivacyShow;
  final loginKey = GlobalKey<PlatformLoginState>();
  final radioKey = GlobalKey<RadioWidgetState>();
  late List<Widget> loginWayItems;
  late int? lastLoginChannel;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    isLoginIng = false;
    isAgreeTermsAndPrivacyShow = false;
    lastLoginChannel = null;
    loginWayItems = [
      LoginWay(loginChannel: 10, lastLoginChannel: lastLoginChannel, onLoginClick: _onLoginClick),
      SizedBox(height: lastLoginChannel == 11 ? 15 : 25),
      LoginWay(loginChannel: 11, lastLoginChannel: lastLoginChannel, onLoginClick: _onLoginClick),
      if (Platform.isIOS) SizedBox(height: lastLoginChannel == 12 ? 15 : 25),
      if (Platform.isIOS)
        LoginWay(loginChannel: 12, lastLoginChannel: lastLoginChannel, onLoginClick: _onLoginClick),
    ];

    fetchData();
  }

  fetchData() async {
    //初始化dio
    await NetWorkManager.instance.initDio();
    await EventReportManager.eventReportOfFirebase(regsOpen);
    lastLoginChannel = await SpUtil.spGetLoginChannel();
    if (mounted) {
      setState(() {});
    }
    LoginWayModel? loginWayModel = await LoginViewModel.getLoginWay();
    if (isAvailable(loginWayModel?.result)) {
      loginWayItems = [];
      for (int i = 0; i < loginWayModel!.result!.length; i++) {
        LoginWayItem? item = loginWayModel.result![i];
        loginWayItems.add(LoginWay(
            loginChannel: item.loginChannel,
            lastLoginChannel: lastLoginChannel,
            onLoginClick: _onLoginClick));
        if (i != loginWayModel.result!.length - 1) {
          LoginWayItem? preItem = loginWayModel.result![i + 1];
          loginWayItems.add(SizedBox(height: lastLoginChannel == preItem.loginChannel ? 15 : 25));
        }
      }

      if (mounted) {
        setState(() {});
      }
    }
  }

  _onLoginClick(int? loginChannel) {
    loginKey.currentState?.onLoginClick(loginChannel);
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        fit: StackFit.expand,
        children: [
          Positioned(
            top: mediaQuery.size.height * 0.15 + mediaQuery.padding.top,
            left: 0,
            width: mediaQuery.size.width,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(height: mediaQuery.padding.top),
                Image.asset(
                  "assets/images/sign/icon_app_logo.png",
                  width: 90,
                  height: 90,
                ),
                const SizedBox(height: 30),
                Image.asset(
                  "assets/images/sign/icon_app_name.png",
                  width: 77,
                  height: 17,
                ),
              ],
            ),
          ),
          Positioned(
              bottom: mediaQuery.padding.bottom + 68 + 33 + 40,
              left: 0,
              width: mediaQuery.size.width,
              child: PlatformLogin(
                  key: loginKey,
                  loginWayItems: loginWayItems,
                  radioKey: radioKey,
                  termsOfService: () {
                    _termsOfService();
                  },
                  privacyPolicy: () {
                    _privacyPolicy();
                  },
                  onIsLoginIng: (isLogin) {
                    if (mounted) {
                      setState(() {
                        isLoginIng = isLogin;
                      });
                    }
                  })),
          Positioned(
            bottom: mediaQuery.padding.bottom + 68,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  height: 33,
                  child: DecoratedBox(
                    decoration: BoxDecoration(
                      color: HexColor("#EFF4FF"),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        const SizedBox(width: 15),
                        Image.asset(
                          'assets/images/sign/icon_polyglot.png',
                          width: 15,
                          height: 15,
                        ),
                        const SizedBox(width: 10),
                        GestureDetector(
                          onTap: () async {
                            ///展示语言列表
                            await Get.toNamed('/storyLanguagePage');
                          },
                          child: Row(
                            children: [
                              Obx(() {
                                String? currentName =
                                    CommonManager.instance.contentLanguageItem.value.name;
                                String? currentDesc =
                                    CommonManager.instance.contentLanguageItem.value.desc;
                                String currentLanguage = currentName ?? '';
                                if (isAvailable(currentDesc)) {
                                  currentLanguage += '($currentDesc)';
                                }
                                return FittedBox(
                                  child: Text("${"story_language".tr} : $currentLanguage",
                                      style: TextStyle(
                                          fontSize: 13,
                                          color: HexColor("#1F1F2F"),
                                          fontWeight: FontWeight.bold)),
                                );
                              }),
                              const SizedBox(width: 10),
                              Image.asset(
                                'assets/images/sign/icon_expand.png',
                                width: 12,
                                height: 8,
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 10),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            bottom: mediaQuery.padding.bottom + 16,
            left: 20,
            right: 20,
            child: Row(
              children: [
                RadioWidget(key: radioKey),
                const SizedBox(width: 7),
                Expanded(
                    child: RichText(
                        softWrap: true,
                        text: TextSpan(
                            text: '${"agree_login".tr} ',
                            style: TextStyle(fontSize: 12, color: HexColor("#FF888C94")),
                            children: [
                              TextSpan(
                                  text: "terms_and_privacy".tr,
                                  style: TextStyle(fontSize: 12, color: HexColor("#0087FB")),
                                  recognizer: TapGestureRecognizer()
                                    ..onTap = () {
                                      loginKey.currentState?.showTermsAndPrivacy();
                                    })
                            ]))),
              ],
            ),
          ),
          if (isLoginIng) const Positioned(child: LottieAnimationView())
        ],
      ),
    );
  }

  //Terms of Service
  Future<void> _termsOfService() async {
    await Get.toNamed('/webViewPage', arguments: {'title': 'terms'.tr, 'url': termsOfServiceUrl});
  }

  //Privacy Policy
  Future<void> _privacyPolicy() async {
    await Get.toNamed('/webViewPage', arguments: {'title': 'pricavy'.tr, 'url': privacyPolicyUrl});
  }
}

class PlatformLogin extends StatefulWidget {
  final List<Widget> loginWayItems;
  final VoidCallback termsOfService;
  final VoidCallback privacyPolicy;
  final GlobalKey<RadioWidgetState> radioKey;
  final Function(bool isLogin) onIsLoginIng;

  const PlatformLogin(
      {super.key,
      required this.loginWayItems,
      required this.radioKey,
      required this.termsOfService,
      required this.privacyPolicy,
      required this.onIsLoginIng});

  @override
  State<PlatformLogin> createState() => PlatformLoginState();
}

class PlatformLoginState extends State<PlatformLogin> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }

  onLoginClick(int? loginChannel) {
    if (loginChannel == 10) {
      //facebook
      _faceBookLogin();
    } else if (loginChannel == 11) {
      //google
      _googleLogin();
    } else if (loginChannel == 12) {
      //apple
      _appleLogin();
    } else if (loginChannel == 15) {
      //tiktok
      _tiktokLogin();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: widget.loginWayItems,
    );
  }

  //协议是否同意
  bool judgeAgreement() {
    bool? isChecked = widget.radioKey.currentState?.isAgreeUserAgreement;

    if (isChecked == false) {
      showTermsAndPrivacy();
      return false;
    } else {
      return true;
    }
  }

  void showTermsAndPrivacy() {
    Get.dialog(
      AlertDialog(
        title: Center(
          child: Text(
            "terms_and_privacy".tr,
            style:
                TextStyle(fontSize: 16, color: HexColor('#FF000000'), fontWeight: FontWeight.bold),
          ),
        ),
        content: RichText(
          text: TextSpan(children: [
            TextSpan(
              text: "${"by_using_app".trParams({'param': 'UrNovel'})}\n",
              style: TextStyle(fontSize: 13, color: HexColor('#FF000000')),
            ),
            const WidgetSpan(
              child: SizedBox(height: 30),
            ),
            TextSpan(
              text: "${"terms_cover".tr}\n",
              style: TextStyle(fontSize: 13, color: HexColor('#FF000000')),
            ),
            TextSpan(
                text: "${'view_terms'.tr}\n",
                style: TextStyle(fontSize: 13, color: HexColor('#FF1B86FF')),
                recognizer: TapGestureRecognizer()..onTap = widget.termsOfService),
            const WidgetSpan(
              child: SizedBox(height: 30),
            ),
            TextSpan(
              text: "${"privacy_covers".tr}\n",
              style: TextStyle(fontSize: 13, color: HexColor('#FF000000')),
            ),
            TextSpan(
                text: "${'view_privacy'.tr}\n",
                style: TextStyle(fontSize: 13, color: HexColor('#FF1B86FF')),
                recognizer: TapGestureRecognizer()..onTap = widget.privacyPolicy),
          ]),
        ),
        actions: [
          Column(
            children: [
              Divider(
                height: 1,
                color: HexColor('#FFE4E6E8'),
              ),
              TextButton(
                onPressed: () {
                  Get.back(); // 关闭对话框
                  widget.radioKey.currentState?.setIsAgreeUserAgreement(true);
                },
                child: SizedBox(
                  width: double.infinity,
                  child: Text(
                    "agreen".tr,
                    style: TextStyle(fontSize: 16, color: HexColor('#FF1B86FF')),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
              Divider(
                height: 1,
                color: HexColor('#FFE4E6E8'),
              ),
              TextButton(
                onPressed: () {
                  widget.radioKey.currentState?.setIsAgreeUserAgreement(false);
                  Get.back();
                },
                child: SizedBox(
                  width: double.infinity,
                  child: Text(
                    "decline".tr,
                    style: TextStyle(fontSize: 16, color: HexColor('#FF000000')),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
      barrierDismissible: false, // 设置为 true 时，点击对话框外部可以关闭对话框
    );
  }

  ///faceBook登录
  Future<void> _faceBookLogin() async {
    if (judgeAgreement()) {
      try {
        TrackingStatus? status;
        if (Platform.isIOS) {
          status = await AppTrackingTransparency.trackingAuthorizationStatus;
        }
        final LoginResult result = await FacebookAuth.instance.login(
          permissions: ['email', 'public_profile'],
          loginBehavior: LoginBehavior.nativeWithFallback,
          loginTracking:
              status == TrackingStatus.authorized ? LoginTracking.enabled : LoginTracking.limited,
        );

        if (result.status == LoginStatus.success) {
          String? tokenString = result.accessToken?.tokenString;
          String? userName;
          String? email;
          String? userId;
          String? picture;
          if (result.accessToken?.type == AccessTokenType.classic) {
            // 获取用户信息
            final userData = await FacebookAuth.instance.getUserData();
            userName = userData['name'];
            email = userData['email'];
            userId = userData['id'];
            picture = userData['picture']['data']['url'];
          } else if (result.accessToken?.type == AccessTokenType.limited) {
            final token = result.accessToken as LimitedToken;
            userName = token.userName;
            email = token.userEmail;
            userId = token.userId;
          }
          String? deviceId = await Device.getDeviceId();
          var params = {
            "userName": userId,
            "loginChannel": 10, //facebookApp
            "brand": "UrNovel", //客户端品牌（UrNovel,NovelTime）
            "deviceToken": deviceId,
            "email": email,
            "thirdToken": tokenString,
            "isReserved": 1, //是否恢复账号,删除超过15天
            "name": userName, //用户名称
            "headerUrl": picture,
          };
          widget.onIsLoginIng(true);
          LoginViewModel.authLogin(params, (loginModel) async {
            widget.onIsLoginIng(false);
            await loginReport(loginModel, "Facebook");
          });
        } else {
          showToast('${result.message}');
        }
        // 你可以在这里更新 UI 或进行其他操作
      } catch (e) {
        showToast('$e');
      }
    }
  }

  ///google登录
  void _googleLogin() async {
    if (judgeAgreement()) {
      try {
        // 触发Google登录流程
        GoogleSignIn googleSignIn = GoogleSignIn(
          clientId: Platform.isIOS ? googleIosClientId : googleAndroidClientId,
          scopes: [
            'email',
            'https://www.googleapis.com/auth/userinfo.profile',
          ],
        );
        final GoogleSignInAccount? googleUser = await googleSignIn.signIn();
        if (isAvailable(googleUser)) {
          // 获取Google用户信息
          final GoogleSignInAuthentication? googleAuth = await googleUser?.authentication;
          // 这里你可以使用googleAuth.accessToken来获取用户信息，或者进行后续操作
          String? deviceId = await Device.getDeviceId();
          var params = {
            "userName": googleUser?.id,
            "loginChannel": 11, //Google登录方式
            "brand": "UrNovel", //客户端品牌（UrNovel,NovelTime）
            "deviceToken": deviceId,
            "email": googleUser?.email,
            "thirdToken": googleAuth?.idToken,
            "accessToken": googleAuth?.accessToken,
            "isReserved": 1, //是否恢复账号,删除超过15天
            "name": googleUser?.displayName, //用户名称
            "headerUrl": googleUser?.photoUrl,
          };
          widget.onIsLoginIng(true);
          LoginViewModel.authLogin(params, (loginModel) async {
            widget.onIsLoginIng(false);
            await loginReport(loginModel, "Google");
          });
        } else {
          showToast("GoogleSignInAccount is not available");
        }
      } catch (error) {
        showToast("$error");
      }
    }
  }

  /// apple登录
  void _appleLogin() async {
    if (judgeAgreement()) {
      try {
        AuthorizationCredentialAppleID? credential = await SignInWithApple.getAppleIDCredential(
          scopes: [
            AppleIDAuthorizationScopes.email,
            AppleIDAuthorizationScopes.fullName,
          ],
        );
        if (isAvailable(credential)) {
          String? deviceId = await Device.getDeviceId();
          var params = {
            "userName": credential.userIdentifier,
            "loginChannel": 12, //Apple登录方式
            "brand": "UrNovel", //客户端品牌（UrNovel,NovelTime）
            "deviceToken": deviceId,
            "email": credential.email,
            "thirdToken": credential.identityToken,
            "isReserved": 1, //是否恢复账号,删除超过15天
            "name": '${credential.familyName} ${credential.givenName}', //用户名称
            "headerUrl": "",
          };
          widget.onIsLoginIng(true);
          LoginViewModel.authLogin(params, (loginModel) async {
            widget.onIsLoginIng(false);
            await loginReport(loginModel, "Apple");
          });
        } else {
          showToast("Apple ID is not available");
        }
      } catch (error) {
        showToast('$error');
      }
    }
  }

  /// tiktok登录
  void _tiktokLogin() async {
    if (judgeAgreement()) {
      try {
        NativeBridge.instance.callTiktokLogin(
          (arguments) async {
            logD("tiktokLoginSuc: $arguments");

            // 解析JSON字符串
            Map<String, dynamic> authData = {};
            String code = "";
            String codeVerifier = "";

            try {
              authData = Map<String, dynamic>.from(jsonDecode(arguments));
              code = authData['code'] ?? "";
              codeVerifier = authData['codeVerifier'] ?? "";
              logD("解析成功 - code: $code, codeVerifier: $codeVerifier");
            } catch (e) {
              logD("JSON解析失败: $e, 将使用原始字符串作为token");
              code = arguments;
            }

            if (isAvailable(code)) {
              String? deviceId = await Device.getDeviceId();
              var params = {
                "userName": codeVerifier,
                "loginChannel": 15, //titok登录方式
                "brand": "UrNovel", //客户端品牌（UrNovel,NovelTime）
                "deviceToken": deviceId,
                "email": "",
                "thirdToken": code,
                "isReserved": 1, //是否恢复账号,删除超过15天
                "name": "", //用户名称
                "headerUrl": "",
              };
              widget.onIsLoginIng(true);
              LoginViewModel.authLogin(params, (loginModel) async {
                widget.onIsLoginIng(false);
                await loginReport(loginModel, "TikTok");
              });
            } else {
              showToast("Tiktok token is not available");
            }
          },
          (arguments) {
            logD("tiktokLoginFailed: $arguments");
          },
        );
      } catch (error) {
        showToast('$error');
      }
    }
  }

  Future<void> loginReport(LoginModel loginModel, String? channel) async {
    if (loginModel.code == 200) {
      String regs = regsApple;
      int loginChannel = 0;
      if (channel == 'Facebook') {
        regs = regsFacebook;
        loginChannel = 10;
      } else if (channel == 'Google') {
        regs = regsGoogle;
        loginChannel = 11;
      } else if (channel == 'Apple') {
        regs = regsApple;
        loginChannel = 12;
      } else if (channel == 'TikTok') {
        regs = regsTiktok;
        loginChannel = 15;
      }

      await SpUtil.spSetLoginChannel(loginChannel);
      await EventReportManager.eventReportOfFirebase(regs);
      await EventReportManager.eventReportOfCommon(addPaymentInfo);
      await EventReportManager.eventReportOfFacebook(fbAddPaymentInfo);

      UserInfoModel? userModel = await SpUtil.spGetUserInfo();
      if (userModel?.newUser == true) {
        await EventReportManager.eventReportOfCommon(regsRegistration);
        await EventReportManager.logCompletedRegistration(registrationMethod: regs);
        //新用户
        await Get.toNamed("/welcomeInfoPage");
      } else {
        await Get.offAllNamed("/bottomNavBarPage");
      }
    } else {
      showToast(loginModel.msg ?? "login failed");
    }
  }
}

//登录方式
class LoginWay extends StatelessWidget {
  final int? loginChannel; //登录渠道(10:facebookApp; 11:googleApp; 12:appleApp 15:tiktokApp)
  final int? lastLoginChannel; //上次登录方式
  final Function(int? loginChannel) onLoginClick;

  const LoginWay(
      {super.key,
      required this.loginChannel,
      required this.lastLoginChannel,
      required this.onLoginClick});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
        width: double.infinity,
        height: lastLoginChannel == loginChannel ? 54 : 44,
        child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 17),
            child: Stack(
              children: [
                Positioned(
                    top: lastLoginChannel == loginChannel ? 10 : 0,
                    left: 13,
                    right: 13,
                    bottom: 0,
                    child: DecoratedBox(
                      decoration: BoxDecoration(
                        color: loginChannel == 10
                            ? HexColor("#1877F2")
                            : loginChannel == 11
                                ? HexColor("#F2F2F2")
                                : loginChannel == 15
                                    ? HexColor("#000000")
                                    : HexColor("#000000"),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: TextButton.icon(
                        onPressed: () {
                          onLoginClick(loginChannel);
                        },
                        icon: SizedBox(
                          width: loginChannel == 10
                              ? 30
                              : loginChannel == 11
                                  ? 22
                                  : 16,
                          height: loginChannel == 10
                              ? 30
                              : loginChannel == 11
                                  ? 22
                                  : 20,
                          child: loginChannel == 10
                              ? Image.asset("assets/images/sign/icon_faceBook.png")
                              : loginChannel == 11
                                  ? Image.asset("assets/images/sign/icon_google.png")
                                  : loginChannel == 15
                                      ? Image.asset("assets/images/sign/icon_tiktok.png")
                                      : Image.asset("assets/images/sign/icon_apple.png"),
                        ),
                        label: Text(
                            loginChannel == 10
                                ? "facebook_login".tr
                                : loginChannel == 11
                                    ? "google_login".tr
                                    : loginChannel == 15
                                        ? "Continue with TikTok"
                                        : "apple_login".tr,
                            style: TextStyle(
                                fontSize: 18,
                                color: loginChannel == 11
                                    ? HexColor("#1F1F1F")
                                    : HexColor("#FFFFFF"))),
                      ),
                    )),
                if (lastLoginChannel == loginChannel)
                  Positioned(
                    top: 0,
                    right: 0,
                    child: DecoratedBox(
                      decoration: BoxDecoration(
                          color: HexColor('#FD6178'), borderRadius: BorderRadius.circular(5)),
                      child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                          child: Text(
                            'last_login'.tr,
                            style: TextStyle(
                                fontSize: 10, color: Colors.white, fontWeight: FontWeight.bold),
                          )),
                    ),
                  )
              ],
            )));
  }
}

//todo: 协议&弹窗
class RadioWidget extends StatefulWidget {
  final bool isAgree;
  final Function(bool isAgree)? onIsAgreeChanged;

  const RadioWidget({super.key, this.isAgree = true, this.onIsAgreeChanged});

  @override
  State<RadioWidget> createState() => RadioWidgetState();
}

class RadioWidgetState extends State<RadioWidget> {
  late bool isAgreeUserAgreement;

  @override
  void initState() {
    super.initState();

    isAgreeUserAgreement = widget.isAgree;
  }

  setIsAgreeUserAgreement(bool? value) {
    if (mounted) {
      setState(() {
        isAgreeUserAgreement = value ?? false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {
          if (mounted) {
            setState(() {
              isAgreeUserAgreement = !isAgreeUserAgreement; // 切换状态
            });

            if (isAvailable(widget.onIsAgreeChanged)) {
              widget.onIsAgreeChanged!(isAgreeUserAgreement); // 通知父组件状态改变
            }
          }
        },
        child: Container(
          width: 15,
          height: 15,
          color: Colors.transparent,
          child: Image.asset(
              isAgreeUserAgreement
                  ? "assets/images/sign/icon_check.png"
                  : "assets/images/sign/icon_uncheck.png",
              fit: BoxFit.contain),
        ));
  }
}
