import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Launch&Login/Model/user_model.dart';
import 'package:UrNovel/Launch&Login/ViewModel/ViewModel.dart';
import 'package:UrNovel/Util/Common/Animation/lottie_animation_view.dart';
import 'package:UrNovel/Util/Common/CommonManager/common_manager.dart';
import 'package:UrNovel/Util/DataReportManager/event_report_manager.dart';
import 'package:UrNovel/Util/SharedPreferences/shared_preferences.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../MainPage/Genres/Model/tag_groups_model.dart';
import '../../MainPage/Genres/Model/tags_model.dart';
import '../../MainPage/Genres/ViewModel/ViewModel.dart';
import '../../Util/DataReportManager/event_name_config.dart';
import '../../Util/Extensions/colorUtil.dart';
import '../../Util/StatusManagement/status_management.dart';
import '../../Util/genericUtil.dart';

class WelcomeInfoPage extends BaseFulWidget {
  WelcomeInfoPage({super.key});

  @override
  State<WelcomeInfoPage> createState() => _WelcomeInfoPageState();
}

class _WelcomeInfoPageState extends State<WelcomeInfoPage> {
  late bool _isLoading;
  late List<TagsGroupModel>? _tagsGroupList;
  final BookDetailReportController _controller =
      findGetXInstance(BookDetailReportController());
  late List<int> _isShowAllSections;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    _isLoading = true;
    _tagsGroupList = [];
    _isShowAllSections = [];

    showSystemUiMode();
    _fetchData();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _fetchData() async {
    await _loadTagGroups();
  }

  //复用分类接口
  Future<void> _loadTagGroups() async {
    List<TagGroupItem>? tagGroups = await GenresViewModel.getTagGroupList();
    if (isAvailable(tagGroups)) {
      for (var i = 0; i < tagGroups!.length; i++) {
        TagGroupItem item = tagGroups[i];
        TagsGroupModel? model = await GenresViewModel.getTagList(item.groupId);
        if (isAvailable(model) && isAvailable(model?.tagInfoList)) {
          _tagsGroupList?.add(model!);
        }
      }
    }

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  //上传数据
  Future<void> saveNewUserChooseData() async {
    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    UserInfoModel? infoModel = await SpUtil.spGetUserInfo();

    var params = {
      "uid": infoModel?.uid, //用户id
      "sex": 0, //性别,传对应的数字。1：男；2：女；0：未设置
      "lang": CommonManager.instance.contentLanguage.code, //语言环境
      "favoriteTags": _getFavoriteTags, //喜欢的分类，多个用英文&拼接
    };
    LoginViewModel.saveNewUserChooseData(params).then((model) async {
      if (mounted) {
        setState(() {
          _isLoading = true;
        });
      }

      EventReportManager.eventReportOfFirebase(startReading);
      await Get.offAllNamed("/bottomNavBarPage");
    }).catchError((e) async {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
      await Get.offAllNamed("/bottomNavBarPage");
    });
  }

  String get _getFavoriteTags {
    var tags = "";
    if (isAvailable(_controller.reportList)) {
      for (var i = 0; i < _controller.reportList.length; i++) {
        var categoryId = _controller.reportList[i];
        tags += '$categoryId';
        tags += "&";
      }
      tags = tags.substring(0, tags.length - 1);
    }

    return tags;
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var count = _tagsGroupList?.length ?? 0;
    var tagWidth = (mediaQuery.size.width - 40) / 2.0;
    var tagHeight = tagWidth / 4.0;

    return Scaffold(
      backgroundColor: HexColor('#FFFFFF'),
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.only(
                      top: mediaQuery.padding.top + 25, left: 2, right: 2),
                  child: Text(
                    'favorite+genres'.tr,
                    style: TextStyle(
                      color: HexColor('#1F1F2F'),
                      fontSize: 25,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                SizedBox(height: 15),
                Expanded(
                  child: MediaQuery.removePadding(
                      context: context,
                      removeTop: true,
                      removeBottom: true,
                      removeLeft: true,
                      removeRight: true,
                      child: ListView.builder(
                          itemCount: count,
                          itemBuilder: (context, index) {
                            if (index < count) {
                              TagsGroupModel model = _tagsGroupList![index];

                              var tagsCount = 0;
                              double tagsHeight = 0.0;
                              if (_isShowAllSections.contains(index)) {
                                ///展开
                                tagsCount = model.tagInfoList!.length;
                                tagsHeight = (tagHeight + 10) *
                                        (tagsCount % 2 == 0
                                            ? tagsCount / 2
                                            : (tagsCount + 1) / 2) -
                                    10;
                              } else {
                                ///收起
                                tagsCount = 4 < model.tagInfoList!.length
                                    ? 4
                                    : model.tagInfoList!.length;
                                tagsHeight = (tagHeight + 10) *
                                        (tagsCount % 2 == 0
                                            ? tagsCount / 2
                                            : (tagsCount + 1) / 2) -
                                    10;
                              }
                              return Column(
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        left: 1, right: 0),
                                    child: SizedBox(
                                      width: double.infinity,
                                      height: 50,
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            model.groupName ?? "",
                                            style: TextStyle(
                                                fontSize: 16,
                                                color: HexColor('#000000'),
                                                fontWeight: FontWeight.bold),
                                          ),
                                          if (4 < model.tagInfoList!.length)
                                            SizedBox(
                                                width: 36,
                                                height: 36,
                                                child: IconButton(
                                                  onPressed: () {
                                                    if (mounted) {
                                                      setState(() {
                                                        if (_isShowAllSections
                                                            .contains(index)) {
                                                          _isShowAllSections
                                                              .remove(index);
                                                        } else {
                                                          _isShowAllSections
                                                              .add(index);
                                                        }
                                                      });
                                                    }
                                                  },
                                                  style: IconButton.styleFrom(
                                                    padding: EdgeInsets.only(
                                                        left: 12, right: 12),
                                                  ),
                                                  icon: Image.asset(
                                                    _isShowAllSections
                                                            .contains(index)
                                                        ? 'assets/images/common/icon_arrow_up_black.png'
                                                        : 'assets/images/common/icon_arrow_down_black.png',
                                                    width: 12,
                                                    height: 8,
                                                    fit: BoxFit.cover,
                                                  ),
                                                )),
                                        ],
                                      ),
                                    ),
                                  ),
                                  SizedBox(
                                    width: double.infinity,
                                    height: tagsHeight,
                                    child: GridView.builder(
                                        scrollDirection: Axis.vertical,
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                        // 设置禁止滚动
                                        padding: EdgeInsets.zero,
                                        gridDelegate:
                                            SliverGridDelegateWithFixedCrossAxisCount(
                                          crossAxisCount: 2,
                                          childAspectRatio:
                                              tagWidth / tagHeight,
                                          mainAxisSpacing: 10,
                                          crossAxisSpacing: 10,
                                        ),
                                        itemCount: tagsCount,
                                        itemBuilder: (context, index) {
                                          if (index < tagsCount) {
                                            TagInfoItem tagItem =
                                                model.tagInfoList![index];
                                            return GestureDetector(
                                              onTap: () {
                                                setState(() {
                                                  _controller
                                                      .selected(tagItem.id);
                                                });
                                              },
                                              child: SizedBox(
                                                width: tagWidth,
                                                height: tagHeight,
                                                child: DecoratedBox(
                                                  decoration: BoxDecoration(
                                                    image: DecorationImage(
                                                      image: AssetImage(_controller
                                                              .isSelected(
                                                                  tagItem.id)
                                                          ? "assets/images/launch/icon_tag_selected.png"
                                                          : "assets/images/launch/icon_tag_normal.png"),
                                                      fit: BoxFit.cover,
                                                    ),
                                                  ),
                                                  child: Center(
                                                    child: Text(
                                                      tagItem.name ?? "",
                                                      style: TextStyle(
                                                          color: HexColor(
                                                              '#787C87'),
                                                          fontSize: 12,
                                                          fontWeight:
                                                              FontWeight.bold),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            );
                                          }
                                          return SizedBox.shrink();
                                        }),
                                  )
                                ],
                              );
                            } else {
                              return SizedBox.shrink();
                            }
                          })),
                ),
                const SizedBox(height: 15),
                Container(
                  height: 44,
                  width: double.infinity,
                  margin: const EdgeInsets.symmetric(horizontal: 15),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        HexColor('#0087FB'),
                        HexColor('#0099F8'),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: TextButton(
                      onPressed: () async {
                        //todo：上传个人爱好信息
                        await saveNewUserChooseData();
                      },
                      child: Text(
                        'start_continue'.tr,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: HexColor('#FFFFFF'),
                        ),
                      )),
                ),
                SizedBox(height: mediaQuery.padding.bottom + 30),
              ],
            ),
          ),
          if (_isLoading) const LottieAnimationView()
        ],
      ),
    );
  }
}
