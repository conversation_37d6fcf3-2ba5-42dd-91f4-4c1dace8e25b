import 'dart:async';

import 'package:UrNovel/Util/Common/network_image_util.dart';
import 'package:UrNovel/Util/DataReportManager/event_report_manager.dart';
import 'package:UrNovel/Util/Extensions/colorUtil.dart';
import 'package:UrNovel/Util/SharedPreferences/shared_preferences.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../BaseWidget/base_ful_widget.dart';
import '../../Util/DataReportManager/event_name_config.dart';
import '../../Util/LocalNotificationManager/local_notification_manager.dart';
import '../../Util/enum.dart';
import '../Model/promotion_content_model.dart';
import '../ViewModel/ViewModel.dart';

class AdLaunchPage extends StatefulWidget {
  const AdLaunchPage({super.key});

  @override
  State<AdLaunchPage> createState() => _AdLaunchPageState();
}

class _AdLaunchPageState extends State<AdLaunchPage> {
  final timerCounterKey = GlobalKey<_TimerCounterState>();
  late PromotionContentItem? result;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    result = null;
    hideSystemUiMode();

    loadData();
  }

  Future<void> loadData() async {
    // 设置为true后延迟时间开始淡入
    await Future.delayed(const Duration(seconds: 1));
    // 启动页倒计时
    timerCounterKey.currentState?.startTimer();

    PromotionContentModel? contentModel = await SpUtil.spGetLaunchNovelAd();
    if (isAvailable(contentModel)) {
      if (mounted) {
        setState(() {
          result = contentModel?.result;
        });
      }
    }

    await LoginViewModel.getPromotionContent();

    // TODO: 获取用户信息
    await LoginViewModel.getUserInfo();
  }

  Future<void> toMainPage() async {
    await Get.offAllNamed("/bottomNavBarPage");
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }

  Future<void> toNovelReadPage() async {
    toMainPage();
    await SpUtil.spSetNovelAdClick(true);
    // TODO: 跳转阅读页
    //todo:去往阅读页
    await LocalNotificationManager.toNovelReadPage({
      "bookId": result?.bookId,
      'jumpType': BookDetailJumpType.other
    });

    await EventReportManager.eventReportOfFirebase(clickLaunch);
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var imgScale = 3 / 4;
    int tagListCount =
        3 < (result?.tagList?.length ?? 0) ? 3 : (result?.tagList?.length ?? 0);
    return SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: Stack(
          children: [
            Positioned(top: 0, left: 0, right: 0, bottom: 0, child: LogoView()),
            if (isAvailable(result?.cover))
              Positioned(
                  top: mediaQuery.padding.top + 30,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: Stack(
                    children: [
                      Positioned(
                          top: 0,
                          left: 0,
                          right: 0,
                          height: mediaQuery.size.width / imgScale,
                          child: NetworkImageUtil(
                            imageUrl: result?.cover,
                            showPlaceholder: false,
                            showError: false,
                          )),
                      Positioned(
                          top: 0,
                          left: 0,
                          right: 0,
                          height: mediaQuery.padding.top + 100,
                          child: DecoratedBox(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  ColorsUtil.hexColor(0x000000, alpha: 1.0),
                                  ColorsUtil.hexColor(0x000000, alpha: 0.9),
                                  ColorsUtil.hexColor(0x000000, alpha: 0.7),
                                  ColorsUtil.hexColor(0x000000, alpha: 0.5),
                                  ColorsUtil.hexColor(0x000000, alpha: 0.3),
                                  ColorsUtil.hexColor(0x000000, alpha: 0.0),
                                ],
                              ),
                            ),
                          )),
                      Positioned(
                          top: mediaQuery.size.width / imgScale / 2 +
                              mediaQuery.padding.top +
                              30,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          child: DecoratedBox(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  ColorsUtil.hexColor(0x000000, alpha: 0.0),
                                  ColorsUtil.hexColor(0x000000, alpha: 0.5),
                                  ColorsUtil.hexColor(0x000000, alpha: 0.7),
                                  ColorsUtil.hexColor(0x000000, alpha: 0.9),
                                  ColorsUtil.hexColor(0x000000, alpha: 1.0),
                                  ColorsUtil.hexColor(0x000000, alpha: 1.0),
                                  ColorsUtil.hexColor(0x000000, alpha: 1.0),
                                  ColorsUtil.hexColor(0x000000, alpha: 1.0),
                                  ColorsUtil.hexColor(0x000000, alpha: 1.0),
                                  ColorsUtil.hexColor(0x000000, alpha: 1.0),
                                  ColorsUtil.hexColor(0x000000, alpha: 1.0),
                                ],
                              ),
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Padding(
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 18),
                                    child: Text(result?.title ?? '',
                                        style: TextStyle(
                                            color: HexColor("#FFFFFF"),
                                            fontSize: 22,
                                            fontWeight: FontWeight.bold),
                                        maxLines: 3,
                                        overflow: TextOverflow.ellipsis,
                                        textAlign: TextAlign.center)),
                                if (0 < tagListCount)
                                  Padding(
                                      padding: EdgeInsets.only(
                                          top: 13,
                                          left: 22,
                                          bottom: 38,
                                          right: 22),
                                      child: SizedBox(
                                        width: double.infinity,
                                        height: 18,
                                        child: ListView.builder(
                                            scrollDirection: Axis.horizontal,
                                            itemCount: tagListCount,
                                            itemBuilder: (context, index) {
                                              String? tagStr =
                                                  result?.tagList?[index];
                                              return Padding(
                                                padding: EdgeInsets.only(
                                                    right: index !=
                                                            tagListCount - 1
                                                        ? 7
                                                        : 0),
                                                child: DecoratedBox(
                                                  decoration: BoxDecoration(
                                                    color: ColorsUtil.hexColor(
                                                        0xF5F5F5,
                                                        alpha: 0.2),
                                                    borderRadius:
                                                        const BorderRadius.all(
                                                            Radius.circular(
                                                                12)),
                                                  ),
                                                  child: Padding(
                                                      padding:
                                                          EdgeInsets.symmetric(
                                                              horizontal: 10),
                                                      child: Center(
                                                        child: Text(
                                                          tagStr!,
                                                          style: TextStyle(
                                                              color: HexColor(
                                                                  "#7E839D"),
                                                              fontSize: 11),
                                                        ),
                                                      )),
                                                ),
                                              );
                                            }),
                                      )),
                                GestureDetector(
                                  onTap: () {
                                    Get.back();
                                    // widget.onFreeSecondPage.call();
                                  },
                                  behavior: HitTestBehavior.opaque,
                                  child: Container(
                                    width: double.infinity,
                                    margin: EdgeInsets.only(
                                        left: 65, right: 65, bottom: 66),
                                    decoration: BoxDecoration(
                                      borderRadius: const BorderRadius.all(
                                          Radius.circular(12)),
                                      gradient: LinearGradient(
                                        begin: Alignment.centerLeft,
                                        end: Alignment.centerRight,
                                        colors: [
                                          HexColor('#0087FB'),
                                          HexColor('#0099F8')
                                        ],
                                      ),
                                    ),
                                    child: TextButton(
                                      onPressed: toNovelReadPage,
                                      style: ButtonStyle(
                                          foregroundColor:
                                              WidgetStateProperty.all(
                                                  HexColor('#FFFFFF'))),
                                      child: Text("read_now".tr,
                                          style: TextStyle(
                                              fontSize: 16,
                                              color: HexColor('#FFFFFF'),
                                              fontWeight: FontWeight.bold)),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          )),
                    ],
                  )),
            Positioned(
              top: mediaQuery.padding.top + 30,
              left: 13,
              right: 13,
              child: Container(
                width: double.infinity,
                alignment: Alignment.centerRight,
                child: SizedBox(
                    width: 68,
                    height: 26,
                    child: TimerCounter(
                      key: timerCounterKey,
                      onTimerEndOrCancel: () {
                        toMainPage();
                      },
                    )),
              ),
            ),
          ],
        ));
  }
}

//todo：倒计时
class TimerCounter extends StatefulWidget {
  final VoidCallback? onTimerEndOrCancel;

  const TimerCounter({super.key, required this.onTimerEndOrCancel});

  @override
  State<TimerCounter> createState() => _TimerCounterState();
}

class _TimerCounterState extends State<TimerCounter> {
  int _counter = 3;
  Timer? _timer;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    _timer?.cancel();
    if (isAvailable(_timer)) {
      _timer = null;
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        widget.onTimerEndOrCancel?.call();
      },
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
              color: ColorsUtil.hexColor(0xDDDDDD, alpha: 1.0), width: 1),
          borderRadius: const BorderRadius.all(Radius.circular(12)),
        ),
        child: Row(
          children: [
            const SizedBox(width: 15),
            Text(
              "${_counter}s",
              style: TextStyle(color: HexColor('#DDDDDD'), fontSize: 13),
            ),
            const SizedBox(width: 10),
            SizedBox(
              width: 9,
              height: 10,
              child: Image.asset('assets/images/launch/skip.png'),
            )
          ],
        ),
      ),
    );
  }

  void startTimer() {
    const oneSec = Duration(seconds: 1);
    _timer = Timer.periodic(oneSec, (Timer timer) {
      setState(() {
        if (_counter > 0) {
          _counter--;
        } else {
          timer.cancel();
          // 倒计时结束后的操作
          widget.onTimerEndOrCancel?.call();
        }
      });
    });
  }
}

//todo：logoView
class LogoView extends StatelessWidget {
  LogoView({super.key});

  final mediaQuery = MediaQuery.of(Get.context!);

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
        decoration: BoxDecoration(color: Colors.black),
        child: Stack(children: [
          Positioned(
              top: 182,
              left: 0,
              right: 0,
              child: Image.asset('assets/images/launch/icon_launch_img.png',
                  width: 255, height: 148, fit: BoxFit.contain)),
          Positioned(
              top: 330,
              left: 0,
              right: 0,
              bottom: 0,
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Image.asset(
                      "assets/images/sign/icon_app_logo.png",
                      width: 48,
                      height: 48,
                    ),
                    const SizedBox(height: 16),
                    Image.asset(
                      "assets/images/sign/icon_app_name.png",
                      width: 95,
                      height: 20,
                    ),
                    SizedBox(height: mediaQuery.padding.bottom + 22),
                  ]))
        ]));
  }
}
