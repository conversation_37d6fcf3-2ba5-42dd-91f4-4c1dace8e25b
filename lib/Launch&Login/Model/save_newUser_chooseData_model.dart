import 'package:json_annotation/json_annotation.dart';
part 'save_newUser_chooseData_model.g.dart';

@JsonSerializable()
class SaveNewUserChooseDataModel {
  int? code;
  bool? result;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  SaveNewUserChooseDataModel({this.code, this.result, this.msg, this.sysAt, this.cost, this.traceId});

  factory SaveNewUserChooseDataModel.fromJson(Map<String, dynamic> json) => _$SaveNewUserChooseDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$SaveNewUserChooseDataModelToJson(this);
}