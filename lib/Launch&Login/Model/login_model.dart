
import 'package:json_annotation/json_annotation.dart';
part 'login_model.g.dart';

@JsonSerializable()
class LoginModel {
  int? code;
  @JsonKey(name: "result")
  LoginInfoModel? loginInfo;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  LoginModel({this.code, this.loginInfo, this.msg, this.sysAt, this.cost, this.traceId});

  factory LoginModel.fromJson(Map<String, dynamic> json) => _$LoginModelFromJson(json);

  Map<String, dynamic> toJson() => _$LoginModelToJson(this);
}

@JsonSerializable()
class LoginInfoModel {
  int? uid;	//用户唯一标识
  String? userLang;	//语言
  String? token; //登录token
  String? refreshToken;	//刷新token
  String? nickName;	//昵称
  int? loginChannel;//登录渠道(10:facebookApp; 11:googleApp; 12:appleApp) ,示例值(12)
  int? countryId; //国家码
  String? brand;	//客户端品牌
  int? loginAt;	//最近登录时间
  int? createTime; //用户创建时间
  int? expireTime;//过期时间
  int? isReg;	//是否是新用户(1-是, 0-否)
  int? isVip;	//是否vip(1-是, 0-否)
  String? headerUrl; //用户头像
  String? email; //邮箱
  String? deviceToken; //设备ID

  LoginInfoModel({this.uid, this.userLang, this.token, this.refreshToken, this.nickName, this.loginChannel, this.loginAt, this.expireTime, this.isReg, this.isVip, this.headerUrl, this.email, this.deviceToken});

  factory LoginInfoModel.fromJson(Map<String, dynamic> json) => _$LoginInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$LoginInfoModelToJson(this);
}