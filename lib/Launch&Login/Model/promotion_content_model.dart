import 'package:json_annotation/json_annotation.dart';

part 'promotion_content_model.g.dart';

@JsonSerializable()
class PromotionContentModel {
  int? code;
  PromotionContentItem? result;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  PromotionContentModel(
      {this.code, this.result, this.msg, this.sysAt, this.cost, this.traceId});

  factory PromotionContentModel.fromJson(Map<String, dynamic> json) =>
      _$PromotionContentModelFromJson(json);

  Map<String, dynamic> toJson() => _$PromotionContentModelToJson(this);
}

@JsonSerializable()
class PromotionContentItem {
  int? bookId; //书籍id
  int? bookLangId; //书籍语言id
  int? authorId; //作者id
  String? authorName; //作者名
  String? title; //书籍标题
  String? cover; //书籍封面
  int? viewCount; //阅读数
  bool? library; //是否加入书架
  bool? liked; //是否已收藏
  int? bookGoldCount; //书籍金币数
  String? description; //描述
  List<String?>? tagList; //标签列表

  PromotionContentItem(
      {this.bookId,
      this.bookLangId,
      this.authorId,
      this.authorName,
      this.title,
      this.cover,
      this.viewCount,
      this.library,
      this.liked,
      this.bookGoldCount,
      this.description,
      this.tagList});

  factory PromotionContentItem.fromJson(Map<String, dynamic> json) =>
      _$PromotionContentItemFromJson(json);

  Map<String, dynamic> toJson() => _$PromotionContentItemToJson(this);
}
