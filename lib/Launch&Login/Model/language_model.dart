import 'package:json_annotation/json_annotation.dart';
part 'language_model.g.dart';

@JsonSerializable()
class LanguageModel {
  int? code;
  LanguageResultModel? result;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  LanguageModel({this.code, this.result, this.msg, this.sysAt, this.cost, this.traceId});

  factory LanguageModel.fromJson(Map<String, dynamic> json) => _$LanguageModelFromJson(json);

  Map<String, dynamic> toJson() => _$LanguageModelToJson(this);
}


@JsonSerializable()
class LanguageResultModel {
  List<LanguageItem>? all;
  LanguageItem? current;

  LanguageResultModel({this.all, this.current});

  factory LanguageResultModel.fromJson(Map<String, dynamic> json) => _$LanguageResultModelFromJson(json);

  Map<String, dynamic> toJson() => _$LanguageResultModelToJson(this);
}


@JsonSerializable()
class LanguageItem{
  String? name;//English
  String? desc;//United States
  String? area;//US
  String? code;//en

  LanguageItem({this.name, this.desc, this.area, this.code});

  factory LanguageItem.fromJson(Map<String, dynamic> json) => _$LanguageItemFromJson(json);

  Map<String, dynamic> toJson() => _$LanguageItemToJson(this);
}