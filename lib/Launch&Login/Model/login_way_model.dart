import 'package:json_annotation/json_annotation.dart';
part 'login_way_model.g.dart';

@JsonSerializable()
class LoginWayModel {
  int? code;
  List<LoginWayItem>? result;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  LoginWayModel({
    this.code,
    this.result,
    this.msg,
    this.sysAt,
    this.cost,
    this.traceId,
  });

  factory LoginWayModel.fromJson(Map<String, dynamic> json) =>
      _$LoginWayModelFromJson(json);

  Map<String, dynamic> toJson() => _$LoginWayModelToJson(this);
}

@JsonSerializable()
class LoginWayItem {
  int? id;
  int? brandType;
  String? name;
  int? status;
  String? loginCode;
  int? loginChannel;//10:google,11:facebook,12:apple
  int? addTime;
  int? updateTime;

  LoginWayItem({
    this.id,
    this.brandType,
    this.name,
    this.status,
    this.loginCode,
    this.loginChannel,
    this.addTime,
    this.updateTime,
  });

  factory LoginWayItem.fromJson(Map<String, dynamic> json) =>
      _$LoginWayItemFromJson(json);

  Map<String, dynamic> toJson() => _$LoginWayItemToJson(this);
}