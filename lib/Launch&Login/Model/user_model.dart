import 'package:get/get.dart';
import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

@JsonSerializable()
class UserModel {
  int? code;
  @JsonKey(name: "result")
  UserInfoModel? userInfo;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  UserModel(
      {this.code,
      this.userInfo,
      this.msg,
      this.sysAt,
      this.cost,
      this.traceId});

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserModelToJson(this);
}

@JsonSerializable()
class UserInfoModel {
  int? uid; //用户id
  String? name; //用户名
  int? goldCoin; //金币
  int? diamonds; //钻石
  String? avatar; //头像
  String? sex; //性别,1：男；2：女；0：未设置
  @<PERSON><PERSON><PERSON><PERSON>(name: "vip_status")
  int? vipStatus; //VIP状态 0:非会员,1：会员,2：会员过期
  @<PERSON><PERSON><PERSON><PERSON>(name: "vip_type")
  int? vipType; //VIP类型,月卡，季卡
  @JsonKey(name: "vip_due_date")
  String? vipDueDate; //vip到期时间
  @JsonKey(name: "sub_expire_time")
  int? subExpireTime; //订阅到期时间
  String? phone; //手机号
  String? email; //邮箱
  String? address; //地址
  int? birthday; //生日
  String? nickName; //昵称
  bool? purchase; //收否充值购买过
  int? autoUnlock; //是否自动解锁章节（0：否，1：是）
  String? userNumber; //用户编号
  String? orderInfo; //客户端用户订单信息
  bool? newUser; //是否新用户，true:是，false:否

  UserInfoModel(
      {this.uid,
      this.name,
      this.goldCoin,
      this.diamonds,
      this.avatar,
      this.sex,
      this.vipStatus,
      this.vipType,
      this.vipDueDate,
      this.subExpireTime,
      this.phone,
      this.email,
      this.address,
      this.birthday,
      this.nickName,
      this.purchase,
      this.autoUnlock,
      this.userNumber,
      this.orderInfo,
      this.newUser});

  factory UserInfoModel.fromJson(Map<String, dynamic> json) =>
      _$UserInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserInfoModelToJson(this);

  ///性别,1：男；2：女；0：未设置
  String getSexString() {
    if (sex == "1") {
      return "gender_male".tr;
    } else if (sex == "2") {
      return "gender_female".tr;
    } else {
      return "gender_other".tr;
    }
  }

  int getSexInt(String gender) {
    if (gender == "gender_male".tr) {
      return 1;
    } else if (gender == "gender_female".tr) {
      return 2;
    } else {
      return 0;
    }
  }
}
