import 'package:flutter/cupertino.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:UrNovel/Util/Common/CommonManager/common_manager.dart';
import 'package:UrNovel/Util/SheetAndAlter/toast.dart';
import 'package:UrNovel/Util/ThirdSdkManger/third_manger.dart';
import '../../Util/NetWorkManager/net_work_manager.dart';
import '../../Util/SharedPreferences/shared_preferences.dart';
import '../../Util/api_config.dart';
import '../Model/language_model.dart';
import '../Model/login_model.dart';
import '../Model/login_way_model.dart';
import '../Model/promotion_content_model.dart';
import '../Model/save_newUser_chooseData_model.dart';
import '../Model/user_model.dart';
import 'package:vpn_check/vpn_check.dart';

typedef LoginCallback = void Function(LoginModel loginModel);
typedef LanguageCallback = void Function(bool isSuccess);

class LoginViewModel extends ChangeNotifier {
  //TODO: 登录
  static Future<void> authLogin(
      Map<String, dynamic> param, LoginCallback callback) async {
    bool isVpnActive = await VPNCheck.isVpnActive;
    param['vpnStatus'] = isVpnActive ? 1 : 0;
    var response =
        await NetWorkManager.instance.post(apiAuthLogin, parameters: param);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      LoginModel loginModel = LoginModel.fromJson(response.data);
      if (loginModel.code == 200) {
        // TODO: 登录成功后，保存token
        await SpUtil.spSetLoginInfo(response.data);
        // TODO: 获取用户信息
        await getUserInfo();
        await CommonManager.instance.initConfigData();
        await CommonManager.instance.getSwitchByCode();
      }

      callback(loginModel);
    } else {
      LoginModel loginModel = LoginModel();
      loginModel.msg = response.data['error'];
      callback(loginModel);
    }
  }

  // TODO: 退出登录
  static Future<bool> authLogout() async {
    var response =
        await NetWorkManager.instance.post(apiAuthLoginOut, parameters: {});
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      // TODO: 退出登录成功后，清除token
      LoginInfoModel? loginInfoModel = await SpUtil.spGetLoginInfo();
      if (loginInfoModel?.loginChannel == 0) {
        await FacebookAuth.instance.logOut();
      }
      await SpUtil.clearLoginInfo();
      ThirdManger.freshChatResetUser();

      return true;
    } else {
      // TODO: 退出登录失败
      showToast(response.data['error']);

      return false;
    }
  }

  // TODO: 用户注销,删除账号
  static Future<bool> authDestroyed() async {
    var response =
        await NetWorkManager.instance.post(apiAuthDestroyed, parameters: {});
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      // TODO: 注销成功后，清除token
      await SpUtil.clearLoginInfo();
      ThirdManger.freshChatResetUser();

      return true;
    } else {
      // TODO: 注销失败
      showToast(response.data['error']);

      return false;
    }
  }

  // TODO: 获取用户信息
  static Future<UserInfoModel?> getUserInfo() async {
    // TODO: 获取用户信息
    if (await SpUtil.isLogin()) {
      var response =
          await NetWorkManager.instance.get(apiGetUserPersonalData, params: {});
      if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
        // TODO: 登录成功后，保存token
        UserModel userModel = UserModel.fromJson(response.data);
        await SpUtil.spSetUserInfo(userModel);

        return userModel.userInfo;
      }
    } else {
      await SpUtil.spSetUserInfo(UserModel());
    }

    return null;
  }

  // TODO: 获取语言
  static Future<LanguageResultModel?> getContentLanguage() async {
    var response =
        await NetWorkManager.instance.get(apiGetAppLanguage, params: {});
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      // TODO: 登录成功后，保存token
      LanguageModel model = LanguageModel.fromJson(response.data);

      return model.result;
    } else {
      return null;
    }
  }

  // TODO: 推广
  static Future<PromotionContentModel?> getPromotionContent() async {
    var response =
        await NetWorkManager.instance.post(apiPromotionContent, parameters: {});

    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      SpUtil.spSetLaunchNovelAd(response.data);
      PromotionContentModel contentModel =
          PromotionContentModel.fromJson(response.data);

      return contentModel;
    } else {
      return null;
    }
  }

  // TODO: 保存用户选择数据
  static Future saveNewUserChooseData(Map<String, dynamic> param) async {
    var response = await NetWorkManager.instance
        .post(apiSaveNewUserChooseData, parameters: param);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      // TODO: 登录成功后，保存token
      SaveNewUserChooseDataModel model =
          SaveNewUserChooseDataModel.fromJson(response.data);

      return model;
    } else {
      SaveNewUserChooseDataModel model = SaveNewUserChooseDataModel();
      model.result = false;
      model.code = response.statusCode;
      model.msg = response.statusMessage;
      return model;
    }
  }

  //根据品牌动态获取登录方式
  static Future<LoginWayModel?> getLoginWay() async {
    var response =
        await NetWorkManager.instance.get(apiGetLoginWay, params: {});
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      LoginWayModel loginWayModel = LoginWayModel.fromJson(response.data);

      return loginWayModel;
    }

    return null;
  }
}
