// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBXOvJJpp3eL_laZM5XYBosqblm1L4JNqk',
    appId: '1:979084064090:web:2da2f23a5a0fdff225af63',
    messagingSenderId: '979084064090',
    projectId: 'urnovel',
    authDomain: 'urnovel.firebaseapp.com',
    storageBucket: 'urnovel.firebasestorage.app',
    measurementId: 'G-B7FPBSJWQC',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBEblYNPEGmLxAq90y7OSXkFqa4v5J3YoU',
    appId: '1:979084064090:android:56e9ab812c76b85025af63',
    messagingSenderId: '979084064090',
    projectId: 'urnovel',
    storageBucket: 'urnovel.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDVEbVO54NInfC43HlwARvCpshRpIpZP4E',
    appId: '1:979084064090:ios:530250dd556e1cc525af63',
    messagingSenderId: '979084064090',
    projectId: 'urnovel',
    storageBucket: 'urnovel.firebasestorage.app',
    iosBundleId: 'urnovel.story.books.wattpad',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDVEbVO54NInfC43HlwARvCpshRpIpZP4E',
    appId: '1:979084064090:ios:37ae74687987246925af63',
    messagingSenderId: '979084064090',
    projectId: 'urnovel',
    storageBucket: 'urnovel.firebasestorage.app',
    iosBundleId: 'urnovel.story.books.wattpad',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBXOvJJpp3eL_laZM5XYBosqblm1L4JNqk',
    appId: '1:979084064090:web:89bc82bde0e4a4ef25af63',
    messagingSenderId: '979084064090',
    projectId: 'urnovel',
    authDomain: 'urnovel.firebaseapp.com',
    storageBucket: 'urnovel.firebasestorage.app',
    measurementId: 'G-BF4VNZGXQF',
  );
}
