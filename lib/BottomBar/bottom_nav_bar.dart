import 'dart:async';

import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/MainPage/Profile/Page/profile_page.dart';
import 'package:UrNovel/Util/AlterManager/alter_manager.dart';
import 'package:UrNovel/Util/DBManager/db_manager.dart';
import 'package:UrNovel/Util/SheetAndAlter/alter.dart';
import 'package:UrNovel/Util/ThirdSdkManger/third_manger.dart';
import 'package:UrNovel/Util/deviceScreenUtil.dart';
import 'package:UrNovel/Util/genericUtil.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../Launch&Login/Model/language_model.dart';
import '../MainPage/Genres/Page/genres_tags_page.dart';
import '../MainPage/Library/Page/library_page.dart';
import '../MainPage/Profile/ViewModel/ViewModel.dart';
import '../MainPage/Read/Page/read_page.dart';
import '../Util/Common/CommonManager/common_manager.dart';
import '../Util/Common/Model/normal_model.dart';
import '../Util/DataReportManager/ServiceReport/data_report_manager.dart';
import '../Util/DataReportManager/event_name_config.dart';
import '../Util/DataReportManager/event_report_manager.dart';
import '../Util/Extensions/colorUtil.dart';
import '../Util/PaymentManager/payment_manager.dart';
import '../Util/PermissionManager/permission_manager.dart';
import '../Util/SharedPreferences/shared_preferences.dart';
import '../Util/SheetAndAlter/bottom_sheet.dart';
import '../Util/StatusManagement/status_management.dart';
import '../Util/device.dart';
import '../Util/enum.dart';
import '../Util/logUtil.dart';

class BottomNavBarPage extends BaseFulWidget {
  BottomNavBarPage({super.key});

  @override
  State<BottomNavBarPage> createState() => _BottomNavBarPageState();
}

class _BottomNavBarPageState extends State<BottomNavBarPage> {
  late List<Widget> _pages;

  static const double width = 22.0;
  static const double height = 22.0;

  // 实例化控制器
  final BottomBarController bottomBarController = findGetXInstance(BottomBarController());
  final readPageKey = GlobalKey<ReadPageState>();

  @override
  void initState() {
    super.initState();

    _initPage();
    // 监听 BottomBarController 的 currentIndex 变化
    ever(bottomBarController.isTurnToForYou, (value) {
      if (value) {
        readPageKey.currentState?.setTabControllerIndex(0);
      }
    });

    //TODO: 通知权限申请
    PermissionManager.requestNotificationPermission();
    fetchData();

    eventBusOn<LanguageItem>((item) async {
      await updateLanguageToProfile(isOnlyLanguage: true);
    });

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      //TODO:显示业务弹窗控制
      await AlterManager.instance.showContentAlter(context);
    });
  }

  @override
  void dispose() {
    super.dispose();

    //TODO:销毁监听
    bottomBarController.dispose();
  }

  //TODO:初始化页面
  void _initPage() {
    _pages = [
      ReadPage(key: readPageKey),
      const LibraryPage(),
      GenresTagsPage(arguments: {'isFromJump': false, 'index': 0}),
      ProfilePage(),
    ];
  }

  Future<void> fetchData() async {
    SpUtil.spSetAppOpenState();
    SpUtil.spSetIsColdStart(false);
    // DebugMgr.instance.showTestBtn(context);
    //TODO:检查剪切板的内容
    await ThirdManger.handleClipboardContent();
    //TODO:获取版本信息
    await getVersionInfo();
    //TODO:app打开行为打点
    await DataReportManager.instance.reportAppOpen();
    //TODO:更新语言到用户信息
    await updateLanguageToProfile();
    //TODO: 阅读数据上报
    ReadReportReadingType type = await SpUtil.spGetLastReadType();
    await DataReportManager.instance.reportOldReadChapterList(ReadReportActivityType.home, type);
    await PaymentManager.instance.initializeInAppPurchase();
    //TODO:推送授权
    bool isFirstOpen = await SpUtil.isFirstOpen();
    //没去过阅读页面，再次打开进行请求权限
    if (!isFirstOpen) {
      NotificationAlterSource source = NotificationAlterSource.none;
      List? recentlyReadBook = DBManager.instance.getRecentlyReadBook();
      //没进行阅读操作
      if (!isAvailable(recentlyReadBook)) {
        source = NotificationAlterSource.notFirstOpenAndNoReading;
      } else {
        int days = await SpUtil.spGetNotificationFirstDeniedTime();
        if (days == 1) {
          source = NotificationAlterSource.openAppAfter24Hours;
        } else if (days % 7 == 0 && days != 0) {
          source = NotificationAlterSource.deniedAfter7days;
        }
      }

      await AlterManager.instance.showNotificationAlter(source);
    }
  }

  //todo: 业务弹窗管理
  Future<void> getVersionInfo() async {
    try {
      //检测版本更新
      await CommonManager.instance.getVersionInfo(context, (isVerify) async {
        await requestTrackingPermission();
      }, () {});
    } catch (e) {
      logP(e.toString());
    }
  }

  //TODO:更新语言到用户信息
  Future<void> updateLanguageToProfile({isOnlyLanguage = false}) async {
    Map<String, dynamic> params = {
      "language": isAvailable(CommonManager.instance.contentLanguage.code)
          ? CommonManager.instance.contentLanguage.code
          : "en",
      if (!isOnlyLanguage)
        "mobileLanguage":
            "${CommonManager.instance.deviceLocale?.languageCode}_${CommonManager.instance.deviceLocale?.countryCode}",
      if (!isOnlyLanguage)
        "skinLanguage":
            "${CommonManager.instance.skinLanguage.languageCode}_${CommonManager.instance.skinLanguage.countryCode}",
    };
    NormalModel? model = await ProfileViewModel.editProfile(params);
    if (model?.code != 200) {
      logP('update language to profile fail :${model?.msg}');
    }
  }

  //TODO:隐私权限
  //TODO:请求广告跟踪隐私权限
  Future<void> requestTrackingPermission() async {
    TrackingStatus trackingStatus =
        await PermissionManager.instance.getTrackingAuthorizationStatus();
    if (trackingStatus == TrackingStatus.notDetermined) {
      await PermissionManager.instance.requestTrackingTransparencyPermission();
    } else if (trackingStatus != TrackingStatus.authorized) {
      bool isShow = await SpUtil.spGetIsTrackingAlterShow();
      bool isVerify = await SpUtil.spGetVerifyStatus();
      if (isShow && !isVerify) {
        await showTrackingPermissionAlter();
      }
    }
  }

  //TODO: 展示追踪弹窗
  Future<void> showTrackingPermissionAlter() async {
    showAlter(NotificationTrackingPermissionSheet(
        isNotificationType: false,
        onTurnOnTap: () async {
          await ThirdManger.openAppSettings();
        }));
    await SpUtil.spSetTrackingAlterShowTime(getCurrentTimeSeconds());
  }

  ///BarItems
  List<BottomNavigationBarItem> _getBarItems() {
    return [
      BottomNavigationBarItem(
        icon:
            Image.asset('assets/images/bottomNavBar/icon_read_n.png', width: width, height: height),
        activeIcon:
            Image.asset('assets/images/bottomNavBar/icon_read_s.png', width: width, height: height),
        label: 'Read'.tr,
      ),
      BottomNavigationBarItem(
        icon: Image.asset('assets/images/bottomNavBar/icon_library_n.png',
            width: width, height: height),
        activeIcon: Image.asset('assets/images/bottomNavBar/icon_library_s.png',
            width: width, height: height),
        label: 'Library'.tr,
      ),
      BottomNavigationBarItem(
        icon: Image.asset('assets/images/bottomNavBar/icon_genres_n.png',
            width: width, height: height),
        activeIcon: Image.asset('assets/images/bottomNavBar/icon_genres_s.png',
            width: width, height: height),
        label: 'Genres'.tr,
      ),
      BottomNavigationBarItem(
        icon: Image.asset('assets/images/bottomNavBar/icon_profile_n.png',
            width: width, height: height),
        activeIcon: Image.asset('assets/images/bottomNavBar/icon_profile_s.png',
            width: width, height: height),
        label: 'Profile'.tr,
      ),
    ];
  }

  Future<void> eventReport(int index) async {
    switch (index) {
      case 0:
        await EventReportManager.eventReportOfFirebase(clickMain);
        break;
      case 1:
        await EventReportManager.eventReportOfFirebase(clickLibrary);
        break;
      case 2:
        await EventReportManager.eventReportOfFirebase(clickGenres);
        break;
      case 3:
        await EventReportManager.eventReportOfFirebase(clickMe);
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    // 使用回调获取屏幕尺寸
    DeviceScreenUtil.instance.setContext(context);
    Device.init(context);
    return Scaffold(
      ///保留tab页面的状态
      body: Obx(() {
        return IndexedStack(
          index: bottomBarController.currentIndex.value,
          children: _pages,
        );
      }),
      bottomNavigationBar: Obx(() {
        return bottomBarController.isBottomBarVisible.value
            ? BottomNavigationBar(
                //超过3个要重写属性
                type: BottomNavigationBarType.fixed,
                currentIndex: bottomBarController.currentIndex.value,
                items: _getBarItems(),
                onTap: (index) async {
                  setState(() {
                    bottomBarController.changeIndex(index, false);
                  });
                  if (index == 3) {
                    eventBusFire({'refreshProfile': true});
                  }
                  await eventReport(index);
                },
                selectedItemColor: HexColor('#0087FB'),
                unselectedItemColor: HexColor('#646872'),
                backgroundColor: HexColor('#FAFAFA'),
              )
            : const SizedBox.shrink();
      }),
    );
  }
}
