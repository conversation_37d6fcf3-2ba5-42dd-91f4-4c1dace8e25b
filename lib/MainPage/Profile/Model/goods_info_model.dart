import 'package:json_annotation/json_annotation.dart';

part 'goods_info_model.g.dart';

@JsonSerializable()
class GoodsInfoModel {
  int? code; //状态码
  List<GoodsListItem>? result; //商品数据
  String? msg; //返回信息
  int? sysAt; //时间
  int? cost; //耗时
  String? traceId; //日志跟踪id

  GoodsInfoModel(
      {this.code, this.result, this.msg, this.sysAt, this.cost, this.traceId});

  factory GoodsInfoModel.fromJson(Map<String, dynamic> json) =>
      _$GoodsInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$GoodsInfoModelToJson(this);
}

@JsonSerializable()
class GoodsListItem {
  int? skuId; //商品skuId
  int? skuLangId; //商品语言skuId
  String? title; //标题
  String? content; //商品内容
  String? icoUrl; //图标地址
  double? money; //商品原金额(美元$)
  double? salesMoney; //销售金额(美元$)
  int? sort; //排序
  String? appStorePid; //商城的商品ID
  int? rewardPacketId; //礼包ID
  int? productSubscription; //商品是否订阅商品（0:不是;1:是）
  String?
      productCode; //商品编码 billed_weekly周续费 billed_monthly月续费 billed_quarterly季续费 billed_semi半年续费 billed_yearly年续费
  int? recommend; //是否推荐（0:不是;1:是）
  int? coinsGift; //赠送的金币
  //活动相关
  int? countDownSecond; //倒计时多少秒
  String?
      activityType; //活动类型 ("First_Charge", "首充活动"), ("Bonus", "额外赠送活动"), ("Best_Deal", "最佳交易"),
  int? giftRatio; //赠送比例，返回的是30这种数字需要后面拼接一下%
  String? activityName; //活动名称(activityType 为Discount时)
  bool? bestDealFlag; //Best Deal标志
  int? activityStatus; //活动状态:-1删除,0草稿,1使用中,2测试中
  bool? activityPricePower; //是否有享受活动价格资格，默认为true，不具备活动价资格时值为false
  int? skuValue; //商品值
  double? pricePercentage; //价格比例字段

  //自定义字段
  String? showPrice; //展示金额(带当地货币符号)
  double? rawPrice; //展示金额
  String? currencyCode; //展示币种国际代号
  String? currencySymbol; //币种符号
  String? introductoryPrice; //促销优惠价格
  int? numberOfPeriods; //周期数
  int? numberOfUnits; //周期单位

  GoodsListItem(
      {this.skuId,
      this.skuLangId,
      this.title,
      this.content,
      this.icoUrl,
      this.money,
      this.salesMoney,
      this.sort,
      this.appStorePid,
      this.rewardPacketId,
      this.productSubscription,
      this.productCode,
      this.recommend,
      this.coinsGift,
      this.countDownSecond,
      this.activityType,
      this.giftRatio,
      this.activityName,
      this.bestDealFlag,
      this.activityStatus,
      this.activityPricePower,
      this.skuValue,
      this.showPrice,
      this.rawPrice,
      this.currencySymbol,
      this.introductoryPrice,
      this.numberOfPeriods,
      this.numberOfUnits,
      this.pricePercentage});

  factory GoodsListItem.fromJson(Map<String, dynamic> json) =>
      _$GoodsListItemFromJson(json);

  Map<String, dynamic> toJson() => _$GoodsListItemToJson(this);
}
