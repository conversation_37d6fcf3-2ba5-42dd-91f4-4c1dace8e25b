import 'package:json_annotation/json_annotation.dart';
part 'order_validate_model.g.dart';

@JsonSerializable()
class OrderValidateModel {
  int? code;
  OrderValidateResultModel? result;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  OrderValidateModel({this.code, this.result, this.msg, this.sysAt, this.cost, this.traceId});

  factory OrderValidateModel.fromJson(Map<String, dynamic> json) => _$OrderValidateModelFromJson(json);

  Map<String, dynamic> toJson() => _$OrderValidateModelToJson(this);
}

@JsonSerializable()
class OrderValidateResultModel {
  int? purchaseState;//订单购买状态，0-已购买，1-已取消，2-待定,3-过期，4-异常，5-匹配不到交易信息，6-退款，7-重复验签

  OrderValidateResultModel({this.purchaseState});

  factory OrderValidateResultModel.fromJson(Map<String, dynamic> json) => _$OrderValidateResultModelFromJson(json);

  Map<String, dynamic> toJson() => _$OrderValidateResultModelToJson(this);
}