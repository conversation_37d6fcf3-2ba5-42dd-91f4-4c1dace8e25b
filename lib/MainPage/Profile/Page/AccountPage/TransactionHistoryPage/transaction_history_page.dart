import 'package:flutter/material.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import '../../../../../Util/Extensions/colorUtil.dart';
import '../../../Widget/AccountPage/PurchasedNovels/purchased_novels_cell.dart';

class TransactionHistoryPage extends BaseFulWidget {
  TransactionHistoryPage({super.key});

  @override
  State<TransactionHistoryPage> createState() => _TransactionHistoryPageState();
}

class _TransactionHistoryPageState extends State<TransactionHistoryPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Purchased Novels"),
        backgroundColor: HexColor("#FFFFFF"),
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        color: HexColor("#FFFFFF"),
        child: ListView.builder(itemCount: 10, itemBuilder: (context, index) {
          return PurchasedNovelsCell();
        }),
      ),
    );
  }
}
