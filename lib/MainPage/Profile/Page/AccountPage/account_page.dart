import 'dart:math' as math;

import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Util/Common/Animation/lottie_animation_view.dart';
import 'package:UrNovel/Util/SheetAndAlter/bottom_sheet.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../Util/Common/CommonManager/common_manager.dart';
import '../../../../Util/Common/no_load_view.dart';
import '../../../../Util/Extensions/colorUtil.dart';
import '../../../../Util/PaymentManager/payment_manager.dart';
import '../../../../Util/SharedPreferences/shared_preferences.dart';
import '../../../../Util/SheetAndAlter/alter.dart';
import '../../../../Util/SheetAndAlter/toast.dart';
import '../../../../Util/enum.dart';
import '../../Model/goods_info_model.dart';
import '../../ViewModel/ViewModel.dart';

class AccountPage extends BaseFulWidget {
  AccountPage({super.key, required super.arguments});

  @override
  State<AccountPage> createState() => _AccountPageState();
}

class _AccountPageState extends State<AccountPage> {
  late List<GoodsListItem>? _goodList;
  late bool _isLoading;
  late GoodsType _goodsType;
  late OrderType _orderType;
  late Color _bgColor;
  bool _isClickable = true;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _goodList = [];
    _isLoading = false;
    _goodsType = widget.arguments['goodsType'] ?? GoodsType.purchaseCoins;
    _orderType =
        _goodsType == GoodsType.purchaseDiamonds ? OrderType.novelChat : OrderType.personalCenter;
    _bgColor = _goodsType == GoodsType.purchaseDiamonds ? HexColor("#000000") : HexColor("#F4F5F6");

    fetchData();
  }

  @override
  void dispose() {
    setSystemUiDark();
    super.dispose();
  }

  Future<void> fetchData() async {
    await getGoodsList();
  }

  Future<void> getGoodsList() async {
    if (!_isClickable) {
      return;
    }
    _isClickable = false;

    // TODO: implement fetchData
    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    _goodList = await ProfileViewModel.getGoodList(_goodsType);

    if (mounted) {
      setState(() {
        _isLoading = false;
      });

      _isClickable = true;
    }
  }

  // TODO: 创建订单
  Future<void> _createShopOrder(GoodsListItem? item) async {
    if (!_isClickable) {
      return;
    }
    _isClickable = false;

    if (!isAvailable(item)) {
      showToast("product is Not Available");
      _isClickable = true;
      return;
    }

    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    PaymentManager.instance.createShopOrder(item!, goodsType: _goodsType, orderType: _orderType,
        pullPurchaseCallback: () async {
      // TODO: 支付掉起回调
    }, onPurchaseCanceled: () async {
      // TODO: 支付取消回调
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
      _isClickable = true;
    }, onPurchaseValidateCallback: (status, info) async {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }

      _isClickable = true;

      // TODO: 验证订单回调
      if (status == SubscribeVerifyStatus.purchased) {
        if (_goodsType != GoodsType.purchaseDiamonds) {
          Get.back();
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
        onPopInvokedWithResult: (didPop, result) async {},
        canPop: false,
        child: Scaffold(
          backgroundColor: _bgColor,
          appBar: AppBar(
              title: Text('account'.tr,
                  style: TextStyle(
                      color: _goodsType == GoodsType.purchaseDiamonds ? Colors.white : null)),
              backgroundColor: _bgColor,
              leading: IconButton(
                icon: Transform.rotate(
                  angle: CommonManager.instance.isReverse() ? math.pi : 0,
                  child: Image.asset(
                      _goodsType == GoodsType.purchaseDiamonds
                          ? "assets/images/common/icon_back_white.png"
                          : "assets/images/common/icon_back_gary.png",
                      width: 12,
                      height: 20),
                ),
                // 使用默认的返回箭头图标
                onPressed: () async {
                  await handleCurrentPage(context, () async {
                    if (isAvailable(_goodList)) {
                      try {
                        GoodsListItem? goodItem = _goodList?.firstWhere(
                                (item) => isGoodsActivity(item.activityPricePower, item.activityType));
                        if (isAvailable(goodItem)) {
                          // TODO: 处理活动商品
                          bool isShow = await SpUtil.spGetIsPurchaseReminderAlterShow();

                          bool isVerify = await SpUtil.spGetVerifyStatus();
                          if (isShow && !isVerify) {
                            showAlter(
                                PurchaseReminderContent(
                                    goodItem: goodItem,
                                    onClaimNow: (item) {
                                      _createShopOrder(item);
                                    },
                                    onBack: () {
                                      Get.back();
                                    }),
                                backgroundColor: Colors.transparent);
                          } else {
                            Get.back();
                          }
                        } else {
                          Get.back();
                        }
                      } catch (e) {
                        Get.back();
                      }
                    } else {
                      Get.back();
                    }
                  });
                },
              )),
          body: Stack(
            children: [
              if (isAvailable(_goodList))
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: AccountPurchaseSheet(
                    goodList: _goodList,
                    isSheetType: false,
                    goodsType: _goodsType,
                    onProductTap: (item) {
                      // TODO: implement onProductTap 购买商品
                      _createShopOrder(item);
                    },
                  ),
                ),
              if (_isLoading)
                const Positioned(top: 0, left: 0, right: 0, bottom: 0, child: LottieAnimationView())
              else if (!isAvailable(_goodList))
                Positioned(top: 0, left: 0, right: 0, bottom: 0, child: NoLoadView())
            ],
          ),
        ));
  }
}
