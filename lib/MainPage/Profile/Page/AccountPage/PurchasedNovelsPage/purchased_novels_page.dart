import 'package:flutter/material.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import '../../../../../Util/Extensions/colorUtil.dart';
import '../../../../../Util/logUtil.dart';
import '../../../Widget/AccountPage/PurchasedNovels/purchased_novels_cell.dart';
import '../../../Widget/AccountPage/PurchasedNovels/purchased_novels_search.dart';

class PurchasedNovelsPage extends BaseFulWidget {
  PurchasedNovelsPage({super.key});

  @override
  State<PurchasedNovelsPage> createState() => _PurchasedNovelsPageState();
}

class _PurchasedNovelsPageState extends State<PurchasedNovelsPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Purchased Novels"),
        backgroundColor: HexColor("#FFFFFF"),
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
            gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [HexColor("#FFFFFF"), HexColor("#F1F2F7")],
        )),
        child: Column(
          children: [
            PurchasedNovelsSearch(onChanged: (value) {
              logP(value);
            }),
            const SizedBox(height: 18),
            Expanded(
                child: ListView.builder(
              itemCount: 10,
              itemBuilder: (context, index) {
                return PurchasedNovelsCell();
              },
            ))
          ],
        ),
      ),
    );
  }
}
