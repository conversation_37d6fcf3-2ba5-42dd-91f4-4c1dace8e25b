import 'package:flutter/material.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import '../../../../../Util/Extensions/colorUtil.dart';
import '../../../Widget/AccountPage/PurchasedNovels/purchased_novels_cell.dart';

class PurchaseHistoryPage extends BaseFulWidget {
  PurchaseHistoryPage({super.key});

  @override
  State<PurchaseHistoryPage> createState() => _PurchaseHistoryPageState();
}

class _PurchaseHistoryPageState extends State<PurchaseHistoryPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Purchased Novels"),
        backgroundColor: HexColor("#FFFFFF"),
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        color: HexColor("#FFFFFF"),
        child: ListView.builder(itemCount: 10, itemBuilder: (context, index) {
          return PurchasedNovelsCell();
        }),
      ),
    );
  }
}
