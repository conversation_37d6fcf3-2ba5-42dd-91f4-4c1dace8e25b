import 'dart:io';
import 'dart:math' as math;

import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Util/ShareManager/share_manager.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../Util/Common/CommonManager/common_manager.dart';
import '../../../../Util/Extensions/colorUtil.dart';
import '../../Widget/BecomeAWriter/become_a_writer_benefits.dart';
import '../../Widget/BecomeAWriter/become_a_writer_choose.dart';
import '../../Widget/BecomeAWriter/become_a_writer_contact.dart';
import '../../Widget/BecomeAWriter/become_a_writer_header.dart';
import '../../Widget/BecomeAWriter/become_a_writer_introduce.dart';
import '../../Widget/BecomeAWriter/become_a_writer_joining.dart';
import '../../Widget/BecomeAWriter/become_a_writer_note.dart';

class BecomeAWriterPage extends BaseFulWidget {
  BecomeAWriterPage({super.key});

  @override
  State<BecomeAWriterPage> createState() => _BecomeAWriterPageState();
}

class _BecomeAWriterPageState extends State<BecomeAWriterPage> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    setSystemUiLight();
  }

  @override
  dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    return PopScope(
        onPopInvokedWithResult: (didPop, result) {
          setSystemUiDark();
        },
        child: Scaffold(
          // 根据需要设置 App,
          body: SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: Stack(
              alignment: Alignment.topCenter,
              children: [
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: SingleChildScrollView(
                    physics: const ClampingScrollPhysics(),

                    ///section
                    child: Column(
                      mainAxisSize: MainAxisSize.min, // 自适应高度
                      children: [
                        //todo：header & 介绍
                        SizedBox(
                          width: double.infinity,
                          height: ((Platform.isIOS ||
                                  mediaQuery.padding.bottom == 0)
                              ? mediaQuery.size.width / 1.38 + 108
                              : mediaQuery.size.width / 1.20 + 130),
                          child: Stack(
                            children: [
                              Positioned(
                                  top: 0,
                                  left: 0,
                                  right: 0,
                                  child: BecomeAWriterHeader()),
                              Positioned(
                                  top: ((Platform.isIOS ||
                                              mediaQuery.padding.bottom == 0)
                                          ? mediaQuery.size.width / 1.38
                                          : mediaQuery.size.width / 1.20) -
                                      10,
                                  left: 0,
                                  right: 0,
                                  child: BecomeAWriterIntroduce()),

                              ///导航栏
                              Positioned(
                                  top: 25,
                                  left: 11,
                                  right: 11,
                                  child: Row(
                                    children: [
                                      SizedBox(
                                        width: 40,
                                        height: 40,
                                        child: IconButton(
                                            onPressed: () {
                                              Get.back();
                                            },
                                            icon: Transform.rotate(
                                              angle: CommonManager.instance
                                                      .isReverse()
                                                  ? math.pi
                                                  : 0,
                                              child: Image.asset(
                                                  "assets/images/common/icon_back_white.png",
                                                  width: 10,
                                                  height: 18),
                                            )),
                                      ),
                                      Expanded(
                                          child: Padding(
                                              padding: const EdgeInsets.only(
                                                  right: 40),
                                              child: Align(
                                                alignment: Alignment.center,
                                                child: Text("Join Us",
                                                    style: TextStyle(
                                                        color:
                                                            HexColor("#FFFFFF"),
                                                        fontSize: 16,
                                                        fontWeight:
                                                            FontWeight.bold)),
                                              )))
                                    ],
                                  )),
                            ],
                          ),
                        ),
                        BecomeAWriterChoose(),
                        const SizedBox(height: 15),
                        BecomeAWriterBenefits(),
                        const SizedBox(height: 15),
                        BecomeAWriterJoining(),
                        const SizedBox(height: 20),
                        BecomeAWriterContact(),
                        const SizedBox(height: 20),
                        BecomeAWriterNote(),
                        SizedBox(height: mediaQuery.padding.bottom + 10),
                      ],
                    ),
                  ),
                ),

                ///悬浮按钮
                Positioned(
                    bottom: 50,
                    left: 65,
                    right: 65,
                    child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                              colors: [
                                HexColor("#0087FB"),
                                HexColor("#0099F8"),
                              ]),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: TextButton(
                            onPressed: () async {
                              final Uri emailLaunchUri = Uri(
                                scheme: 'mailto',
                                path: '<EMAIL>',
                              );
                              await ShareManager.instance
                                  .launchLinkUrl(emailLaunchUri);
                            },
                            child: Text("Join Now",
                                style: TextStyle(
                                    color: HexColor("#FFFFFF"),
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold)))))
              ],
            ),
          ),
        ));
  }
}
