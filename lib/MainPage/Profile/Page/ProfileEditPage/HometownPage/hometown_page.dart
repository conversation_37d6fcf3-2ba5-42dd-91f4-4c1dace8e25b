import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import '../../../../../Util/StatusManagement/status_management.dart';
import '../../../../../Util/Extensions/colorUtil.dart';
import '../../../../../Util/genericUtil.dart';

class HometownPage extends BaseFulWidget {
  HometownPage({super.key});

  @override
  State<HometownPage> createState() => _HometownPageState();
}

class _HometownPageState extends State<HometownPage> {
  late final List reportList;
  final BookDetailReportController controller = findGetXInstance(BookDetailReportController());
  late final Color color;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    reportList = [
      "France",
      "Deutschland",
      "España",
      "Italia",
      "भारत",
      "Australia",
      "México",
      "المملكة العربية السعودية",
    ];
    color = ColorsUtil.hexColor(0x7E839D, alpha: 0.1);
  }

  @override
  void dispose() {
    // TODO: implement dispose
    controller.clearReport();
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Hometown'),
        backgroundColor: HexColor('#FFFFFF'),
        actions: [
          TextButton(
              onPressed: () {
                Get.back();
              },
              child: Text(
                "Done",
                style: TextStyle(
                    color: HexColor("#1B86FF"),
                    fontSize: 16,
                    fontWeight: FontWeight.bold),
              ))
        ],
      ),
      body: SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: ListView.builder(
            itemCount: reportList.length,
            itemBuilder: (context, index) {
              return GestureDetector(
                onTap: () {
                  setState(() {
                    controller.clearReport();
                    controller.selected(reportList[index]);
                  });
                },
                child: SizedBox(
                  height: 62,
                  width: double.infinity,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Padding(
                          padding: const EdgeInsets.only(left: 36, right: 36),
                          child: SizedBox(
                              height: 61,
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Text(reportList[index],
                                      style: TextStyle(
                                          fontSize: 15,
                                          color: HexColor("#1F1F2F"),
                                          fontWeight: FontWeight.bold)),
                                  Image.asset(
                                      controller.isSelected(reportList[index])
                                          ? 'assets/images/bookDetails/icon_choosed.png'
                                          : 'assets/images/bookDetails/icon_unchoose.png',
                                      width: 20,
                                      height: 20),
                                ],
                              ))),
                      Padding(
                          padding: const EdgeInsets.only(left: 15, right: 15),
                          child: Divider(height: 1, color: color))
                    ],
                  ),
                ),
              );
            }),
      ),
    );
  }
}
