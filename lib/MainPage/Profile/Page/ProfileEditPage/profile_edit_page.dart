import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Launch&Login/Model/user_model.dart';
import 'package:UrNovel/MainPage/Profile/ViewModel/ViewModel.dart';
import 'package:UrNovel/Util/Common/data_config_util.dart';
import 'package:UrNovel/Util/SheetAndAlter/alter.dart';
import 'package:UrNovel/Util/SheetAndAlter/bottom_sheet.dart';
import 'package:UrNovel/Util/SheetAndAlter/toast.dart';
import 'package:UrNovel/Util/tools.dart';

import '../../../../Launch&Login/ViewModel/ViewModel.dart';
import '../../../../Util/Common/Model/normal_model.dart';
import '../../../../Util/Common/Animation/lottie_animation_view.dart';
import '../../../../Util/Extensions/colorUtil.dart';
import '../../Widget/ProfileEditPage/profile_head.dart';
import '../../Widget/ProfileEditPage/profile_personal_info.dart';

class ProfileEditPage extends BaseFulWidget {
  ProfileEditPage({super.key, super.arguments});

  @override
  State<ProfileEditPage> createState() => _ProfileEditPageState();
}

class _ProfileEditPageState extends State<ProfileEditPage> {
  late bool _isLogin;
  late DateTime _selectedDate = DateTime.now();
  late UserInfoModel? _infoModel;
  late String _avatar;
  late Function(UserInfoModel?) _callback;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    _isLogin = false;
    _infoModel = widget.arguments["infoModel"] ?? UserInfoModel();
    _avatar = widget.arguments["avatar"];
    _callback = widget.arguments["callback"];
  }

  @override
  Widget build(BuildContext context) {
    var list = DataConfigUtil.instance.personalList;
    return PopScope(
        onPopInvokedWithResult: (didPop, result) {
          _callback.call(_infoModel);
        },
        child: Stack(
          children: [
            Scaffold(
              appBar: AppBar(
                title: Text("edit_profile".tr),
              ),
              body: SizedBox(
                width: double.infinity,
                height: double.infinity,
                child: ListView.builder(
                    itemCount: 1 + list.length,
                    itemBuilder: (context, section) {
                      if (section == 0) {
                        return ProfileHead(
                            infoModel: _infoModel, avatar: _avatar);
                      }
                      var groupTitle = list[section - 1]["group"];
                      var items = list[section - 1]["items"];
                      return Column(
                        children: [
                          if (groupTitle.isNotEmpty && items.length > 0)
                            Container(
                              margin: const EdgeInsets.fromLTRB(17, 27, 17, 19),
                              alignment: Alignment.centerLeft,
                              child: Text(
                                groupTitle,
                                style: TextStyle(
                                    color: HexColor("#1F1F2F"),
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold),
                              ),
                            ),
                          Container(
                              height: 58.0 * items.length,
                              margin: const EdgeInsets.fromLTRB(11, 0, 11, 0),
                              decoration: BoxDecoration(
                                color: HexColor("#FFFFFF"),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: ListView.builder(
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemCount: items.length,
                                  itemBuilder: (context, index) {
                                    var subTitle = "";
                                    if (section == 1) {
                                      if (index == 0) {
                                        subTitle = _infoModel?.nickName ?? "";
                                      } else if (index == 1) {
                                        subTitle =
                                            _infoModel?.getSexString() ?? "";
                                      } else if (index == 2) {
                                        if (isAvailable(_infoModel?.birthday) &&
                                            0 < _infoModel!.birthday!) {
                                          subTitle = getDateWithFormat(
                                              _infoModel!.birthday!,
                                              "yyyy.MM.dd");
                                        }
                                      }
                                    } else if (section == 2) {
                                      if (index == 0) {
                                        subTitle = _infoModel!.phone ?? "";
                                      } else if (index == 1) {
                                        subTitle = _infoModel!.email ?? "";
                                      }
                                    }
                                    return GestureDetector(
                                      onTap: () {
                                        cellSelected(
                                            context, section - 1, index);
                                      },
                                      child: ProfilePersonalInfo(
                                          arguments: items[index],
                                          subTitle: subTitle),
                                    );
                                  }))
                        ],
                      );
                    }),
              ),
            ),
            if (_isLogin) const LottieAnimationView()
          ],
        ));
  }

  Future<void> cellSelected(BuildContext context, int section, int index) async {
    switch (section) {
      case 0:
        switch (index) {
          case 0:
            showAlter(ChangeProfileContent(
                title: "nickname".tr,
                currentContent: _infoModel?.nickName,
                onContentChanged: (content) {
                  if (isAvailable(content) && content != _infoModel?.nickName) {
                    editProfile(getProfileParam(nickName: content));
                  }
                }));
            break;
          case 1:
            String selectedGender =
                _infoModel?.getSexString() ?? "gender_other".tr;
            showBookBottomSheet(
                context,
                SettingsGenderSheet(
                    gender: _infoModel?.getSexString() ?? "gender_other".tr,
                    onGenderChanged: (gender) {
                      selectedGender = gender;
                    }),
                HexColor('#F7F7FA'), onDismiss: () {
              if (selectedGender != _infoModel?.getSexString()) {
                editProfile(getProfileParam(
                    sex: _infoModel?.getSexInt(selectedGender)));
              }
            });
            break;
          case 2:
            _selectDate(context);
            break;
        }
        break;
      case 1:
        switch (index) {
          case 0:
            await Get.toNamed("/phoneNumberPage", arguments: {
              "phone": _infoModel?.phone ?? "",
              "onPhoneDown": onPhoneDown
            });
            break;
          case 1:
            showAlter(ChangeProfileContent(
                title: "email_address".tr,
                currentContent: _infoModel?.email,
                onContentChanged: (content) {
                  if (isAvailable(content) && content != _infoModel?.email) {
                    editProfile(getProfileParam(email: content));
                  }
                }));
            break;
        }
        break;
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isAvailable(_infoModel?.birthday)
          ? DateTime.fromMillisecondsSinceEpoch(_infoModel!.birthday! * 1000)
          : DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _selectedDate) {
      _selectedDate = picked;
      editProfile(
          getProfileParam(birthday: picked.millisecondsSinceEpoch ~/ 1000));
    }
  }

  void onPhoneDown(String? phone) {
    editProfile(getProfileParam(phone: phone));
  }

  Map<String, Object?> getProfileParam(
      {String? nickName,
      int? sex,
      int? birthday,
      String? address,
      String? phone,
      String? email}) {
    return {
      "phone": phone,
      "email": email,
      "address": address,
      "birthday": birthday,
      "nickName": nickName,
      "sex": sex
    };
  }

  Future<void> editProfile(Map<String, dynamic> param) async {
    setState(() {
      _isLogin = true;
    });

    NormalModel? model = await ProfileViewModel.editProfile(param);
    if (model?.code == 200) {
      _infoModel = await LoginViewModel.getUserInfo();
    } else {
      if (isAvailable(model?.msg)) {
        showToast(model!.msg!);
      }
    }

    if (mounted) {
      setState(() {
        _isLogin = false;
      });
    }
  }
}
