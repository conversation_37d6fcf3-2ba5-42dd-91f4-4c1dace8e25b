import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';

import '../../../../../Util/Extensions/colorUtil.dart';
import '../../../Widget/ProfileEditPage/PhoneNumberPage/phone_edit_header.dart';

class PhoneNumberPage extends BaseFulWidget {
  PhoneNumberPage({super.key, super.arguments});

  @override
  State<PhoneNumberPage> createState() => _PhoneNumberPageState();
}

class _PhoneNumberPageState extends State<PhoneNumberPage> {
  late String? _phone;
  late Function(String?) _onPhoneDown;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    _phone = widget.arguments['phone'];
    _onPhoneDown = widget.arguments['onPhoneDown'];
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
        onPopInvokedWithResult: (didPop, result) {
          FocusScope.of(context).unfocus(); // 取消焦点，隐藏键盘
        },
        child: Scaffold(
          appBar: AppBar(
            title: Text("phone_number".tr),
            backgroundColor: HexColor("#FFFFFF"),
            actions: [
              TextButton(
                  onPressed: () {
                    _onPhoneDown(_phone);
                    Get.back();
                  },
                  child: Text(
                    "done".tr,
                    style: TextStyle(
                        color: HexColor("#1B86FF"),
                        fontSize: 16,
                        fontWeight: FontWeight.bold),
                  ))
            ],
          ),
          body: SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: Column(
              children: [
                PhoneEditHeader(
                    phone: _phone,
                    onPhoneChanged: (code, phone) {
                      _phone = "$code $phone";
                    }),
              ],
            ),
          ),
        ));
  }
}
