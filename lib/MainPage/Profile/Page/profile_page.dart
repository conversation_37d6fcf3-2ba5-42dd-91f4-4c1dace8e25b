import 'dart:async';

import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Launch&Login/ViewModel/ViewModel.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:flutter/material.dart';
import 'package:freshchat_sdk/freshchat_sdk.dart';
import 'package:get/get.dart';

import '../../../Launch&Login/Model/user_model.dart';
import '../../../Util/AlterManager/alter_manager.dart';
import '../../../Util/Common/data_config_util.dart';
import '../../../Util/DataReportManager/event_name_config.dart';
import '../../../Util/DataReportManager/event_report_manager.dart';
import '../../../Util/Extensions/colorUtil.dart';
import '../../../Util/RefreshLoad/refresh_load.dart';
import '../../../Util/ShareManager/share_manager.dart';
import '../../../Util/ThirdSdkManger/third_manger.dart';
import '../../../Util/enum.dart';
import '../../../Util/stringUtil.dart';
import '../Widget/profile_card_cell.dart';
import '../Widget/profile_card_premium.dart';
import '../Widget/profile_header.dart';

class ProfilePage extends BaseFulWidget {
  ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  late UserInfoModel? _userInfoModel;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    _userInfoModel = null;
    showSystemUiMode();

    ///事件监听
    eventBusOn<Map>((obj) async {
      if (obj['refreshProfile'] == true) {
        await getUserInfo(false);
        //TODO:推送授权
        await AlterManager.instance.showNotificationAlter(NotificationAlterSource.afterCharge);
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> getUserInfo(bool isCharged) async {
    // TODO: 获取用户信息
    _userInfoModel = await LoginViewModel.getUserInfo();
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> onRefresh() async {
    await getUserInfo(false);

    widget.dealRefreshState(1, 1, []);
  }

  Future<void> onLoading() async {}

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var profileList = DataConfigUtil.instance.profileList(_userInfoModel);
    var cardWidth = mediaQuery.size.width * 0.9;
    var cardHeight = cardWidth / 4.9;
    var marginH = (mediaQuery.size.width - cardWidth) / 2.0;
    var iconWidth = mediaQuery.size.width * 0.061;
    return Scaffold(
      body: SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Padding(
                  padding:
                      EdgeInsets.only(top: mediaQuery.padding.top, right: 18),
                  child: IconButton(
                      icon: Image.asset(
                          "assets/images/profile/icon_setting.png",
                          width: 19,
                          height: 15),
                      onPressed: () async {
                        await Get.toNamed("/settingsPage");
                      }),
                ),
              ],
            ),
            ProfileHeader(
                infoModel: _userInfoModel,
                onEditTap: (avatar) async {
                  await Get.toNamed('/profileEditPage', arguments: {
                    "infoModel": _userInfoModel,
                    "avatar": avatar,
                    "callback": (infoModel) {
                      if (isAvailable(infoModel)) {
                        setState(() {
                          _userInfoModel = infoModel;
                        });
                      }
                    }
                  });
                  EventReportManager.eventReportOfFirebase(clickMeEdit);
                }),
            Expanded(
                child: MediaQuery.removePadding(
                    context: context,
                    removeTop: true,
                    removeLeft: true,
                    removeRight: true,
                    removeBottom: true,
                    child: RefreshLoadUtil(
                        onRefresh: onRefresh,
                        onLoading: onLoading,
                        controller: widget.refreshController,
                        enablePullUp: false,
                        child: ListView.builder(
                            itemCount: profileList.length,
                            itemBuilder: (context, i) {
                              return Container(
                                  width: cardWidth,
                                  height: profileList[i]["items"]!.length *
                                      cardHeight,
                                  margin: EdgeInsets.only(
                                      left: marginH,
                                      right: marginH,
                                      bottom: 12),
                                  decoration: i == 0
                                      ? BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          gradient: LinearGradient(
                                              colors: [
                                                HexColor('#20243d'),
                                                HexColor('#2b304d'),
                                              ],
                                              begin: Alignment.centerLeft,
                                              end: Alignment.centerRight),
                                        )
                                      : BoxDecoration(
                                          color: HexColor('#FFFFFF'),
                                          borderRadius:
                                              BorderRadius.circular(12),
                                        ),
                                  child: ListView.builder(
                                      physics:
                                          const NeverScrollableScrollPhysics(),
                                      itemCount:
                                          profileList[i]["items"]?.length,
                                      itemBuilder: (context, j) {
                                        return GestureDetector(
                                          onTap: () {
                                            cellTaped(i, j);
                                          },
                                          behavior: HitTestBehavior.opaque,
                                          child: i == 0
                                              ? SizedBox(
                                                  height: cardHeight,
                                                  child: ProfileCardPremium(
                                                    cardWidth: cardWidth,
                                                    cardHeight: cardHeight,
                                                    userInfoModel:
                                                        _userInfoModel,
                                                  ))
                                              : Padding(
                                                  padding:
                                                      const EdgeInsets.fromLTRB(
                                                          15, 0, 15, 0),
                                                  child: ProfileCardCell(
                                                    arguments: profileList[i]
                                                        ["items"]?[j],
                                                    size: Size(cardWidth - 30,
                                                        cardHeight),
                                                    iconSize: Size(
                                                        iconWidth, iconWidth),
                                                    cellType: i == 1
                                                        ? ProfileCellType
                                                            .account
                                                        : ProfileCellType.none,
                                                    title: i == 1
                                                        ? "${_userInfoModel?.goldCoin.toString() ?? ''} ${'coins'.tr}"
                                                        : "",
                                                  )),
                                        );
                                      }));
                            }))))
          ],
        ),
      ),
    );
  }

  Future<void> cellTaped(int section, int index) async {
    switch (section) {
      case 0:
        _toPremiumPage();
        break;
      case 1:
        await Get.toNamed("/accountPage", arguments: {});
        EventReportManager.eventReportOfFirebase(clickMeCoin);
        break;
      case 2:
        switch (index) {
          case 0:
            await Get.toNamed("/becomeWriterPage");
            break;
          // case 1:
          //   showAlter(const RedeemCodeContent());
          //   break;
          case 1:
            ///语言点击事件
            await Get.toNamed('/storyLanguagePage');
            EventReportManager.eventReportOfFirebase(meLanguage);
            break;
          case 2:
            // 打开 FreshChat
            await _openFreshChat();
            break;
        }
        break;
      case 3:
        switch (index) {
          case 0:
            //feedback
            await _followFacebook();
            break;
          case 1:
            //instagram
            await _followInstagram();
            break;
          case 2:
            //whatsapp
            await _followWhatsapp();
            break;
        }
        break;
    }
  }

  Future<void> _toPremiumPage() async {
    // TODO: implement premium purchase
    await Get.toNamed("/premiumPage", arguments: {
      "userInfo": _userInfoModel,
      'bookId': null,
      'chapterId': null,
      'orderType': OrderType.personalCenter,
    });
    EventReportManager.eventReportOfFirebase(clickMeVip);
  }

  //打开客服
  Future<void> _openFreshChat() async {
    await ThirdManger.identifyUser(_userInfoModel);
    // Freshchat.showConversations();
    Freshchat.showFAQ(
        faqTags: null,
        faqFilterType: FaqFilterType.Category,
        showFaqCategoriesAsGrid: false);
  }

  //Terms of Service
  Future<void> _followFacebook() async {
    await ShareManager.instance.launchLinkUrl(followFacebookUrl);
  }

  //Privacy Policy
  Future<void> _followInstagram() async {
    await ShareManager.instance.launchLinkUrl(followInstagramUrl);
  }

  //about Us
  Future<void> _followWhatsapp() async {
    await ShareManager.instance.launchLinkUrl(followWhatsappUrl);
  }
}
