import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';

import '../../../../Util/Extensions/colorUtil.dart';
import '../../../../Util/tools.dart';
import '../../Model/goods_info_model.dart';
import '../../Widget/PremiumPage/premium_about_subscription.dart';

class PremiumRulePage extends BaseFulWidget {
  PremiumRulePage({super.key, required super.arguments});

  @override
  State<PremiumRulePage> createState() => _PremiumRulePageState();
}

class _PremiumRulePageState extends State<PremiumRulePage> {
  late List<GoodsListItem>? _goodsList;
  late List<GoodsListItem> _normalList;
  late List<GoodsListItem> _subscriptionList;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    _goodsList = widget.arguments['goodsList'];
    _normalList = [];
    _subscriptionList = [];

    _goodsList?.forEach((item) {
      //商品是否订阅商品（0:不是;1:是）
      if (item.productSubscription == 0) {
        _normalList.add(item);
      } else {
        _subscriptionList.add(item);
      }
    });
  }

  // TODO: 商品介绍
  ///isAuto: 是否是自动订阅
  List<TextSpan> _buildAboutSubscription(bool isAuto) {
    List<TextSpan> textSpans = [];
    if (isAuto) {
      if (isAvailable(_subscriptionList)) {
        for (var item in _subscriptionList) {
          String title = getPremiumTitle(item.productCode);
          String subTitle =
              getPremiumSubTitle(item.numberOfPeriods, item.productCode);
          textSpans.add(TextSpan(
              text: '- $title ',
              style: TextStyle(
                  color: HexColor('#1F233B'),
                  fontSize: 15,
                  fontWeight: FontWeight.bold,
                  height: 1),
              children: [
                if (isAvailable(item.introductoryPrice))
                  TextSpan(
                      text: "(${item.currencySymbol}${item.introductoryPrice}",
                      style: TextStyle(
                        color: HexColor('#1F233B'),
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                        height: 1,
                      ),
                      children: [
                        TextSpan(text: '('),
                        TextSpan(
                            text: "${item.showPrice}",
                            style: TextStyle(
                              color: HexColor('#1F233B'),
                              fontSize: 15,
                              fontWeight: FontWeight.bold,
                              height: 1,
                              decoration: TextDecoration.lineThrough,
                              decorationColor: HexColor('#1F233B'),
                              decorationStyle: TextDecorationStyle.solid,
                              decorationThickness: 2,
                            )),
                        TextSpan(text: ')'),
                        TextSpan(
                            text: "/$subTitle)\n",
                            style: TextStyle(
                              color: HexColor('#1F233B'),
                              fontSize: 15,
                              fontWeight: FontWeight.bold,
                            )),
                      ])
                else
                  TextSpan(
                      text: "(${item.showPrice}/$subTitle)\n",
                      style: TextStyle(
                        color: HexColor('#1F233B'),
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                      )),
              ]));
        }
      }
    } else {
      if (isAvailable(_normalList)) {
        for (var item in _normalList) {
          String title = getPremiumTitle(item.productCode);
          String subTitle = '${item.showPrice}';
          textSpans.add(TextSpan(
              text: '- $title ($subTitle)\n',
              style: TextStyle(
                  color: HexColor('#1F233B'),
                  fontSize: 15,
                  fontWeight: FontWeight.bold,
                  height: 1)));
        }
      }
    }

    return textSpans;
  }

  @override
  Widget build(BuildContext context) {
    // TODO: 底部about商品介绍
    var autoList = _buildAboutSubscription(true);
    var normalList = _buildAboutSubscription(false);
    return Scaffold(
        appBar: AppBar(
          title: Text('rules'.tr),
          centerTitle: true,
          backgroundColor: HexColor('#F5F5F5'),
        ),
        body: SingleChildScrollView(
          child: PremiumAboutSubscription(
              autoSubscriptionList: autoList,
              normalSubscriptionList: normalList),
        ));
  }
}
