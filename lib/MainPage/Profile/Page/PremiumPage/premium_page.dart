import 'dart:math' as math;

import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/MainPage/Profile/Widget/PremiumPage/premium_list.dart';
import 'package:UrNovel/Util/Common/Animation/lottie_animation_view.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../Launch&Login/Model/user_model.dart';
import '../../../../Launch&Login/ViewModel/ViewModel.dart';
import '../../../../Util/Common/CommonManager/common_manager.dart';
import '../../../../Util/Common/no_load_view.dart';
import '../../../../Util/Extensions/colorUtil.dart';
import '../../../../Util/PaymentManager/payment_manager.dart';
import '../../../../Util/SharedPreferences/shared_preferences.dart';
import '../../../../Util/SheetAndAlter/alter.dart';
import '../../../../Util/SheetAndAlter/toast.dart';
import '../../../../Util/enum.dart';
import '../../../../Util/logUtil.dart';
import '../../../../Util/tools.dart';
import '../../Model/goods_info_model.dart';
import '../../ViewModel/ViewModel.dart';
import '../../Widget/PremiumPage/premium_offer_activity_card.dart';
import '../../Widget/PremiumPage/premium_offer_card.dart';
import '../../Widget/PremiumPage/premium_trialAndProbation_card.dart';

class PremiumPage extends BaseFulWidget {
  PremiumPage({super.key, required super.arguments});

  @override
  State<PremiumPage> createState() => _PremiumPageState();
}

class _PremiumPageState extends State<PremiumPage> {
  late bool _isLoading;
  late UserInfoModel? _userInfo;
  late int? _bookId;
  late int? _chapterId;
  late OrderType? _orderType;
  late List<GoodsListItem>? _goodsList;
  late List<GoodsListItem> _activityList;
  late List<GoodsListItem> _normalList;
  late List<GoodsListItem> _subscriptionList;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    _isLoading = false;
    _activityList = [];
    _subscriptionList = [];
    _normalList = [];
    _userInfo = widget.arguments['userInfo'];
    _bookId = widget.arguments['bookId'];
    _chapterId = widget.arguments['chapterId'];
    _orderType = widget.arguments['orderType'];

    fetchData();
  }

  @override
  void dispose() {
    setSystemUiDark();
    super.dispose();
  }

  Future<void> fetchData() async {
    await getLocalUserInfo();
    await getGoodsList();
  }

  Future<void> getLocalUserInfo() async {
    // TODO: Implement getUserInfo
    _userInfo = await SpUtil.spGetUserInfo();
    setState(() {});
  }

  Future<void> getUserInfo() async {
    // TODO: 获取用户信息
    _userInfo = await LoginViewModel.getUserInfo();
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> getGoodsList({bool isNeedLoading = true}) async {
    // TODO: implement fetchData
    if (mounted && isNeedLoading) {
      setState(() {
        _isLoading = true;
      });
    }

    _goodsList = await ProfileViewModel.getGoodList(GoodsType.purchaseMembership);
    dealGoodsType(_goodsList);
  }

  // TODO: 处理商品类型（自动订阅/普通订阅）
  void dealGoodsType(List<GoodsListItem>? goodsList) {
    _activityList = [];
    _subscriptionList = [];
    _normalList = [];

    List<GoodsListItem> otherList = [];
    goodsList?.forEach((item) {
      if (isGoodsActivity(item.activityPricePower, item.activityType)) {
        _activityList.add(item);
      } else {
        otherList.add(item);
      }
    });

    for (var item in otherList) {
      //商品是否订阅商品（0:不是;1:是）
      if (item.productSubscription == 0) {
        _normalList.add(item);
      } else {
        _subscriptionList.add(item);
      }
    }

    // TODO: 排序订阅商品（推荐/普通订阅）
    _subscriptionList.sort((a, b) {
      bool? recommendA = a.recommend == 1;
      bool? recommendB = b.recommend == 1;
      // 如果 b 推荐且 a 不推荐，b 排在前面
      if (recommendA == false && recommendB == true) {
        return 1; // b在前
      }

      return 0; //保持不变
    });

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // TODO: 创建订单
  Future<void> _createShopOrder(GoodsListItem? item) async {
    if (_isLoading) {
      logP("正在进行中");
      return;
    }

    if (!isAvailable(item)) {
      showToast("product is Not Available");
      return;
    }

    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    //商品是否订阅商品（0:不是;1:是）
    PaymentManager.instance.createShopOrder(item!,
        goodsType: item.productSubscription == 0
            ? GoodsType.purchaseMembership
            : GoodsType.subscriptionMembership,
        bookId: _bookId,
        chapterId: _chapterId,
        orderType: _orderType ?? OrderType.personalCenter, pullPurchaseCallback: () async {
      // TODO: 支付掉起回调
    }, onPurchaseCanceled: () async {
      // TODO: 支付取消回调
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }, onPurchaseValidateCallback: (status, info) async {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }

      // TODO: 验证订单回调
      if (status == SubscribeVerifyStatus.purchased) {
        Get.back();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    var count = 2 + _activityList.length + _subscriptionList.length;
    return PopScope(
        onPopInvokedWithResult: (didPop, result) async {},
        canPop: !_isLoading,
        child: Scaffold(
          appBar: AppBar(
            title: Text("premium".tr,
                style: TextStyle(
                    fontSize: 19, color: HexColor('#FFFFFF'), fontWeight: FontWeight.bold)),
            backgroundColor: HexColor("#20243D"),
            leading: IconButton(
              icon: Transform.rotate(
                angle: CommonManager.instance.isReverse() ? math.pi : 0,
                child:
                    Image.asset("assets/images/common/icon_back_white.png", width: 12, height: 20),
              ),
              // 使用默认的返回箭头图标
              onPressed: () async {
                await handleCurrentPage(context, () async {
                  if (isAvailable(_activityList)) {
                    GoodsListItem? goodItem = _activityList.first;
                    if (isAvailable(goodItem)) {
                      // TODO: 处理活动商品
                      bool isShow = await SpUtil.spGetIsPremiumReminderAlterShow();

                      bool isVerify = await SpUtil.spGetVerifyStatus();
                      if (isShow && !isVerify) {
                        showAlter(
                            SubscribeReminderContent(
                                goodItem: goodItem,
                                onClaimNow: (item) {
                                  _createShopOrder(item);
                                },
                                onBack: () {
                                  Get.back();
                                }),
                            backgroundColor: Colors.transparent);
                      } else {
                        Get.back();
                      }
                    } else {
                      Get.back();
                    }
                  } else {
                    Get.back();
                  }
                });
              },
            ),
            actions: [
              TextButton(
                  onPressed: () {
                    setState(() {
                      _isLoading = true;
                    });
                    PaymentManager.instance.restorePurchase(() {
                      setState(() {
                        _isLoading = false;
                      });
                      showToast('restored_successfully'.tr);
                    });
                  },
                  child: Text(
                    "restore".tr,
                    style: TextStyle(
                        color: HexColor("#91969C"), fontSize: 16, fontWeight: FontWeight.bold),
                  )),
              IconButton(
                icon: Image.asset('assets/images/profile/icon_premium_rule_white.png',
                    width: 20, height: 20),
                // 使用默认的返回箭头图标
                onPressed: () async {
                  await Get.toNamed('/premiumRulePage', arguments: {'goodsList': _goodsList});
                },
              ),
            ],
          ),
          body: Stack(
            children: [
              Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: MediaQuery.removePadding(
                      context: context,
                      removeTop: true,
                      removeRight: true,
                      removeLeft: true,
                      removeBottom: true,
                      child: Padding(
                        padding: EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom),
                        child: DecoratedBox(
                          decoration: BoxDecoration(color: HexColor("#FFFFFF")),
                          child: ListView.builder(
                              physics: ClampingScrollPhysics(),
                              itemCount: count,
                              itemBuilder: (context, index) {
                                if (index == 0) {
                                  return PremiumTrialAndProbationCard(userInfo: _userInfo);
                                } else if (index < 1 + _activityList.length) {
                                  var item = _activityList[index - 1];
                                  return PremiumOfferActivityCard(
                                    goodsItem: item,
                                    onGoodsTap: () {
                                      // TODO: 购买会员
                                      _createShopOrder(item);
                                    },
                                    onRefreshGoodsList: () async {
                                      // TODO: 刷新商品列表
                                      await getGoodsList(isNeedLoading: false);
                                    },
                                    topMargin: index == 1 ? 0 : 15,
                                  );
                                } else if (index <
                                    1 + _activityList.length + _subscriptionList.length) {
                                  GoodsListItem item =
                                      _subscriptionList[index - 1 - _activityList.length];
                                  return PremiumOfferCard(
                                      goodsItem: item,
                                      onGoodsTap: () {
                                        // TODO: 购买会员
                                        _createShopOrder(item);
                                      },
                                      topMargin: index == 1 ? 0 : 15);
                                } else {
                                  return Padding(
                                    padding: EdgeInsets.only(bottom: 15),
                                    child: PremiumList(
                                        goodsList: _normalList,
                                        onGoodsTap: (item) {
                                          // TODO: 购买会员
                                          _createShopOrder(item);
                                        }),
                                  );
                                }
                              }),
                        ),
                      ))),
              if (_isLoading)
                const Positioned(child: LottieAnimationView())
              else if (!isAvailable(_activityList) &&
                  !isAvailable(_subscriptionList) &&
                  !isAvailable(_normalList))
                Positioned(top: 0, left: 0, right: 0, bottom: 0, child: NoLoadView()),
            ],
          ),
        ));
  }
}
