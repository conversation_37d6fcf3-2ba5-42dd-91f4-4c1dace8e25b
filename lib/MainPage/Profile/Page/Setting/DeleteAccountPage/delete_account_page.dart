import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:UrNovel/BaseWidget/base_less_widget.dart';
import '../../../../../Util/Extensions/colorUtil.dart';

class DeleteAccountPage extends BaseLessWidget {
  DeleteAccountPage({super.key});

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    return Scaffold(
      appBar: AppBar(
        backgroundColor: HexColor('#FFFFFF'),
        title: Text('delete_account'.tr),
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        padding: const EdgeInsets.only(top: 30, left: 20, right: 20),
        color: HexColor('#FFFFFF'),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
                child: Text(
                  'delete_account_pop'.tr,
                  style: TextStyle(color: HexColor('#000000'), fontSize: 17),
                  maxLines: null,
                )),
            const SizedBox(height:20),
            Container(
              width: double.infinity,
              height: 44,
              margin:
              EdgeInsets.only(bottom: mediaQuery.padding.bottom + 30, left: 29, right: 29),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: LinearGradient(
                    colors: [HexColor("#0087FB"), HexColor("#0099F8")]),
              ),
              child: TextButton(
                onPressed: () async {
                  await Get.toNamed("/deletionScheduledPage");
                },
                child: Text(
                  "continue".tr,
                  style: TextStyle(
                      color: HexColor('#FFFFFF'),
                      fontSize: 16,
                      fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
