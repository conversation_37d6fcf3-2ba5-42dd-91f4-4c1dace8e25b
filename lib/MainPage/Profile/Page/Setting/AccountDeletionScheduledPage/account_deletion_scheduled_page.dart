import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:UrNovel/Launch&Login/ViewModel/ViewModel.dart';
import 'package:UrNovel/Util/Common/Animation/lottie_animation_view.dart';
import 'package:UrNovel/Util/SheetAndAlter/alter.dart';
import 'package:UrNovel/Util/SheetAndAlter/toast.dart';
import '../../../../../Util/Extensions/colorUtil.dart';
import '../../../../../Util/SharedPreferences/shared_preferences.dart';

class AccountDeletionScheduledPage extends StatefulWidget {
  const AccountDeletionScheduledPage({super.key});

  @override
  State<AccountDeletionScheduledPage> createState() => _AccountDeletionScheduledPageState();
}

class _AccountDeletionScheduledPageState extends State<AccountDeletionScheduledPage> {
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
  }

  destroyed() async {
    //TODO:用户注销,删除账号
    if (mounted) {
      setState(() {
        isLoading = true;
      });
    }
    bool isSuccess = await LoginViewModel.authDestroyed();
    if (isSuccess) {
      await SpUtil.clearLoginInfo();

      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }

      showToast('deletion_Scheduled'.tr);
      Future.delayed(const Duration(seconds: 3), () {
         exit(0);
      });
    } else {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    return Scaffold(
      appBar: AppBar(
        backgroundColor: HexColor('#FFFFFF'),
        title: Text('deletion_Scheduled'.tr),
      ),
      body: Stack(
        children: [
          Container(
            width: double.infinity,
            height: double.infinity,
            padding: const EdgeInsets.only(top: 30, left: 20, right: 20),
            color: HexColor('#FFFFFF'),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: SingleChildScrollView(
                      child: RichText(
                          text: TextSpan(
                              text: '${'delete_submit'.tr}\n\n',
                              style: TextStyle(
                                  color: HexColor('#000000'), fontSize: 17),
                              children: [
                                TextSpan(
                                  text: '${'detele_desc1'.tr}\n\n',
                                  style:
                                  TextStyle(color: HexColor('#000000'), fontSize: 17),
                                ),
                                TextSpan(
                                  text: '${'detele_desc2'.tr}\n\n',
                                  style:
                                  TextStyle(color: HexColor('#000000'), fontSize: 17),
                                ),
                                TextSpan(
                                  text: '${'detele_desc3'.tr}\n\n',
                                  style:
                                  TextStyle(color: HexColor('#000000'), fontSize: 17),
                                ),
                                TextSpan(
                                  text: '${'detele_desc4'.tr}\n\n',
                                  style:
                                  TextStyle(color: HexColor('#000000'), fontSize: 17),
                                ),
                                TextSpan(
                                  text: '${'detele_desc5'.tr}\n\n',
                                  style:
                                  TextStyle(color: HexColor('#000000'), fontSize: 17),
                                ),
                              ]))),
                ),
                const SizedBox(height: 20),
                Container(
                  width: double.infinity,
                  height: 44,
                  margin: EdgeInsets.only(
                      bottom: mediaQuery.padding.bottom + 30, left: 29, right: 29),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    gradient: LinearGradient(
                        colors: [HexColor("#0087FB"), HexColor("#0099F8")]),
                  ),
                  child: TextButton(
                    onPressed: () {
                      showAlter(DeleteAccountContent(onConfirm: () {
                        destroyed();
                      }));
                    },
                    child: Text(
                      "delete_exit".tr,
                      style: TextStyle(
                          color: HexColor('#FFFFFF'),
                          fontSize: 16,
                          fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
              ],
            ),
          ),

          if (isLoading)
            const LottieAnimationView()
        ],
      ),
    );
  }
}
