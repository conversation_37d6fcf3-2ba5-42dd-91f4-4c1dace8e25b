import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:UrNovel/Launch&Login/ViewModel/ViewModel.dart';
import 'package:UrNovel/Util/Common/Animation/lottie_animation_view.dart';
import 'package:UrNovel/Util/SheetAndAlter/bottom_sheet.dart';
import 'package:UrNovel/Util/tools.dart';

import '../../../../Util/Extensions/colorUtil.dart';
import '../../../../Util/SharedPreferences/shared_preferences.dart';
import '../../../../Util/StatusManagement/status_management.dart';
import '../../../../Util/enum.dart';
import '../../../../Util/genericUtil.dart';
import '../../../../Util/stringUtil.dart';
import '../../Widget/Settings/settings_full_cell.dart';
import '../../Widget/Settings/settings_less_cell.dart';
import '../../Widget/Settings/settings_version.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  final list = [
    {
      "items": [
        {"title": "enable_notifications".tr},
        {"title": "auto_unlock".tr}
      ],
    },
    {
      "items": [
        {"title": "clear_cache".tr}
      ],
    },
    {
      "items": [
        {"title": "terms".tr},
        {"title": "pricavy".tr},
        {"title": "DMCA".tr},
        {"title": "rate_us".tr},
      ],
    },
    // {
    //   "items": [
    //     {"title": "network_diagnosis".tr}
    //   ],
    // }
  ];

  late bool isLoading;

  // 实例化控制器
  final BottomBarController bottomBarController =
  findGetXInstance(BottomBarController());

  @override
  initState() {
    super.initState();

    isLoading = false;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: HexColor("#FFFFFF"),
        title: Text(
          "settings".tr,
          style: TextStyle(
              color: HexColor("#000000"),
              fontSize: 19,
              fontWeight: FontWeight.bold),
        ),
      ),
      body: SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: Stack(
          children: [
            Column(
              children: [
                Expanded(
                    flex: 9,
                    child: ListView.builder(
                        itemCount: list.length + 2,
                        itemBuilder: (context, i) {
                          if (i < list.length) {
                            return Container(
                                margin: const EdgeInsets.only(top: 9),
                                color: HexColor("#FFFFFF"),
                                height: list[i]["items"] != null
                                    ? 50.0 * list[i]["items"]!.length
                                    : 0,
                                child: ListView.builder(
                                    physics:
                                    const NeverScrollableScrollPhysics(),
                                    itemCount: list[i]["items"]?.length,
                                    itemBuilder: (context, j) {
                                      if (i <= 1 &&
                                          j < list[i]["items"]!.length) {
                                        return SettingsFullCell(
                                            arguments: list[i]["items"]?[j],
                                            sectionType: i == 0
                                                ? j == 0
                                                ? SettingSectionType
                                                .notifications
                                                : SettingSectionType
                                                .autoUnlock
                                                : SettingSectionType.other);
                                      } else {
                                        return GestureDetector(
                                          onTap: () {
                                            cellSelected(context, i - 2, j);
                                          },
                                          child: SettingsLessCell(
                                              arguments: list[i]["items"]?[j]),
                                        );
                                      }
                                    }));
                          } else {
                            return GestureDetector(
                              onTap: () {
                                cellSelected(context, i - 2, 0);
                              },
                              child: Container(
                                margin: const EdgeInsets.only(top: 9),
                                width: double.infinity,
                                height: 50,
                                color: HexColor('#FFFFFF'),
                                alignment: Alignment.center,
                                child: i == list.length
                                    ? Text("sign_out".tr,
                                    style: TextStyle(
                                        color: HexColor('#F42222'),
                                        fontSize: 17,
                                        fontWeight: FontWeight.bold))
                                    : Text("delete_account".tr,
                                    style: TextStyle(
                                        color: HexColor('#7E839D'),
                                        fontSize: 17,
                                        fontWeight: FontWeight.bold)),
                              ),
                            );
                          }
                        })),
                Expanded(flex: 1, child: SettingsVersion()),
              ],
            ),

            if (isLoading)
              LottieAnimationView()
          ],
        ),
      ),
    );
  }

  Future<void> cellSelected(
      BuildContext context, int section, int index) async {
    switch (section) {
      case 0:
        switch (index) {
          case 0:
            _termsOfService();
            break;
          case 1:
            _privacyPolicy();
            break;
          case 2:
            _dmca();
            break;
          case 3:
            _rateUs();
            break;
        }
        break;
    // case 1:
    //   _networkDiagnosis();
    //   break;
      case 1:
        showBookBottomSheet(context, SettingsLoginOutSheet(onConfirm: () {
          //退出登录
          _authLogout();
        }), HexColor('#F4F5F6'));
        break;
      case 2:
        await Get.toNamed('/deleteAccountPage');
        break;
    }
  }

  //Terms of Service
  Future<void> _termsOfService() async {
    await Get.toNamed('/webViewPage',
        arguments: {'title': 'terms'.tr, 'url': termsOfServiceUrl});
  }

  //Privacy Policy
  Future<void> _privacyPolicy() async {
    await Get.toNamed('/webViewPage',
        arguments: {'title': 'pricavy'.tr, 'url': privacyPolicyUrl});
  }

  //DMCA
  Future<void> _dmca() async {
    await Get.toNamed('/webViewPage',
        arguments: {'title': 'DMCA'.tr, 'url': dmca});
  }

  //Rate Us
  void _rateUs() {
    toAppStore(appStoreRatingUrl);
  }

  //网络诊断
  void _networkDiagnosis() {
    // PermissionManager.instance.checkNetworkStatus();
  }

  //logout
  _authLogout() async {
    setState(() {
      isLoading = true;
    });
    bool success = await LoginViewModel.authLogout();
    setState(() {
      isLoading = false;
    });

    if (success) {
      await SpUtil.clearLoginInfo();

      bottomBarController.changeIndex(0, false);
      await Get.offAllNamed('/loginPage');
    }
  }
}
