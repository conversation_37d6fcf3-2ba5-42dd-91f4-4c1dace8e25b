import 'package:flutter/cupertino.dart';
import 'package:UrNovel/BaseWidget/base_less_widget.dart';
import 'package:UrNovel/Util/Extensions/colorUtil.dart';

class PurchasedNovelsCell extends BaseLessWidget {
  PurchasedNovelsCell({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 90,
      child: Padding(
        padding: const EdgeInsets.only(bottom: 18),
        child: Row(
          children: [
            const SizedBox(width: 14),
            Image.asset('assets/images/profile/icon_bonus.png',
                width: 54, height: 72),
            const SizedBox(width: 9),
            Expanded(
              flex: 7,
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text("Captivated by the ",
                        style: TextStyle(
                            fontSize: 15,
                            color: HexColor("#000000"),
                            fontWeight: FontWeight.bold)),
                    const Sized<PERSON>ox(height: 8),
                    Text("author's name",
                        style: TextStyle(
                            fontSize: 13, color: HexColor("#555A65"))),
                  ]),
            ),
            Expanded(
                flex: 3,
                child: Padding(
                    padding: const EdgeInsets.only(right: 14),
                    child: Text("40.12 Coins",
                        style: TextStyle(
                            fontSize: 12,
                            color: HexColor("#000000")),
                        textAlign: TextAlign.right)))
          ],
        ),
      ),
    );
  }
}
