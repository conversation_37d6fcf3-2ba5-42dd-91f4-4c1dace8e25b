import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:UrNovel/BaseWidget/base_less_widget.dart';
import 'package:UrNovel/Util/Extensions/colorUtil.dart';

class PurchasedNovelsSearch extends BaseLessWidget {
  final Function(String) onChanged;
  PurchasedNovelsSearch({super.key, required this.onChanged});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 40,
      margin: const EdgeInsets.symmetric(horizontal: 10),
      decoration: BoxDecoration(
        color: HexColor("#ECEDF1"),
        borderRadius: BorderRadius.circular(16),
      ),
      child: CupertinoSearchTextField(
        placeholder: '',
        prefixInsets: const EdgeInsetsDirectional.fromSTEB(13, 0, 8, 0),
        // 调整图标内边距
        prefixIcon: Image.asset(
          'assets/images/read/icon_search.png',
          width: 13,
          height: 13,
        ),

        decoration: BoxDecoration(
          border: Border.all(
            width: 0, // 设置边框宽度为0
            color: Colors.transparent, // 设置边框颜色为透明
          ),
        ),
        onChanged: onChanged,
      ),
    );
  }
}
