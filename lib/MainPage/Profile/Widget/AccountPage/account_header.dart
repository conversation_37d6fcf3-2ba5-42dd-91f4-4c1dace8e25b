import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import '../../../../Launch&Login/Model/user_model.dart';
import '../../../../Util/Extensions/colorUtil.dart';

class AccountHeader extends BaseFulWidget {
  final UserInfoModel? userInfo;
  final Function()? onPurchase;
  AccountHeader({super.key, required this.userInfo, required this.onPurchase});

  @override
  State<AccountHeader> createState() => _AccountHeaderState();
}

class _AccountHeaderState extends State<AccountHeader> {

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10),
      width: double.infinity,
      decoration: BoxDecoration(
        color: HexColor('#FFFFFF'),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          const SizedBox(height: 16),
          Text("coin_balance".tr, style: TextStyle(fontSize: 15, color: HexColor('#91969C'))),
          const SizedBox(height: 14),
          Text("${widget.userInfo?.goldCoin?? 0.00}", style: TextStyle(fontSize: 32, color: HexColor('#000000'), fontWeight: FontWeight.bold)),
          const SizedBox(height: 20),
          Container(
            width: double.infinity,
            height: 45,
            margin: const EdgeInsets.symmetric(horizontal: 24),
            decoration: BoxDecoration(
              color: HexColor('#F5F6F8'),
              borderRadius: BorderRadius.circular(12),
            ),
            child: TextButton(
              onPressed: widget.onPurchase,
              child: Text("buy".tr, style: TextStyle(fontSize: 15, color: HexColor('#787C87'))),
            )
          ),
          const SizedBox(height: 12),
          Text("coin_rule".tr, style: TextStyle(fontSize: 11, color: HexColor('#91969C'))),
          const SizedBox(height: 20),
        ],
      ),
    );
  }
}
