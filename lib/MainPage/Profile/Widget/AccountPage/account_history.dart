import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:UrNovel/BaseWidget/base_less_widget.dart';
import 'package:UrNovel/MainPage/Profile/Widget/Settings/settings_less_cell.dart';
import 'package:UrNovel/Util/DataReportManager/event_report_manager.dart';

import '../../../../Util/DataReportManager/event_name_config.dart';
import '../../../../Util/Extensions/colorUtil.dart';

class AccountHistory extends BaseLessWidget {
  AccountHistory({super.key});

  final List<Map<String, String>> hisList = [
    {"title": "purchased_novels".tr},
    {"title": "transaction_history".tr},
    {"title": "purchase_history".tr}
  ];

  @override
  Widget build(BuildContext context) {
    double height = hisList.length * 55.0;
    return Container(
      width: double.infinity,
      height: height,
      margin: const EdgeInsets.symmetric(horizontal: 10),
      decoration: BoxDecoration(
        color: HexColor('#FFFFFF'),
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListView.builder(
          physics: const NeverScrollableScrollPhysics(),
          itemCount: hisList.length,
          itemBuilder: (context, index) {
            return GestureDetector(
              onTap: () {
                cellSelected(index);
              },
              child: SettingsLessCell(
                  arguments: hisList[index],
                  rowHeight: 55.0,
                  textColorHex: "#1F1F2F",
                  isTextBold: true),
            );
          }),
    );
  }

  Future<void> cellSelected(int index) async {
    switch (index) {
      case 0:
        await Get.toNamed('/purchasedNovelsPage');
        EventReportManager.eventReportOfFirebase(clickCoinNovels);
        break;
      case 1:
        await Get.toNamed('/transactionHistoryPage');
        EventReportManager.eventReportOfFirebase(clickCoinTransaction);
        break;
      case 2:
        await Get.toNamed('/purchaseHistoryPage');
        EventReportManager.eventReportOfFirebase(clickCoinPurchase);
        break;
    }
  }
}
