import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Launch&Login/Model/user_model.dart';
import 'package:UrNovel/Util/SharedPreferences/shared_preferences.dart';
import 'package:UrNovel/Util/tools.dart';

import '../../../../Util/CacheManager/cache_manager.dart';
import '../../../../Util/Extensions/colorUtil.dart';
import '../../../../Util/PermissionManager/permission_manager.dart';
import '../../../../Util/SheetAndAlter/toast.dart';
import '../../../../Util/ThirdSdkManger/third_manger.dart';
import '../../../../Util/enum.dart';

class SettingsFullCell extends BaseFulWidget {
  final SettingSectionType sectionType;

  SettingsFullCell(
      {super.key, required super.arguments, required this.sectionType});

  @override
  State<SettingsFullCell> createState() => _SettingsFullCellState();
}

class _SettingsFullCellState extends State<SettingsFullCell> {
  late String _cacheSize;
  late AuthorizationStatus notificationsStatus;
  late int _autoUnlock;
  final switchButtonKey = GlobalKey<SwitchButtonState>();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    _cacheSize = '';
    notificationsStatus = AuthorizationStatus.denied;
    _autoUnlock = 1;

    if (widget.sectionType == SettingSectionType.notifications) {
      getAuthorizationStatus();
    } else if (widget.sectionType == SettingSectionType.autoUnlock) {
      getChapterAutoUnlock();
    } else {
      _loadCacheSize();
    }
  }

  Future<void> getAuthorizationStatus() async {
    notificationsStatus =
        await PermissionManager.instance.getNotificationAuthorizationStatus();
    setState(() {});
  }

  Future<void> getChapterAutoUnlock() async {
    _autoUnlock = await SpUtil.spGetChapterAutoUnlock();
    if (_autoUnlock == -1) {
      UserInfoModel? user = await SpUtil.spGetUserInfo();
      _autoUnlock = user?.autoUnlock?? 1;
    }
    setState(() {});
  }

  Future<void> _loadCacheSize() async {
    final size = await CacheManager.getCacheSize();
    var sizeStr = (size / (1024 * 1024)).toStringAsFixed(1);
    if (0 < double.parse(sizeStr)) {
      setState(() {
        _cacheSize = "${sizeStr}M";
      });
    } else {
      setState(() {
        _cacheSize = "";
      });
    }
  }

  Future<void> clearCache() async {
    try {
      await CacheManager.clearCache();
      await _loadCacheSize();
      _loadCacheSize();
    } catch (e) {
      showToast(e.toString());
    }
  }

  Future<void> toggleNotificationSetting() async {
    if (notificationsStatus == AuthorizationStatus.notDetermined) {
      ThirdManger.requestNotificationPermission();
    } else {
      Get.back();
      ThirdManger.openAppSettings();
    }
  }

  Future<void> setChapterAutoUnlock() async {
    if (_autoUnlock == 1) {
      _autoUnlock = 0;
    } else {
      _autoUnlock = 1;
    }
    await SpUtil.spSetChapterAutoUnlock(_autoUnlock);
    switchButtonKey.currentState?.updateSwitchState(_autoUnlock);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: _buildFuture(context),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          } else if (snapshot.hasData) {
            return snapshot.data!;
          } else {
            return Center(child: Text('No data'));
          }
        });
  }

  Future<Widget> _buildFuture(BuildContext context) async {
    return GestureDetector(
        onTap: () async {
          if (widget.sectionType == SettingSectionType.notifications) {
            await toggleNotificationSetting();
          } else if (widget.sectionType == SettingSectionType.autoUnlock) {
            await setChapterAutoUnlock();
          } else if (isAvailable(_cacheSize)) {
            await clearCache();
          }
        },
        child: Container(
          width: double.infinity,
          height: 50,
          color: HexColor('#FFFFFF'),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                    child: Text(
                  widget.arguments['title'],
                  style: TextStyle(color: HexColor('#1F1F2F'), fontSize: 15),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                )),
                if (widget.sectionType == SettingSectionType.notifications ||
                    widget.sectionType == SettingSectionType.autoUnlock)
                  SwitchButton(
                    key: switchButtonKey,
                    sectionType: widget.sectionType,
                    notificationsStatus: notificationsStatus,
                    autoUnlock: _autoUnlock,
                  )
                else if (isAvailable(_cacheSize))
                  Text(_cacheSize,
                      style:
                          TextStyle(color: HexColor('#91969C'), fontSize: 13)),
              ],
            ),
          ),
        ));
  }
}

class SwitchButton extends StatefulWidget {
  final SettingSectionType sectionType;
  final AuthorizationStatus notificationsStatus;
  final int autoUnlock;

  const SwitchButton(
      {super.key,
      required this.sectionType,
      required this.notificationsStatus,
      required this.autoUnlock});

  @override
  State<SwitchButton> createState() => SwitchButtonState();
}

class SwitchButtonState extends State<SwitchButton> {
  late int _autoUnlock;

  @override
  initState() {
    super.initState();

    _autoUnlock = widget.autoUnlock;
  }

  updateSwitchState(int isOn) {
    setState(() {
      _autoUnlock = isOn;
    });
  }

  @override
  Widget build(BuildContext context) {
    return IconButton(
        onPressed: null,
        icon: SizedBox(
            width: 47,
            height: 27,
            child: widget.sectionType == SettingSectionType.notifications
                ? widget.notificationsStatus == AuthorizationStatus.authorized
                    ? Image.asset("assets/images/profile/icon_switch_on.png")
                    : Image.asset("assets/images/profile/icon_switch_off.png")
                : _autoUnlock == 1
                    ? Image.asset("assets/images/profile/icon_switch_on.png")
                    : Image.asset(
                        "assets/images/profile/icon_switch_off.png")));
  }
}
