import 'dart:math' as math;

import 'package:UrNovel/BaseWidget/base_less_widget.dart';
import 'package:flutter/cupertino.dart';

import '../../../../Util/Common/CommonManager/common_manager.dart';
import '../../../../Util/Extensions/colorUtil.dart';

class SettingsLessCell extends BaseLessWidget {
  final double rowHeight;
  final String textColorHex;
  final bool isTextBold;

  SettingsLessCell(
      {super.key,
      required super.arguments,
      this.rowHeight = 50.0,
      this.textColorHex = '#1F1F2F',
      this.isTextBold = false});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: rowHeight,
      color: HexColor('#FFFFFF'),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(arguments['title'],
                style: TextStyle(
                    color: HexColor(textColorHex),
                    fontSize: 15,
                    fontWeight:
                        isTextBold ? FontWeight.bold : FontWeight.normal)),
            Transform.rotate(
              angle: CommonManager.instance.isReverse() ? math.pi : 0,
              child: Image.asset(
                'assets/images/common/icon_arrow_gray.png',
                width: 6,
                height: 9,
              ),
            )
          ],
        ),
      ),
    );
  }
}
