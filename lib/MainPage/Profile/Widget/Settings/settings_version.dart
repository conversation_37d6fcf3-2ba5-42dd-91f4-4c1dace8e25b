import 'package:flutter/cupertino.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import '../../../../Util/Extensions/colorUtil.dart';

class SettingsVersion extends BaseFulWidget {
  SettingsVersion({super.key});

  @override
  State<SettingsVersion> createState() => _SettingsVersionState();
}

class _SettingsVersionState extends State<SettingsVersion> {

  late String _version = '';
  late String _buildNumber = '';

  @override
  void initState() {
    super.initState();
    _initPackageInfo();
  }

  Future<void> _initPackageInfo() async {
    final info = await PackageInfo.fromPlatform();
    setState(() {
      _version = info.version;
      _buildNumber = info.buildNumber;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Text("Version $_version  Build $_buildNumber", style: TextStyle(color: HexColor('#7E839D'), fontSize: 11));
  }
}
