import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';

import '../../../Launch&Login/Model/user_model.dart';
import '../../../Util/Extensions/colorUtil.dart';
import '../../../Util/tools.dart';

class ProfileCardPremium extends BaseFulWidget {
  final double cardWidth;
  final double cardHeight;
  final UserInfoModel? userInfoModel;

  ProfileCardPremium(
      {super.key,
      required this.cardWidth,
      required this.cardHeight,
      required this.userInfoModel});

  @override
  State<ProfileCardPremium> createState() => _PremiumCardState();
}

class _PremiumCardState extends State<ProfileCardPremium> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var premiumWidth = mediaQuery.size.width * 0.066;
    var premiumHeight = premiumWidth / 2.0;
    var buyWidth = mediaQuery.size.width * 0.26;
    var buyHeight = buyWidth / 2.66;

    var subExpireDaya = isAvailable(widget.userInfoModel?.subExpireTime)
        ? getDaysDifference(widget.userInfoModel!.subExpireTime!)
        : 0;
    if (subExpireDaya < 0) {
      subExpireDaya = 0;
    }
    var premiumExpireTimeStr =
        getPremiumExpireTimeStr(widget.userInfoModel?.subExpireTime);

    return Stack(children: [
      Positioned(
          left: 20,
          top: 10,
          bottom: 10,
          width: (widget.cardWidth - 39) * 0.677,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Image.asset('assets/images/profile/icon_premium_card.png',
                      width: premiumWidth,
                      height: premiumHeight,
                      fit: BoxFit.cover),
                  const SizedBox(width: 10),
                  Expanded(
                      child: Text(
                    "premium".tr,
                    style: TextStyle(
                        fontSize: 17,
                        color: HexColor("#F6D88F"),
                        fontWeight: FontWeight.bold),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  )),
                ],
              ),
              // if (widget.userInfoModel?.vipStatus != 1) SizedBox(height: 5),
              if (widget.userInfoModel?.vipStatus != 1)
                Expanded(
                    child: Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                            widget.userInfoModel?.vipStatus == 0
                                ? 'unlock_all'.tr
                                : 'renew_premium'.tr,
                            style: TextStyle(
                                fontSize: 12.sp,
                                color: HexColor('#F6D88F'),
                                height: 1),
                            maxLines: 3))),
            ],
          )),
      if (widget.userInfoModel?.vipStatus == 1)
        Positioned(
            top: 10,
            bottom: 10,
            right: 16,
            child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    'days'.trParams({'param': subExpireDaya.toString()}),
                    style: TextStyle(
                        fontSize: 17,
                        color: HexColor("#F6D88F"),
                        fontWeight: FontWeight.bold),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(width: 10),
                  Text(
                    'expires_on'.trParams({'param': premiumExpireTimeStr}),
                    style: TextStyle(
                        fontSize: 12,
                        color: HexColor("#F6D88F"),
                        fontWeight: FontWeight.bold),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ]))
      else
        Positioned(
            right: 17,
            top: (widget.cardHeight - buyHeight) / 2,
            width: buyWidth,
            height: buyHeight,
            child: Container(
                decoration: BoxDecoration(
                  color: HexColor("#F6D88F"),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: TextButton(
                  onPressed: null,
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                  ),
                  child: Text(
                    widget.userInfoModel?.vipStatus == 0
                        ? 'buy'.tr
                        : 'Renew'.tr,
                    style: TextStyle(
                        fontSize: Platform.isIOS ? 16 : 15,
                        color: HexColor("#704E1F"),
                        fontWeight: FontWeight.bold,
                        height: 1),
                  ),
                )))
    ]);
  }
}
