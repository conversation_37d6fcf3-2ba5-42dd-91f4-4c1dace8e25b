import 'package:flutter/material.dart';
import 'package:UrNovel/BaseWidget/base_less_widget.dart';
import '../../../../Util/Extensions/colorUtil.dart';

class BecomeAWriterContact extends BaseLessWidget {
  BecomeAWriterContact({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 15),
      decoration: BoxDecoration(
        color: HexColor('#FFFFFF'),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(mainAxisSize: MainAxisSize.min, children: [
        Padding(
            padding: const EdgeInsets.only(top: 15, left: 50, right: 50),
            child: Text('Ready to start? Contact us at:',
                style: TextStyle(
                    color: HexColor('#1F1F2F'),
                    fontSize: 15,
                    fontWeight: FontWeight.bold))),
        Padding(
            padding: const EdgeInsets.only(top: 10, left: 34, right: 34),
            child: Divider(color: ColorsUtil.hexColor(0x1F1F2F, alpha: 0.1))),

        Padding(
            padding: const EdgeInsets.only(top: 22, bottom: 24),
            child: Text('<EMAIL>',
                style: TextStyle(
                    color: HexColor('#1F1F2F'),
                    fontSize: 16,
                    fontWeight: FontWeight.bold))),
      ]),
    );
  }
}
