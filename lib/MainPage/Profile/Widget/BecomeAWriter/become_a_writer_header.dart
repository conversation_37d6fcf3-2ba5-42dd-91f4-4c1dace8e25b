import 'dart:io';

import 'package:flutter/material.dart';
import 'package:UrNovel/BaseWidget/base_less_widget.dart';

import '../../../../Util/Extensions/colorUtil.dart';

class BecomeAWriterHeader extends BaseLessWidget {
  BecomeAWriterHeader({super.key});

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    return Container(
      width: double.infinity,
      height: (Platform.isIOS || mediaQuery.padding.bottom == 0)
          ? mediaQuery.size.width / 1.38
          : mediaQuery.size.width / 1.20,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            HexColor("#068CE9"),
            HexColor("#3EDDFB"),
          ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(height: 77),
          Text("Write For UrNovel",
              style: TextStyle(
                  fontSize: 29,
                  color: HexColor("#FFFFFF"),
                  fontWeight: FontWeight.bold)),
          const SizedBox(height: 11),
          Text("———  Fast Fiction For Modern Readers  ———",
              style: TextStyle(fontSize: 14, color: HexColor("#FFFFFF"))),
          const Spacer(),
          Image.asset(
              "assets/images/profile/becomeAWriter/icon_author_header.png",
              width: 164,
              height: 112),
          const SizedBox(height: 10),
        ],
      ),
    );
  }
}
