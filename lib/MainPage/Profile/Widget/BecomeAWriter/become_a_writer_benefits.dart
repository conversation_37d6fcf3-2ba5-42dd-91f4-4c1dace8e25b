import 'package:flutter/cupertino.dart';
import 'package:UrNovel/BaseWidget/base_less_widget.dart';

import '../../../../Util/Extensions/colorUtil.dart';

class BecomeAWriterBenefits extends BaseLessWidget {
  BecomeAWriterBenefits({super.key});

  final List<Map<String, dynamic>> itemList = [
    {"text": "· Readership Share:  50% of the readership revenue."},
    {
      "text":
          "· Additional Bonuses:  Up to an extra 50% bonus based on monthly performance (e.g., entering the top 10 of the monthly rankings)."
    },
    {"text": "· Readership Share:  30% of the readership revenue"},
    {
      "text":
          "· Additional Bonuses:  Up to an extra 20% bon us based on monthly performance (e.g., entering the top 10 of the monthly rankings)."
    }
  ];

  @override
  Widget build(BuildContext context) {
    return  Stack(
      alignment: Alignment.topCenter,
      children: [
        //todo:白色背景
        Positioned(
            top: 11,
            left: 15,
            right: 15,
            bottom: 0,
            child: Container(
              width: double.infinity,
              height: 100,
              decoration: BoxDecoration(
                color: HexColor("#FFFFFF"),
                borderRadius: const BorderRadius.all(Radius.circular(12)),
              ),
            )),

        Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            //todo:header
            Container(
              width: 160,
              height: 40,
              alignment: Alignment.center,
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(
                      'assets/images/profile/becomeAWriter/icon_author_section_header.png'),
                  fit: BoxFit.fill,
                ),
              ),
              child: Text("Benefits",
                  style: TextStyle(
                      fontSize: 17,
                      color: HexColor("#FFFFFF"),
                      fontWeight: FontWeight.bold)),
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.fromLTRB(8, 10, 16, 20),
                  margin: const EdgeInsets.only(left: 7),
                  alignment: Alignment.centerLeft,
                  decoration: const BoxDecoration(
                    image: DecorationImage(
                      alignment: Alignment.centerLeft,
                      image: AssetImage(
                          'assets/images/profile/becomeAWriter/icon_author_section.png'),
                      fit: BoxFit.fill,
                    ),
                  ),
                  child: Text("Exclusive Partnership",
                      style: TextStyle(
                          fontSize: 15,
                          color: HexColor("#0D84E7"),
                          fontWeight: FontWeight.bold)),
                )
              ],
            ),
            const SizedBox(height: 12),
            BecomeAWriterBenefitsItem(arguments: itemList[0]),
            const SizedBox(height: 26),
            BecomeAWriterBenefitsItem(arguments: itemList[1]),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.fromLTRB(8, 10, 16, 20),
                  margin: const EdgeInsets.only(left: 7),
                  alignment: Alignment.centerLeft,
                  decoration: const BoxDecoration(
                    image: DecorationImage(
                      alignment: Alignment.centerLeft,
                      image: AssetImage(
                          'assets/images/profile/becomeAWriter/icon_author_section.png'),
                      fit: BoxFit.fill,
                    ),
                  ),
                  child: Text("Non-Exclusive Partnership",
                      style: TextStyle(
                          fontSize: 15,
                          color: HexColor("#0D84E7"),
                          fontWeight: FontWeight.bold)),
                )
              ],
            ),
            const SizedBox(height: 18),
            BecomeAWriterBenefitsItem(arguments: itemList[2]),
            const SizedBox(height: 26),
            BecomeAWriterBenefitsItem(arguments: itemList[3]),
            const SizedBox(height: 22),
          ],
        )
      ],
    );
  }
}

class BecomeAWriterBenefitsItem extends BaseLessWidget {
  BecomeAWriterBenefitsItem({super.key, required super.arguments});

  @override
  Widget build(BuildContext context) {
    List<String>? textList = arguments['text']?.split(':');
    return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 30),
        child: Align(
          alignment: Alignment.centerLeft,
          child: RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: "${textList?.first}:",
                  style: TextStyle(
                      color: HexColor("#1F1F2F"),
                      fontSize: 15,
                      fontWeight: FontWeight.bold),
                ),
                TextSpan(
                  text: ' ${textList?.last}',
                  style: TextStyle(color: HexColor("#1F1F2F"), fontSize: 12),
                ),
              ],
            ),
          )
        ));
  }
}
