import 'package:flutter/cupertino.dart';
import 'package:UrNovel/BaseWidget/base_less_widget.dart';

import '../../../../Util/Extensions/colorUtil.dart';

class BecomeAWriterIntroduce extends BaseLessWidget {
  BecomeAWriterIntroduce({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 25),
      decoration: BoxDecoration(
        color: HexColor('F2F4F8'),
        borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(12), topRight: Radius.circular(12)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 22),
          Text("Dear Storytellers:",
              style: TextStyle(
                  color: HexColor('#1F1F2F'),
                  fontSize: 19,
                  fontWeight: FontWeight.bold)),
          const SizedBox(height: 10),
          Text(
              "Turn your ideas into bite-sized stories that captivate readers worldwide. Join our community of writers crafting modern fiction for the digital age.",
              style: TextStyle(color: Hex<PERSON>olor('#1F1F2F'), fontSize: 14),
              maxLines: null),
        ],
      ),
    );
  }
}
