import 'package:flutter/cupertino.dart';
import 'package:UrNovel/BaseWidget/base_less_widget.dart';
import 'package:UrNovel/Util/Common/data_config_util.dart';

import '../../../../Util/Extensions/colorUtil.dart';

class BecomeAWriterNote extends BaseLessWidget {
  BecomeAWriterNote({super.key});

  @override
  Widget build(BuildContext context) {
    var itemList = DataConfigUtil.instance.contentGuidelines;
    return Container(
      margin: const EdgeInsets.only(left: 15, right: 15, bottom: 15),
      padding: const EdgeInsets.fromLTRB(15, 18, 15, 25),
      decoration: BoxDecoration(
        color: HexColor('#FFFFFF'),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Align(
            alignment: Alignment.centerLeft,
            child: Text("Content Guidelines:",
                style: TextStyle(
                    color: HexColor("#1F1F2F"),
                    fontSize: 15,
                    fontWeight: FontWeight.bold)),
          ),
          const SizedBox(height: 26),
          BecomeAWriterNoteItem(arguments: itemList[0]),
          const SizedBox(height: 26),
          BecomeAWriterNoteItem(arguments: itemList[1]),
          const SizedBox(height: 26),
          BecomeAWriterNoteItem(arguments: itemList[2]),
        ],
      ),
    );
  }
}

class BecomeAWriterNoteItem extends BaseLessWidget {
  BecomeAWriterNoteItem({super.key, required super.arguments});

  @override
  Widget build(BuildContext context) {
    List<String>? textList = arguments['text']?.split(':');
    return Align(
        alignment: Alignment.centerLeft,
        child: RichText(
          text: TextSpan(
            text: ' ${textList?.last}',
            style: TextStyle(color: HexColor("#1F1F2F"), fontSize: 12),
          ),
        ));
  }
}
