import 'package:flutter/cupertino.dart';
import 'package:UrNovel/BaseWidget/base_less_widget.dart';

import '../../../../Util/Extensions/colorUtil.dart';

class BecomeAWriterJoining extends BaseLessWidget {
  BecomeAWriterJoining({super.key});

  final List<Map<String, dynamic>> itemList = [
    {
      "icon": "assets/images/profile/becomeAWriter/icon_author_joining_one.png",
      "text": "· Step1:  Create your first story (minimum 5000 words)"
    },
    {
      "icon": "assets/images/profile/becomeAWriter/icon_author_joining_two.png",
      "text":
          "· Step2:  Submit for quick review"
    },
    {
      "icon":
          "assets/images/profile/becomeAWriter/icon_author_joining_three.png",
      "text": "· Step3:  Start publishing and earning"
    }
  ];

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        //todo:白色背景
        Positioned(
            top: 11,
            left: 15,
            right: 15,
            bottom: 0,
            child: Container(
              width: double.infinity,
              height: 100,
              decoration: BoxDecoration(
                color: HexColor("#FFFFFF"),
                borderRadius: const BorderRadius.all(Radius.circular(12)),
              ),
            )),

        Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            //todo:header
            Container(
              width: 205,
              height: 40,
              alignment: Alignment.center,
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(
                      'assets/images/profile/becomeAWriter/icon_author_section_header.png'),
                  fit: BoxFit.fill,
                ),
              ),
              child: Text("Joining Process",
                  style: TextStyle(
                      fontSize: 17,
                      color: HexColor("#FFFFFF"),
                      fontWeight: FontWeight.bold)),
            ),
            const SizedBox(height: 15),
            BecomeAWriterJoiningItem(arguments: itemList[0]),
            const SizedBox(height: 10),
            BecomeAWriterJoiningItem(arguments: itemList[1]),
            const SizedBox(height: 10),
            BecomeAWriterJoiningItem(arguments: itemList[2]),
            const SizedBox(height: 30),
          ],
        )
      ],
    );
  }
}

class BecomeAWriterJoiningItem extends BaseLessWidget {
  BecomeAWriterJoiningItem({super.key, required super.arguments});

  @override
  Widget build(BuildContext context) {
    List<String>? textList = arguments['text']?.split(':');
    return Stack(alignment: Alignment.topLeft, children: [
      Padding(
        padding: const EdgeInsets.only(left: 50),
        child: Image.asset(arguments['icon'], width: 18, height: 30),
      ),
      Padding(
          padding: const EdgeInsets.only(top: 10, left: 30, right: 30),
          child: Align(
              alignment: Alignment.centerLeft,
              child: RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: "${textList?.first}:",
                      style: TextStyle(
                          color: HexColor("#1F1F2F"),
                          fontSize: 15,
                          fontWeight: FontWeight.bold),
                    ),
                    TextSpan(
                      text: ' ${textList?.last}',
                      style:
                          TextStyle(color: HexColor("#1F1F2F"), fontSize: 12),
                    ),
                  ],
                ),
              )))
    ]);
  }
}
