import 'package:flutter/cupertino.dart';
import 'package:UrNovel/BaseWidget/base_less_widget.dart';

import '../../../../Util/Extensions/colorUtil.dart';

class BecomeAWriterChoose extends BaseLessWidget {
  BecomeAWriterChoose({super.key});

  final List<Map<String, dynamic>> itemList = [
    {
      "icon": "assets/images/profile/becomeAWriter/icon_author_global.png",
      "text": "Global Audience:   Your stories, translated and shared globally."
    },
    {
      "icon": "assets/images/profile/becomeAWriter/icon_author_attractive.png",
      "text":
          "Attractive Royalties:   Earn high royalties and maximize your earnings."
    },
    {
      "icon": "assets/images/profile/becomeAWriter/icon_author_diverse.png",
      "text":
          "Diverse Promotion Channels:   Promote your work through social media, events, and more."
    },
    {
      "icon":
          "assets/images/profile/becomeAWriter/icon_author_professional.png",
      "text":
          "Professional Editing:  Providing You with 1-on-1 Writing Guidance and Cover Design."
    }
  ];

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 15),
      decoration: const BoxDecoration(
          image: DecorationImage(
        image: AssetImage(
            'assets/images/profile/becomeAWriter/icon_author_choose.png'),
        fit: BoxFit.fill,
      )),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
              padding: const EdgeInsets.only(top: 30, left: 20),
              child: Text("Why choose us?",
                  style: TextStyle(
                      fontSize: 17,
                      color: HexColor("#FFFFFF"),
                      fontWeight: FontWeight.bold))),
          Column(mainAxisSize: MainAxisSize.min, children: [
            const SizedBox(height: 20),
            BecomeAWriterChooseItem(arguments: itemList[0]),
            const SizedBox(height: 22),
            BecomeAWriterChooseItem(arguments: itemList[1]),
            const SizedBox(height: 22),
            BecomeAWriterChooseItem(arguments: itemList[2]),
            const SizedBox(height: 22),
            BecomeAWriterChooseItem(arguments: itemList[3]),
            const SizedBox(height: 20)
          ]),
        ],
      ),
    );
  }
}

class BecomeAWriterChooseItem extends BaseLessWidget {
  BecomeAWriterChooseItem({super.key, required super.arguments});

  @override
  Widget build(BuildContext context) {
    List<String>? textList = arguments['text']?.split(':');
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(width: 13),
        SizedBox(
          height: 17,
          width: 17,
          child: Image.asset(arguments['icon']),
        ),
        Expanded(
            child: Padding(
                padding: const EdgeInsets.only(left: 12, right: 27),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: "${textList?.first}:",
                          style: TextStyle(
                              color: HexColor("#1F1F2F"),
                              fontSize: 15,
                              fontWeight: FontWeight.bold),
                        ),
                        TextSpan(
                          text: ' ${textList?.last}',
                          style: TextStyle(
                              color: HexColor("#1F1F2F"), fontSize: 12),
                        ),
                      ],
                    ),
                  ),
                )))
      ],
    );
  }
}
