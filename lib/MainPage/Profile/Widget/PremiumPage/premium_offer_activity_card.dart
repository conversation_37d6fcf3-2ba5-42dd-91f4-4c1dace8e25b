import 'package:UrNovel/Util/tools.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../Util/Extensions/colorUtil.dart';
import '../../../../Util/SheetAndAlter/bottom_sheet.dart';
import '../../Model/goods_info_model.dart';

class PremiumOfferActivityCard extends StatelessWidget {
  final GoodsListItem? goodsItem;
  final double topMargin;
  final VoidCallback? onGoodsTap;
  final VoidCallback? onRefreshGoodsList;

  const PremiumOfferActivityCard(
      {super.key,
      required this.goodsItem,
      this.topMargin = 0,
      required this.onGoodsTap,
      required this.onRefreshGoodsList});

  @override
  Widget build(BuildContext context) {
    bool isFirstCharge = goodsItem?.activityType == 'First_Charge';
    bool isDiscount = goodsItem?.activityType == 'Discount';
    bool isOtherActivity = goodsItem?.activityType == 'Best_Deal';

    return GestureDetector(
        onTap: onGoodsTap,
        child: Padding(
            padding: EdgeInsets.only(top: topMargin),
            child: SizedBox(
              width: double.infinity,
              height: 90,
              child: Stack(
                children: [
                  Positioned(
                      top: isFirstCharge
                          ? 10
                          : isDiscount
                              ? 9
                              : 5,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      child: Container(
                        height: 80,
                        margin: EdgeInsets.only(top: 0, left: 18, right: 18),
                        padding: EdgeInsets.only(top: 10),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            image: DecorationImage(
                                image: isFirstCharge
                                    ? AssetImage(
                                        'assets/images/profile/icon_premium_commend_bg.png') // 使图
                                    : AssetImage(
                                        'assets/images/profile/icon_premium_otherActivity_bg.png'),
                                fit: BoxFit.cover)),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Expanded(
                                child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                    padding: const EdgeInsets.only(
                                        top: 15, left: 15, right: 15),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.end,
                                      children: [
                                        ///有优惠价
                                        Text(
                                            '${getPremiumTitle(goodsItem?.productCode)}  ',
                                            style: TextStyle(
                                                color: HexColor('#704E1F'),
                                                fontSize: 19,
                                                fontWeight: FontWeight.bold,
                                                height: 1)),
                                        if (goodsItem?.activityPricePower ==
                                                true &&
                                            isAvailable(
                                                goodsItem?.introductoryPrice))
                                          Row(
                                            children: [
                                              Text(
                                                  "${goodsItem?.currencySymbol}${goodsItem?.introductoryPrice}",
                                                  style: TextStyle(
                                                      color:
                                                          HexColor('#DB534C'),
                                                      fontSize: 18,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      height: 1)),
                                              Text('('),
                                              Text("${goodsItem?.showPrice}",
                                                  style: TextStyle(
                                                    color: HexColor(
                                                        goodsItem?.recommend ==
                                                                1
                                                            ? '#704E1F'
                                                            : '#CFB693'),
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.bold,
                                                    decoration: TextDecoration
                                                        .lineThrough,
                                                    decorationColor:
                                                        HexColor('#704E1F'),
                                                    decorationStyle:
                                                        TextDecorationStyle
                                                            .solid,
                                                    decorationThickness: 2,
                                                  )),
                                              Text(')'),
                                            ],
                                          )
                                        else
                                          Text("${goodsItem?.showPrice}",
                                              style: TextStyle(
                                                  color: HexColor('#704E1F'),
                                                  fontSize: 18,
                                                  fontWeight: FontWeight.bold,
                                                  height: 1)),
                                      ],
                                    )),
                                Padding(
                                    padding: const EdgeInsets.only(
                                        top: 15, left: 15, right: 15),
                                    child: Text(
                                        getPremiumSubTitle(
                                            goodsItem?.numberOfPeriods,
                                            goodsItem?.productCode),
                                        style: TextStyle(
                                            color: HexColor('#704E1F'),
                                            fontSize: 12.sp,
                                            height: 1))),
                              ],
                            )),
                            Padding(
                                padding: const EdgeInsets.only(right: 13),
                                child: SizedBox(
                                    width: 80,
                                    height: 28,
                                    child: DecoratedBox(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(14),
                                        gradient: LinearGradient(
                                            colors: isFirstCharge
                                                ? [
                                                    HexColor('#8C5F32'),
                                                    HexColor('#63401C'),
                                                  ]
                                                : [
                                                    HexColor('#F3AD7F'),
                                                    HexColor('#EC6392'),
                                                  ],
                                            begin: Alignment.topCenter,
                                            end: Alignment.bottomCenter),
                                      ),
                                      child: Center(
                                        child: Text("buy".tr,
                                            style: TextStyle(
                                                color: HexColor('#FFFFFF'),
                                                fontSize: 13,
                                                fontWeight: FontWeight.bold)),
                                      ),
                                    )))
                          ],
                        ),
                      )),
                  if (isFirstCharge || isDiscount || isOtherActivity)
                    Positioned(
                        top: 0,
                        left: isFirstCharge || isDiscount ? 14 : 18,
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            if (isFirstCharge || isDiscount)
                              Image.asset(
                                  isFirstCharge
                                      ? 'assets/images/profile/purchase/icon_top_first_charge.png'
                                      : 'assets/images/profile/purchase/icon_top_discount.png',
                                  fit: BoxFit.cover,
                                  width: isFirstCharge ? 22 : 25,
                                  height: isFirstCharge ? 28 : 24),
                            Container(
                              height: 19,
                              decoration: isFirstCharge || isDiscount
                                  ? BoxDecoration(
                                      color: isFirstCharge
                                          ? HexColor('#FE6F0C')
                                          : HexColor('#404243'),
                                      borderRadius: BorderRadius.only(
                                          topRight: Radius.circular(8),
                                          bottomRight: Radius.circular(8)),
                                    )
                                  : BoxDecoration(
                                      gradient: LinearGradient(
                                          colors: [
                                            HexColor('#FF87C0'),
                                            HexColor('#FC348C'),
                                          ],
                                          begin: Alignment.topCenter,
                                          end: Alignment.bottomCenter),
                                      borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(8),
                                          topRight: Radius.circular(8),
                                          bottomRight: Radius.circular(8)),
                                    ),
                              child: Padding(
                                  padding: EdgeInsets.only(
                                      left: isOtherActivity ? 18 : 5,
                                      right: isOtherActivity ? 18 : 10),
                                  child: Align(
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                        isFirstCharge
                                            ? getPremiumSubTitle(
                                                goodsItem?.numberOfPeriods,
                                                goodsItem?.productCode,
                                                isBilled: false)
                                            : isDiscount
                                                ? '${goodsItem?.activityName}'
                                                : 'best_deal'.tr,
                                        style: TextStyle(
                                            color: HexColor(isFirstCharge
                                                ? '#FFFB18'
                                                : '#F7DCB1'),
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        textAlign: TextAlign.center),
                                  )),
                            )
                          ],
                        )),
                  if (isAvailable(goodsItem?.countDownSecond))
                    Positioned(
                        top: 5,
                        right: 19,
                        height: 18,
                        child: Container(
                            padding: EdgeInsets.only(left: 22, right: 22),
                            decoration: BoxDecoration(
                              color: HexColor('#E9AF5D'),
                              borderRadius: BorderRadius.only(
                                  bottomLeft: Radius.circular(12),
                                  topRight: Radius.circular(12)),
                            ),
                            child: CountDownCard(
                              countDownSecond: goodsItem?.countDownSecond,
                              onTimerEnd: () {
                                ///重新拉去数据
                                onRefreshGoodsList?.call();
                              },
                            ))),
                ],
              ),
            )));
  }
}
