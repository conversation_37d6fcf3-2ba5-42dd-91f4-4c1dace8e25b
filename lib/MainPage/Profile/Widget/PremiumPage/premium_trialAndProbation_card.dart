import 'package:flutter/cupertino.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/MainPage/Profile/Widget/PremiumPage/premium_trial.dart';
import 'package:UrNovel/MainPage/Profile/Widget/PremiumPage/probation_card.dart';

import '../../../../Launch&Login/Model/user_model.dart';
import '../../../../Util/Extensions/colorUtil.dart';
import '../../../../Util/deviceScreenUtil.dart';

class PremiumTrialAndProbationCard extends BaseFulWidget {
  final UserInfoModel? userInfo;

  PremiumTrialAndProbationCard({super.key, required this.userInfo});

  @override
  State<PremiumTrialAndProbationCard> createState() =>
      _PremiumTrialAndProbationCardState();
}

class _PremiumTrialAndProbationCardState
    extends State<PremiumTrialAndProbationCard> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var height = mediaQuery.size.width / 3;
    var vipLogoWidth = mediaQuery.size.width * 0.261;
    var vipLogoHeight = vipLogoWidth / 1.342;

    return Stack(
      children: [
        ClipPath(
            clipper: BottomArcClipper(), // 使用自定义剪切器
            child: Container(
              width: double.infinity,
              height: height,
              padding: const EdgeInsets.only(top: 20),
              decoration: BoxDecoration(
                gradient: LinearGradient(colors: [
                  HexColor("#1F253D"),
                  HexColor("#2A314B"),
                ], begin: Alignment.topCenter, end: Alignment.bottomCenter),
              ),
              child: widget.userInfo?.vipStatus == 1
                  ? ProbationCard(userInfo: widget.userInfo)
                  : PremiumTrial(userInfo: widget.userInfo),
            )),
        Positioned(
            right: 4,
            child: Image.asset(
                widget.userInfo?.vipStatus == 1
                    ? 'assets/images/profile/icon_premium_vip.png'
                    : 'assets/images/profile/icon_premium_unVip.png',
                width: vipLogoWidth,
                height: vipLogoHeight,
                fit: BoxFit.cover))
      ],
    );
  }
}

class BottomArcClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    var path = Path();
    path.lineTo(0, size.height - (DeviceScreenUtil.instance.bottomSafeHeight == 0 ? 30 : 20)); // 绘制左下角
    path.quadraticBezierTo(
        size.width / 2, size.height, size.width, size.height - (DeviceScreenUtil.instance.bottomSafeHeight == 0 ? 30 : 20)); // 绘制弧形
    path.lineTo(size.width, 0); // 绘制右上角
    path.close(); // 关闭路径
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) {
    return false; // 返回是否需要重新剪裁
  }
}
