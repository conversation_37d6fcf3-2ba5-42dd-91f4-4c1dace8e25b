import 'package:flutter/cupertino.dart';
import 'package:UrNovel/BaseWidget/base_less_widget.dart';
import 'package:UrNovel/MainPage/Profile/Widget/PremiumPage/premium_list_card.dart';

import '../../Model/goods_info_model.dart';

class PremiumList extends BaseLessWidget {
  final List<GoodsListItem>? goodsList;
  final Function(GoodsListItem? item)? onGoodsTap;

  PremiumList(
      {super.key, required this.goodsList, required this.onGoodsTap});

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var carWidth = (mediaQuery.size.width - 48) / 2.0;
    var carHeight = 72.0;
    int count = goodsList?.length ?? 0;
    int rowCount = (count / 2 + count % 2).floor();
    var height = (carHeight + 12) * rowCount;

    return SizedBox(
        width: double.infinity,
        height: height,
        child: Padding(
          padding: const EdgeInsets.only(top: 12.0, left: 18, right: 18),
          child: GridView.builder(
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  mainAxisSpacing: 12,
                  crossAxisSpacing: 12,
                  childAspectRatio: carWidth / carHeight),
              itemCount: count,
              itemBuilder: (context, index) {
                if (index < count) {
                  var item = goodsList?[index];
                  return PremiumListCard(
                    size: Size(carWidth, carHeight),
                    goodsItem: item,
                    onGoodsTap: (item) {
                      // TODO: 购买商品
                      onGoodsTap?.call(item);
                    },
                  );
                }

                return Container();
              }),
        ));
  }
}
