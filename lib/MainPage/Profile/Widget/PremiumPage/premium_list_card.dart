import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:UrNovel/BaseWidget/base_less_widget.dart';

import '../../../../Util/Extensions/colorUtil.dart';
import '../../../../Util/tools.dart';
import '../../Model/goods_info_model.dart';

class PremiumListCard extends BaseLessWidget {
  final Size size;
  final GoodsListItem? goodsItem;
  final Function(GoodsListItem? item)? onGoodsTap;

  PremiumListCard(
      {super.key,
      required this.size,
      required this.goodsItem,
      required this.onGoodsTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // TODO: 订阅会员
        onGoodsTap?.call(goodsItem);
      },
      child: Container(
          width: size.width,
          height: size.height,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              image: DecorationImage(
                  image: AssetImage(
                      'assets/images/profile/icon_premium_normal_bg.png'),
                  fit: BoxFit.cover)),
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Padding(
              padding: const EdgeInsets.only(top: 10, left: 15, right: 15),
              child: Text(getPremiumTitle(goodsItem?.productCode),
                  style: TextStyle(
                      color: HexColor('#704E1F'),
                      fontSize: 19,
                      fontWeight: FontWeight.bold,
                      height: 1.2)),
            ),
            Padding(
                padding: const EdgeInsets.only(top: 10, left: 15, right: 15),
                child: Text('${goodsItem?.showPrice}',
                    style: TextStyle(
                        color: HexColor('#704E1F'),
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        height: 1)))
          ])),
    );
  }
}
