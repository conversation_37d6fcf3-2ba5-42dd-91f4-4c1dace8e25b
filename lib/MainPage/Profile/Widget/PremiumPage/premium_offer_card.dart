import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:UrNovel/Util/tools.dart';

import '../../../../Util/Extensions/colorUtil.dart';
import '../../Model/goods_info_model.dart';

class PremiumOfferCard extends StatelessWidget {
  final double topMargin;
  final GoodsListItem? goodsItem;
  final VoidCallback? onGoodsTap;

  const PremiumOfferCard(
      {super.key,
      required this.topMargin,
      required this.goodsItem,
      required this.onGoodsTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onGoodsTap,
      child: Container(
        width: double.infinity,
        height: 80,
        margin: EdgeInsets.only(top: topMargin, left: 18, right: 18),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            image: DecorationImage(
                image: AssetImage(
                    'assets/images/profile/icon_premium_unCommend_bg.png'),
                fit: BoxFit.cover)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
                child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                    padding:
                        const EdgeInsets.only(top: 15, left: 15, right: 15),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        ///有优惠价
                        Text('${getPremiumTitle(goodsItem?.productCode)}  ',
                            style: TextStyle(
                                color: HexColor('#704E1F'),
                                fontSize: 19,
                                fontWeight: FontWeight.bold,
                                height: 1)),
                        if (goodsItem?.activityPricePower == true &&
                            isAvailable(goodsItem?.introductoryPrice))
                          Row(
                            children: [
                              Text(
                                  "${goodsItem?.currencySymbol}${goodsItem?.introductoryPrice}",
                                  style: TextStyle(
                                      color: HexColor('#DB534C'),
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      height: 1)),
                              Text('('),
                              Text("${goodsItem?.showPrice}",
                                  style: TextStyle(
                                    color: HexColor('#CFB693'),
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                    decoration: TextDecoration.lineThrough,
                                    decorationColor: HexColor('#704E1F'),
                                    decorationStyle: TextDecorationStyle.solid,
                                    decorationThickness: 2,
                                  )),
                              Text(')'),
                            ],
                          )
                        else
                          Text("${goodsItem?.showPrice}",
                              style: TextStyle(
                                  color: HexColor('#704E1F'),
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  height: 1)),
                      ],
                    )),
                Padding(
                    padding:
                        const EdgeInsets.only(top: 15, left: 15, right: 15),
                    child: Text(
                        getPremiumSubTitle(
                            goodsItem?.numberOfPeriods, goodsItem?.productCode),
                        style: TextStyle(
                            color: HexColor('#704E1F'),
                            fontSize: 12.sp,
                            height: 1))),
              ],
            )),
            Padding(
                padding: const EdgeInsets.only(right: 13),
                child: SizedBox(
                    width: 80,
                    height: 28,
                    child: DecoratedBox(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(14),
                        gradient: LinearGradient(
                            colors: [
                              HexColor('#E8BB75'),
                              HexColor('#E8BB75'),
                            ],
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter),
                      ),
                      child: Center(
                        child: Text("buy".tr,
                            style: TextStyle(
                                color: HexColor('#FFFFFF'),
                                fontSize: 13,
                                fontWeight: FontWeight.bold)),
                      ),
                    )))
          ],
        ),
      ),
    );
  }
}
