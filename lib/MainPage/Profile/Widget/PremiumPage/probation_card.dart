import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';

import '../../../../Launch&Login/Model/user_model.dart';
import '../../../../Util/Extensions/colorUtil.dart';
import '../../../../Util/tools.dart';

class ProbationCard extends BaseFulWidget {
  final UserInfoModel? userInfo;

  ProbationCard({super.key, required this.userInfo});

  @override
  State<ProbationCard> createState() => _ProbationCardState();
}

class _ProbationCardState extends State<ProbationCard> {
  // bool isAuto = false; //是否自动续费
  late int _days; //天数
  late String _expiresOn; //到期时间

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  getVipInfo() {
    if (isAvailable(widget.userInfo?.subExpireTime)) {
      _days = getDaysDifference(widget.userInfo!.subExpireTime!);
      if (_days < 0) {
        _days = 0;
      }
    } else {
      _days = 0;
    }

    _expiresOn = getPremiumExpireTimeStr(widget.userInfo?.subExpireTime);
  }

  @override
  Widget build(BuildContext context) {
    getVipInfo();
    var mediaQuery = MediaQuery.of(context);
    var cardWidth = mediaQuery.size.width * 0.916;
    var cardHeight = cardWidth / 3.47;

    return Container(
        height: cardHeight,
        margin: EdgeInsets.symmetric(
            horizontal: (mediaQuery.size.width - cardWidth) / 2.0),
        decoration: const BoxDecoration(
            image: DecorationImage(
                image: AssetImage(
                    'assets/images/profile/icon_premium_probation_bg.png'),
                fit: BoxFit.fill)),
        child: Padding(
            padding: EdgeInsets.only(top: 12, left: 15, right: 10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Image.asset(
                      "assets/images/profile/icon_premium_card.png",
                      width: 27,
                      height: 14,
                    ),
                    const SizedBox(width: 9),
                    Text(
                        widget.userInfo?.name ??
                            widget.userInfo?.nickName ??
                            "",
                        style: TextStyle(
                            color: HexColor("#F6D88F"),
                            fontSize: 17,
                            fontWeight: FontWeight.bold),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis)
                  ],
                ),
                Expanded(
                    child: Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                      '${'days'.trParams({
                            'param': 0 <= _days ? "$_days" : "--"
                          })} · ${'expires_on'.trParams({
                            'param': _expiresOn
                          })}',
                      style: TextStyle(
                          fontSize: 14,
                          color: HexColor("#F6D88F"),
                          fontWeight: FontWeight.bold),
                      maxLines: 3),
                )),
                const SizedBox(height: 15),
              ],
            )));
  }
}
