import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:get/get.dart';
import 'package:UrNovel/BaseWidget/base_less_widget.dart';
import 'package:UrNovel/Util/ShareManager/share_manager.dart';
import 'package:UrNovel/Util/tools.dart';

import '../../../../Util/Extensions/colorUtil.dart';
import '../../../../Util/stringUtil.dart';

class PremiumAboutSubscription extends BaseLessWidget {
  final List<TextSpan> autoSubscriptionList;
  final List<TextSpan> normalSubscriptionList;

  PremiumAboutSubscription(
      {super.key,
      required this.autoSubscriptionList,
      required this.normalSubscriptionList});

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    return Padding(
        padding: EdgeInsets.only(
            left: 14,
            right: 14,
            top: 24,
            bottom: mediaQuery.padding.bottom + 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('About Premium',
                style: TextStyle(
                    color: HexColor('#1F233B'),
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    height: 1)),
            const SizedBox(
              height: 18,
            ),
            if (isAvailable(autoSubscriptionList))
              Text.rich(
                TextSpan(
                    text: "Auto-renewable Subscriptions:\n",
                    style: TextStyle(
                        color: HexColor('#1F233B'),
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                        height: 1),
                    children: autoSubscriptionList),
              ),
            if (isAvailable(normalSubscriptionList))
              Text.rich(
                TextSpan(
                    text: "Non-renewing Subscriptions:\n",
                    style: TextStyle(
                        color: HexColor('#1F233B'),
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                        height: 1),
                    children: normalSubscriptionList),
              ),
            Text(
                "Subscription Terms:\n"
                "- Benefits will be activated within 24 hours after purchase\n"
                "- For auto-renewable subscriptions:\n"
                "- Payment will be charged to your Apple ID account at confirmation of purchase\n"
                "- Subscription automatically renews unless auto-renew is turned off at least 24-hours before the end of the current period\n"
                "- Account will be charged for renewal within 24-hours prior to the end of the current period\n\n"
                "How to Manage Subscription:\n"
                "1. Open Settings on your iOS device\n"
                "2. Tap your name at the top\n"
                "3. Tap 'Subscriptions'\n"
                "4. Find 'UrNovel'\n"
                "5. Choose 'Cancel Subscription' to end your subscription'\n",
                style: TextStyle(
                    color: HexColor('#1F233B'),
                    fontSize: 15,
                    fontWeight: FontWeight.bold,
                    height: 1)),
            Text.rich(TextSpan(children: [
              TextSpan(
                  text: "Legal Documents:\n",
                  style: TextStyle(
                      color: HexColor('#1F233B'),
                      fontSize: 15,
                      fontWeight: FontWeight.bold,
                      height: 1)),
              TextSpan(
                  text: "- Privacy Policy\n\n",
                  style: TextStyle(
                      color: HexColor('#1F233B'),
                      fontSize: 15,
                      fontWeight: FontWeight.bold,
                      height: 1,
                      decoration: TextDecoration.underline,
                      decorationColor: HexColor('#1F233B')),
                  recognizer: TapGestureRecognizer()..onTap = _privacyPolicy),
              TextSpan(
                  text: "- Terms of Service\n\n",
                  style: TextStyle(
                      color: HexColor('#1F233B'),
                      fontSize: 15,
                      fontWeight: FontWeight.bold,
                      height: 1,
                      decoration: TextDecoration.underline,
                      decorationColor: HexColor('#1F233B')),
                  recognizer: TapGestureRecognizer()..onTap = _termsOfService),
              TextSpan(
                  text: "- LICENSED APPLICATION END USER LICENSE AGREEMENT\n",
                  style: TextStyle(
                      color: HexColor('#1F233B'),
                      fontSize: 15,
                      fontWeight: FontWeight.bold,
                      height: 1,
                      decoration: TextDecoration.underline,
                      decorationColor: HexColor('#1F233B')),
                  recognizer: TapGestureRecognizer()
                    ..onTap = _toAppleServicePage),
            ])),
          ],
        ));
  }

  //Terms of Service
  Future<void> _termsOfService() async {
    await Get.toNamed('/webViewPage',
        arguments: {'title': 'terms'.tr, 'url': termsOfServiceUrl});
  }

  //Privacy Policy
  Future<void> _privacyPolicy() async {
    await Get.toNamed('/webViewPage',
        arguments: {'title': 'pricavy'.tr, 'url': privacyPolicyUrl});
  }

  //Apple Service Policy
  Future<void> _toAppleServicePage() async {
    await ShareManager.instance.launchLinkUrl(appleServicePolicyUrl);
  }
}
