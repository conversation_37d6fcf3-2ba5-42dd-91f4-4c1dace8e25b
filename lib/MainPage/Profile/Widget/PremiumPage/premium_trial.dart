import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';

import '../../../../Launch&Login/Model/user_model.dart';
import '../../../../Util/Extensions/colorUtil.dart';

class PremiumTrial extends BaseFulWidget {
  final UserInfoModel? userInfo;

  PremiumTrial({super.key, this.userInfo});

  @override
  State<PremiumTrial> createState() => _PremiumTrialState();
}

class _PremiumTrialState extends State<PremiumTrial> {
  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var cardWidth = mediaQuery.size.width * 0.916;
    var cardHeight = cardWidth / 3.47;

    return Container(
        height: cardHeight,
        margin: EdgeInsets.symmetric(
            horizontal: (mediaQuery.size.width - cardWidth) / 2.0),
        decoration: const BoxDecoration(
            image: DecorationImage(
                image: AssetImage(
                    'assets/images/profile/icon_premium_probation_bg.png'),
                fit: BoxFit.fill)),
        child: Padding(
          padding: EdgeInsets.only(top: 12, left: 15, right: 10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Image.asset(
                    "assets/images/profile/icon_premium_card.png",
                    width: 27,
                    height: 14,
                  ),
                  const SizedBox(width: 9),
                  Text("premium".tr,
                      style: TextStyle(
                          color: HexColor("#F6D88F"),
                          fontSize: 17,
                          fontWeight: FontWeight.bold),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis)
                ],
              ),
              Expanded(
                  child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  widget.userInfo?.vipStatus == 0
                      ? 'unlock_all'.tr
                      : 'renew_premium'.tr,
                  style: TextStyle(
                      fontSize: 14,
                      color: HexColor("#F6D88F"),
                      fontWeight: FontWeight.bold),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              )),
              const SizedBox(height: 10),
            ],
          ),
        ));
  }
}
