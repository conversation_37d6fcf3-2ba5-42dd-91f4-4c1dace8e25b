import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:UrNovel/BaseWidget/base_less_widget.dart';

import '../../../Launch&Login/Model/user_model.dart';
import '../../../Util/Extensions/colorUtil.dart';
import '../../../Util/tools.dart';

class ProfileCardPremiumCell extends BaseLessWidget {
  final Size size;
  final UserInfoModel? userInfoModel;

  ProfileCardPremiumCell(
      {super.key,
      required super.arguments,
      required this.size,
      required this.userInfoModel});

  @override
  Widget build(BuildContext context) {
    var subExpireDaya = isAvailable(userInfoModel?.subExpireTime)
        ? getDaysDifference(userInfoModel!.subExpireTime!)
        : 0;
    var subExpireTimeStr = isAvailable(userInfoModel?.subExpireTime)
        ? getDateWithFormat(userInfoModel!.subExpireTime!, 'MMM dd, yyyy')
        : "";

    if (subExpireDaya < 0) {
      subExpireDaya = 0;
    }

    return Stack(children: [
      Positioned(
          left: 22,
          top: 20,
          bottom: 20,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(arguments["assets"],
                  width: size.width, height: size.height, fit: BoxFit.cover),
              const SizedBox(width: 10),
              Text(
                arguments["title"],
                style: TextStyle(
                    fontSize: 17,
                    color: HexColor(
                        userInfoModel?.vipStatus == 1 ? "#F6D88F" : "#9BA0B2"),
                    fontWeight: FontWeight.bold),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          )),
      if (userInfoModel?.vipStatus == 1)
        Positioned(
            top: 17,
            bottom: 10,
            right: 17,
            child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    'days'.trParams({'param': subExpireDaya.toString()}),
                    style: TextStyle(
                        fontSize: 17,
                        color: HexColor("#F6D88F"),
                        fontWeight: FontWeight.bold),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(width: 10),
                  Text(
                    'expires_on'.trParams({'param': subExpireTimeStr}),
                    style: TextStyle(
                        fontSize: 12,
                        color: HexColor("#F6D88F"),
                        fontWeight: FontWeight.bold),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ]))
    ]);
  }
}
