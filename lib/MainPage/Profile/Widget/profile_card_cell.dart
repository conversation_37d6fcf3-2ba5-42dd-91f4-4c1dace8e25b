import 'package:flutter/cupertino.dart';
import 'package:UrNovel/BaseWidget/base_less_widget.dart';

import '../../../Util/Extensions/colorUtil.dart';
import '../../../Util/enum.dart';

class ProfileCardCell extends BaseLessWidget {
  final Size size;
  final Size iconSize;
  final ProfileCellType cellType;
  final String title;

  ProfileCardCell({
    super.key,
    required super.arguments,
    required this.size,
    this.iconSize = const Size(22, 22),
    this.cellType = ProfileCellType.none,
    this.title = "",
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            SizedBox(
              width: cellType == ProfileCellType.account ? size.width / 2.5 : size.width,
              height: size.height,
              child: Row(
                children: [
                  Image.asset(arguments["assets"],
                      width: iconSize.width, height: iconSize.height, fit: BoxFit.cover),
                  const SizedBox(width: 10),
                  Expanded(child: Text(
                    arguments["title"],
                    style: TextStyle(
                        fontSize: 17,
                        color: HexColor("#000000"),
                        fontWeight: FontWeight.bold),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  )),
                ],
              ),
            ),
            //显示右侧title
            if (cellType == ProfileCellType.account && title.isNotEmpty)
              Text(title,
                  style: TextStyle(
                      fontSize: 17,
                      color: HexColor("#555A65"),
                      fontWeight: FontWeight.bold)),
          ],
        ),
      ],
    );
  }
}
