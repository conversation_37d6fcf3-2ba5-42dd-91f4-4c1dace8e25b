import 'package:flutter/material.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/MainPage/Profile/Widget/ProfileEditPage/profile_cell.dart';

class ProfilePersonalInfo extends BaseFulWidget {
  final String subTitle;
  ProfilePersonalInfo({super.key, super.arguments, required this.subTitle});

  @override
  State<ProfilePersonalInfo> createState() => _ProfilePersonalInfoState();
}

class _ProfilePersonalInfoState extends State<ProfilePersonalInfo> {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 58,
      width: double.infinity,
      color: Colors.transparent,
      padding: const EdgeInsets.symmetric(horizontal: 19),
      child: ProfileCell(arguments: widget.arguments, subTitle: widget.subTitle),
    );
  }
}
