import 'package:flutter/cupertino.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';

import '../../../../Util/Extensions/colorUtil.dart';

class ProfileInterestsPage extends BaseFulWidget {
  ProfileInterestsPage({super.key, required super.arguments});

  @override
  State<ProfileInterestsPage> createState() => _ProfileInterestsPageState();
}

class _ProfileInterestsPageState extends State<ProfileInterestsPage> {
  late final double lineCount;

  @override
  void initState() {
    super.initState();
    lineCount = (widget.arguments.length / 2 + widget.arguments.length % 2);
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var cardWidth = (mediaQuery.size.width - 33) / 2.0;
    var cardHeight = cardWidth / 2.0;
    var height = lineCount * cardHeight + (lineCount - 1) * 11;
    return Container(
      margin: EdgeInsets.only(
          left: 11, right: 11, bottom: mediaQuery.padding.bottom + 20),
      height: height,
      child: GridView.builder(
          physics: const NeverScrollableScrollPhysics(),
          padding: EdgeInsets.zero,
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              mainAxisSpacing: 11,
              crossAxisSpacing: 11,
              childAspectRatio: 2.0),
          itemBuilder: (context, index) {
            if (index < widget.arguments.length) {
              return GestureDetector(
                onTap: () {},
                child: Container(
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: HexColor("#FFFFFF"),
                    borderRadius: const BorderRadius.all(Radius.circular(12)),
                  ),
                  child: Text(
                    widget.arguments[index]["title"],
                    style: TextStyle(
                        fontSize: 17,
                        color: HexColor("#1F1F2F"),
                        fontWeight: FontWeight.bold),
                  ),
                ),
              );
            } else {
              return null;
            }
          }),
    );
  }
}
