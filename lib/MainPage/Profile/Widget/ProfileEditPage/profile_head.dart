import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Util/Common/network_image_util.dart';
import 'package:flutter/cupertino.dart';

import '../../../../Launch&Login/Model/user_model.dart';

class ProfileHead extends BaseFulWidget {
  final UserInfoModel? infoModel;
  final String avatar;

  ProfileHead({super.key, required this.infoModel, required this.avatar});

  @override
  State<ProfileHead> createState() => ProfileHeadState();
}

class ProfileHeadState extends State<ProfileHead> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 126,
      child: Center(
          child: (widget.avatar.startsWith('http://') ||
                  widget.avatar.startsWith('https://'))
              ? NetworkImageUtil(
                  imageUrl: widget.avatar,
                  w: 68,
                  h: 68,
                  width: 80,
                  height: 80,
                  isNeedRedirect: false,
                  isCircle: true)
              : Image.asset(widget.avatar, width: 88, height: 88)),
    );
  }
}
