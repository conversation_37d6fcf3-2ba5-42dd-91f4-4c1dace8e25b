import 'dart:math' as math;

import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:country_code_picker/country_code_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../Util/Common/CommonManager/common_manager.dart';
import '../../../../../Util/Extensions/colorUtil.dart';

class PhoneEditHeader extends BaseFulWidget {
  final String? phone;
  final Function(String, String) onPhoneChanged;

  PhoneEditHeader(
      {super.key, required this.phone, required this.onPhoneChanged});

  @override
  State<PhoneEditHeader> createState() => _PhoneEditHeaderState();
}

class _PhoneEditHeaderState extends State<PhoneEditHeader> {
  var countryCodeKey = GlobalKey<CountryCodePickerState>();
  late String _code;
  late String _phone;
  final TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    super.initState();

    _code = "+1";
    _phone = "";
    if (isAvailable(widget.phone)) {
      List<String> phoneList = widget.phone!.split(" ");
      _code = phoneList.first;
      _phone = phoneList.last;
    }
    _controller.text = _phone;
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();

    _controller.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50,
      width: double.infinity,
      margin: const EdgeInsets.fromLTRB(20, 22, 20, 7),
      decoration: BoxDecoration(
        border: Border.all(
            color: ColorsUtil.hexColor(0x7E839D, alpha: 0.2), width: 1),
        borderRadius: BorderRadius.circular(12),
        color: HexColor('#FFFFFF'),
        boxShadow: [
          BoxShadow(
              color: ColorsUtil.hexColor(0x7E839D, alpha: 0.2),
              blurRadius: 12,
              offset: const Offset(0, 0))
        ], //BoxShadow
      ),
      child: Row(
        children: [
          const SizedBox(width: 18),
          SizedBox(
            width: 1,
            height: 1,
            child: CountryCodePicker(
              key: countryCodeKey,
              onChanged: (CountryCode countryCode) {
                // 打印电话区号
                if (countryCode.dialCode != null) {
                  setState(() {
                    _code = countryCode.dialCode!;
                  });
                  widget.onPhoneChanged(_code, _phone);
                }
              },
              // 初始值
              initialSelection: 'CA',
              // 取消显示国旗
              showFlag: false,
            ),
          ),
          GestureDetector(
            onTap: () {
              countryCodeKey.currentState?.showCountryCodePickerDialog();
            },
            child: Row(
              children: [
                Text(_code,
                    style: TextStyle(
                        color: HexColor('#1F1F2F'),
                        fontSize: 15,
                        fontWeight: FontWeight.bold)),
                const SizedBox(width: 10),
                Transform.rotate(
                  angle: CommonManager.instance.isReverse() ? math.pi : 0,
                  child: Image.asset(
                    'assets/images/profile/icon_arrow.png',
                    width: 11,
                    height: 7,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 18),
          Container(
            width: 1,
            height: 45,
            color: ColorsUtil.hexColor(0x7E839D, alpha: 0.2),
          ),
          const SizedBox(width: 18),
          Expanded(
              child: TextField(
            controller: _controller,
            style: TextStyle(
                color: HexColor("#1F1F2F"),
                fontSize: 15,
                fontWeight: FontWeight.bold),
            decoration: InputDecoration(
              hintText: "enter_number".tr,
              hintStyle: TextStyle(color: HexColor("#CACCCF"), fontSize: 15),
              border: InputBorder.none,
              // 取消边框及下划线
              focusedBorder: InputBorder.none,
              // 取消聚焦时的边框及下划线
              enabledBorder: InputBorder.none,
              // 取消启用时的边框及下划线
              errorBorder: InputBorder.none,
              // 取消错误时的边框及下划线
              disabledBorder: InputBorder.none, // 取消禁用时的边框及下划线
            ),
            keyboardType: TextInputType.phone,
            onChanged: (phone) {
              _phone = phone;
              widget.onPhoneChanged(_code, _phone);
            },
          )),
          const SizedBox(width: 18),
        ],
      ),
    );
  }
}
