import 'dart:math' as math;

import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:flutter/cupertino.dart';

import '../../../../Util/Common/CommonManager/common_manager.dart';
import '../../../../Util/Extensions/colorUtil.dart';

class ProfileCell extends BaseFulWidget {
  final String subTitle;

  ProfileCell({super.key, super.arguments, required this.subTitle});

  @override
  State<ProfileCell> createState() => _ProfileCellState();
}

class _ProfileCellState extends State<ProfileCell> {
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          widget.arguments["title"],
          style: TextStyle(
              fontSize: 15,
              color: HexColor("#1F1F2F"),
              fontWeight: FontWeight.bold),
        ),
        const Spacer(),
        Container(
            width: 140,
            alignment: Alignment.centerRight,
            child: Text(
              widget.subTitle,
              style: TextStyle(fontSize: 13, color: HexColor("#91969C")),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            )),
        const SizedBox(width: 11),
        Transform.rotate(
          angle: CommonManager.instance.isReverse() ? math.pi : 0,
          child: Image.asset(
            'assets/images/common/icon_arrow_gray.png',
            width: 5,
            height: 8,
          ),
        )
      ],
    );
  }
}
