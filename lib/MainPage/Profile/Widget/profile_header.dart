import 'dart:math' as math;

import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Launch&Login/Model/user_model.dart';
import 'package:UrNovel/Util/Common/network_image_util.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:flutter/material.dart';

import '../../../Util/Common/CommonManager/common_manager.dart';
import '../../../Util/Extensions/colorUtil.dart';

class ProfileHeader extends BaseFulWidget {
  final UserInfoModel? infoModel;
  final Function(String) onEditTap;

  ProfileHeader({super.key, required this.infoModel, required this.onEditTap});

  @override
  State<ProfileHeader> createState() => _ProfileHeaderState();
}

class _ProfileHeaderState extends State<ProfileHeader> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  String get avatar {
    return getUserAvatar(widget.infoModel?.avatar, widget.infoModel?.sex);
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: const EdgeInsets.fromLTRB(18, 20, 18, 18),
        child: GestureDetector(
          onTap: () {
            widget.onEditTap(avatar);
          },
          child: Container(
            color: Colors.transparent,
            child: Row(
              children: [
                if (widget.infoModel?.vipStatus == 1)
                  Stack(
                    children: [
                      Image.asset('assets/images/profile/icon_profile_king.png',
                          width: 62, height: 70),
                      Positioned(
                          left: 2,
                          right: 2,
                          bottom: 2,
                          child: NetworkImageUtil(
                            imageUrl: avatar,
                            w: 68,
                            h: 68,
                            width: 58,
                            height: 58,
                            errorHolder: getSexAvatarStr(widget.infoModel?.sex),
                            isNeedRedirect: false,
                            isCircle: true,
                          ))
                    ],
                  )
                else
                  NetworkImageUtil(
                    imageUrl: avatar,
                    width: 58,
                    height: 58,
                    errorHolder: getSexAvatarStr(widget.infoModel?.sex),
                    isNeedRedirect: false,
                    isCircle: true,
                  ),
                const SizedBox(width: 15),
                Expanded(
                    flex: 9,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                            padding: const EdgeInsets.only(top: 13),
                            child: Text(widget.infoModel?.nickName ?? "",
                                style: TextStyle(
                                    fontSize: 20,
                                    color: HexColor("#000000"),
                                    fontWeight: FontWeight.bold))),
                        Padding(
                            padding: const EdgeInsets.only(bottom: 5),
                            child: Row(
                              children: [
                                Text("NO.${widget.infoModel?.userNumber ?? ""}",
                                    style: TextStyle(
                                        fontSize: 11,
                                        color: HexColor("#787C87"))),
                                SizedBox(width: 6),
                                SizedBox(
                                  width: 25,
                                  height: 25,
                                  child: IconButton(
                                    onPressed: () {
                                      copyToClipboard(
                                          widget.infoModel?.userNumber ?? "");
                                    },
                                    icon: Image.asset(
                                      'assets/images/profile/icon_copy.png',
                                      width: 10,
                                      height: 10,
                                    ),
                                    style: IconButton.styleFrom(
                                        padding: EdgeInsets.zero),
                                  ),
                                )
                              ],
                            ))
                      ],
                    )),
                Expanded(
                    flex: 1,
                    child: Transform.rotate(
                      angle: CommonManager.instance.isReverse() ? math.pi : 0,
                      child: Image.asset(
                        'assets/images/profile/icon_arrow.png',
                        width: 20,
                        height: 20,
                      ),
                    ))
              ],
            ),
          ),
        ));
  }
}
