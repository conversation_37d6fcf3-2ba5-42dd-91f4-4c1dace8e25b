import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:in_app_purchase_storekit/store_kit_wrappers.dart';
import 'package:UrNovel/Util/Common/Model/normal_model.dart';

import '../../../Util/NetWorkManager/net_work_manager.dart';
import '../../../Util/PaymentManager/payment_manager.dart';
import '../../../Util/SheetAndAlter/toast.dart';
import '../../../Util/api_config.dart';
import '../../../Util/enum.dart';
import '../../../Util/logUtil.dart';
import '../../../Util/tools.dart';
import '../Model/goods_info_model.dart';
import '../Model/order_validate_model.dart';

class ProfileViewModel {
  //TODO: 个人信息编辑
  static Future<NormalModel?> editProfile(Map<String, dynamic> param) async {
    // TODO: Implement fetchProfile method
    var response =
    await NetWorkManager.instance.post(apiEditUserInfo, parameters: param);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      NormalModel model = NormalModel.fromJson(response.data);

      return model;
    }

    return null;
  }

  // TODO: 获取商品列表
  static Future<List<GoodsListItem>?> getGoodList(GoodsType goodsType) async {
    // TODO: Implement fetchProfile method
    var parameters = {
      //商品类型(1:购买金币；2:购买会员；3:购买钻石)
      "goodsType": goodsType == GoodsType.purchaseCoins
          ? 1
          : goodsType == GoodsType.purchaseDiamonds
          ? 3
          : 2,
      "isFirst": true
    };
    var response = await NetWorkManager.instance
        .post(apiGetGoodsList, parameters: parameters);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      GoodsInfoModel goodsInfoModel = GoodsInfoModel.fromJson(response.data);

      List<GoodsListItem>? result = goodsInfoModel.result;
      if (isAvailable(result)) {
        return await getStoreProductDetails(result);
      } else {
        if (isAvailable(goodsInfoModel.msg)) {
          showToast(goodsInfoModel.msg!);
        }
      }
    }

    return null;
  }

  // TODO: 从商店获取商品详细信息
  static Future<List<GoodsListItem>?> getStoreProductDetails(
      List<GoodsListItem>? list) async {
    List<String> kIds = [];
    list?.forEach((e) {
      if (isAvailable(e.appStorePid)) {
        kIds.add(e.appStorePid!);
      }
    });

    await PaymentManager.instance.getProductDetails(kIds);

    List<GoodsListItem> result = [];
    list?.forEach((e) {
      if (isAvailable(e.appStorePid)) {
        try {
          ProductDetails? productDetails =
          PaymentManager.instance.getProduct(e.appStorePid!);
          if (isAvailable(productDetails)) {
            e.showPrice = productDetails!.price;
            e.rawPrice = productDetails.rawPrice;
            e.currencyCode = productDetails.currencyCode;
            e.currencySymbol = productDetails.currencySymbol;
            // 提取 SKProduct 对象
            if (productDetails is AppStoreProductDetails) {
              SKProductWrapper skProduct = productDetails.skProduct;
              e.introductoryPrice = skProduct.introductoryPrice?.price;
              e.numberOfPeriods = skProduct.introductoryPrice?.numberOfPeriods;
              e.numberOfUnits =
                  skProduct.introductoryPrice?.subscriptionPeriod.numberOfUnits;
            }

            result.add(e);
          }
        } catch (e) {
          logP("获取商店商品价格失败: $e");
        }
      }
    });

    return result;
  }

  // TODO: 创建订单
  static Future<NormalModel?> createShopOrder(
      GoodsListItem item,
      PaymentGateway paymentGateway,
      GoodsType goodsType,
      int? bookId,
      int? chapterId,
      OrderType orderType) async {
    // TODO: Implement fetchProfile method

    int goodsTypeValue = 0;
    switch (goodsType) {
      case GoodsType.subscriptionMembership:
        goodsTypeValue = 1211;
        break;
      case GoodsType.purchaseMembership:
        goodsTypeValue = 1212;
        break;
      case GoodsType.purchaseCoins:
        goodsTypeValue = 1251;
        break;
      case GoodsType.purchaseDiamonds:
        goodsTypeValue = 1249;
      default:
        break;
    }

    int orderTypeValue = 0;
    switch (orderType) {
      case OrderType.novelReadDownLoad:
        orderTypeValue = 1;
        break;
      case OrderType.personalCenter:
        orderTypeValue = 2;
        break;
      case OrderType.novelDetail:
        orderTypeValue = 3;
        break;
      case OrderType.novelReadTrialUnlock:
        orderTypeValue = 4;
        break;
      case OrderType.novelChat:
        orderTypeValue = 5;
        break;
    }

    var parameters = {
      "skuId": item.skuId,
      //支付网关(1:GOOGLE PAY; 2:APPLE PAY)
      "signGateway": paymentGateway == PaymentGateway.googlePay ? 1 : 2,
      //支付模式的id(先不管)
      "paymentModelId": 0,
      //业务类型(1251:购买金币；1212:购买会员；1211:订阅会员; 1249:购买钻石)
      "dataType": goodsTypeValue,
      "orderType": orderTypeValue,
      //订单创建类型(1:小说阅读、小说下载时创建订单 2:个人中心、首页创建订单, 3:小说详情页创建订单)
      "bookId": bookId,
      //bookId，orderType=1时，传入创建订单的bookId
      "chapterId": chapterId,
      //章节id，来自阅读页时，传入解锁的章节id
    };
    var response = await NetWorkManager.instance
        .post(apiCreateShopOrder, parameters: parameters);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      NormalModel model = NormalModel.fromJson(response.data);

      return model;
    }

    return null;
  }

  // TODO: 订单验签
  static Future<OrderValidateResultModel?> shopOrderValidate(
      String? token,
      String? thirdOrderId,
      String? productId) async {
    // TODO: Implement fetchProfile method
    var parameters = {
      //套餐购买token:购买成功后Purchase对象的getPurchaseToken()
      "token": token,
      //三方订单号
      "thirdOrderId": thirdOrderId,
      //三方商品id
      "productId": productId
    };
    var response = await NetWorkManager.instance
        .post(apiShopOrderValidateV2, parameters: parameters);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      OrderValidateModel model = OrderValidateModel.fromJson(response.data);

      return model.result;
    } else {
      if (isAvailable(response.data["msg"])) {
        showToast(response.data["msg"]);
      }
    }

    return null;
  }

  // TODO: 获取用户信息
  static Future<void> saveFireBaseToken(String token) async {
    // TODO: Implement fetchProfile method
    var response = await NetWorkManager.instance
        .get(apiSaveFireBaseToken, params: {"firebaseToken": token});
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      NormalModel model = NormalModel.fromJson(response.data);
      if (model.code == 200) {
        logP('FireBase FCM token 收集成功');
      } else {
        logP(model.msg);
      }
    } else {
      if (isAvailable(response.data["msg"])) {
        logP(response.data["msg"]);
      }
    }
  }

  // TODO: 收集af数据
  static Future<void> saveAFMessage(String? afJsonStr) async {
    // TODO: Implement fetchProfile method
    var parameters = {"jsonString": afJsonStr};
    var response = await NetWorkManager.instance
        .post(apiSaveAFMessage, parameters: parameters);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      NormalModel model = NormalModel.fromJson(response.data);
      if (model.code == 200) {
        logP('af数据收集成功');
      } else {
        logP(model.msg);
      }
    } else {
      if (isAvailable(response.data["msg"])) {
        logP(response.data["msg"]);
      }
    }
  }
}
