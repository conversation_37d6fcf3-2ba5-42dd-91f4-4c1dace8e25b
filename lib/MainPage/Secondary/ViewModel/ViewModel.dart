import '../../../Util/NetWorkManager/net_work_manager.dart';
import '../../../Util/api_config.dart';
import '../Model/secondary_list_model.dart';

class SecondaryViewModel{
  /// 分类&标签二级
  static Future<List<SecondaryListItem>?> getGenresAndTagListData(String urlPath, Map<String, dynamic> parameters) async {
    var response = await NetWorkManager.instance
        .post(urlPath, parameters: parameters);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      SecondaryListModel secondaryListModel = SecondaryListModel.fromJson(response.data);

      return secondaryListModel.result;
    }

    return null;
  }
}