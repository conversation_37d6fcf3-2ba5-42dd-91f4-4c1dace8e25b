import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/MainPage/Read/ViewModel/ViewModel.dart';
import 'package:UrNovel/MainPage/Secondary/ViewModel/ViewModel.dart';
import 'package:UrNovel/Util/Common/Animation/lottie_animation_view.dart';
import 'package:UrNovel/Util/api_config.dart';
import 'package:flutter/material.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../../../../../Util/Extensions/colorUtil.dart';
import '../../../Util/Common/no_load_view.dart';
import '../../../Util/LocalNotificationManager/local_notification_manager.dart';
import '../../../Util/RefreshLoad/refresh_load.dart';
import '../../../Util/enum.dart';
import '../../../Util/tools.dart';
import '../../BookInfo/View/BookPersonal/personal_novel_cell.dart';
import '../../Read/Model/ForYou/home_read_model.dart';
import '../Model/secondary_list_model.dart';

class SecondaryPage extends BaseFulWidget {
  SecondaryPage({super.key, required super.arguments});

  @override
  State<SecondaryPage> createState() => _SecondaryPageState();
}

class _SecondaryPageState extends State<SecondaryPage> {
  dynamic code;
  late List<dynamic>? bookList;
  late SecondaryPageType pageType;
  int pageIndex = 1; //page默认从1开始
  int pageSize = 10;
  late bool isLoading; //判断是否加载中

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    bookList = [];
    code = widget.arguments['code'];
    pageType = widget.arguments['pageType'];
    isLoading = true;

    onRefresh();
  }

  Future<void> onRefresh() async {
    // TODO: 刷新数据
    pageIndex = 1;
    if (pageType == SecondaryPageType.formHomeReadSection) {
      await getHomeSectionListData(true);
    } else if (pageType == SecondaryPageType.fromGenres) {
      await getGenresAndTagsListData(true, true);
    } else if (pageType == SecondaryPageType.fromTags) {
      await getGenresAndTagsListData(false, true);
    }

    if (isLoading) {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  Future<void> onLoading() async {
    // TODO: 加载更多数据
    pageIndex++;
    if (pageType == SecondaryPageType.formHomeReadSection) {
      await getHomeSectionListData(false);
    } else if (pageType == SecondaryPageType.fromGenres) {
      await getGenresAndTagsListData(true, false);
    } else if (pageType == SecondaryPageType.fromTags) {
      await getGenresAndTagsListData(false, false);
    }
  }

  ///首页二级
  Future getHomeSectionListData(bool isRefresh) async {
    List<HomeReadBookListModel>? list = await HomeReadViewModel.loadBookData(
        homeDataColumnCode: code, page: pageIndex, pageSize: pageSize, secondaryList: true);
    if (isAvailable(list)) {
      HomeReadBookListModel? bookListModel = list?.first;
      if (isAvailable(bookListModel?.bookList)) {
        if (isRefresh) {
          bookList = bookListModel!.bookList;
        } else {
          bookList?.addAll(bookListModel!.bookList!);
        }
      }
    }

    if (mounted) {
      setState(() {});
    }

    widget.dealRefreshState(pageIndex, pageSize, bookList);
    await widget.dealExposureList(bookList, 6);
  }

  ///分类&标签二级
  Future getGenresAndTagsListData(bool isGenres, bool isRefresh) async {
    var parameters = {"page": pageIndex, "pageSize": pageSize, "id": code, "offset": 0};
    List<SecondaryListItem>? list = await SecondaryViewModel.getGenresAndTagListData(
        isGenres ? apiGetBookByCategory : apiGetBookByTag, parameters);
    if (isAvailable(list)) {
      if (isRefresh) {
        bookList = list;
      } else {
        bookList?.addAll(list!);
      }
      if (mounted) {
        setState(() {});
      }
    }

    widget.dealRefreshState(pageIndex, pageSize, bookList);
    await widget.dealExposureList(bookList, 6);
  }

  @override
  Widget build(BuildContext context) {
    var count = bookList?.length ?? 0;

    return Scaffold(
      backgroundColor: HexColor('#FFFFFF'),
      appBar: AppBar(
        backgroundColor: HexColor('#FFFFFF'),
        title: Text(widget.arguments['title']),
      ),
      body: Stack(
        children: [
          if (!isLoading)
            if (0 < count)
              MediaQuery.removePadding(
                  context: context,
                  removeTop: true,
                  removeLeft: true,
                  removeRight: true,
                  removeBottom: true,
                  child: NotificationListener(
                    onNotification: (ScrollNotification notification) {
                      if (notification is ScrollStartNotification) {
                        widget.exposureList.removeRange(0, widget.exposureList.length);
                      } else if (notification is ScrollEndNotification) {
                        widget.exposureReport();
                      }
                      return true;
                    },
                    child: RefreshLoadUtil(
                      onRefresh: onRefresh,
                      onLoading: onLoading,
                      controller: widget.refreshController,
                      child: ListView.builder(
                          itemCount: count,
                          itemBuilder: (context, index) {
                            if (index < count) {
                              var item = bookList?[index];
                              int? bookId;
                              if (item is HomeReadBookItem) {
                                bookId = item.id;
                              } else if (item is SecondaryListItem) {
                                bookId = item.bookId;
                              }
                              return VisibilityDetector(
                                  key: Key('myList_$index'),
                                  onVisibilityChanged: (visibilityInfo) {
                                    if (0.8 <= visibilityInfo.visibleFraction) {
                                      if (bookId is int) {
                                        widget.exposureList.add(bookId);
                                      }
                                    }
                                  },
                                  child: GestureDetector(
                                    onTap: () async {
                                      //todo:去往阅读页
                                      await LocalNotificationManager.toNovelReadPage(
                                          {'bookId': bookId, 'jumpType': BookDetailJumpType.other});
                                    },
                                    child: Container(
                                      margin: const EdgeInsets.only(left: 16, right: 16),
                                      padding: EdgeInsets.only(
                                          top: index == 0 ? 22 : 18,
                                          bottom: index == count - 1 ? 15 : 0),
                                      decoration: BoxDecoration(
                                        borderRadius: index == 2
                                            ? const BorderRadius.only(
                                                topLeft: Radius.circular(12),
                                                topRight: Radius.circular(12))
                                            : index == 8
                                                ? const BorderRadius.only(
                                                    bottomLeft: Radius.circular(12),
                                                    bottomRight: Radius.circular(12))
                                                : null,
                                      ),
                                      child: PersonalNovelCell(bookItem: item),
                                    ),
                                  ));
                            } else {
                              return Container();
                            }
                          }),
                    ),
                  ))
            else
              NoLoadView()
          else
            const LottieAnimationView()
        ],
      ),
    );
  }
}
