import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Util/Common/Animation/lottie_animation_view.dart';
import 'package:UrNovel/Util/genericUtil.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

import '../../../Util/Extensions/colorUtil.dart';
import '../../../Util/RefreshLoad/refresh_load.dart';
import '../../../Util/SheetAndAlter/alter.dart';
import '../../../Util/StatusManagement/status_management.dart';
import '../../../Util/enum.dart';
import '../../BookInfo/Model/book_comments_model.dart';
import '../../BookInfo/View/BookDetail/BookDetailComments/book_comments_cell.dart';
import '../../BookInfo/ViewModel/ViewModel.dart';

class CommentListPage extends BaseFulWidget {
  CommentListPage({super.key, required super.arguments});

  @override
  State<CommentListPage> createState() => _RatingListPageState();
}

class _RatingListPageState extends State<CommentListPage> {
  late int? _bookId;
  late VoidCallback? _onBackPressed;
  late int _pageIndex; //page默认从1开始
  late int _pageSize;
  late bool _isLoading;
  late dynamic _curCommentItem;
  late List<BookCommentsItem>? _commentsList;
  final CommonStatusController _statusController = findGetXInstance(CommonStatusController());
  final FocusNode _focusNode = FocusNode();
  final TextEditingController _textController = TextEditingController();
  final ItemScrollController _scrollController = ItemScrollController();
  final ScrollOffsetController _offsetController = ScrollOffsetController();
  final ItemPositionsListener _itemPositionsListener = ItemPositionsListener.create();
  late dynamic _item;

  @override
  void initState() {
    super.initState();

    _bookId = widget.arguments['bookId'];
    _curCommentItem = widget.arguments['curCommentItem'];
    _commentsList = widget.arguments['commendList'];
    _onBackPressed = widget.arguments['onBackPressed'];
    _pageIndex = widget.arguments['pageIndex'] ?? 1; //page默认从1开始
    _pageSize = 10;
    _isLoading = false;
    _item = null;

    fetchData();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (isAvailable(_curCommentItem)) {
        Future.delayed(Duration(milliseconds: 300), () {
          _commentLocation(null, _curCommentItem);
        });
      }
    });
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    _statusController.dispose();
    super.dispose();
  }

  Future<void> fetchData() async {
    if (!isAvailable(_commentsList)) {
      _isLoading = true;
      await onRefresh();
    }
  }

  // TODO: 刷新数据
  Future<void> onRefresh() async {
    _pageIndex = 1;
    loadData(true);
  }

  // TODO: 加载更多数据
  Future<void> onLoading() async {
    _pageIndex++;
    loadData(false);
  }

  //获取详情数据
  Future<void> loadData(bool isRefresh) async {
    BookCommentsRatingModel? model = await getCommentList();
    if (isRefresh) {
      _commentsList = model?.list as List<BookCommentsItem>;
    } else {
      if (isAvailable(model?.list)) {
        _commentsList?.addAll(model?.list as Iterable<BookCommentsItem>);
      }
    }
    // 处理刷新状态
    widget.dealRefreshState(_pageIndex, _pageSize, _commentsList);

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  //评论定位
  void _commentLocation(int? index, dynamic item) {
    if (!isAvailable(item?.id) || !isAvailable(_commentsList)) return;

    _item = item;
    _statusController.setReply(true);
    _statusController.setReplyName(item.name);

    if (isAvailable(index)) {
      showKeyBoard();
      _scrollController.scrollTo(index: index!, duration: Duration(seconds: 1));
    } else {
      if (item is BookCommentsItem) {
        for (int i = 0; i < _commentsList!.length; i++) {
          if (_commentsList?[i].id == item.id) {
            showKeyBoard();
            _scrollController.scrollTo(index: i, duration: Duration(seconds: 1));
            break;
          }
        }
      }
    }
  }

  //todo:获取评论、评分信息
  Future<BookCommentsRatingModel?> getCommentList() async {
    var parameters = {"bookId": _bookId, "page": _pageIndex, "size": _pageSize};
    return await BookInfoViewModel.getCommentListV2(parameters);
  }

  // TODO: 评分评论页面
  Future<void> toRatingCommentPage(double? score, String? content, int? id, bool isEdit) async {
    await Get.toNamed('/ratingCommentPage', arguments: {
      "bookId": _bookId,
      "score": score,
      "content": content,
      "id": id,
      "onBackPressed": onRefresh
    });
  }

  ///删除评论
  Future<void> commentDelete(int? id) async {
    // TODO: implement
    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    Map<String, dynamic> parameters = {
      "commentId": id,
    };
    bool isEdited = await BookInfoViewModel.commentDelete(parameters);
    if (isEdited) {
      await loadData(true);
    } else {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  ///回复评论
  Future<void> commentReply() async {
    _statusController.setReply(false);
    removeKeyBoard();
    int? commentId;
    int? bookId;
    String? parentCommentId;
    String? receivedReplyUserId;
    String? receivedReplyUserName;
    String? content = _textController.text.trim();
    if (!isAvailable(content)) {
      return;
    }

    if (_item is BookCommentsItem) {
      BookCommentsItem item = _item;
      commentId = item.id;
      bookId = item.bookId;
      parentCommentId = '${item.id}';
      receivedReplyUserId = '${item.uid}';
      receivedReplyUserName = '${item.name}';
    } else if (_item is CommentsV2DtoItem) {
      CommentsV2DtoItem item = _item;
      commentId = item.id;
      bookId = item.bookId;
      parentCommentId = item.parentCommentId;
      receivedReplyUserId = '${item.uid}';
      receivedReplyUserName = '${item.name}';
    }
    Map<String, dynamic> parameters = {
      "commentId": commentId,
      "bookId": bookId,
      "score": 0,
      "content": content,
      "parentCommentId": parentCommentId,
      "receivedReplyUserId": receivedReplyUserId,
      "receivedReplyUserName": receivedReplyUserName
    };
    bool isAdd = await BookInfoViewModel.commentAdd(parameters);
    if (isAdd) {
      _textController.text = '';
      await loadData(true);
    } else {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void showKeyBoard() {
    if (_focusNode.hasFocus) return;

    _focusNode.requestFocus();
  }

  void removeKeyBoard() {
    _focusNode.unfocus();
  }

  @override
  Widget build(BuildContext context) {
    var count = _commentsList?.length ?? 0;
    return PopScope(
        onPopInvokedWithResult: (didPop, result) {
          _onBackPressed?.call();
        },
        child: Scaffold(
          appBar: AppBar(
            title: Text('comments'.tr),
          ),
          body: Stack(
            children: [
              MediaQuery.removePadding(
                context: context,
                removeTop: true,
                removeLeft: true,
                removeRight: true,
                removeBottom: true,
                child: RefreshLoadUtil(
                  onRefresh: onRefresh,
                  onLoading: onLoading,
                  controller: widget.refreshController,
                  child: ScrollablePositionedList.builder(
                      itemCount: count,
                      itemScrollController: _scrollController,
                      scrollOffsetController: _offsetController,
                      itemPositionsListener: _itemPositionsListener,
                      itemBuilder: (context, index) {
                        var item = _commentsList?[index];
                        return Container(
                          margin: EdgeInsets.fromLTRB(16, 0, 16, 20),
                          decoration: BoxDecoration(
                            color: HexColor('#FFFFFF'),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: BookCommentsCell(
                              item: item,
                              isSecondaryCommentList: true,
                              onItemTap: (index, commendId) {
                                if (index == 0) {
                                  //todo:编辑评论
                                  toRatingCommentPage(item?.score, item?.content, item?.id, true);
                                } else if (index == 1) {
                                  //todo:删除评论
                                  commentDelete(commendId);
                                } else if (index == 2) {
                                  //todo:举报评论
                                  showAlter(ReportErrorContent(
                                    reportType: ReportType.commentSecond,
                                    onConfirm: (content) async {
                                      //todo:举报评论接口
                                      await errorReport(ReportType.commentSecond, content,
                                          commentId: item?.id, bookId: item?.bookId);
                                    },
                                  ));
                                }
                              },
                              onReplyTap: (item) {
                                _commentLocation(index, item);
                              }),
                        );
                      }),
                ),
              ),
              Obx(() {
                if (_statusController.isReply.value) {
                  return Positioned(
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: DecoratedBox(
                      decoration: BoxDecoration(
                        color: HexColor("#FFFFFF"),
                      ),
                      child: Row(
                        children: [
                          const SizedBox(width: 15),
                          Expanded(
                            child: DecoratedBox(
                              decoration: BoxDecoration(
                                color: HexColor("#ECEDF1"),
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(vertical: 10),
                                child: CupertinoTextField(
                                  controller: _textController,
                                  focusNode: _focusNode,
                                  textInputAction: TextInputAction.newline,
                                  maxLines: null,
                                  // 支持无限行，自动换行
                                  placeholder: '${'reply'.tr} ${_statusController.replyName.value}',
                                  prefix: const SizedBox.shrink(),
                                  // 移除前面的图标
                                  padding: const EdgeInsetsDirectional.fromSTEB(10, 0, 8, 0),
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                      width: 0, // 设置边框宽度为0
                                      color: Colors.transparent, // 设置边框颜色为透明
                                    ),
                                  ),
                                  keyboardType: TextInputType.multiline,
                                  // 确保键盘类型正确
                                  suffix: Padding(
                                    padding: const EdgeInsets.only(right: 10),
                                    child: const Icon(Icons.clear, size: 16),
                                  ),
                                  suffixMode: OverlayVisibilityMode.editing,
                                ),
                              ),
                            ),
                          ),
                          TextButton(
                            onPressed: () async {
                              await commentReply();
                            },
                            style: ButtonStyle(
                              padding: WidgetStateProperty.all(EdgeInsets.zero),
                            ),
                            child: Text("post".tr,
                                style: TextStyle(
                                    color: HexColor("#555A65"),
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold)),
                          ),
                          const SizedBox(width: 13),
                        ],
                      ),
                    ),
                  );
                } else {
                  return SizedBox.shrink();
                }
              }),
              if (_isLoading) const LottieAnimationView()
            ],
          ),
        ));
  }
}
