import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../BaseWidget/base_ful_widget.dart';
import '../../../Launch&Login/Model/user_model.dart';
import '../../../Util/Common/network_image_util.dart';
import '../../../Util/DataReportManager/event_name_config.dart';
import '../../../Util/DataReportManager/event_report_manager.dart';
import '../../../Util/Extensions/colorUtil.dart';
import '../../../Util/SharedPreferences/shared_preferences.dart';
import '../../../Util/tools.dart';
import '../../BookInfo/Model/book_comments_model.dart';

class CommentReplyCell extends BaseFulWidget {
  final CommentsV2DtoItem? item;
  final bool isNeedDivider;
  final Function(int, int?) onItemTap;
  final Function(CommentsV2DtoItem? item)? onReplyTap;

  CommentReplyCell(
      {super.key,
      required this.item,
      this.isNeedDivider = false,
      required this.onItemTap,
      this.onReplyTap});

  @override
  State<CommentReplyCell> createState() => _CommentReplyCellState();
}

class _CommentReplyCellState extends State<CommentReplyCell> {
  UserInfoModel? _userInfoModel;
  late bool _isMyComment;
  late bool _isAuthor;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    _isMyComment = false;
    _isAuthor = false;

    getUserInfo();
  }

  Future<void> getUserInfo() async {
    _userInfoModel = await SpUtil.spGetUserInfo();
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    _isMyComment = _userInfoModel?.uid == widget.item?.uid;
    _isAuthor = widget.item?.authorReply == true;
    return Column(
      children: [
        //todo:跟人信息相关
        Padding(
          padding: const EdgeInsets.only(top: 13, left: 15, right: 15),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () async {
                          //todo:点击去往作者页
                          EventReportManager.eventReportOfFirebase(openAuthor);
                          await Get.toNamed('/bookAuthorPage', arguments: {"authorId": 'authorId'});
                        },
                        behavior: HitTestBehavior.opaque,
                        child: SizedBox(
                          width: mediaQuery.size.width - 32 - 30 - 100,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              //头像
                              SizedBox(
                                width: _isAuthor ? 24 : 21,
                                height: _isAuthor ? 24 : 21,
                                child: Stack(
                                  children: [
                                    Positioned(
                                      top: 0,
                                      left: 0,
                                      child: NetworkImageUtil(
                                        imageUrl: widget.item?.avatar,
                                        width: 25,
                                        height: 25,
                                        w: 32,
                                        h: 32,
                                        placeholder: getSexAvatarStr(_userInfoModel?.sex),
                                        errorHolder: getSexAvatarStr(_userInfoModel?.sex),
                                        isCircle: true,
                                      ),
                                    ),
                                    if (_isAuthor)
                                      Positioned(
                                        bottom: 0,
                                        right: 0,
                                        child: Image.asset(
                                          'assets/images/bookDetails/ratingCommend/icon_commend_author.png',
                                          width: 12,
                                          height: 12,
                                        ),
                                      )
                                  ],
                                ),
                              ),
                              const SizedBox(width: 6),
                              Flexible(
                                child: Text(
                                  widget.item?.name ?? "",
                                  style: TextStyle(
                                      fontSize: 14,
                                      color: _isAuthor ? HexColor('#FF7A22') : HexColor('#555A65'),
                                      height: 1),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 40),
              GestureDetector(
                  onTap: () {
                    widget.onItemTap(_isMyComment ? 1 : 2, widget.item?.id);
                  },
                  behavior: HitTestBehavior.opaque,
                  child: Image.asset(
                    _isMyComment
                        ? 'assets/images/bookDetails/ratingCommend/icon_delete.png'
                        : 'assets/images/bookDetails/ratingCommend/icon_report.png',
                    width: 14,
                    height: 13,
                  ))
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(left: 31),
          child: Column(
            children: [
              //todo:评论
              Container(
                padding: const EdgeInsets.only(left: 15, right: 15, top: 10),
                alignment: Alignment.centerLeft,
                child: RichText(
                    text: TextSpan(
                        text: "${"reply".tr} ${widget.item?.receivedReplyUserName}:  ",
                        style: TextStyle(
                            fontSize: 14, color: HexColor('#555A65'), fontWeight: FontWeight.bold),
                        children: [
                      TextSpan(
                        text: widget.item?.content ?? "",
                        style: TextStyle(
                            fontSize: 15,
                            color: HexColor('#000000'),
                            fontWeight: FontWeight.bold,
                            height: 1.5),
                      )
                    ])),
              ),
              //todo:评论&编辑操作
              Padding(
                  padding: const EdgeInsets.only(top: 15, left: 15, bottom: 16, right: 16),
                  //如果是别人的就是187
                  child: Row(
                    children: [
                      Text(getTimeDifference(widget.item!.addTime ?? 0),
                          style: TextStyle(
                              fontSize: 12,
                              color: HexColor('#555A65'),
                              fontWeight: FontWeight.bold)),
                      if (_isMyComment) const SizedBox(width: 40),
                      const Spacer(),
                      GestureDetector(
                          onTap: () {
                            widget.onReplyTap?.call(widget.item);
                          },
                          behavior: HitTestBehavior.opaque,
                          child: Row(
                            children: [
                              Image.asset(
                                'assets/images/bookDetails/ratingCommend/icon_reply.png',
                                width: 11,
                                height: 10,
                              ),
                              const SizedBox(width: 9),
                              Text('reply'.tr,
                                  style: TextStyle(
                                      fontSize: 12,
                                      color: HexColor('#555A65'),
                                      fontWeight: FontWeight.bold)),
                            ],
                          )),
                    ],
                  )),
              //todo:分割线
              if (widget.isNeedDivider)
                Divider(
                  height: 0.5,
                  indent: 10,
                  endIndent: 15,
                  color: HexColor('#D0D0D0'),
                ),
            ],
          ),
        )
      ],
    );
  }
}
