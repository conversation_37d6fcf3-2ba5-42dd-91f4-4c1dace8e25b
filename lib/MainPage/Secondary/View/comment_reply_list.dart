import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:flutter/cupertino.dart';

import '../../BookInfo/Model/book_comments_model.dart';
import 'comment_reply_cell.dart';

class CommentReplyList extends BaseFulWidget {
  final BookCommentsItem? item;
  final Function(int, int?) onItemTap;
  final Function(CommentsV2DtoItem? item)? onReplyTap;

  CommentReplyList({super.key, required this.item, required this.onItemTap, this.onReplyTap});

  @override
  State<CommentReplyList> createState() => _CommentReplyListState();
}

class _CommentReplyListState extends State<CommentReplyList> {
  @override
  Widget build(BuildContext context) {
    var count = widget.item?.commentsV2DtoList?.length ?? 0;
    return 0 < count
        ? Padding(
            padding: const EdgeInsets.only(left: 32),
            child: SizedBox(
              width: double.infinity,
              child: ListView.builder(
                shrinkWrap: true, // 允许ListView自适应内容高度
                physics: NeverScrollableScrollPhysics(), // 禁止滑动
                itemCount: count,
                itemBuilder: (context, index) {
                  if (index < count) {
                    CommentsV2DtoItem? item = widget.item?.commentsV2DtoList?[index];
                    return CommentReplyCell(
                      item: item,
                      isNeedDivider: index != count - 1,
                      onItemTap: widget.onItemTap,
                      onReplyTap: widget.onReplyTap,
                    );
                  }

                  return SizedBox.shrink();
                },
              ),
            ),
          )
        : SizedBox.shrink();
  }
}
