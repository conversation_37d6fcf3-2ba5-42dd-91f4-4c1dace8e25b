import 'package:json_annotation/json_annotation.dart';

part 'secondary_list_model.g.dart';

@JsonSerializable()
class SecondaryListModel {
  int? code;
  List<SecondaryListItem>? result;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  SecondaryListModel(
      {this.code, this.result, this.msg, this.sysAt, this.cost, this.traceId});

  factory SecondaryListModel.fromJson(Map<String, dynamic> json) =>
      _$SecondaryListModelFromJson(json);

  Map<String, dynamic> toJson() => _$SecondaryListModelToJson(this);
}

@JsonSerializable()
class SecondaryListItem {
  int? bookId; //书籍id
  int? bookLangId; //书籍语言id
  int? authorId; //作者id
  String? authorName; //作者名称
  String? title; //标题
  String? cover; //封面
  int? viewCount; //阅读数量
  bool? library; // 是否加入书架

  SecondaryListItem(
      {this.bookId,
      this.bookLangId,
      this.authorId,
      this.authorName,
      this.title,
      this.cover,
      this.viewCount,
      this.library});

  factory SecondaryListItem.fromJson(Map<String, dynamic> json) =>
      _$SecondaryListItemFromJson(json);

  Map<String, dynamic> toJson() => _$SecondaryListItemToJson(this);
}
