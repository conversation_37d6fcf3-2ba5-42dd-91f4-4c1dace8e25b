import 'dart:async';
import 'dart:convert';

import 'package:UrNovel/Util/Common/Model/normal_model.dart';

import '../../../Util/NetWorkManager/net_work_manager.dart';
import '../../../Util/SheetAndAlter/toast.dart';
import '../../../Util/api_config.dart';
import '../../../Util/enum.dart';
import '../../../Util/tools.dart';
import '../../Profile/Model/goods_info_model.dart';
import '../../Profile/ViewModel/ViewModel.dart';
import '../Model/author_reward_model.dart';
import '../Model/batchBuy_chapter_model.dart';
import '../Model/book_detailInfo_model.dart';
import '../Model/content_list_model.dart';
import '../Model/illustration_liked_model.dart';

class NovelReadViewModel {
  //获取书籍和章节信息V2-内容改为地址
  /// [bookId] 书籍id
  /// [jumpType] 跳转类型
  /// [isAll] 是否获取所有章节内容，默认获取
  static Future<BookDetailInfoResultModel?> getBookDetailInfo(
      int? bookId, BookDetailJumpType? jumpType,
      {bool isAll = true}) async {
    var parameters = {'bookId': bookId, "eventWay": getBookDetailJumpType(jumpType)};
    var response = await NetWorkManager.instance
        .get(isAll ? apiGetBookAllChapterInfoV2 : apiGetBookFirstChapterInfo, params: parameters);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      BookDetailInfoModel model = BookDetailInfoModel.fromJson(response.data);

      return model.result;
    }

    return null;
  }

  //根据地址行亚马逊云获取书籍章节内容json文件(暂废)
  // static Future<BookDetailInfoResultModel?> getChapterContentByUrl(
  //     BookDetailInfoResultModel infoResultModel) async {
  //   var response = await NetWorkManager.instance
  //       .downloadFile(infoResultModel.contentUrl!, DownLoadResponseType.json,
  //           onReceiveProgress: (int count, int total) {
  //     // logP("下载进度: ${((count.toDouble() / total.toDouble()) * 100)}%");
  //   });
  //   if (isAvailable(response.data) && response.data is String) {
  //     List<dynamic> list = jsonDecode(response.data);
  //     Map<String, dynamic> map = {'contents': list};
  //     ContentListModel? model = ContentListModel.fromJson(map);
  //     infoResultModel.contentList = model.contents;
  //   }
  //
  //   return infoResultModel;
  // }

  //下载s3上面的文件(书籍章节内容json文件)
  static Future<BookDetailInfoResultModel?> downloadS3FileOfChapterContent(
      BookDetailInfoResultModel infoResultModel,
      {ProgressCallback? onReceiveProgress}) async {
    var parameters = {
      'url': infoResultModel.contentUrl!,
    };
    var response = await NetWorkManager.instance
        .post(apiDownloadS3File, parameters: parameters, onReceiveProgress: onReceiveProgress);
    if (response.statusCode == 200 && isAvailable(response.data) && response.data is String) {
      List<dynamic> list = jsonDecode(response.data);
      Map<String, dynamic> map = {'contents': list};
      ContentListModel? model = ContentListModel.fromJson(map);
      infoResultModel.contentList = model.contents;

      return infoResultModel;
    }

    return null;
  }

  // 批量购买章节
  static Future<BatchBuyChapterResultModel?> batchBuyChapter(
      int? bookId, List<String>? chapterIds, bool? buyBook) async {
    var parameters = {
      "bookId": bookId,
      "chapterIdList": chapterIds,
      "buyBook": buyBook //是否购买整本书
    };
    var response = await NetWorkManager.instance.post(apiBatchBuyChapter, parameters: parameters);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      BatchBuyChapterModel model = BatchBuyChapterModel.fromJson(response.data);

      return model.result;
    }

    return null;
  }

  // 获取阅读页推荐订阅商品
  static Future<List<GoodsListItem>?> getReadRecommendSubGoodList() async {
    var response = await NetWorkManager.instance.post(apiReadRecommendSubGoodList, parameters: {});
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      GoodsInfoModel model = GoodsInfoModel.fromJson(response.data);
      if (model.code == 200) {
        if (isAvailable(model.result)) {
          return await ProfileViewModel.getStoreProductDetails(model.result);
        }
      } else {
        if (isAvailable(model.msg)) {
          showToast(model.msg!);
        }
      }
    }

    return null;
  }

  // 获取阅读页推荐金币商品
  static Future<List<GoodsListItem>?> getReadRecommendCoinGoodList() async {
    var response = await NetWorkManager.instance.post(apiReadRecommendCoinGoodList, parameters: {});
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      GoodsInfoModel model = GoodsInfoModel.fromJson(response.data);
      if (model.code == 200) {
        if (isAvailable(model.result)) {
          return await ProfileViewModel.getStoreProductDetails(model.result);
        }
      } else {
        if (isAvailable(model.msg)) {
          showToast(model.msg!);
        }
      }
    }

    return null;
  }

  //作者打赏
  static Future<AuthorRewardResultModel?> authorReward(
      int? bookId, int? authorId, int? coins) async {
    var parameters = {
      "bookId": bookId,
      "authorId": authorId,
      "coins": coins,
    };
    var response = await NetWorkManager.instance.post(apiAuthorReward, parameters: parameters);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      AuthorRewardModel model = AuthorRewardModel.fromJson(response.data);
      if (model.code == 200) {
        if (model.result?.rewardStatus == 2) {
          showToast(model.msg ?? "reward failed");
        }

        return model.result;
      } else {
        showToast(model.msg ?? "reward failed");
      }
    } else {
      showToast(response.data['msg'] ?? "reward failed");
    }

    return null;
  }

  ///插图打点
  static Future<void> illustrationEvent(Map<String, dynamic> parameters, Function(bool isSuccess) callBack) async {
    var response =
        await NetWorkManager.instance.post(apiEventIllustration, parameters: parameters);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      var model = NormalModel.fromJson(response.data);
      if (model.code == 200) {
        callBack(true);
      } else {
        if (isAvailable(model.msg)) {
          showToast(model.msg!);
        }
        callBack(false);
      }
    } else {
      if (isAvailable(response.data['msg'])) {
        showToast(response.data['msg']);
      }
      callBack(false);
    }
  }

  ///查询当前用户点赞了的所有插图信息
  static Future<List> getIllustrationAll() async {
    var response = await NetWorkManager.instance.get(apiIllustrationLikeAll, params: {});
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      var model = IllustrationLikedModel.fromJson(response.data);
      if (model.code == 200) {
        return model.result?? [];
      } else {
        if (isAvailable(model.msg)) {
          showToast(model.msg!);
        }
      }
    } else {
      if (isAvailable(response.data['msg'])) {
        showToast(response.data['msg']);
      }
    }

    return [];
  }
}
