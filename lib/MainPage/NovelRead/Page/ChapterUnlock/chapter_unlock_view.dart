import 'dart:math' as math;

import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Util/Common/CommonManager/common_manager.dart';
import 'package:UrNovel/Util/deviceScreenUtil.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../Launch&Login/Model/user_model.dart';
import '../../../../Launch&Login/Page/login_page.dart';
import '../../../../Util/Common/Animation/lottie_animation_view.dart';
import '../../../../Util/DataReportManager/ServiceReport/data_report_manager.dart';
import '../../../../Util/DataReportManager/event_name_config.dart';
import '../../../../Util/DataReportManager/event_report_manager.dart';
import '../../../../Util/Extensions/colorUtil.dart';
import '../../../../Util/PaymentManager/payment_manager.dart';
import '../../../../Util/SharedPreferences/shared_preferences.dart';
import '../../../../Util/SheetAndAlter/alter.dart';
import '../../../../Util/SheetAndAlter/bottom_sheet.dart';
import '../../../../Util/SheetAndAlter/toast.dart';
import '../../../../Util/StatusManagement/status_management.dart';
import '../../../../Util/enum.dart';
import '../../../../Util/tools.dart';
import '../../../Profile/Model/goods_info_model.dart';
import '../../Model/batchBuy_chapter_model.dart';
import '../../ViewModel/ViewModel.dart';

class ChapterUnlockView extends BaseFulWidget {
  final int? chapterId; //章节id
  final int? cost; //章节价格
  final UserInfoModel? userInfoModel;
  final GoodsListItem? premiumGoodItem;
  final List<GoodsListItem>? subCoinGoodList;
  final bool isGoldCoinEnabled; //是否金币充足
  final NovelPreferencesSettingController preferencesController;
  final Function(int? chapterId)? onRechargeCallBack;
  final VoidCallback? onJumpCallBack;
  final Function(Map<String, dynamic>? purchaseInfo)? onRefreshDataBack;
  final Function(int value)? onLockStateChanged;
  final double? topMaskHeight;

  ChapterUnlockView(
      {super.key,
      required this.chapterId,
      required this.cost,
      required this.userInfoModel,
      required this.premiumGoodItem,
      required this.subCoinGoodList,
      required this.isGoldCoinEnabled,
      required this.preferencesController,
      required this.onRechargeCallBack,
      required this.onJumpCallBack,
      required this.onRefreshDataBack,
      this.onLockStateChanged,
      required this.topMaskHeight,
      required super.arguments});

  @override
  State<ChapterUnlockView> createState() => PremiumPurchaseViewState();
}

class PremiumPurchaseViewState extends State<ChapterUnlockView> {
  late bool _isLoading;
  late int? _bookId;
  late int? _autoUnlock;
  final _radioUnlockKey = GlobalKey<RadioWidgetState>();
  final _coinsGoodsListKey = GlobalKey<CoinsGoodsListState>();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    _bookId = widget.arguments["bookId"];
    _autoUnlock = 0;
    _isLoading = false;

    _initAutoUnlockState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  get autoUnlock => _autoUnlock;

  Future<void> updateAutoUnlock(int value) async {
    _autoUnlock = value;
    _radioUnlockKey.currentState?.setIsAgreeUserAgreement(value == 1);

    await SpUtil.spSetChapterAutoUnlock(value);
  }

  Future<void> _initAutoUnlockState() async {
    _autoUnlock = await SpUtil.spGetChapterAutoUnlock();
    if (_autoUnlock == -1) {
      _autoUnlock = widget.userInfoModel?.autoUnlock ?? 1;
    }

    if (mounted) {
      _radioUnlockKey.currentState?.setIsAgreeUserAgreement(_autoUnlock == 1);
    }

    if (widget.preferencesController.readingMode.value == ReadingMode.horizontalScroll) {
      await onChapterUnlock();
      if (!isAlterShow) {
        isAlterShow = true;
        await showUnlockGoodsAlter();
      }
    }
  }

  Future<void> showUnlockGoodsAlter() async {
    Future.delayed(Duration(milliseconds: 1500), () async {
      isAlterShow = false;
      bool isVerify = await SpUtil.spGetVerifyStatus();
      if (!isVerify) {
        if (!widget.isGoldCoinEnabled && _autoUnlock == 1 || _autoUnlock != 1) {
          bool isShow = await SpUtil.spGetIsUnlockGoodsAlterShow();
          if (isShow &&
              isGoodsActivity(widget.premiumGoodItem?.activityPricePower,
                  widget.premiumGoodItem?.activityType)) {
            await SpUtil.spSetUnlockGoodsAlterShowTime(getCurrentTimeSeconds());
            showAlter(
                NovelUnlockGoodsContent(
                    premiumGoodItem: widget.premiumGoodItem,
                    onClaimNow: () async {
                      await onPremiumPurchase();
                    },
                    onClose: () {
                      Get.back();
                    }), onDismiss: () {
              isAlterShow = false;
            }, backgroundColor: Colors.transparent);
          }
        }
      }
    });
  }

  // TODO: 跳转到订阅页面
  Future<void> _toPremiumPage() async {
    widget.onJumpCallBack?.call();
    // TODO: implement premium purchase
    await Get.toNamed("/premiumPage", arguments: {
      "userInfo": widget.userInfoModel,
      'bookId': _bookId,
      'chapterId': widget.chapterId,
      'orderType': OrderType.novelReadTrialUnlock,
      'callback': widget.onRechargeCallBack,
    });

    await DataReportManager.instance
        .reportOldReadChapterList(ReadReportActivityType.reading, ReadReportReadingType.reading);
  }

  // TODO: 章节购买解锁
  Future<void> onChapterUnlock({bool isUnlockOnce = false}) async {
    if (_autoUnlock != 1 && !isUnlockOnce) {
      return;
    }

    if (!widget.isGoldCoinEnabled) {
      return;
    }

    if (!isAvailable(widget.chapterId)) {
      return;
    }

    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }
    BatchBuyChapterResultModel? result =
        await NovelReadViewModel.batchBuyChapter(_bookId, [widget.chapterId.toString()], false);

    if (result?.succ == true) {
      await reportUnlockData();
      widget.onRechargeCallBack?.call(widget.chapterId);
    }

    if (mounted) {
      Future.delayed(const Duration(seconds: 1), () {
        setState(() {
          _isLoading = false;
        });
      });
    }
  }

  // TODO: 解锁数据上报
  Future<void> reportUnlockData() async {
    if (widget.userInfoModel?.newUser == true) {
      await EventReportManager.eventReportOfFirebase(trialUnlockNew);
      await EventReportManager.eventReportOfCommon(addToCart);
      await EventReportManager.logAddToCart('', 'readUnlock', '', widget.cost?.toDouble() ?? 0.0);
    } else {
      await EventReportManager.eventReportOfFirebase(trialUnlockOld);
    }

    await EventReportManager.eventReportOfFirebase(trialUnlockAll);
    await EventReportManager.eventReportOfCommon(startTrial);
    await EventReportManager.logStartTrial('');
  }

  // TODO: 会员购买
  Future<void> onPremiumPurchase() async {
    await createShopOrder(
        widget.premiumGoodItem!,
        widget.premiumGoodItem?.productSubscription == 1
            ? GoodsType.subscriptionMembership
            : GoodsType.purchaseMembership);
    await DataReportManager.instance
        .reportOldReadChapterList(ReadReportActivityType.subscribe, ReadReportReadingType.reading);
  }

  // TODO: 创建订单
  Future<void> createShopOrder(GoodsListItem item, GoodsType goodsType) async {
    if (_isLoading) {
      return;
    }

    if (!isAvailable(item)) {
      showToast("product is Not Available");
      return;
    }

    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    PaymentManager.instance.createShopOrder(item,
        goodsType: goodsType,
        bookId: widget.arguments['bookId'],
        chapterId: widget.chapterId,
        orderType: OrderType.novelReadTrialUnlock, pullPurchaseCallback: () async {
      // TODO: 支付掉起回调
    }, onPurchaseCanceled: () async {
      // TODO: 支付取消回调
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }

      _coinsGoodsListKey.currentState?.updateItemSelectedState(true);
    }, onPurchaseValidateCallback: (status, info) async {
      // TODO: 验证订单回调
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }

      _coinsGoodsListKey.currentState?.updateItemSelectedState(true);

      if (status == SubscribeVerifyStatus.purchased) {
        widget.onRefreshDataBack?.call(info);
        Future.delayed(const Duration(seconds: 3), () async {
          if (goodsType == GoodsType.purchaseCoins) {
            await onChapterUnlock();
          } else if (goodsType == GoodsType.purchaseMembership ||
              goodsType == GoodsType.subscriptionMembership) {
            if (isAvailable(widget.onRechargeCallBack)) {
              widget.onRechargeCallBack?.call(null);
            }
          }
        });
      }
    });
  }

  // TODO: 自动解锁
  Future<void> _updateAutoUnlockState() async {
    if (_autoUnlock == 1) {
      _autoUnlock = 0;
    } else {
      _autoUnlock = 1;
    }

    if (mounted) {
      setState(() {});
    }

    await SpUtil.spSetChapterAutoUnlock(_autoUnlock!);
    _radioUnlockKey.currentState?.setIsAgreeUserAgreement(_autoUnlock == 1);
    if (_autoUnlock == 1 && !_isLoading) {
      await onChapterUnlock();
    }
    widget.onLockStateChanged?.call(_autoUnlock!);
  }

  @override
  Widget build(BuildContext context) {
    var perDayPrice = getPremiumPricePerDay(
        widget.premiumGoodItem?.introductoryPrice ?? widget.premiumGoodItem?.rawPrice.toString(),
        widget.premiumGoodItem?.productCode);
    String premiumTitle = getPremiumTitle(widget.premiumGoodItem?.productCode);
    bool activityPricePower = widget.premiumGoodItem?.activityPricePower == true;
    bool isFirstCharge = widget.premiumGoodItem?.activityType == 'First_Charge';
    bool isDiscount = widget.premiumGoodItem?.activityType == 'Discount';
    bool isOtherActivity = widget.premiumGoodItem?.activityType == 'Best_Deal';

    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: PopScope(
          onPopInvokedWithResult: (route, result) {},
          child: Directionality(
            textDirection: TextDirection.ltr,
            child: Stack(
              children: [
                Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    height: widget.topMaskHeight,
                    child: Obx(() => DecoratedBox(
                            decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              HexColor(widget.preferencesController.themeColor.value)
                                  .withValues(alpha: 0.0),
                              HexColor(widget.preferencesController.themeColor.value)
                                  .withValues(alpha: 0.5),
                              HexColor(widget.preferencesController.themeColor.value)
                                  .withValues(alpha: 0.8),
                              HexColor(widget.preferencesController.themeColor.value)
                                  .withValues(alpha: 0.9),
                              HexColor(widget.preferencesController.themeColor.value)
                                  .withValues(alpha: 1.0),
                            ],
                          ),
                        )))),
                Positioned(
                    top: widget.topMaskHeight ?? 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: DecoratedBox(
                      decoration: BoxDecoration(
                        color: HexColor(widget.preferencesController.themeColor.value),
                      ),
                      child: Column(
                        children: [
                          GestureDetector(
                            onTap: () {
                              _toPremiumPage();
                            },
                            behavior: HitTestBehavior.opaque,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                    'as_low'.trParams({
                                      'param':
                                          '${widget.premiumGoodItem?.currencySymbol ?? ""}$perDayPrice'
                                    }),
                                    style: TextStyle(fontSize: 15, color: HexColor('#B8863D'))),
                                Text('     '),
                                Text('${'details'.tr}  ',
                                    style: TextStyle(fontSize: 15, color: HexColor('#B8863D'))),
                                Transform.rotate(
                                  angle: CommonManager.instance.isReverse() ? math.pi : 0,
                                  child: Image.asset('assets/images/common/icon_arrow_gold.png',
                                      width: 6, height: 9),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            margin: const EdgeInsets.only(top: 8, left: 16, right: 16),
                            decoration: BoxDecoration(
                                gradient: LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    colors: [HexColor('#20243D'), HexColor('#2B304D')]),
                                borderRadius: BorderRadius.all(Radius.circular(18))),
                            child: Column(
                              children: [
                                SizedBox(height: 10),
                                Padding(
                                  padding: EdgeInsets.symmetric(horizontal: 15),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Image.asset(
                                        'assets/images/profile/icon_premium_card.png',
                                        width: 27,
                                        height: 14,
                                      ),
                                      SizedBox(width: 16),
                                      Expanded(
                                          child: Text('unlock_all'.tr,
                                              style: TextStyle(
                                                  fontSize: 15,
                                                  color: HexColor('#F6D88F'),
                                                  fontWeight: FontWeight.bold),
                                              maxLines: 2,
                                              overflow: TextOverflow.ellipsis,
                                              textAlign: TextAlign.left))
                                    ],
                                  ),
                                ),
                                SizedBox(height: 20),
                                Padding(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: DeviceScreenUtil.instance.width * 0.05),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Expanded(
                                          child: Column(
                                        children: [
                                          Image.asset(
                                            'assets/images/novelReading/icon_unlimited_reading.png',
                                            width: 33,
                                            height: 33,
                                          ),
                                          SizedBox(height: 10),
                                          Text('unlimited_reading'.tr,
                                              style: TextStyle(
                                                  fontSize: 13,
                                                  color: HexColor('#CFB693'),
                                                  fontWeight: FontWeight.bold),
                                              maxLines: 2,
                                              overflow: TextOverflow.ellipsis,
                                              textAlign: TextAlign.center)
                                        ],
                                      )),
                                      Expanded(
                                          child: Column(
                                        children: [
                                          Image.asset(
                                            'assets/images/novelReading/icon_member_badge.png',
                                            width: 33,
                                            height: 33,
                                          ),
                                          SizedBox(height: 10),
                                          Text('member_badge'.tr,
                                              style: TextStyle(
                                                  fontSize: 13,
                                                  color: HexColor('#CFB693'),
                                                  fontWeight: FontWeight.bold),
                                              maxLines: 2,
                                              overflow: TextOverflow.ellipsis,
                                              textAlign: TextAlign.center)
                                        ],
                                      ))
                                    ],
                                  ),
                                ),
                                if (isAvailable(widget.premiumGoodItem))
                                  GestureDetector(
                                      onTap: () async {
                                        await onPremiumPurchase();
                                      },
                                      behavior: HitTestBehavior.opaque,
                                      child: SizedBox(
                                        width: double.infinity,
                                        height: 60,
                                        child: Stack(
                                          children: [
                                            Positioned(
                                                top: 15,
                                                left: 30,
                                                right: 30,
                                                height: 42,
                                                child: DecoratedBox(
                                                    decoration: BoxDecoration(
                                                        gradient: LinearGradient(
                                                            begin: Alignment.centerLeft,
                                                            end: Alignment.centerRight,
                                                            colors: [
                                                              HexColor('#ECCA95'),
                                                              HexColor('#F4D8AC')
                                                            ]),
                                                        borderRadius: BorderRadius.all(
                                                          Radius.circular(12),
                                                        )),
                                                    child: isAvailable(widget
                                                            .premiumGoodItem?.introductoryPrice)
                                                        ? Row(
                                                            mainAxisAlignment:
                                                                MainAxisAlignment.center,
                                                            children: [
                                                              Text(
                                                                  // 强制从左到右
                                                                  "$premiumTitle ${widget.premiumGoodItem?.currencySymbol}${widget.premiumGoodItem?.introductoryPrice}",
                                                                  style: TextStyle(
                                                                    color: HexColor(widget
                                                                                .premiumGoodItem
                                                                                ?.recommend ==
                                                                            1
                                                                        ? '#704E1F'
                                                                        : '#CFB693'),
                                                                    fontSize: 14,
                                                                    fontWeight: FontWeight.bold,
                                                                    height: 1,
                                                                  )),
                                                              Row(
                                                                children: [
                                                                  Text('('),
                                                                  Text(
                                                                      "${widget.premiumGoodItem?.showPrice}",
                                                                      style: TextStyle(
                                                                        color: HexColor('#704E1F'),
                                                                        fontSize: 14,
                                                                        fontWeight: FontWeight.bold,
                                                                        decoration: TextDecoration
                                                                            .lineThrough,
                                                                        decorationColor:
                                                                            HexColor('#704E1F'),
                                                                        decorationStyle:
                                                                            TextDecorationStyle
                                                                                .solid,
                                                                        decorationThickness: 2,
                                                                      )),
                                                                  Text(')'),
                                                                ],
                                                              )
                                                            ],
                                                          )
                                                        : Center(
                                                            child: Text(
                                                              '$premiumTitle ${widget.premiumGoodItem?.showPrice ?? ""}',
                                                              style: TextStyle(
                                                                  fontSize: 14,
                                                                  color: HexColor('#704E1F'),
                                                                  fontWeight: FontWeight.bold),
                                                            ),
                                                          ))),
                                            if (activityPricePower &&
                                                (isFirstCharge || isDiscount || isOtherActivity))
                                              Positioned(
                                                  top: isFirstCharge
                                                      ? 0
                                                      : isDiscount
                                                          ? 2
                                                          : 10,
                                                  left: isFirstCharge
                                                      ? 26
                                                      : isDiscount
                                                          ? 24
                                                          : 30,
                                                  child: Row(
                                                    crossAxisAlignment: CrossAxisAlignment.end,
                                                    children: [
                                                      if (isFirstCharge || isDiscount)
                                                        Image.asset(
                                                            isFirstCharge
                                                                ? 'assets/images/profile/purchase/icon_top_first_charge.png'
                                                                : 'assets/images/profile/purchase/icon_top_discount.png',
                                                            fit: BoxFit.cover,
                                                            width: isFirstCharge ? 22 : 25,
                                                            height: isFirstCharge ? 28 : 24),
                                                      Container(
                                                        height: 19,
                                                        decoration: isFirstCharge || isDiscount
                                                            ? BoxDecoration(
                                                                color: isFirstCharge
                                                                    ? HexColor('#FE6F0C')
                                                                    : HexColor('#404243'),
                                                                borderRadius: BorderRadius.only(
                                                                    topRight: Radius.circular(8),
                                                                    bottomRight:
                                                                        Radius.circular(8)),
                                                              )
                                                            : BoxDecoration(
                                                                gradient: LinearGradient(
                                                                    colors: [
                                                                      HexColor('#FF87C0'),
                                                                      HexColor('#FC348C'),
                                                                    ],
                                                                    begin: Alignment.topCenter,
                                                                    end: Alignment.bottomCenter),
                                                                borderRadius: BorderRadius.only(
                                                                    topLeft: Radius.circular(8),
                                                                    topRight: Radius.circular(8),
                                                                    bottomRight:
                                                                        Radius.circular(8)),
                                                              ),
                                                        child: Padding(
                                                            padding: EdgeInsets.only(
                                                                left: isOtherActivity ? 18 : 5,
                                                                right: isOtherActivity ? 18 : 10),
                                                            child: Text(
                                                                isFirstCharge
                                                                    ? getPremiumSubTitle(
                                                                        widget.premiumGoodItem
                                                                            ?.numberOfPeriods,
                                                                        widget.premiumGoodItem
                                                                            ?.productCode,
                                                                        isBilled: false)
                                                                    : isDiscount
                                                                        ? '${widget.premiumGoodItem?.activityName}'
                                                                        : 'best_deal'.tr,
                                                                style: TextStyle(
                                                                    color: HexColor(isFirstCharge
                                                                        ? '#FFFB18'
                                                                        : '#F7DCB1'),
                                                                    fontSize: 12,
                                                                    fontWeight: FontWeight.bold),
                                                                maxLines: 1,
                                                                overflow: TextOverflow.ellipsis,
                                                                textAlign: TextAlign.center)),
                                                      )
                                                    ],
                                                  )),
                                            if (activityPricePower &&
                                                isAvailable(
                                                    widget.premiumGoodItem?.countDownSecond))
                                              Positioned(
                                                  bottom: 0,
                                                  right: 30,
                                                  height: 16,
                                                  child: Container(
                                                      padding: EdgeInsets.only(left: 9, right: 9),
                                                      decoration: BoxDecoration(
                                                        gradient: LinearGradient(
                                                          colors: [
                                                            HexColor('#FD8413'),
                                                            HexColor('#FE4101')
                                                          ],
                                                          begin: Alignment.centerLeft,
                                                          end: Alignment.centerRight,
                                                        ),
                                                        borderRadius: BorderRadius.only(
                                                            topLeft: Radius.circular(12),
                                                            bottomLeft: Radius.circular(12),
                                                            bottomRight: Radius.circular(12)),
                                                      ),
                                                      child: CountDownCard(
                                                          countDownSecond: widget
                                                              .premiumGoodItem?.countDownSecond,
                                                          onTimerEnd: () {
                                                            widget.onRefreshDataBack?.call(null);
                                                          }))),
                                          ],
                                        ),
                                      )),
                                SizedBox(height: 10),
                              ],
                            ),
                          ),
                          SizedBox(height: 5),
                          if (widget.isGoldCoinEnabled && _autoUnlock != 1)
                            GestureDetector(
                              onTap: () {
                                onChapterUnlock(isUnlockOnce: true);
                              },
                              behavior: HitTestBehavior.opaque,
                              child: Container(
                                  width: double.infinity,
                                  height: 40,
                                  alignment: Alignment.center,
                                  margin: const EdgeInsets.symmetric(horizontal: 22),
                                  decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                          begin: Alignment.centerLeft,
                                          end: Alignment.centerRight,
                                          colors: [HexColor('#ECCA95'), HexColor('#F4D8AC')]),
                                      borderRadius: BorderRadius.all(
                                        Radius.circular(12),
                                      )),
                                  child: Text('unlock_now'.tr,
                                      style: TextStyle(
                                          fontSize: 14,
                                          color: HexColor('#704E1F'),
                                          fontWeight: FontWeight.bold))),
                            )
                          else if (!widget.isGoldCoinEnabled)
                            CoinsGoodsList(
                                key: _coinsGoodsListKey,
                                subCoinGoodList: widget.subCoinGoodList,
                                onItemTap: (item) async {
                                  if (isAvailable(item)) {
                                    await createShopOrder(item!, GoodsType.purchaseCoins);
                                    await DataReportManager.instance.reportOldReadChapterList(
                                        ReadReportActivityType.purchase,
                                        ReadReportReadingType.reading);
                                  }
                                }),
                          SizedBox(height: 10),
                          GestureDetector(
                            onTap: () async {
                              // TODO: 自动解锁
                              await _updateAutoUnlockState();
                            },
                            behavior: HitTestBehavior.opaque,
                            child: Padding(
                              padding: EdgeInsets.symmetric(horizontal: 14),
                              child: Row(
                                children: [
                                  RadioWidget(
                                      key: _radioUnlockKey,
                                      isAgree: _autoUnlock == 1,
                                      onIsAgreeChanged: (isAgree) async {
                                        // TODO: 自动解锁
                                        await _updateAutoUnlockState();
                                      }),
                                  SizedBox(width: 8),
                                  Expanded(
                                      child: Text(
                                    'auto_unlock'.tr,
                                    style: TextStyle(fontSize: 13, color: HexColor('#888C94')),
                                    maxLines: null,
                                    textAlign: TextAlign.left,
                                  )),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(height: 20),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text('${'balance'.tr}:  ${widget.userInfoModel?.goldCoin ?? 0}',
                                  style: TextStyle(
                                      fontSize: 14,
                                      color: HexColor('#888C94'),
                                      fontWeight: FontWeight.bold)),
                              SizedBox(width: 7),
                              Image.asset('assets/images/profile/purchase/icon_coin.png',
                                  width: 16, height: 16)
                            ],
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                  'unlock_this_chapter'
                                      .trParams({'param': '${widget.cost ?? '--'}'}),
                                  style: TextStyle(
                                      fontSize: 15,
                                      color: HexColor('#B8863D'),
                                      fontWeight: FontWeight.bold)),
                              SizedBox(width: 7),
                              Image.asset('assets/images/profile/purchase/icon_coin.png',
                                  width: 16, height: 16)
                            ],
                          ),
                        ],
                      ),
                    )),
                if (_isLoading) const Positioned(child: LottieAnimationView())
              ],
            ),
          )),
    );
  }
}

class CoinsGoodsList extends StatefulWidget {
  final List<GoodsListItem>? subCoinGoodList;
  final Function(GoodsListItem? item)? onItemTap;

  const CoinsGoodsList({super.key, required this.subCoinGoodList, required this.onItemTap});

  @override
  State<CoinsGoodsList> createState() => CoinsGoodsListState();
}

class CoinsGoodsListState extends State<CoinsGoodsList> with AutomaticKeepAliveClientMixin {
  late int _currentIndex;
  late bool _isCanSelect;
  late GoodsListItem? _firstChargeItem;
  late List<GoodsListItem>? _otherList;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    _currentIndex = 0;
    _isCanSelect = true;

    _firstChargeItem = null;
    _otherList = [];
  }

  @override
  void dispose() {
    super.dispose();
  }

  updateItemSelectedState(bool isCanSelect) {
    _isCanSelect = isCanSelect;
  }

  _checkFirstChargeCard() {
    _firstChargeItem = null;
    _otherList = [];
    if (isAvailable(widget.subCoinGoodList)) {
      _otherList!.addAll(widget.subCoinGoodList!);
      for (var item in widget.subCoinGoodList!) {
        if (item.activityPricePower == true) {
          //活动类型 ("First_Charge", "首充活动"), ("Discount", "额外赠送&折扣活动"), ("Best_Deal", "最佳交易"),
          if (item.activityType == 'First_Charge') {
            _firstChargeItem = item;
            break;
          }
        }
      }

      if (isAvailable(_firstChargeItem)) {
        _otherList!.remove(_firstChargeItem);
      }
    }
  }

  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);

    _checkFirstChargeCard();

    var cardWidth = 160.0;
    var cardHeight = 110.0;
    var count = _otherList?.length ?? 0;

    return Column(
      children: [
        if (isAvailable(_firstChargeItem))
          Padding(
              padding: EdgeInsets.only(bottom: 7),
              child: FirstChargeCard(
                item: _firstChargeItem,
                goodsType: GoodsType.purchaseCoins,
                isUnLock: true,
                onProductTap: (item) {
                  widget.onItemTap?.call(item);
                },
              )),
        SizedBox(
            height: cardHeight,
            child: ListView.builder(
                itemCount: count,
                scrollDirection: Axis.horizontal,
                itemBuilder: (context, index) {
                  GoodsListItem? item = _otherList?[index];
                  int? coinsGift = item?.coinsGift ?? 0;
                  bool activityPricePower = item?.activityPricePower == true;
                  bool isFirstCharge = item?.activityType == 'First_Charge';
                  bool isDiscount = item?.activityType == 'Discount';
                  bool isOtherActivity = item?.activityType == 'Best_Deal';

                  var title = item?.title ?? '0';
                  if (title.contains('coins')) {
                    title = title.replaceAll('coins', '');
                  }

                  double leftPadding = 10;
                  return GestureDetector(
                      onTap: () {
                        if (!_isCanSelect) {
                          return;
                        }

                        _isCanSelect = false;

                        if (mounted) {
                          setState(() {
                            _currentIndex = index;
                          });
                        }

                        widget.onItemTap?.call(item);
                      },
                      child: SizedBox(
                        width: cardWidth,
                        height: cardHeight,
                        child: Stack(
                          children: [
                            if (activityPricePower && isAvailable(item?.countDownSecond))
                              Positioned(
                                  left: leftPadding,
                                  right: 0,
                                  bottom: 0,
                                  height: 36,
                                  child: DecoratedBox(
                                      decoration: BoxDecoration(
                                          gradient: LinearGradient(
                                              begin: Alignment.centerLeft,
                                              end: Alignment.centerRight,
                                              colors: item?.activityType == 'Discount'
                                                  ? [HexColor('#404243'), HexColor('#404243')]
                                                  : [HexColor('#FF87C0'), HexColor('#FC348C')]),
                                          borderRadius: BorderRadius.only(
                                              bottomLeft: Radius.circular(12),
                                              bottomRight: Radius.circular(12))),
                                      child: Padding(
                                          padding: EdgeInsets.only(top: 16),
                                          child: CountDownCard(
                                            countDownSecond: item?.countDownSecond,
                                          )))),
                            Positioned(
                              top: 10,
                              left: leftPadding,
                              right: index == count - 1 ? leftPadding : 0,
                              bottom: 20,
                              child: Container(
                                padding: EdgeInsets.only(top: 11),
                                decoration: BoxDecoration(
                                  color: _currentIndex == index
                                      ? HexColor('#FBF6E6')
                                      : HexColor('#F4F5F6'),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Image.asset(
                                          'assets/images/profile/purchase/icon_coin.png',
                                          width: 23,
                                          height: 23,
                                        ),
                                        SizedBox(width: 11),
                                        Text('${item?.skuValue ?? ""}',
                                            style: TextStyle(
                                                color: HexColor('#C79648'),
                                                fontSize: 22,
                                                fontWeight: FontWeight.bold,
                                                height: 1)),
                                      ],
                                    ),
                                    const SizedBox(height: 4),
                                    if (0 < coinsGift)
                                      Text("bonus_count".trParams({'param': coinsGift.toString()}),
                                          style: TextStyle(
                                              color: HexColor('#C79648'),
                                              fontSize: 12.sp,
                                              height: 1)),
                                    if (0 < coinsGift) const SizedBox(height: 8),
                                    if (item?.activityPricePower == true &&
                                        isAvailable(item?.introductoryPrice))
                                      Text.rich(TextSpan(
                                          text:
                                              "${item?.currencySymbol} ${item?.introductoryPrice}",
                                          style: TextStyle(
                                              color: HexColor('#1F1F2F'),
                                              fontSize: 15,
                                              fontWeight: FontWeight.bold,
                                              height: 1),
                                          children: [
                                            TextSpan(
                                                text: '(',
                                                style: TextStyle(
                                                  color: HexColor('#5D6884'),
                                                  fontSize: 12,
                                                )),
                                            TextSpan(
                                                text: "${item?.showPrice}",
                                                style: TextStyle(
                                                  color: HexColor('#5D6884'),
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.bold,
                                                  height: 1,
                                                  decoration: TextDecoration.lineThrough,
                                                  decorationColor: HexColor('#1F233B'),
                                                  decorationStyle: TextDecorationStyle.solid,
                                                  decorationThickness: 2,
                                                )),
                                            TextSpan(
                                                text: ')',
                                                style: TextStyle(
                                                  color: HexColor('#5D6884'),
                                                  fontSize: 12,
                                                )),
                                          ]))
                                    else
                                      Text(item?.showPrice ?? "",
                                          style: TextStyle(
                                              color: HexColor('#1F1F2F'),
                                              fontSize: 15,
                                              fontWeight: FontWeight.bold,
                                              height: 1)),
                                    const SizedBox(height: 4),
                                  ],
                                ),
                              ),
                            ),
                            if (activityPricePower &&
                                (isFirstCharge || isDiscount || isOtherActivity))
                              Positioned(
                                  top: isFirstCharge || isDiscount ? 0 : 3,
                                  left: isFirstCharge || isDiscount ? 5 : leftPadding,
                                  right: 0,
                                  child: Row(
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    children: [
                                      if (isFirstCharge || isDiscount)
                                        Image.asset(
                                            isFirstCharge
                                                ? 'assets/images/profile/purchase/icon_top_first_charge.png'
                                                : 'assets/images/profile/purchase/icon_top_discount.png',
                                            fit: BoxFit.cover,
                                            width: isFirstCharge ? 16 : 19,
                                            height: isFirstCharge ? 22 : 18),
                                      Container(
                                        height: 15,
                                        decoration: isFirstCharge || isDiscount
                                            ? BoxDecoration(
                                                color: isFirstCharge
                                                    ? HexColor('#FE6F0C')
                                                    : HexColor('#404243'),
                                                borderRadius: BorderRadius.only(
                                                    topRight: Radius.circular(8),
                                                    bottomRight: Radius.circular(8)),
                                              )
                                            : BoxDecoration(
                                                gradient: LinearGradient(
                                                    colors: [
                                                      HexColor('#FF87C0'),
                                                      HexColor('#FC348C'),
                                                    ],
                                                    begin: Alignment.topCenter,
                                                    end: Alignment.bottomCenter),
                                                borderRadius: BorderRadius.only(
                                                    topLeft: Radius.circular(8),
                                                    topRight: Radius.circular(8),
                                                    bottomRight: Radius.circular(8)),
                                              ),
                                        child: Padding(
                                            padding: EdgeInsets.only(
                                                top: 1, left: isOtherActivity ? 5 : 2, right: 5),
                                            child: Text(
                                                isFirstCharge
                                                    ? '1st_top_up'.tr
                                                    : isDiscount
                                                        ? '${item?.activityName}'
                                                        : 'best_deal'.tr,
                                                style: TextStyle(
                                                    color: HexColor(
                                                        isFirstCharge ? '#FFFB18' : '#F7DCB1'),
                                                    fontSize: 9.sp,
                                                    fontWeight: FontWeight.bold),
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                                textAlign: TextAlign.left)),
                                      )
                                    ],
                                  )),
                            if (isFirstCharge || isDiscount || isOtherActivity)
                              Positioned(
                                  bottom: 20,
                                  right: 0,
                                  width: 50,
                                  height: 50,
                                  child: DecoratedBox(
                                      decoration: BoxDecoration(
                                          image: DecorationImage(
                                              image: AssetImage(isFirstCharge
                                                  ? 'assets/images/profile/purchase/icon_first_charge.png'
                                                  : isDiscount
                                                      ? 'assets/images/profile/purchase/icon_bonus.png'
                                                      : 'assets/images/profile/purchase/icon_best_deal.png'),
                                              fit: BoxFit.cover)))),
                          ],
                        ),
                      ));
                }))
      ],
    );
  }
}
