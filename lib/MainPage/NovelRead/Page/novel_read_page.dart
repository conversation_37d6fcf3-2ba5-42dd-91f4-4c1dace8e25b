import 'dart:async';

import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Launch&Login/Model/user_model.dart';
import 'package:UrNovel/Launch&Login/ViewModel/ViewModel.dart';
import 'package:UrNovel/Util/AlterManager/alter_manager.dart';
import 'package:UrNovel/Util/Common/RouteObserver/Model/my_route_model.dart';
import 'package:UrNovel/Util/Common/no_load_view.dart';
import 'package:UrNovel/Util/DBManager/db_manager.dart';
import 'package:UrNovel/Util/DataReportManager/event_report_manager.dart';
import 'package:UrNovel/Util/Extensions/colorUtil.dart';
import 'package:UrNovel/Util/ShareManager/share_manager.dart';
import 'package:UrNovel/Util/SheetAndAlter/alter.dart';
import 'package:UrNovel/Util/deviceScreenUtil.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:live_activities/models/live_activity_file.dart';

import '../../../Util/Common/Animation/lottie_animation_view.dart';
import '../../../Util/Common/CommonManager/common_manager.dart';
import '../../../Util/Common/Model/share_channel_model.dart';
import '../../../Util/Common/novel_download.dart';
import '../../../Util/DataReportManager/ServiceReport/ViewModel/data_report_viewModel.dart';
import '../../../Util/DataReportManager/ServiceReport/data_report_manager.dart';
import '../../../Util/DataReportManager/event_name_config.dart';
import '../../../Util/LiveActivitiesManger/Model/novel_activities_model.dart';
import '../../../Util/LiveActivitiesManger/live_activities_manager.dart';
import '../../../Util/LocalNotificationManager/local_notification_manager.dart';
import '../../../Util/ReaderUtils/novel_page_agent.dart';
import '../../../Util/ReaderUtils/novel_read_chapter.dart';
import '../../../Util/ReaderUtils/readerUtil.dart';
import '../../../Util/SharedPreferences/shared_preferences.dart';
import '../../../Util/SheetAndAlter/bottom_sheet.dart';
import '../../../Util/SheetAndAlter/toast.dart';
import '../../../Util/StatusManagement/status_management.dart';
import '../../../Util/enum.dart';
import '../../../Util/genericUtil.dart';
import '../../../Util/global.dart';
import '../../../Util/logUtil.dart';
import '../../BookInfo/Model/BookDownLoad/download_book_detail_info_model.dart';
import '../../Profile/Model/goods_info_model.dart';
import '../../SignIn/View/signin_popup_widget.dart';
import '../ListeningBook/Page/listening_book_page.dart';
import '../Model/book_detailInfo_model.dart';
import '../Model/book_preferencesSetting_model.dart';
import '../Model/content_list_model.dart';
import '../Model/illustration_rule_model.dart';
import '../ViewModel/ViewModel.dart';
import '../Widget/NovelMenu/novel_menu.dart';
import '../Widget/NovelReadingView/ReadPageModel/read_page_horizontal.dart';
import '../Widget/NovelReadingView/ReadPageModel/read_page_vertical.dart';
import 'ChapterUnlock/chapter_unlock_view.dart';

class NovelReadPage extends BaseFulWidget {
  NovelReadPage({super.key, required super.arguments});

  @override
  State<NovelReadPage> createState() => _NovelReadPageState();
}

class _NovelReadPageState extends State<NovelReadPage> {
  late bool _isMenuVisible;
  late bool _isNoData;

  ///书籍id
  late int? _bookId;

  ///页面跳转来源类型
  late BookDetailJumpType? _pageJumpType;

  ///章节索引
  late int? _chapterIndex;

  ///书籍信息
  late BookDetailInfoResultModel? _bookDetailInfoModel;

  ///当前章节
  late ChapterVoModel? _currentChapter;

  ///章节内容
  late ContentModel? preArticle;
  late ContentModel? currentArticle;
  late ContentModel? nextArticle;

  ///当前页码
  late int pageIndex;
  late double currentOffset;
  late bool _isLoading;

  final PageController _pageController = PageController();

  late OverlayEntry _overlayEntry;
  final _menuKey = GlobalKey<NovelMenuState>();

  // 阅读速记收藏点赞等状态控制器
  final NovelReadStatusController _novelReadStatusController =
      findGetXInstance(NovelReadStatusController());

  // 偏好设置控制器
  final NovelPreferencesSettingController _preferencesController =
      findGetXInstance(NovelPreferencesSettingController());

  // 网络状态
  final NetWorkStatusController _netWorkStatusController = Get.put(NetWorkStatusController());

  late final List<Map<String, dynamic>> chapterReadRecords;

  bool isPagePop = false;

  ///竖版阅读模式key
  final _readPageHorizontalKey = GlobalKey<ReadPageHorizontalState>();
  final _readPageVerticalKey = GlobalKey<ReadPageVerticalState>();
  final _lottieAnimationKey = GlobalKey<LottieAnimationViewState>();

  late UserInfoModel? _userInfoModel;
  late GoodsListItem? _premiumGoodItem;
  late List<GoodsListItem>? _subCoinGoodList;

  late bool _modelChangeIsTheSameChapter; //阅读模型转换，是否是同一个章节

  late int? _currentNextBookIndex; //当前下本书的索引
  late List<BookDetailInfoResultModel?>? _nextBookInfoList; //下一本书信息列表

  //当前设置
  late BookPreferencesSettingModel _currentPreferencesSettingModel;
  late bool _isOfflineReading; //是否离线阅读
  late Timer _loadTryAgainTimer; //重试定时器
  late bool _isTryAgain; //是否重试
  late bool _isDownload; //是否下载
  late bool _isShowChatEntry; //是否显示聊天入口
  late bool _isShowVoiceEntry; //是否显示有声入口
  late Timer _activityTimer; //活跃定时器

  StreamSubscription<AppLifecycleState>? _lifecycleSubscription; //生命周期
  StreamSubscription<PageRouteModel>? _routeSubscription; //页面路由
  StreamSubscription<IllustrationRuleModel>? _illustrationRuleModel; //插画like事件

  //活动数据
  late NovelActivitiesModel? _curActivityModel;

  late bool _isExitPage; //是否是退出于都页面

  @override
  void initState() {
    // TODO: implement initState
    preventScreenshotOn();
    super.initState();
    _isMenuVisible = false;
    _isNoData = false;
    _bookId = widget.arguments['bookId'];
    _chapterIndex = widget.arguments['chapterIndex'];
    _pageJumpType = widget.arguments['jumpType'] ?? BookDetailJumpType.other;
    _bookDetailInfoModel = null;
    _currentChapter = null;
    pageIndex = 0;
    currentOffset = 0.0;
    _isLoading = false;
    _overlayEntry = OverlayEntry(
      builder: (context) => _buildMenu(),
    );
    chapterReadRecords = [];
    _userInfoModel = null;
    _premiumGoodItem = null;
    _subCoinGoodList = null;
    _modelChangeIsTheSameChapter = true;
    _currentNextBookIndex = null;
    _nextBookInfoList = null;
    _pageController.addListener(_onScroll);
    _isTryAgain = false;
    _isDownload = false;
    _curActivityModel = null;
    _isExitPage = false;

    _onSetup();
    fetchData(false);
    dataReport();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      // TODO: implement initState
      isShowGuideView();
    });
  }

  @override
  void dispose() {
    // TODO: implement dispose
    preventScreenshotOff();
    _setTryAgainTimer();
    _curActivityModel = null;
    if (_activityTimer.isActive) {
      _activityTimer.cancel();
    }
    _pageController.removeListener(_onScroll);
    _pageController.dispose();

    // 移除 OverlayEntry
    try {
      _lifecycleSubscription?.cancel();
      _routeSubscription?.cancel();
      _illustrationRuleModel?.cancel();

      // 移除 OverlayEntry
      if (isAvailable(_overlayEntry)) {
        if (_overlayEntry.mounted) {
          _overlayEntry.remove();
        }
        _overlayEntry.dispose();
      }
    } catch (e) {
      logP('overlayEntry error: $e');
    }

    super.dispose();
  }

  Future<void> _onSetup() async {
    preArticle = null;
    currentArticle = null;
    nextArticle = null;
    _isOfflineReading = DBManager.instance.checkIsNovelExist<DownloadBookDetailInfoModel>(_bookId);
    _currentPreferencesSettingModel = BookPreferencesSettingModel();
    if (_pageJumpType == BookDetailJumpType.localAdPage) {
      _preferencesController.setReadingMode(ReadingMode.verticalScroll);
    }
    DataReportManager.instance.lastReadTime = getCurrentTimeMillis();
    isChapterStyleNeedSync();

    ///事件监听
    //生命周期监听
    _lifecycleSubscription = eventBusOn<AppLifecycleState>((state) async {
      if (state == AppLifecycleState.resumed) {
        _setActivityTimer(isReset: true);
      } else if (state == AppLifecycleState.paused) {
        _setActivityTimer();
        // 阅读记录服务上报
        await reportReadRecord(
            ReadReportActivityType.enterBackground, ReadReportReadingType.reading, false, true);
      } else if (state == AppLifecycleState.inactive) {
        /// 阅读数据上报
        await DataReportManager.instance.reportOldReadChapterList(
            ReadReportActivityType.inactive,
            audioBookFloating?.mounted == true
                ? ReadReportReadingType.listen
                : ReadReportReadingType.reading);
      }
    });
    //路由监听
    _routeSubscription = eventBusOn<PageRouteModel>((route) async {
      if (route.routeType == MyRouteType.push) {
        ///取消活跃定时器
        _setActivityTimer();
      } else {
        ///重置活跃定时器
        _setActivityTimer(isReset: true);
      }
      // 阅读记录服务上报(做记录)
      await reportReadRecord(
          ReadReportActivityType.reading, ReadReportReadingType.reading, false, false);
    });
    //插画like事件监听
    _illustrationRuleModel = eventBusOn<IllustrationRuleModel>((rule) async {
      await getIllustrationAll();
    });
  }

  //TODO: 获取章节列表和章节内容
  Future fetchData(bool isStartOfBook) async {
    if (_isLoading ||
        !isAvailable(_bookId) ||
        (isAvailable(_bookDetailInfoModel) && _bookDetailInfoModel?.bookId == _bookId)) {
      return;
    }

    _lottieAnimationKey.currentState?.updateProgress(0.0);
    _setTryAgainTimer(isReset: true);
    _setActivityTimer(isReset: true);

    _nextBookInfoList = null;
    _currentNextBookIndex = null;
    _isShowChatEntry = false;
    _isShowVoiceEntry = false;
    await getIllustrationAll();
    await getBookInfo(_bookId);

    _isNoData = false;

    bool isShowVoiceEntry = true;
    if (isAvailable(_bookDetailInfoModel)) {
      _currentChapter = _bookDetailInfoModel?.chapterVoList?.first;
      if (isAvailable(_bookDetailInfoModel?.chapterVoList)) {
        for (int i = 0; i < _bookDetailInfoModel!.chapterVoList!.length; i++) {
          ChapterVoModel? chapter = _bookDetailInfoModel!.chapterVoList![i];
          if (chapter?.lock == 1) {
            chapter?.chapterUnLockKey = GlobalKey<PremiumPurchaseViewState>();
          }
          //是否从头开始阅读(下一本书)
          if (!isStartOfBook && _chapterIndex == i + 1) {
            //如果来自推送&落地页书籍推广，需要根据索引跳转指定章节
            _currentChapter = chapter;
          }
          if (!isAvailable(chapter?.chapterVoice)) {
            isShowVoiceEntry = false;
          }
        }
      }
    } else {
      _isNoData = true;
    }

    _isShowChatEntry = _bookDetailInfoModel?.hasChat == true;
    _isShowVoiceEntry = isShowVoiceEntry;

    //无数据
    if (_isNoData) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
      return;
    }

    //查看是否有章节内容信息
    if (!isAvailable(_bookDetailInfoModel?.contentList)) {
      //根据地址下载内容
      if (isAvailable(_bookDetailInfoModel?.contentUrl)) {
        _bookDetailInfoModel = await NovelReadViewModel.downloadS3FileOfChapterContent(
            _bookDetailInfoModel!, onReceiveProgress: (count, total) {
          double receiveProgress = (count.toDouble() / total.toDouble());
          _lottieAnimationKey.currentState?.updateProgress(receiveProgress);
        });
      }
    }

    _isNoData = !_checkIsBookContentAvailable();

    if (_isNoData) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
      _setActivityTimer();
      return;
    } else {
      _setTryAgainTimer();
    }

    PageJumpType jumpType = PageJumpType.firstPage;
    if (!isStartOfBook) {
      ///读取记录，重置进度
      NovelReadChapterModel? readBookChapterModel =
          DBManager.instance.getNovelReadBookChapter(_bookId);
      //如果不是推广过来的，重置进度
      if (isAvailable(readBookChapterModel?.chapterId) && !isAvailable(_chapterIndex)) {
        jumpType = PageJumpType.stay;
        pageIndex = readBookChapterModel!.pageIndex ?? 0;
        if (isAvailable(readBookChapterModel.chapterIndex) &&
            readBookChapterModel.chapterIndex! - 1 < _bookDetailInfoModel!.chapterVoList!.length) {
          _currentChapter =
              _bookDetailInfoModel!.chapterVoList![readBookChapterModel.chapterIndex! - 1];
        }
      }
    }

    ///保存用户阅读记录
    DataReportViewModel.reportUserReadingHistory(_currentChapter?.id);
    setNovelReadBookChapter(true);

    if (isAvailable(_currentChapter?.id)) {
      await resetContent(_currentChapter?.id ?? 0, jumpType, false);
      //上报阅读记录
      reportReadRecord(ReadReportActivityType.reading, ReadReportReadingType.reading, false, false);
    }

    if (mounted && _isLoading) {
      setState(() {
        _isLoading = false;
      });
    }

    if (_preferencesController.readingMode.value == ReadingMode.verticalScroll) {
      _readPageVerticalKey.currentState?.checkCurrentChapter();
    }

    await _onGetUnLockGoods();
    await _createLiveActivity();
  }

  void _setTryAgainTimer({bool isReset = false}) {
    // 重试定时任务
    if (isReset) {
      int count = 10;
      _loadTryAgainTimer = Timer.periodic(Duration(seconds: 1), (timer) {
        count -= 1;
        if (count <= 0) {
          timer.cancel();
          if (mounted) {
            setState(() {
              _isTryAgain = true;
            });
          }
        }
      });
    } else {
      if (_loadTryAgainTimer.isActive) {
        _loadTryAgainTimer.cancel();
      }
      _isTryAgain = false;
    }
  }

  void _setActivityTimer({bool isReset = false}) {
    // 活跃定时任务
    if (isReset) {
      _activityTimer = timerStartDateTime(1, 5 * 60, (_, bool isEnd) async {
        if (isEnd) {
          _setActivityTimer();
          // 阅读记录服务上报
          await reportReadRecord(
              ReadReportActivityType.activityTimeout, ReadReportReadingType.reading, false, true);
        }
      });
    } else {
      if (_activityTimer.isActive) {
        _activityTimer.cancel();
      }
    }
  }

  Future<void> getIllustrationAll() async {
    List list = await NovelReadViewModel.getIllustrationAll();
    _novelReadStatusController.setIllustrationList(list);
  }

  Future<void> getBookInfo(int? bookId) async {
    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    if (_isOfflineReading) {
      try {
        DownloadBookDetailInfoModel? localChapterModel = DBManager.instance.getNovel(_bookId);
        if (isAvailable(localChapterModel)) {
          _bookDetailInfoModel = novelDetailInfoModelMapping(localChapterModel);
        }
      } catch (e) {
        logP('getBookInfo error: $e');
      }
    }

    if (!isAvailable(_bookDetailInfoModel)) {
      _bookDetailInfoModel = await NovelReadViewModel.getBookDetailInfo(bookId, _pageJumpType);
    }

    ///一开始数据是空的，拿到数据后需要重新初始化数据
    if (_preferencesController.readingMode.value == ReadingMode.horizontalScroll) {
      _readPageHorizontalKey.currentState?.onInitData(_bookDetailInfoModel);
    } else {
      _readPageVerticalKey.currentState?.onInitData(_bookDetailInfoModel);
    }

    _novelReadStatusController.setAddLibrary(_bookDetailInfoModel?.library ?? false);
    _novelReadStatusController.setLiked(_bookDetailInfoModel?.liked ?? false);
    _novelReadStatusController.setLikes(_bookDetailInfoModel?.likes ?? '0');
    _novelReadStatusController.setFloatMenuOpacity(0.5);
  }

  Future<void> _onGetUnLockGoods() async {
    //获取用户信息
    await _getUserInfo();
    if (!_isOfflineReading) {
      //获取解锁商品列表
      List<GoodsListItem>? premiumGoodList = await NovelReadViewModel.getReadRecommendSubGoodList();
      _premiumGoodItem = premiumGoodList?.first;
      if (isAvailable(premiumGoodList)) {
        for (var item in premiumGoodList!) {
          if (isGoodsActivity(item.activityPricePower, item.activityType)) {
            _premiumGoodItem = item;
            break;
          }
        }
      }
      _subCoinGoodList = await NovelReadViewModel.getReadRecommendCoinGoodList();
    }

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _getUserInfo() async {
    _userInfoModel = await LoginViewModel.getUserInfo();
  }

  Future<void> dataReport() async {
    EventReportManager.eventReportOfCommon(viewContent);
    EventReportManager.logViewedContent();
    if (_pageJumpType == BookDetailJumpType.push) {
      DataReportViewModel.pushEventReport({'pushId': pushId, 'eventType': PushType.read.name});
    }
  }

  bool _checkIsBookContentAvailable() {
    return isAvailable(_bookDetailInfoModel?.contentList);
  }

  ///isResetChapter:是否重置章节，需要赋值当前章节与所重置章节的样式，防止未重置样式而调用次方法
  ///isResetBySetting:是否重置样式，需要重新计算章节内容展示样式
  Future<void> resetContent(int chapterId, PageJumpType jumpType, bool isResetBySetting) async {
    try {
      //如果是重置样式，则重置章节内容显示
      if (isResetBySetting) {
        currentArticle = await paginateContent(currentArticle);
        preArticle = await paginateContent(preArticle);
        nextArticle = await paginateContent(nextArticle);
      } else {
        currentArticle = await getChapterContent(chapterId, ChapterQueryType.current);
        preArticle = await getChapterContent(currentArticle!.id, ChapterQueryType.previous);
        nextArticle = await getChapterContent(currentArticle!.id, ChapterQueryType.next);
      }

      if (mounted) {
        setState(() {});
      }

      if (jumpType == PageJumpType.firstPage) {
        pageIndex = 0;
      } else if (jumpType == PageJumpType.lastPage) {
        pageIndex = currentArticle!.pageCount - 1;
      }

      Future.delayed(Duration(milliseconds: 100), () {
        onPageJump((preArticle != null ? preArticle!.pageCount : 0) + pageIndex);
      });
    } catch (e) {
      logP('resetContent error: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  //TODO: 获取章节内容
  Future<ContentModel?> getChapterContent(int? chapterId, ChapterQueryType queryType) async {
    try {
      if (isAvailable(chapterId)) {
        //当前是需要购买的章节
        ContentModel? contentModel;
        if (isAvailable(_bookDetailInfoModel?.contentList)) {
          int? chapterIndex;
          for (int i = 0; i < _bookDetailInfoModel!.contentList!.length; i++) {
            ContentModel? content = _bookDetailInfoModel?.contentList![i];
            if (content?.id == chapterId) {
              chapterIndex = i;
              contentModel = content;
              break;
            }
          }

          if (isAvailable(chapterIndex)) {
            if (queryType == ChapterQueryType.previous) {
              if (chapterIndex == 0) {
                contentModel = null;
              } else {
                contentModel = _bookDetailInfoModel?.contentList![chapterIndex! - 1];
              }
            } else if (queryType == ChapterQueryType.next) {
              if (chapterIndex == _bookDetailInfoModel!.contentList!.length - 1) {
                contentModel = null;
              } else {
                contentModel = _bookDetailInfoModel?.contentList![chapterIndex! + 1];
              }
            }
          }
        }

        if (isAvailable(contentModel)) {
          if (queryType == ChapterQueryType.none) {
            return contentModel;
          } else {
            return paginateContent(contentModel);
          }
        } else {
          return null;
        }
      } else {
        return null;
      }
    } catch (e) {
      logP('getChapterContent error: $e');
      return null;
    }
  }

  //TODO: 章节内容分页
  Future<ContentModel?> paginateContent(ContentModel? contentModel) async {
    var contentHeight = DeviceScreenUtil.instance.height -
        DeviceScreenUtil.instance.topSafeHeight -
        ReaderUtil.pageTopOffset -
        DeviceScreenUtil.instance.bottomSafeHeight -
        ReaderUtil.pageBottomOffset -
        _preferencesController.getOffset();
    var contentWidth = DeviceScreenUtil.instance.width -
        _preferencesController.horizontalPadding.value * 2 -
        _preferencesController.getOffset();
    ChapterVoModel? currentChapter = queryChapterList(contentModel?.id);
    Map<String, dynamic> object = NovelPageAgent.paginateText(
        contentModel?.getContent() ?? '',
        contentHeight,
        contentWidth,
        TextStyle(
            fontSize: _preferencesController.fontSize.value,
            fontWeight: ReaderUtil.instance.fontWeight,
            height: _preferencesController.lineSpace.value,
            fontFamily: _preferencesController.fontFamily.value),
        firstShowLineCount: currentChapter?.lock == 1 ? 2 : 0);
    List<String> pages = object['pages'];
    if (contentModel?.id == _bookDetailInfoModel?.contentList?.first?.id) {
      pages.insert(0, 'Synopsis');
    }
    contentModel?.pages = pages;
    contentModel?.firstShowLineHeight = object['firstShowLineHeight'];

    return contentModel;
  }

  //TODO: 书籍数据转换
  BookDetailInfoResultModel? novelDetailInfoModelMapping(
      DownloadBookDetailInfoModel? bookDetailInfoModel) {
    return DBManager.instance.novelDataTurnToNormalObject(bookDetailInfoModel);
  }

  Future<void> _goBack() async {
    _isExitPage = true;
    _hideMenu();

    ///挽留弹窗
    bool isShowPremium = _currentChapter?.lock == 1;
    if (isShowPremium && pageIndex == 0) {
      //解锁页面场景
      int isShowReminder = await SpUtil.spGetIsUnlockReminderAlterGroupShow();
      //返回1：展示收费弹窗，0：展示免费弹窗，-1：不展示弹窗
      if (isShowReminder != -1) {
        if (isShowReminder == 0) {
          showBookBottomSheet(
              context,
              NovelUnLockReminderContent(onBookClick: (item) async {
                //去往阅读页
                await LocalNotificationManager.toNovelReadPage(
                    {'bookId': item?.id, 'jumpType': BookDetailJumpType.other});
              }, onFreeSecondPage: () async {
                ///二级页面
                await Get.toNamed('/secondaryListPage', arguments: {
                  "title": 'columns_free'.tr,
                  'code': 'Hurry_Free_for_Now',
                  'pageType': SecondaryPageType.formHomeReadSection
                });
              }),
              null,
              isDismissible: true,
              enableDrag: false);

          await SpUtil.spSetUnlockReminderAlterGroupShowTime(getCurrentTimeSeconds());
        } else if (isShowReminder == 1) {
          showBookBottomSheet(
              context,
              NovelReadingReminderContent(onBookClick: (item) async {
                //去往阅读页
                await LocalNotificationManager.toNovelReadPage(
                    {'bookId': item?.id, 'jumpType': BookDetailJumpType.other});
              }, onBack: () async {
                await handleBack();
              }),
              null,
              isDismissible: true,
              enableDrag: false);
        }

        await SpUtil.spSetUnlockReminderAlterGroupShowTimes();
      } else {
        await handleBack();
      }
    } else {
      //正常阅读页场景
      bool isShowReminder = await SpUtil.spGetIsReadingReminderAlterShow();
      if (isShowReminder) {
        showBookBottomSheet(
            context,
            NovelReadingReminderContent(onBookClick: (item) async {
              //去往阅读页
              await LocalNotificationManager.toNovelReadPage(
                  {'bookId': item?.id, 'jumpType': BookDetailJumpType.other});
            }, onBack: () async {
              await handleBack();
            }),
            null,
            isDismissible: true,
            enableDrag: false);
        await SpUtil.spSetReadingReminderAlterShowTime(getCurrentTimeSeconds());
      } else {
        await handleBack();
      }
    }

    if (isSignInSwitchOn) {
      //检查是否已经显示过 Alter
      bool hasShownAlter = await SpUtil.spGetHasShownAlter();
      UserInfoModel? userModel = await SpUtil.spGetUserInfo();
      if (!hasShownAlter && userModel?.newUser == true) {
        showAlter(SignInPopUpWidget(),
            backgroundColor: const Color.fromARGB(0, 255, 255, 255), barrierDismissible: true);
        await SpUtil.spSetHasShownAlter();
      }
    }
  }

  // TODO: 下载书籍
  void onNovelDownload() {
    NovelDownload.instance.onNovelDownload(context, _bookDetailInfoModel,
        onLoadingCallBack: (isLoading) {
      if (mounted && _isLoading != isLoading) {
        setState(() {
          _isDownload = isLoading;
          _isLoading = isLoading;
        });
      }
    }, onRecharged: () async {
      await _onGetUnLockGoods();
    }, onFinished: (detailInfoModel) async {
      _isDownload = false;
      await _onGetUnLockGoods();
    });

    EventReportManager.eventReportOfFirebase(clickReadDownload);
  }

  Future<void> _shareNovel(ShareType type) async {
    // TODO: 获取分享链接
    if (mounted) {
      setState(() {
        _isDownload = true;
        _isLoading = true;
      });
    }
    ShareBookUrlModel? model = await CommonManager.getShareBookUrl(_bookId);
    if (mounted) {
      setState(() {
        _isDownload = false;
        _isLoading = false;
      });
    }
    ShareManager.instance.shareNovel(
        _bookDetailInfoModel?.title,
        _bookDetailInfoModel?.description,
        _bookDetailInfoModel?.cover, // 传递封面图URL
        model,
        type,
        context: context); // 传递context
  }

  Future<void> _onScroll() async {
    if (!isAvailable(currentArticle)) {
      return;
    }

    var mediaQuery = MediaQuery.of(context);
    var page = _pageController.offset / mediaQuery.size.width;

    var nextArticlePage =
        currentArticle!.pageCount + (preArticle != null ? preArticle!.pageCount : 0);

    if (!isAvailable(preArticle) && page == 0) {
      logP('已经是第一章节了');
      return;
    }

    if (page >= nextArticlePage) {
      if (isAvailable(nextArticle)) {
        logP('到达下个章节了');
        preArticle = currentArticle;
        currentArticle = nextArticle;
        _updateCurrentChapter();
        if (mounted) {
          setState(() {});
        }
        nextArticle = await getChapterContent(currentArticle?.id, ChapterQueryType.next);
        if (mounted) {
          setState(() {});
        }
        pageIndex = preArticle!.pageCount;
        onPageJump(pageIndex);

        ///保存用户阅读记录
        await DataReportViewModel.reportUserReadingHistory(currentArticle?.id);
      } else {
        logP('已经是最后一章节了');
      }
    }
    if (preArticle != null && page <= preArticle!.pageCount - 1) {
      logP('到达上个章节了');

      nextArticle = currentArticle;
      currentArticle = preArticle;
      _updateCurrentChapter();
      //需要先刷新，等不然快速滑动，前一张需要解锁，会导致没内容
      if (mounted) {
        setState(() {});
      }
      preArticle = await getChapterContent(currentArticle?.id, ChapterQueryType.previous);
      if (mounted) {
        setState(() {});
      }
      pageIndex = (preArticle?.pageCount ?? 0) + (currentArticle?.pageCount ?? 0) - 1;
      onPageJump(pageIndex);

      ///保存用户阅读记录
      await DataReportViewModel.reportUserReadingHistory(currentArticle?.id);
    }
  }

  Future<void> _updateCurrentChapter() async {
    _currentChapter = queryChapterList(currentArticle?.id);
    await reportReadRecord(
        ReadReportActivityType.reading, ReadReportReadingType.reading, false, false);

    //更新activity状态
    await _updateLiveActivity();
  }

  Future _createLiveActivity() async {
    //创建activity状态
    String? path = await LiveActivitiesManager.instance
        .downloadAndSaveCover(_bookDetailInfoModel?.cover, 174, 232);
    _curActivityModel = NovelActivitiesModel(
        bookId: _bookDetailInfoModel?.bookId,
        coverUrl: LiveActivityFileFromAsset.image(
          path ?? 'assets/images/common/icon_activity_cover.png',
        ),
        bookName: _bookDetailInfoModel?.title ?? '',
        chapterTitle: 'tips_chapter'.tr,
        chapterIndex: _currentChapter?.chapterIndex ?? 1,
        continueTitle: 'continue_reading'.tr);
    LiveActivitiesManager.instance.createLiveActivity(_curActivityModel);
  }

  Future _updateLiveActivity() async {
    //更新activity状态
    _curActivityModel?.chapterIndex = _currentChapter?.chapterIndex;
    if (isAvailable(_curActivityModel)) {
      await LiveActivitiesManager.instance.updateLiveActivity(_curActivityModel);
    }
  }

  Future<void> onPageChanged(int index) async {
    ///更新为前阅读章节的页面索引，不然就包含了前面章节的页数，从而导致进来定位不准
    int page = (preArticle?.pageCount ?? 0) +
        (currentArticle?.pageCount ?? 0) +
        (nextArticle?.pageCount ?? 0);
    if (page < index + 1) {
      if (isAvailable(currentArticle)) {
        pageIndex = currentArticle!.pageCount - 1;
      } else {
        pageIndex = 0;
      }
    } else {
      _currentNextBookIndex = null;
      pageIndex = index - (preArticle?.pageCount ?? 0);
    }

    if (isAvailable(currentArticle)) {
      if (pageIndex == currentArticle!.pageCount - 1) {
        //如果是最后一页，更新完成状态
        DataReportManager.instance
            .updateLastChapter(pointType: ReadReportActivityType.reading, onlySetFinish: true);
      }
    }

    if (_isMenuVisible) {
      _hideMenu();
    }
  }

  //TODO: 页面跳转
  void onPageJump(int pageIndex) {
    try {
      if (isAvailable(_nextBookInfoList) && isAvailable(_currentNextBookIndex)) {
        int page = (preArticle?.pageCount ?? 0) +
            (currentArticle?.pageCount ?? 0) +
            (nextArticle?.pageCount ?? 0) +
            _currentNextBookIndex!;
        if (_pageController.positions.isNotEmpty) {
          _pageController.jumpToPage(page);
        }
      } else {
        if (_pageController.positions.isNotEmpty) {
          _pageController.jumpToPage(pageIndex);
        }
      }
    } catch (e) {
      logP(e.toString());
    }
  }

  //TODO: 查询章节
  ChapterVoModel? queryChapterList(int? id) {
    if (isAvailable(_bookDetailInfoModel?.chapterVoList)) {
      for (var item in _bookDetailInfoModel!.chapterVoList!) {
        //章节列表中存在该章节,设置为当前章节
        if (item?.id == id) {
          return item;
        }
      }
    }

    return null;
  }

  //TODO: 查询章节
  ContentModel? queryContentList(int? id) {
    if (isAvailable(_bookDetailInfoModel?.contentList)) {
      for (var item in _bookDetailInfoModel!.contentList!) {
        //章节列表中存在该章节,设置为当前章节
        if (item?.id == id) {
          return item;
        }
      }
    }

    return null;
  }

  //TODO: 存储阅读记录上报
  Future<void> reportReadRecord(ReadReportActivityType pointType, ReadReportReadingType readType,
      bool finish, bool isReport) async {
    DataReportManager.instance
        .recordReadChapter(_currentChapter, pointType, readType, finish, isReport);
  }

  //TODO: 存储阅读记录
  Future<void> setNovelReadBookChapter(bool isAdd) async {
    NovelReadChapterModel readBookChapterModel =
        NovelReadChapterModel(_currentChapter?.bookId, chapterId: 0, chapterIndex: 0, pageIndex: 0);
    if (isAvailable(_currentChapter?.bookId) && isAvailable(_currentChapter?.id)) {
      readBookChapterModel = NovelReadChapterModel(_currentChapter?.bookId,
          chapterId: _currentChapter?.id,
          chapterIndex: _currentChapter?.chapterIndex,
          pageIndex: pageIndex);
    }
    if (isAdd) {
      DBManager.instance.addNovelReadBookChapter(readBookChapterModel);
    } else {
      DBManager.instance.updateNovelReadBookChapter(readBookChapterModel);
    }

    //更新最近阅读记录
    NovelReadRecentlyModel recentlyModel = NovelReadRecentlyModel(readBookChapterModel.bookId,
        chapterId: readBookChapterModel.chapterId,
        chapterIndex: readBookChapterModel.chapterIndex,
        pageIndex: readBookChapterModel.pageIndex);
    DBManager.instance.addRecentlyReadBook(recentlyModel);
  }

  //设置章节解锁状态
  Future<void> _onUnlockStatusChanged(int? chapterId) async {
    if (!isAvailable(chapterId)) {
      return;
    }
    for (var item in _bookDetailInfoModel?.chapterVoList ?? []) {
      if (item.id == chapterId) {
        if (item.lock == 1) {
          item.lock = 2;
          item.chapterUnLockKey = null;
          _currentChapter = item;
          await paginateContent(queryContentList(chapterId));
        }
        break;
      }
    }
  }

  //todo: 书籍章节购买回调
  Future<void> _onRechargeCallBack(int? chapterId) async {
    // 购买成功，更新章节列表
    await _onUnlockStatusChanged(chapterId);
    await _onGetUnLockGoods();
  }

  Future<void> isShowGuideView() async {
    if (await SpUtil.isFirstGuide()) {
      showAlter(NovelReadingGuideContent(onTap: () {
        showAlter(const NovelReadingMenuGuideContent(),
            backgroundColor: ColorsUtil.hexColor(0x000000, alpha: 0.1), useSafeArea: false);
      }), backgroundColor: ColorsUtil.hexColor(0x000000, alpha: 0.1), useSafeArea: false);
      SpUtil.spSetFirstGuideState();
    }
  }

  Future<void> onTap(BuildContext context, Offset position) async {
    var size = MediaQuery.of(context).size;
    double xRate = position.dx / size.width;
    if (xRate > 0.33 && xRate < 0.66) {
      if (_isMenuVisible) {
        _hideMenu();
      } else {
        _showMenu(context);
      }
    } else {
      if (_currentChapter?.lock == 1) {
        return;
      }

      if (xRate >= 0.66) {
        nextPage();
      } else {
        previousPage();
      }
    }
  }

  void previousPage() {
    if (_pageController.positions.isNotEmpty) {
      var page = _pageController.offset / DeviceScreenUtil.instance.width;
      if (currentArticle?.id == _bookDetailInfoModel?.contentList?.first?.id && page == 0) {
        logP('已经是第一章节了');
        return;
      }
      _pageController.previousPage(
          duration: const Duration(milliseconds: 300), curve: Curves.easeOut);
      if (_pageController.positions.isEmpty) {
        return;
      }
    }
  }

  void nextPage() {
    if (_pageController.positions.isNotEmpty) {
      _pageController.nextPage(duration: const Duration(milliseconds: 300), curve: Curves.easeOut);
    }
  }

  bool isChapterStyleNeedSync() {
    if (_currentPreferencesSettingModel.fontSize != _preferencesController.fontSize.value ||
        _currentPreferencesSettingModel.lineSpace != _preferencesController.lineSpace.value ||
        _currentPreferencesSettingModel.horizontalPadding !=
            _preferencesController.horizontalPadding.value ||
        _currentPreferencesSettingModel.fontType != _preferencesController.fontType.value) {
      ///暂存字体大小、行距、页面边距、字体改变，重新计算页面
      _currentPreferencesSettingModel.fontSize = _preferencesController.fontSize.value;
      _currentPreferencesSettingModel.lineSpace = _preferencesController.lineSpace.value;
      _currentPreferencesSettingModel.horizontalPadding =
          _preferencesController.horizontalPadding.value;
      _currentPreferencesSettingModel.fontType = _preferencesController.fontType.value;

      return true;
    }

    return false;
  }

  void _showMenu(BuildContext context) {
    _updateFloatingEnterOpacity(0.6);

    if (_isMenuVisible) {
      return;
    }

    _isMenuVisible = true;
    final overlay = Overlay.of(context);
    if (isAvailable(_overlayEntry)) {
      try {
        if (_overlayEntry.mounted) {
          _overlayEntry.remove();
        }
        overlay.insert(_overlayEntry);
      } catch (e) {
        logP(e.toString());
      }
    }
  }

  Future<void> _hideMenu({double opacity = 0.6}) async {
    _updateFloatingEnterOpacity(opacity);

    if (!_isMenuVisible) {
      return;
    }

    _isMenuVisible = false;
    _menuKey.currentState?.hide();
    if (isAvailable(_overlayEntry)) {
      Timer(const Duration(milliseconds: 500), () {
        try {
          if (_overlayEntry.mounted) {
            _overlayEntry.remove();
          }
        } catch (e) {
          logP(e.toString());
        }
      });
    }

    if (_isExitPage) return;

    //todo: 根据设置重新计算页面
    /// 字体边距改变，重新计算页面
    if (isChapterStyleNeedSync()) {
      //字体边距改变，重新计算页面
      await resetContent(_currentChapter?.id ?? 0, PageJumpType.stay, true);
    }
  }

  //页面滑动状态
  void _onReaderScroll(ScrollNotification notification) {
    if (notification is ScrollStartNotification) {
      _hideMenu();

      ///取消活跃定时器
      _setActivityTimer();
    } else if (notification is ScrollEndNotification) {
      ///重置活跃定时器
      _setActivityTimer(isReset: true);
    }
  }

  //初始化下一本书
  void _onNextBookTap(BookDetailInfoResultModel? bookDetailInfo) {
    _bookId = bookDetailInfo?.bookId ?? 0;
    pageIndex = 0;
    _currentChapter = null;
    fetchData(true);
    dataReport();
    if (_pageController.positions.isEmpty) {
      return;
    }
    _pageController.nextPage(duration: const Duration(milliseconds: 300), curve: Curves.easeOut);
  }

  //浮窗入口点击
  Future<void> _updateFloatingEnterOpacity(double opacity) async {
    if (_novelReadStatusController.floatMenuOpacity.value != opacity) {
      _novelReadStatusController.setFloatMenuOpacity(opacity);
    }
  }

  // 上下滑动阅读模式
  ReadPageVertical _buildVerticalReader() {
    List<String> contentList = [];
    if (isAvailable(preArticle?.pages)) {
      contentList.addAll(preArticle!.pages!);
    }
    if (isAvailable(currentArticle?.pages)) {
      contentList.addAll(currentArticle!.pages!);
    }
    if (isAvailable(nextArticle?.pages)) {
      contentList.addAll(nextArticle!.pages!);
    }

    return ReadPageVertical(
      key: _readPageVerticalKey,
      bookDetailInfo: _bookDetailInfoModel,
      chapterIndex: _currentChapter?.chapterIndex ?? 0,
      page: pageIndex,
      pageJumpType: _pageJumpType,
      currentNextBookIndex: _currentNextBookIndex,
      nextBookInfoList: _nextBookInfoList,
      userInfoModel: _userInfoModel,
      premiumGoodItem: _premiumGoodItem,
      subCoinGoodList: _subCoinGoodList,
      preferencesController: _preferencesController,
      novelReadStatusController: _novelReadStatusController,
      onRechargeCallBack: _onRechargeCallBack,
      onScrollCallBack: _onReaderScroll,
      onJumpCallBack: () {
        _hideMenu();
      },
      onRefreshDataBack: (info) async {
        await _onGetUnLockGoods();
        if (isAvailable(info?['orderSnStr'])) {
          DataReportViewModel.pushEventReport({
            'pushId': pushId,
            'eventType': PushType.pay.name,
            'orderSnStr': info?['orderSnStr'],
            'salesMoney': info?['salesMoney']
          });
        }
      },
      onScrollPageOffset: (chapterId, chapterIndex, page, currentIndex, nextBookInfoList) async {
        pageIndex = page;
        _modelChangeIsTheSameChapter = true;
        _currentNextBookIndex = currentIndex;
        _nextBookInfoList = nextBookInfoList;
        if (_currentChapter?.id == chapterId) {
          //当前章节,如果是最后一页，算完成,只更新章节完成状态
          if (isAvailable(currentArticle)) {
            //如果页面偏移计算大于章节页数，则重置为章节页数
            if (currentArticle!.pageCount < page) {
              pageIndex = currentArticle!.pageCount;
            }

            //如果是最后一页，更新完成状态
            if (pageIndex == currentArticle?.pageCount) {
              DataReportManager.instance.updateLastChapter(
                  pointType: ReadReportActivityType.reading, onlySetFinish: true);
            }
          }
        } else {
          //下一章
          _modelChangeIsTheSameChapter = false;
          _currentChapter = queryChapterList(chapterId);
          currentArticle = await getChapterContent(_currentChapter?.id, ChapterQueryType.none);

          await reportReadRecord(
              ReadReportActivityType.reading, ReadReportReadingType.reading, false, false);

          ///保存用户阅读记录
          await DataReportViewModel.reportUserReadingHistory(chapterId);

          //更新activity状态
          await _updateLiveActivity();
        }
      },
      onNextBookTap: _onNextBookTap,
    );
  }

  //左右翻页阅读模式
  ReadPageHorizontal _buildHorizontalReader() {
    return ReadPageHorizontal(
        key: _readPageHorizontalKey,
        currentChapter: _currentChapter,
        preArticle: preArticle,
        currentArticle: currentArticle,
        nextArticle: nextArticle,
        pageController: _pageController,
        bookDetailInfo: _bookDetailInfoModel,
        chapterListLength: _bookDetailInfoModel?.chapterVoList?.length ?? 0,
        pageJumpType: _pageJumpType,
        nextBookInfoList: _nextBookInfoList,
        preferencesController: _preferencesController,
        novelReadStatusController: _novelReadStatusController,
        userInfoModel: _userInfoModel,
        premiumGoodItem: _premiumGoodItem,
        subCoinGoodList: _subCoinGoodList,
        onRechargeCallBack: _onRechargeCallBack,
        onScrollCallBack: _onReaderScroll,
        onJumpCallBack: () {
          _hideMenu();
        },
        onRefreshDataBack: (info) async {
          await _onGetUnLockGoods();
          if (isAvailable(info?['orderSnStr'])) {
            DataReportViewModel.pushEventReport({
              'pushId': pushId,
              'eventType': PushType.pay.name,
              'orderSnStr': info?['orderSnStr'],
              'salesMoney': info?['salesMoney']
            });
          }
        },
        onPageChanged: onPageChanged,
        onNextBookTap: _onNextBookTap,
        onNextBookCallBack: (currentIndex, nextBookInfoList) {
          _currentNextBookIndex = currentIndex;
          _nextBookInfoList = nextBookInfoList;
        });
  }

  Obx _buildPageView(BuildContext context) {
    return Obx(() {
      return Container(
          width: double.infinity,
          height: double.infinity,
          color: HexColor(_preferencesController.themeColor.value),

          /// 允许 PageView 和 ListView 在一起使用,并管理它们的滑动事件
          child: _preferencesController.readingMode.value == ReadingMode.verticalScroll
              ? _buildVerticalReader()
              : _buildHorizontalReader());
    });
  }

  NovelMenu _buildMenu() {
    return NovelMenu(
        key: _menuKey,
        arguments: widget.arguments,
        detailInfoResultModel: _bookDetailInfoModel,
        currentChapterId: _currentChapter?.id,
        preferencesController: _preferencesController,
        onTopItemsTap: (index) async {
          _hideMenu();
          if (index == -1) {
            //todo:去往书籍详情页
            await Get.toNamed('/bookDetailPage',
                arguments: {"bookDetailInfo": _bookDetailInfoModel, 'jumpType': _pageJumpType});
          } else if (index == 0) {
            //todo: 返回上一页
            _goBack();
          } else if (index == 1) {
            //todo: 下载小说
            onNovelDownload();
          } else if (index == 2) {
            showBookBottomSheet(context, BookShareSheet(onShareTap: (type) async {
              await _shareNovel(type);
            }), HexColor('#F4F5F6'));

            EventReportManager.eventReportOfFirebase(clickReadShare);
          } else if (index == 3) {
            showAlter(ReportErrorContent(
              reportType: ReportType.bookRead,
              onConfirm: (content) async {
                await errorReport(ReportType.bookRead, content, bookId: _bookId);
              },
            ));

            EventReportManager.eventReportOfFirebase(clickReadReport);
          }
        },
        onChaptersTap: (item) async {
          _currentNextBookIndex = null;

          if (_bookDetailInfoModel?.chapterVoList?.length == 1 || _currentChapter?.id == item.id) {
            return;
          }
          _currentChapter = item;

          EventReportManager.eventReportOfFirebase(clickReadChapter);

          //todo: 先判断列表是否只有一条，或者是切换的章节鱼当前的相同章节，如果是，不做任何操作
          if (_preferencesController.readingMode.value == ReadingMode.verticalScroll) {
            // 竖屏阅读模式下，章节列表点击，直接切到对应的章节

            _readPageVerticalKey.currentState?.onItemTap(item?.chapterIndex - 1);
          } else {
            await resetContent(_currentChapter?.id ?? 0, PageJumpType.firstPage, false);
            reportReadRecord(
                ReadReportActivityType.reading, ReadReportReadingType.reading, false, false);
          }
        },
        onReadingModelChanged: () async {
          logP('reading model转换');
          if (_preferencesController.readingMode.value == ReadingMode.horizontalScroll) {
            Future.delayed(const Duration(milliseconds: 500), () async {
              if (_modelChangeIsTheSameChapter) {
                await resetContent(_currentChapter?.id ?? 0, PageJumpType.stay, false);
              } else {
                _currentChapter = queryChapterList(currentArticle?.id);
                await resetContent(_currentChapter?.id ?? 0, PageJumpType.firstPage, false);
              }
            });
          }
        },
        onDismiss: _hideMenu);
  }

  Widget _buildFloatMenuOpacity() {
    return Obx(
      () {
        double itemWidth = _novelReadStatusController.floatMenuOpacity.value * 55;
        double itemPadding = _novelReadStatusController.floatMenuOpacity.value * 15;

        return Padding(
            padding: EdgeInsets.only(right: 2),
            child: AnimatedOpacity(
              opacity: _novelReadStatusController.floatMenuOpacity.value,
              duration: Duration(milliseconds: 500),
              child: DecoratedBox(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: (_isShowChatEntry ||
                            _isShowVoiceEntry && !audioBookController.isShowFloating.value)
                        ? ColorsUtil.hexColor(0x000000,
                            alpha: _novelReadStatusController.floatMenuOpacity.value + 0.2)
                        : Colors.transparent),
                child: Padding(
                    padding: EdgeInsets.all(5),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (_isShowChatEntry)
                          AnimatedContainer(
                            width: itemWidth,
                            height: itemWidth,
                            duration: Duration(milliseconds: 500),
                            child: IconButton(
                                onPressed: () async {
                                  if (_novelReadStatusController.floatMenuOpacity.value == 1.0) {
                                    await Get.toNamed('/chatNavBarPage',
                                        arguments: {'bookId': _bookId});
                                    _updateFloatingEnterOpacity(0.6);
                                    await setNovelReadBookChapter(false);

                                    _hideMenu(opacity: 0.6);
                                  } else {
                                    _hideMenu(opacity: 1.0);
                                  }
                                },
                                style: IconButton.styleFrom(padding: EdgeInsets.zero),
                                icon:
                                    Image.asset('assets/images/novelReading/icon_chat_enter.png')),
                          ),
                        if (_isShowVoiceEntry && !audioBookController.isShowFloating.value)
                          Padding(
                            padding: EdgeInsets.only(top: _isShowChatEntry ? itemPadding : 0),
                            child: AnimatedContainer(
                              width: itemWidth,
                              height: itemWidth,
                              duration: Duration(milliseconds: 500),
                              child: IconButton(
                                  onPressed: () async {
                                    if (_novelReadStatusController.floatMenuOpacity.value == 1.0) {
                                      await Get.to(() => ListeningBookPage(
                                          detailInfoModel: _bookDetailInfoModel,
                                          currentChapter: _currentChapter,
                                          bookUpdateCallBack: (bookDetailInfo, currChapter) async {
                                            /// 书籍更新回调
                                            _bookDetailInfoModel = bookDetailInfo;
                                            pageIndex = 0;
                                            _currentChapter = currChapter;
                                            //章节列表中存在该章节,设置为当前章节

                                            if (isAvailable(_currentChapter)) {
                                              Future.delayed(const Duration(milliseconds: 100),
                                                  () async {
                                                audioBookController.setShowFloating(true);
                                                if (_preferencesController.readingMode.value ==
                                                    ReadingMode.verticalScroll) {
                                                  _readPageVerticalKey.currentState?.onItemTap(
                                                      _currentChapter!.chapterIndex! - 1);
                                                } else {
                                                  await resetContent(currChapter!.id!,
                                                      PageJumpType.firstPage, false);
                                                }
                                              });
                                            }
                                          }));
                                      _hideMenu(opacity: 0.6);
                                    } else {
                                      _hideMenu(opacity: 1.0);
                                    }
                                  },
                                  style: IconButton.styleFrom(padding: EdgeInsets.zero),
                                  icon: Image.asset(
                                      'assets/images/novelReading/icon_listening_enter.png')),
                            ),
                          )
                      ],
                    )),
              ),
            ));
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    return PopScope(
        onPopInvokedWithResult: (didPop, result) async {
          _hideMenu();
          isPagePop = true;
          //todo: 退出阅读页，保存偏好设置
          _preferencesController.setPreferencesSettings();
          await setNovelReadBookChapter(false);
          await reportReadRecord(
              ReadReportActivityType.exitReading, ReadReportReadingType.reading, false, true);
          _bookDetailInfoModel?.library = _novelReadStatusController.isAddLibrary.value;
          _bookDetailInfoModel?.liked = _novelReadStatusController.isLiked.value;
          _bookDetailInfoModel?.likes = '${_novelReadStatusController.likes.value}';
          if (_isOfflineReading) {
            DownloadBookDetailInfoModel? model =
                DBManager.instance.novelDataTurnToRealmObject(_bookDetailInfoModel);
            if (isAvailable(model)) {
              DBManager.instance.updateObject<DownloadBookDetailInfoModel>(model!);
            }
          }
          //TODO:推送授权
          await AlterManager.instance
              .showNotificationAlter(NotificationAlterSource.firstPopReadPage);
        },
        canPop: false,
        child: Scaffold(
            backgroundColor: HexColor(_preferencesController.themeColor.value),
            body: GestureDetector(
                onTapUp: (TapUpDetails details) {
                  onTap(context, details.globalPosition);
                },
                behavior: HitTestBehavior.opaque,
                child: Stack(
                  children: [
                    if (_isNoData)
                      Obx(() => DecoratedBox(
                          decoration: BoxDecoration(
                              color: HexColor(_preferencesController.themeColor.value)),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              NoLoadView(),
                              Padding(
                                  padding: EdgeInsets.only(left: 10, top: 18, right: 10),
                                  child: Text.rich(TextSpan(
                                      text: 'failed_load'.tr,
                                      style: TextStyle(
                                          color: HexColor('#555A65'),
                                          fontSize: 14,
                                          fontWeight: FontWeight.bold)))),
                            ],
                          )))
                    else
                      _buildPageView(context),
                    if (_isLoading)
                      LottieAnimationView(
                          key: _lottieAnimationKey,
                          isFromReadPage: !_isDownload,
                          isLoadingTimeOut: _isTryAgain,
                          netWorkStatusController: _netWorkStatusController),
                    if (_isNoData || _isTryAgain)
                      Positioned(
                          bottom: 70,
                          left: 0,
                          right: 0,
                          child: TextButton(
                              onPressed: () {
                                if (_netWorkStatusController.isConnectivity.value) {
                                  if (mounted) {
                                    setState(() {
                                      _isNoData = false;
                                      _isTryAgain = false;
                                    });
                                  }
                                  fetchData(true);
                                } else {
                                  showToast('no_network'.tr);
                                }
                              },
                              child: Text('try_again'.tr,
                                  style: TextStyle(
                                      color: HexColor('#1B86FF'),
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold)))),
                    if (_isShowChatEntry || _isShowVoiceEntry)
                      Positioned(
                          right: 0,
                          bottom: mediaQuery.padding.bottom + 70,
                          child: _buildFloatMenuOpacity())
                  ],
                ))));
  }
}
