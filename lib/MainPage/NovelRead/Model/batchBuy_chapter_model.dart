import 'package:json_annotation/json_annotation.dart';
part 'batchBuy_chapter_model.g.dart';

@JsonSerializable()
class BatchBuyChapterModel {
  int? code;
  BatchBuyChapterResultModel? result;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  BatchBuyChapterModel({this.code, this.result, this.msg, this.sysAt, this.cost, this.traceId});

  factory BatchBuyChapterModel.fromJson(Map<String, dynamic> json) => _$BatchBuyChapterModelFromJson(json);
  Map<String, dynamic> toJson() => _$BatchBuyChapterModelToJson(this);
}

@JsonSerializable()
class BatchBuyChapterResultModel {
  String? msg;
  int? code;
  int? coins;
  List<String?>? buyList;
  List<String?>? allList;
  int? batchId;
  int? balance;
  int? voucher;
  bool? succ;

  BatchBuyChapterResultModel({this.msg, this.code, this.coins, this.buyList, this.allList, this.batchId, this.balance, this.voucher, this.succ});

  factory BatchBuyChapterResultModel.fromJson(Map<String, dynamic> json) => _$BatchBuyChapterResultModelFromJson(json);
  Map<String, dynamic> toJson() => _$BatchBuyChapterResultModelToJson(this);
}