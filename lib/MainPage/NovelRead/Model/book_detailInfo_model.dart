import 'package:flutter/cupertino.dart';
import 'package:json_annotation/json_annotation.dart';

import '../../../Util/encryptUtil.dart';
import '../../../Util/tools.dart';
import '../Page/ChapterUnlock/chapter_unlock_view.dart';
import 'content_list_model.dart';

part 'book_detailInfo_model.g.dart';

//todo: 为书籍详情信息模型 与 书籍下载模型 合并共用模型

@JsonSerializable()
class BookDetailInfoModel {
  int? code; //状态码
  BookDetailInfoResultModel? result;
  String? msg; //返回信息
  int? sysAt; //时间
  int? cost; //耗时
  String? traceId; //日志跟踪id

  BookDetailInfoModel({
    this.code,
    this.result,
    this.msg,
    this.sysAt,
    this.cost,
    this.traceId,
  });

  factory BookDetailInfoModel.fromJson(Map<String, dynamic> json) =>
      _$BookDetailInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$BookDetailInfoModelToJson(this);
}

@JsonSerializable()
class BookDetailInfoResultModel {
  int? bookId; //书籍id
  int? nextBookId; //下一本书籍id
  int? bookLangId; //书籍语言id
  String? authorName; //作者名称
  int? authorId; //作者id
  String? authorCover; //作者头像
  String? authorDescription; //作者描述
  String? title; //标题
  String? cover; //封面
  List<String>? tagList; //标签列表
  String? reads; //阅读数
  String? likes; //收藏数
  String? time; //阅读时长
  String? description; //描述
  bool? library; //是否加入书架
  bool? liked; //是否加入收藏
  int? bookGoldCount; //书籍金币数
  String? contentUrl; //书籍内容地址
  int? lock; //书籍锁的状态（0-解锁，1-试看，需要购买，2-已购买） 只有书籍下载才有这个字段
  bool? hasChat; //是否有聊天功能
  List<ChapterVoModel?>? chapterVoList; //章节数据
  List<ContentModel?>? contentList; //章节内容数据

  BookDetailInfoResultModel({
    this.bookId,
    this.nextBookId,
    this.bookLangId,
    this.authorName,
    this.authorId,
    this.authorCover,
    this.authorDescription,
    this.title,
    this.cover,
    this.tagList,
    this.reads,
    this.likes,
    this.time,
    this.description,
    this.library,
    this.liked,
    this.bookGoldCount,
    this.contentUrl,
    this.lock,
    this.hasChat,
    this.chapterVoList,
    this.contentList,
  });

  factory BookDetailInfoResultModel.fromJson(Map<String, dynamic> json) =>
      _$BookDetailInfoResultModelFromJson(json);

  Map<String, dynamic> toJson() => _$BookDetailInfoResultModelToJson(this);
}

@JsonSerializable()
class ChapterVoModel {
  int? id; //章节ID
  int? bookId; //书籍ID
  String? content; //章节内容 (只有下一本书才有第一章的内容)
  int? chapterIndex; //章节索引
  String? title; //章节标题
  int? lock; //章节解锁状态，0免费，1-试看，需要购买，需要购买，2-已购买
  int? cost; //章节价格
  int? prevId; //上一章节ID (废弃)
  int? nextId; //下一章节ID (废弃)
  String? lang; //章节语言项
  List<ChapterVoiceModel?>? chapterVoice; //章节语音文件
  @JsonKey(includeFromJson: false, includeToJson: false)
  GlobalKey<PremiumPurchaseViewState>? chapterUnLockKey;

  String getContent({int? length}) {
    if (isAvailable(content) && isAvailable(id.toString())) {
      try {
        String contentResult = EncryptUtil.chapterContentDecrypt(content!, id!);
        if (lock == 1) {
          return contentResult.substring(0, 188);
        } else {
          if (length != null && 0 < length) {
            return contentResult.substring(0, length);
          }

          return contentResult;
        }
      } catch (e) {
        return "";
      }
    }
    return "";
  }

  ChapterVoModel({
    this.id,
    this.bookId,
    this.content,
    this.chapterIndex,
    this.title,
    this.lock,
    this.cost,
    this.prevId,
    this.nextId,
    this.lang,
    this.chapterVoice,
    this.chapterUnLockKey,
  });

  factory ChapterVoModel.fromJson(Map<String, dynamic> json) =>
      _$ChapterVoModelFromJson(json);

  Map<String, dynamic> toJson() => _$ChapterVoModelToJson(this);
}

@JsonSerializable()
class ChapterVoiceModel {
  int? id; //id
  String? voiceName; //发生人
  String? headImg; //人物头像名称
  int? chapterId; //章节id
  String? name; //语音名称
  String? url; //语音文件链接

  ChapterVoiceModel({
    this.id,
    this.voiceName,
    this.headImg,
    this.chapterId,
    this.name,
    this.url,
  });

  factory ChapterVoiceModel.fromJson(Map<String, dynamic> json) =>
      _$ChapterVoiceModelFromJson(json);

  Map<String, dynamic> toJson() => _$ChapterVoiceModelToJson(this);
}
