import 'package:json_annotation/json_annotation.dart';
part 'illustration_rule_model.g.dart';

@JsonSerializable()
class IllustrationRuleModel {
  int? id;
  String? url;
  double? w;
  double? h;

  IllustrationRuleModel({this.id, this.url, this.w, this.h});

  factory IllustrationRuleModel.fromJson(Map<String, dynamic> json) =>
      _$IllustrationRuleModelFromJson(json);

  Map<String, dynamic> toJson() => _$IllustrationRuleModelToJson(this);
}