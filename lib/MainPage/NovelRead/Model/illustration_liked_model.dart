import 'package:json_annotation/json_annotation.dart';

part 'illustration_liked_model.g.dart';

@JsonSerializable()
class IllustrationLikedModel {
  int? code;
  List<dynamic>? result;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  IllustrationLikedModel({this.code, this.result, this.msg, this.sysAt, this.cost, this.traceId});

  factory IllustrationLikedModel.fromJson(Map<String, dynamic> json) =>
      _$IllustrationLikedModelFromJson(json);

  Map<String, dynamic> toJson() => _$IllustrationLikedModelToJson(this);
}
