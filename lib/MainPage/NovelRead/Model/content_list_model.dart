import 'package:json_annotation/json_annotation.dart';

import '../../../Util/ReaderUtils/readerUtil.dart';
import '../../../Util/encryptUtil.dart';
import '../../../Util/enum.dart';
import '../../../Util/tools.dart';
part 'content_list_model.g.dart';

@JsonSerializable()
class ContentListModel {
  List<ContentModel?>? contents;

  ContentListModel({this.contents});

  factory ContentListModel.fromJson(Map<String, dynamic> json) =>
      _$ContentListModelFromJson(json);

  Map<String, dynamic> toJson() => _$ContentListModelToJson(this);
}

@JsonSerializable()
class ContentModel {
  int? id;
  int? chapterIndex;
  String? content;
  //自加
  List<Map<String, int>>? pageOffsets; //章节内容偏移量 暂时不用
  List<String>? pages; //章节内容分页
  //settings
  double? fontSize = ReaderUtil.instance.fontSize;
  double? lineSpace = ReaderUtil.instance.fontSize;
  double? horizontalPadding = ReaderUtil.instance.horizontalPadding;
  FontType? fontType = ReaderUtil.instance.fontType;
  String? fontFamily = ReaderUtil.instance.fontFamily;
  ReadingMode? readingMode = ReaderUtil.instance.readingMode;
  double? firstShowLineHeight;

  String getContent({int? length}) {
    if (isAvailable(content) && isAvailable(id.toString())) {
      try {
        String contentResult = EncryptUtil.chapterContentDecrypt(content!, id!);
        if (length != null && 0 < length) {
          return contentResult.substring(0, length);
        }

        return contentResult;
      } catch (e) {
        return "";
      }
    }
    return "";
  }

  String getPageContent(int index) {
    // var content = getContent();
    // var offset = pageOffsets?[index];
    // if (offset?['start'] != -1 && offset?['end'] != -1) {
    //   return content.substring(offset!['start']!, offset['end']);
    // }

    if (index < pages!.length && index >= 0) {
      return pages![index];
    }

    return "";
  }

  int get pageCount {
    var count = pages?.length ?? 0;

    return count;
  }

  ContentModel({this.id, this.chapterIndex, this.content});

  factory ContentModel.fromJson(Map<String, dynamic> json) =>
      _$ContentModelFromJson(json);

  Map<String, dynamic> toJson() => _$ContentModelToJson(this);
}