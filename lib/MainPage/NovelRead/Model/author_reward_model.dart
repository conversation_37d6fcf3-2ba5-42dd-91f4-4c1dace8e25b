import 'package:json_annotation/json_annotation.dart';

part 'author_reward_model.g.dart';

@JsonSerializable()
class AuthorRewardModel {
  int? code;
  AuthorRewardResultModel? result;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  AuthorRewardModel(
      {this.code, this.result, this.msg, this.sysAt, this.cost, this.traceId});

  factory AuthorRewardModel.fromJson(Map<String, dynamic> json) =>
      _$AuthorRewardModelFromJson(json);

  Map<String, dynamic> toJson() => _$AuthorRewardModelToJson(this);
}

@JsonSerializable()
class AuthorRewardResultModel {
  int? rewardStatus;//打赏状态（0：成功；1：金币不足；2：失败）

  AuthorRewardResultModel({this.rewardStatus});

  factory AuthorRewardResultModel.fromJson(Map<String, dynamic> json) =>
      _$AuthorRewardResultModelFromJson(json);

  Map<String, dynamic> toJson() => _$AuthorRewardResultModelToJson(this);
}
