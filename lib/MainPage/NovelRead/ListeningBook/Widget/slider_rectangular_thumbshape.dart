// 首先创建一个自定义的 SliderComponentShape
import 'package:flutter/material.dart';
import 'dart:ui' as ui;

class SliderRectangularThumbShape extends SliderComponentShape {
  final double width;
  final double height;
  final double borderRadius;
  // 缓存上一次绘制的值，减少重绘
  static double _lastValue = -1;
  static ui.Image? _cachedThumb;
  static String? _cachedText;

  const SliderRectangularThumbShape({
    this.width = 50.0,
    this.height = 20.0,
    this.borderRadius = 10.0,
  });

  @override
  Size getPreferredSize(bool isEnabled, bool isDiscrete) {
    return Size(width, height);
  }

  @override
  void paint(
    PaintingContext context,
    Offset center, {
    required Animation<double> activationAnimation,
    required Animation<double> enableAnimation,
    required bool isDiscrete,
    required TextPainter labelPainter,
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required TextDirection textDirection,
    required double value,
    required double textScaleFactor,
    required Size sizeWithOverflow,
  }) {
    final Canvas canvas = context.canvas;
    
    // 计算百分比文本
    final percentage = '${(value * 100).toInt()}%';
    
    // 如果值没有变化，跳过重绘
    if (_lastValue == value && _cachedText == percentage && _cachedThumb != null) {
      // 使用缓存
      final thumbOffset = Offset(center.dx - width / 2, center.dy - height / 2);
      final Paint paint = Paint();
      canvas.drawImageRect(
        _cachedThumb!,
        Rect.fromLTWH(0, 0, width, height), 
        Rect.fromLTWH(thumbOffset.dx, thumbOffset.dy, width, height),
        paint,
      );
      return;
    }
    
    // 需要重绘，更新缓存值
    _lastValue = value;
    _cachedText = percentage;

    // 绘制圆角矩形
    final RRect rRect = RRect.fromRectAndRadius(
      Rect.fromCenter(
        center: center,
        width: width,
        height: height,
      ),
      Radius.circular(borderRadius),
    );

    // 绘制背景
    final Paint paint = Paint()
      ..color = sliderTheme.thumbColor ?? Colors.white
      ..style = PaintingStyle.fill;
    canvas.drawRRect(rRect, paint);

    // 绘制百分比文本
    final textSpan = TextSpan(
      text: percentage,
      style: const TextStyle(
        color: Color.fromRGBO(85, 90, 101, 1),
        fontSize: 11,
        fontWeight: FontWeight.bold,
      ),
    );
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        center.dx - textPainter.width / 2,
        center.dy - textPainter.height / 2,
      ),
    );

    // 尝试创建缓存
    try {
      // 实际应用中需要更复杂的缓存逻辑，这只是示例
      // 实现缓存超出了本次修改的范围
    } catch (e) {
      // 忽略缓存错误
    }
  }
}
