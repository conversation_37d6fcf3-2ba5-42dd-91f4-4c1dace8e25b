import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:UrNovel/Util/Common/network_image_util.dart';
import 'package:UrNovel/Util/tools.dart';

import '../../../../Util/deviceScreenUtil.dart';
import '../../../../Util/enum.dart';
import '../Page/listening_book_page.dart';

class ListeningBookFloatingWidget extends StatefulWidget {
  const ListeningBookFloatingWidget({
    super.key,
  });

  @override
  State<ListeningBookFloatingWidget> createState() =>
      _ListeningBookFloatingWidgetState();
}

class _ListeningBookFloatingWidgetState
    extends State<ListeningBookFloatingWidget>
    with SingleTickerProviderStateMixin {
  late double containerWidth; // 悬浮窗宽度
  late double containerHeight;
  late double top;
  late double left;
  late final AnimationController _rotationController;

  @override
  void initState() {
    super.initState();

    containerWidth = 135.0; // 悬浮窗宽度
    containerHeight = 50.0;
    top = DeviceScreenUtil.instance.height - 230;
    left = DeviceScreenUtil.instance.width - containerWidth - 2;

    _rotationController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    // 移除监听
    _rotationController.dispose();
    super.dispose();
  }

  void _updatePosition(DragUpdateDetails details) {
    setState(() {
      final size = MediaQuery.of(context).size;
      final padding = MediaQuery.of(context).padding;

      // 更新位置并限制在屏幕安全区域内
      left = (left + details.delta.dx)
          .clamp(padding.left, size.width - containerWidth - padding.right);
      top = (top + details.delta.dy)
          .clamp(padding.top, size.height - 50 - padding.bottom);
    });
  }

  void _endPosition(DragEndDetails details) {
    Offset offset = details.globalPosition;
    if (offset.dx.abs() < DeviceScreenUtil.instance.width / 2) {
      //回归左侧
      left = 2;
    } else {
      //回归右侧
      left = DeviceScreenUtil.instance.width - containerWidth - 2;
    }
  }

  _updateRotation() {
    if (audioBookController.isPlaying.value) {
      _rotationController.repeat();
    } else {
      _rotationController.stop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: left,
      top: top,
      child: Material(
        color: Colors.transparent,
        child: GestureDetector(
          onPanUpdate: _updatePosition,
          onPanEnd: _endPosition,
          child: Container(
            width: containerWidth,
            height: 50,
            decoration: BoxDecoration(
              color: const Color.fromRGBO(95, 99, 110, 1),
              borderRadius: BorderRadius.circular(containerHeight / 2),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                const SizedBox(width: 5),
                GestureDetector(
                  onTap: () {
                    Get.to(() => ListeningBookPage(bookUpdateCallBack:
                            (bookDetailInfo, currChapter) async {
                          Future.delayed(const Duration(milliseconds: 100),
                              () async {
                            audioBookController.setShowFloating(true);
                          });
                        }));
                  },
                  child: RotationTransition(
                    turns: _rotationController,
                    child: isAvailable(audioBookController.coverUrl)
                        ? SizedBox(
                            width: containerHeight - 5,
                            height: containerHeight - 5,
                            child: NetworkImageUtil(
                                imageUrl: audioBookController.coverUrl,
                                isCircle: true),
                          )
                        : null,
                  ),
                ),
                const SizedBox(width: 8),
                Stack(
                  alignment: Alignment.center,
                  children: [
                    SizedBox(
                      width: 36,
                      height: 36,
                      child: Obx(() => CircularProgressIndicator(
                            value: audioBookController.progress,
                            backgroundColor: Colors.grey.shade500,
                            valueColor: const AlwaysStoppedAnimation<Color>(
                                Colors.white),
                            strokeWidth: 2,
                          )),
                    ),
                    // 修改为
                    SizedBox(
                      width: 36,
                      height: 36,
                      child: IconButton(
                        icon: Obx(() {
                          _updateRotation();
                          return Icon(
                            audioBookController.isPlaying.value
                                ? Icons.pause
                                : Icons.play_arrow,
                            color: Colors.white,
                          );
                        }),
                        style: IconButton.styleFrom(padding: EdgeInsets.zero),
                        onPressed: () async {
                          if (audioBookController.isAllCompleted.value) {
                            Get.to(() => ListeningBookPage());
                            return;
                          }
                          if (audioBookController.isPlaying.value) {
                            audioBookController.playerPause();
                            await audioBookController.reportReadRecord(
                                ReadReportActivityType.reading,
                                ReadReportReadingType.listen,
                                true);
                          } else {
                            audioBookController.playerPlay();
                            await audioBookController.reportReadRecord(
                                ReadReportActivityType.reading,
                                ReadReportReadingType.listen,
                                false);
                          }
                          _updateRotation();
                        },
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  width: 40,
                  height: 40,
                  child: IconButton(
                    icon: const Icon(Icons.close, color: Colors.grey),
                    onPressed: () async {
                      audioBookController.dispose();
                    },
                    iconSize: 20,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(), // 移除默认约束
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
