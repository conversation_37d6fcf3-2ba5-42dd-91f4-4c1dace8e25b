import 'dart:ui';

import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Launch&Login/ViewModel/ViewModel.dart';
import 'package:UrNovel/MainPage/NovelRead/ListeningBook/Widget/slider_rectangular_thumbshape.dart';
import 'package:UrNovel/Util/Common/Animation/lottie_animation_view.dart';
import 'package:UrNovel/Util/Common/network_image_util.dart';
import 'package:UrNovel/Util/enum.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../Launch&Login/Model/user_model.dart';
import '../../../../Util/Common/CommonManager/common_manager.dart';
import '../../../../Util/DataReportManager/event_name_config.dart';
import '../../../../Util/DataReportManager/event_report_manager.dart';
import '../../../../Util/Extensions/colorUtil.dart';
import '../../../../Util/PaymentManager/payment_manager.dart';
import '../../../../Util/SharedPreferences/shared_preferences.dart';
import '../../../../Util/SheetAndAlter/bottom_sheet.dart';
import '../../../../Util/SheetAndAlter/toast.dart';
import '../../../../Util/StatusManagement/status_management.dart';
import '../../../../Util/genericUtil.dart';
import '../../../Profile/Model/goods_info_model.dart';
import '../../../Profile/ViewModel/ViewModel.dart';
import '../../Model/batchBuy_chapter_model.dart';
import '../../Model/book_detailInfo_model.dart';
import '../../ViewModel/ViewModel.dart';

class ListeningBookPage extends BaseFulWidget {
  // final bool? autoPlay;
  final BookDetailInfoResultModel? detailInfoModel;
  final ChapterVoModel? currentChapter;
  final Function(BookDetailInfoResultModel? detailInfoModel, ChapterVoModel? currentChapter)?
      bookUpdateCallBack;

  ListeningBookPage(
      {super.key, this.detailInfoModel, this.currentChapter, this.bookUpdateCallBack});

  @override
  State<ListeningBookPage> createState() => _ListeningBookPageState();
}

typedef IntCallback = void Function(int value);

class _ListeningBookPageState extends State<ListeningBookPage> with SingleTickerProviderStateMixin {
  // 阅读器控制器
  final NovelReadStatusController _novelReadStatusController =
      findGetXInstance(NovelReadStatusController());

  late AnimationController _rotationController;

  late UserInfoModel? _userInfoModel;

  @override
  void initState() {
    super.initState();

    _userInfoModel = null;
    fetchData();

    if (isAvailable(widget.detailInfoModel) && isAvailable(widget.currentChapter)) {
      audioBookController.init(widget.detailInfoModel!, widget.currentChapter!);
    }
    audioBookController.onChapterChangeCallBack = (isNeedUnlock) async {
      ///章节变更回调
      if (isNeedUnlock) {
        await _onChapterUnlock();
      }
    };

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      audioBookController.setShowFloating(false);
      _novelReadStatusController.setAddLibrary(audioBookController.isAddToLibrary);

      if (!audioBookController.isShowFloating.value) {
        showAudioBookFloating(context);
      }
    });

    _rotationController = AnimationController(
      duration: const Duration(seconds: 30),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    setSystemUiDark();
    widget.bookUpdateCallBack
        ?.call(audioBookController.bookDetailInfoModel, audioBookController.currentChapter.value);
    _rotationController.dispose();

    super.dispose();
  }

  Future<void> fetchData() async {
    await _getUserInfo();
    await EventReportManager.eventReportOfFirebase(audiobook, parameters: null);
  }

  Future<void> _getUserInfo() async {
    _userInfoModel = await LoginViewModel.getUserInfo();
    if (!isAvailable(_userInfoModel)) {
      _userInfoModel = await SpUtil.spGetUserInfo();
    }
  }

  ///添加到书架
  _addBookToLibrary() async {
    audioBookController.isLoading.value = true;
    await CommonManager.instance.addBookToLibrary(widget.detailInfoModel?.bookId, 1, (isSuccess) {
      if (isSuccess) {
        _novelReadStatusController.setAddLibrary(true);
        audioBookController.bookDetailInfoModel.library = true;
      }
    });
    audioBookController.isLoading.value = false;

    EventReportManager.eventReportOfFirebase(clickReadLibrary);
  }

  ///从书架移除
  _cancelBookFromLibrary() async {
    audioBookController.isLoading.value = true;
    await CommonManager.instance.cancelBookFromLibrary(widget.detailInfoModel?.bookId, 1,
        (isSuccess) {
      if (isSuccess) {
        _novelReadStatusController.setAddLibrary(false);
        audioBookController.bookDetailInfoModel.library = false;
      }
    });
    audioBookController.isLoading.value = false;

    EventReportManager.eventReportOfFirebase(clickReadRemove);
  }

  // TODO: 章节解锁
  Future<void> _onChapterUnlock() async {
    if (!isAvailable(_userInfoModel)) {
      await _getUserInfo();
    }

    if ((audioBookController.currentChapter.value.cost ?? 0) <= (_userInfoModel?.goldCoin ?? 0)) {
      await onBuyChapter();
    } else {
      await _onShowCoinPurchaseSheet();
    }
  }

  Future<void> _onShowCoinPurchaseSheet() async {
    audioBookController.isLoading.value = true;
    await _onGetGoodsList((List<GoodsListItem>? goodList) {
      showBookBottomSheet(
          context,
          AccountPurchaseSheet(
            goodList: goodList,
            onProductTap: (item) {
              // TODO: implement onProductTap 购买商品
              _createShopOrder(item!);
            },
          ),
          Colors.transparent);
    });
  }

  // TODO: 创建订单
  _createShopOrder(GoodsListItem item) async {
    if (!isAvailable(item)) {
      showToast("Product is Not Available");
      audioBookController.isLoading.value = false;

      return;
    }

    PaymentManager.instance.createShopOrder(item,
        goodsType: GoodsType.purchaseCoins,
        bookId: audioBookController.bookId,
        orderType: OrderType.novelReadDownLoad, pullPurchaseCallback: () async {
      // TODO: 支付掉起回调
    }, onPurchaseCanceled: () async {
      // TODO: 支付取消回调
      audioBookController.isLoading.value = false;
    }, onPurchaseValidateCallback: (status, info) async {
      // TODO: 验证订单回调
      audioBookController.isLoading.value = false;
      if (status == SubscribeVerifyStatus.purchased) {
        await _getUserInfo();
        //充值完成
        await onBuyChapter();
      }
    });
  }

  // TODO: 获取商品列表
  Future<void> _onGetGoodsList(Function(List<GoodsListItem>?)? onDataFetched) async {
    audioBookController.isLoading.value = true;
    // TODO: implement fetchData
    List<GoodsListItem>? list = await ProfileViewModel.getGoodList(GoodsType.purchaseCoins);
    if (isAvailable(list)) {
      onDataFetched?.call(list);
    }
    audioBookController.isLoading.value = false;
  }

  Future<void> onBuyChapter() async {
    BatchBuyChapterResultModel? result = await NovelReadViewModel.batchBuyChapter(
        audioBookController.bookId, [audioBookController.chapterId.toString()], false);

    if (result?.succ == true) {
      await reportUnlockData();

      ///对收费章节进行处理
      audioBookController.updateCurrentChapterLockStatus();
    }
  }

  // TODO: 解锁数据上报
  Future<void> reportUnlockData() async {
    if (_userInfoModel?.newUser == true) {
      await EventReportManager.eventReportOfFirebase(trialUnlockNew);
      await EventReportManager.eventReportOfCommon(addToCart);
      await EventReportManager.logAddToCart(
          '', 'readUnlock', '', audioBookController.cost?.toDouble() ?? 0.0);
    } else {
      await EventReportManager.eventReportOfFirebase(trialUnlockOld);
    }

    await EventReportManager.eventReportOfFirebase(trialUnlockAll);
    await EventReportManager.eventReportOfCommon(startTrial);
    await EventReportManager.logStartTrial('');
  }

  ///语速选择
  void showSelectAudioBottomSheet(BuildContext context, IntCallback callback) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
      builder: (context) {
        return SizedBox(
          height: 180,
          child: ListView.builder(
              itemCount: 3,
              itemBuilder: (c, index) {
                return ListTile(
                  title: Text('Character $index'),
                  onTap: () {
                    callback(index);
                    Navigator.pop(context);
                  },
                );
              }),
        );
      },
    );
  }

  ///章节选择
  void showChapterBottomSheet(Function(ChapterVoModel? item) onChaptersTap) {
    showBookBottomSheet(
      context,
      NovelChaptersSheet(
          detailInfoResultModel: audioBookController.bookDetailInfoModel,
          currentChapterId: audioBookController.currentChapter.value.id,
          isFromVoce: true,
          onBookHeaderTap: () async {
            Get.back();
            //todo:去往书籍详情页
            await Get.toNamed('/bookDetailPage', arguments: {
              "bookDetailInfo": audioBookController.bookDetailInfoModel,
              'jumpType': BookDetailJumpType.voiceListening,
            });
          },
          onChaptersTap: onChaptersTap),
      const Color.fromRGBO(46, 46, 46, 1),
      isScrollControlled: true,
    );
  }

  ///语音选择
  void showVoiceBottomSheet() {
    showBookBottomSheet(
      context,
      ListeningBookVoiceToneSheet(
          currentVoiceModel: audioBookController.currentVoiceModel.value,
          chapterVoiceList: audioBookController.chapterVoiceList,
          onChapterVoiceSelected: (model) async {
            if (isAvailable(model)) {
              await audioBookController.setChapterVoiceModel(model!);
            }
          }),
      HexColor("#F4F5F6"),
      isScrollControlled: true,
    );
  }

  ///播速选择
  void showSpeedBottomSheet() {
    showBookBottomSheet(
      context,
      ListeningBookSpeedBottomSheet(
          speedOptions: audioBookController.speedOptions,
          currOption: audioBookController.currSpeedOption.value,
          onSpeedSelected: (option) {
            if (isAvailable(option)) {
              audioBookController.setCurrSpeedOption(option!);
            }
          }),
      const Color.fromRGBO(46, 46, 46, 1),
      isScrollControlled: true,
    );
  }

  Widget _buildBottomNavItem(String icon, String label, int index,
      {bool isSelected = false, VoidCallback? onTap}) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: onTap,
      child: SizedBox(
        width: 80,
        height: 60,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              "assets/images/listening_book/$icon.png",
              width: 22,
              height: 22,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: const TextStyle(
                fontSize: 11,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent, // 修改为透明
        elevation: 0, // 移除阴影
        automaticallyImplyLeading: true,
        iconTheme: const IconThemeData(
          color: Colors.white,
        ),
      ),
      extendBodyBehindAppBar: true, // 允许内容延伸到AppBar后面
      body: Stack(
        children: [
          // 背景图片层
          Positioned.fill(
            child: ImageFiltered(
              imageFilter: ImageFilter.blur(
                sigmaX: 10.0, // 水平模糊程度
                sigmaY: 10.0, // 垂直模糊程度
              ),
              child: NetworkImageUtil(imageUrl: audioBookController.coverUrl),
            ),
          ),

          // 在Stack中，背景图片层之后，添加渐变遮罩层
          Positioned.fill(
            child: DecoratedBox(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  stops: const [
                    0.0, // 0%
                    0.12, // 30%
                    0.4, // 40%
                    0.7, // 70%
                    0.9, // 80%
                    1.0 // 100%
                  ],
                  colors: [
                    Colors.black, // 底部不透明
                    Colors.black, // 30%位置不透明
                    Colors.black.withValues(alpha: 0.2), // 40%位置80%透明度
                    Colors.black.withValues(alpha: 0.2), // 70%位置80%透明度
                    Colors.black, // 80%位置不透明
                    Colors.black, // 顶部不透明
                  ],
                ),
              ),
            ),
          ),

          // 主要内容
          SafeArea(
            child: Stack(
              children: [
                Column(
                  children: [
                    // Book Cover and Title
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Center(
                            child: SizedBox(
                              width: 300 * 0.8,
                              height: 400 * 0.8,
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(20),
                                child: isAvailable(audioBookController.coverUrl)
                                    ? NetworkImageUtil(imageUrl: audioBookController.coverUrl)
                                    : Image.asset(
                                        'assets/images/common/img_placeholder.png',
                                      ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 20),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 10),
                            child: Text(
                              audioBookController.bookName ?? "",
                              style: const TextStyle(
                                fontSize: 18,
                                color: Colors.white,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Obx(() => Text(
                                audioBookController.chapterName ?? "",
                                style: TextStyle(fontSize: 14, color: Colors.white),
                              )),
                        ],
                      ),
                    ),

                    Container(
                      padding: const EdgeInsets.only(right: 40, bottom: 10),
                      alignment: Alignment.centerRight,
                      child: Obx(() => Text(
                            '${audioBookController.formatTime(audioBookController.position.value)} / ${audioBookController.formatTime(audioBookController.duration.value)}',
                            textAlign: TextAlign.right,
                            style: const TextStyle(color: Colors.white),
                          )),
                    ),
                    // Progress Bar
                    Padding(
                      padding: const EdgeInsets.only(left: 16, top: 0, right: 16, bottom: 0),
                      child: SliderTheme(
                        data: SliderTheme.of(context).copyWith(
                          overlayShape: SliderComponentShape.noOverlay,
                          thumbShape: const SliderRectangularThumbShape(),
                          trackHeight: 2,
                          activeTrackColor: const Color.fromRGBO(255, 255, 255, 1),
                          inactiveTrackColor: const Color.fromRGBO(99, 94, 95, 1),
                          thumbColor: const Color.fromRGBO(255, 255, 255, 1),
                          trackShape: const RectangularSliderTrackShape(),
                        ),
                        child: Obx(() => Slider(
                              value: audioBookController.duration.value.inSeconds > 0
                                  ? audioBookController.position.value.inSeconds.toDouble().clamp(
                                        0,
                                        audioBookController.duration.value.inSeconds.toDouble(),
                                      )
                                  : 0.0,
                              min: 0,
                              max: audioBookController.duration.value.inSeconds > 0
                                  ? audioBookController.duration.value.inSeconds.toDouble()
                                  : 1.0,
                              onChangeStart: (value) {
                                // 开始拖拽
                                audioBookController.startDragging();
                              },
                              onChanged: (value) {
                                if (audioBookController.duration.value.inSeconds > 0) {
                                  // 拖拽过程中更新位置
                                  audioBookController
                                      .updateDragPosition(Duration(seconds: value.toInt()));
                                }
                              },
                              onChangeEnd: (value) {
                                if (audioBookController.duration.value.inSeconds > 0) {
                                  // 结束拖拽，并应用新位置
                                  audioBookController.endDragging(Duration(seconds: value.toInt()));
                                }
                              },
                            )),
                      ),
                    ),

                    // Control Buttons
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          IconButton(
                            icon: Image.asset(
                              "assets/images/listening_book/Listening_icon_next01.png",
                              width: 14,
                              height: 18,
                            ),
                            iconSize: 32,
                            onPressed: () {
                              audioBookController.playPrevious();
                            },
                          ),
                          IconButton(
                            icon: Image.asset(
                              "assets/images/listening_book/Listening_icon_15-.png",
                              width: 23,
                              height: 20,
                            ),
                            iconSize: 32,
                            onPressed: () {
                              audioBookController.seek(Duration(
                                  seconds: audioBookController.position.value.inSeconds - 15));
                            },
                          ),
                          Obx(() => IconButton(
                                icon: audioBookController.isPlaying.value
                                    ? Image.asset(
                                        "assets/images/listening_book/Listening_icon_Pause_big.png",
                                        width: 49,
                                        height: 49,
                                      )
                                    : Image.asset(
                                        "assets/images/listening_book/Listening_icon_Start_big.png",
                                        width: 49,
                                        height: 49,
                                      ),
                                iconSize: 32,
                                onPressed: () => audioBookController.isPlaying.value
                                    ? audioBookController.playerPause()
                                    : audioBookController.playerPlay(),
                              )),
                          IconButton(
                            icon: Image.asset(
                              "assets/images/listening_book/Listening_icon_15+.png",
                              width: 23,
                              height: 20,
                            ),
                            iconSize: 32,
                            onPressed: () {
                              audioBookController.seek(Duration(
                                  seconds: audioBookController.position.value.inSeconds + 15));
                            },
                          ),
                          IconButton(
                            icon: Image.asset(
                              "assets/images/listening_book/Listening_icon_next02.png",
                              width: 14,
                              height: 18,
                            ),
                            iconSize: 32,
                            onPressed: () {
                              audioBookController.playNext();
                            },
                          ),
                        ],
                      ),
                    ),
                    // Bottom Navigation
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          _buildBottomNavItem("Listening_icon_Chapter", 'Chapters'.tr, 1,
                              onTap: () => {
                                    showChapterBottomSheet((ChapterVoModel? item) async {
                                      if (isAvailable(item)) {
                                        ///匹配声音列表
                                        await audioBookController.setCurrChapter(item!);
                                      }
                                    }),
                                  }),
                          _buildBottomNavItem("Listening_icon_Timbre", 'voice_tone'.tr, 2,
                              onTap: () {
                            showVoiceBottomSheet();
                          }),
                          Obx(() => _buildBottomNavItem(
                                "Listening_icon_Slower",
                                audioBookController.currSpeedOption.value.name,
                                3,
                                onTap: showSpeedBottomSheet,
                              )),
                          Obx(() => _buildBottomNavItem(
                                _novelReadStatusController.isAddLibrary.value
                                    ? "Listening_icon_Library02"
                                    : "Listening_icon_Library01",
                                _novelReadStatusController.isAddLibrary.value
                                    ? "library_added".tr
                                    : 'library_add'.tr,
                                4,
                                isSelected: false,
                                onTap: () {
                                  if (_novelReadStatusController.isAddLibrary.value) {
                                    _cancelBookFromLibrary();
                                  } else {
                                    _addBookToLibrary();
                                  }
                                },
                              )),
                        ],
                      ),
                    ),
                  ],
                ),
                Obx(() => audioBookController.isLoading.value ? LottieAnimationView() : SizedBox())
              ],
            ),
          ),
        ],
      ),
    );
  }
}
