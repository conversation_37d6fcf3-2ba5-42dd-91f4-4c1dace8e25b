import 'dart:async';
import 'package:audio_service/audio_service.dart';
import 'package:just_audio/just_audio.dart';

import '../../../../Util/logUtil.dart';

class ListeningBookAudioHandler extends BaseAudioHandler with SeekHandler {
  final AudioPlayer _player = AudioPlayer();
  final _completer = Completer<void>();
  Duration _position = Duration.zero;
  Duration _bufferedPosition = Duration.zero;
  bool _playing = false;
  bool _isDragging = false;
  Duration _dragPosition = Duration.zero;
  AudioProcessingState _processingState = AudioProcessingState.idle;

  ListeningBookAudioHandler() {
    // 监听播放状态变化
    _player.playerStateStream.listen((state) {
      _processingState = _convertProcessingState(state.processingState);
      _playing = state.playing;
      
      // 处理播放完成状态
      if (state.processingState == ProcessingState.completed) {
        // 确保位置设置为总时长
        final duration = _player.duration;
        if (duration != null) {
          _position = duration;
          // 重置拖拽状态，确保不影响位置显示
          _isDragging = false;
          _dragPosition = duration;
        }

        //TODO: 播放完成时暂停,临时解决锁屏界面显示
        // WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        //     pause();
        // });
      }
      
      _updatePlaybackState();
    });

    // 监听播放位置变化
    _player.positionStream.listen((position) {
      if (!_isDragging) {
        _position = position;
        _updatePlaybackState();
      }
    });

    // 监听缓冲位置变化
    _player.bufferedPositionStream.listen((position) {
      _bufferedPosition = position;
      _updatePlaybackState();
    });

    // 监听音频时长变化
    _player.durationStream.listen((duration) {
      if (duration != null) {
        final mediaItem = this.mediaItem.value;
        if (mediaItem != null) {
          this.mediaItem.add(mediaItem.copyWith(duration: duration));
        }
      }
    });
  }

  AudioProcessingState _convertProcessingState(ProcessingState state) {
    switch (state) {
      case ProcessingState.idle:
        return AudioProcessingState.idle;
      case ProcessingState.loading:
        return AudioProcessingState.loading;
      case ProcessingState.buffering:
        return AudioProcessingState.buffering;
      case ProcessingState.ready:
        return AudioProcessingState.ready;
      case ProcessingState.completed:
        return AudioProcessingState.completed;
    }
  }

  Future<void> setAudioSource(String url, String title, String artist, String artUri) async {
    try {
      await _player.setUrl(url);
      // 等待音频准备就绪以获取时长
      await _player.load();
      final duration = _player.duration?.abs();
      
      mediaItem.add(MediaItem(
        id: url,
        title: title,
        artist: artist,
        duration: duration,
        artUri: Uri.parse(artUri),
      ));

      // 确保时长被正确设置
      if (duration != null) {
        mediaItem.add(mediaItem.value?.copyWith(duration: duration));
      }
      
      _completer.complete();
    } catch (e) {
      logP('Error setting audio source: $e');
    }
  }

  Future<void> _updatePlaybackState() async {
    // 处理播放完成状态优先级最高，确保100%进度
    final mediaItem = this.mediaItem.value;
    if (_processingState == AudioProcessingState.completed && mediaItem?.duration != null) {
      _position = mediaItem!.duration!.abs();
      _isDragging = false; // 确保拖拽状态不会干扰完成状态
      _dragPosition = _position; // 同步拖拽位置与当前位置
    }
    
    // 根据状态选择显示位置
    final displayPosition = _isDragging ? _dragPosition : 
                           (_processingState == AudioProcessingState.completed && mediaItem?.duration != null) ? 
                           mediaItem!.duration! : _position;
    
    final state = PlaybackState(
      controls: [
        MediaControl.rewind,
        _playing ? MediaControl.pause : MediaControl.play,
        MediaControl.stop,
        MediaControl.fastForward,
      ],
      systemActions: const {
        MediaAction.seek,
        MediaAction.seekForward,
        MediaAction.seekBackward,
      },
      androidCompactActionIndices: const [0, 1, 3],
      processingState: _processingState,
      playing: _playing,
      updatePosition: displayPosition,
      bufferedPosition: _bufferedPosition,
      speed: 1.0,
    );

    playbackState.add(state);
  }

  @override
  Future<void> play() async {
    await _completer.future;
    
    // 如果是从播放完成状态开始播放，先设置位置为0
    if (_processingState == AudioProcessingState.completed) {
      _position = Duration.zero;
      await _player.seek(Duration.zero);
    }
    
    _playing = true;
    await _player.play();
    // 确保播放开始后立即更新状态
    _updatePlaybackState();
  }

  @override
  Future<void> pause() async {
    _playing = false;
    _updatePlaybackState();
    return _player.pause();
  }

  @override
  Future<void> seek(Duration position) async {
    // 如果是播放完成状态，先取消该状态
    if (_processingState == AudioProcessingState.completed) {
      _processingState = AudioProcessingState.ready;
    }
    
    if (!_isDragging) {
      _position = position;
      await _player.seek(position);
    } else {
      _dragPosition = position;
    }
    _updatePlaybackState();
  }

  @override
  Future<void> stop() async {
    _playing = false;
    _updatePlaybackState();
    return _player.stop();
  }

  @override
  Future<void> fastForward() async {
    final newPosition = _position + const Duration(seconds: 15);
    return seek(newPosition);
  }

  @override
  Future<void> rewind() async {
    final newPosition = _position - const Duration(seconds: 15);
    return seek(newPosition.isNegative ? Duration.zero : newPosition);
  }

  void startDragging() {
    _isDragging = true;
    _dragPosition = _position;
    _updatePlaybackState();
  }

  Future<void> endDragging(Duration position) async {
    // 如果是播放完成状态，保持位置为总时长
    // if (_processingState == AudioProcessingState.completed) {
    //   final mediaItem = this.mediaItem.value;
    //   if (mediaItem?.duration != null) {
    //     _position = mediaItem!.duration!;
    //     _dragPosition = _position;
    //   }
    // } else {
      // 正常拖拽结束流程
      _position = _dragPosition = position;
      await _player.seek(_dragPosition);
      await _player.play();
    // }
    
    _isDragging = false;
    _updatePlaybackState();
  }

  void updateDragPosition(Duration position) {
    if (_isDragging) {
      _dragPosition = position;
      _updatePlaybackState();
    }
  }

  AudioPlayer get player => _player;

  void dispose() async {
    await _player.dispose();
  }
} 