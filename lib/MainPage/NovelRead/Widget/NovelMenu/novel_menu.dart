import 'package:flutter/material.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Util/ReaderUtils/readerUtil.dart';
import 'package:UrNovel/Util/StatusManagement/status_management.dart';
import 'package:get/get.dart';

import '../../Model/book_detailInfo_model.dart';
import 'novel_bottom_bar.dart';
import 'novel_top_bar.dart';

class NovelMenu extends BaseFulWidget {
  final BookDetailInfoResultModel? detailInfoResultModel;
  final int? currentChapterId;
  final NovelPreferencesSettingController preferencesController;
  final Function(int) onTopItemsTap;
  final Function(dynamic item) onChaptersTap;
  final VoidCallback? onReadingModelChanged;
  final VoidCallback? onDismiss;

  NovelMenu(
      {super.key,
      required super.arguments,
      required this.detailInfoResultModel,
      required this.currentChapterId,
      required this.preferencesController,
      required this.onTopItemsTap,
      required this.onChaptersTap,
      required this.onReadingModelChanged,
      required this.onDismiss});

  @override
  State<NovelMenu> createState() => NovelMenuState();
}

class NovelMenuState extends State<NovelMenu>
    with SingleTickerProviderStateMixin {
  late int? _currentChapterId;
  late AnimationController animationController;
  late Animation<double> animation;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    animationController = AnimationController(
        duration: const Duration(milliseconds: 500), vsync: this);
    animation = Tween(begin: 0.0, end: 1.0).animate(animationController);
    animation.addListener(() {
      setState(() {});
    });
    animationController.forward();
  }

  @override
  void dispose() {
    animationController.dispose();
    animation.removeListener(() {});
    super.dispose();
  }

  _updateCurrentChapterId(int chapterId) {
    if (mounted) {
      setState(() {
        _currentChapterId = chapterId;
      });
    }
  }

  hide() {
    animationController.reverse();
  }

  buildTopView(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    return Positioned(
      top: -(mediaQuery.padding.top + ReaderUtil.menuHeight) *
          (1 - animation.value),
      left: 0,
      right: 0,
      child: SizedBox(
        height: mediaQuery.padding.top + ReaderUtil.menuHeight,
        child: NovelTopBar(
            preferencesController: widget.preferencesController,
            onItemPressed: (index) {
              widget.onTopItemsTap(index);
            }),
      ),
    );
  }

  buildBottomView(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    return Positioned(
      bottom: -(mediaQuery.padding.bottom + ReaderUtil.menuHeight) *
          (1 - animation.value),
      left: 0,
      right: 0,
      child: SizedBox(
        height: mediaQuery.padding.bottom + ReaderUtil.menuHeight,
        child: NovelBottomBar(
          arguments: widget.arguments,
          detailInfoResultModel: widget.detailInfoResultModel,
          currentChapterId: _currentChapterId,
          preferencesController: widget.preferencesController,
          onChaptersTap: (item) {
            _updateCurrentChapterId(item.id);
            widget.onChaptersTap(item);
          },
          onReadingModelChanged: widget.onReadingModelChanged,
          onDismiss: widget.onDismiss,
          onItemPressed: () {
            widget.onTopItemsTap(-1);
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    _currentChapterId = widget.currentChapterId;
    return Stack(
      children: <Widget>[
        buildTopView(context),
        buildBottomView(context),
      ],
    );
  }
}
