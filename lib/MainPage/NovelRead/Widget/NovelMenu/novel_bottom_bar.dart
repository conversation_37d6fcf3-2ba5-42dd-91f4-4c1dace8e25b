import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Util/Extensions/colorUtil.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../Util/Common/CommonManager/common_manager.dart';
import '../../../../Util/DataReportManager/event_name_config.dart';
import '../../../../Util/DataReportManager/event_report_manager.dart';
import '../../../../Util/SheetAndAlter/bottom_sheet.dart';
import '../../../../Util/StatusManagement/status_management.dart';
import '../../../../Util/enum.dart';
import '../../../../Util/genericUtil.dart';
import '../../Model/book_detailInfo_model.dart';

class NovelBottomBar extends BaseFulWidget {
  final BookDetailInfoResultModel? detailInfoResultModel;
  final int? currentChapterId;
  final NovelPreferencesSettingController preferencesController;
  final Function(dynamic item) onChaptersTap;
  final VoidCallback? onDismiss;
  final VoidCallback? onItemPressed;
  final VoidCallback? onReadingModelChanged;

  // 偏好设置控制器

  NovelBottomBar(
      {super.key,
      required super.arguments,
      required this.detailInfoResultModel,
      required this.currentChapterId,
      required this.preferencesController,
      required this.onChaptersTap,
      required this.onDismiss,
      required this.onItemPressed,
      required this.onReadingModelChanged});

  @override
  State<NovelBottomBar> createState() => _NovelBottomBarState();
}

class _NovelBottomBarState extends State<NovelBottomBar> {
  NovelBottomBarType currentBottomBarType = NovelBottomBarType.none;
  final _characterKey = GlobalKey<NovelCharacterSheetState>();
  late int? _bookId;

  //阅读状态管理器
  final NovelReadStatusController novelReadStatusController =
      findGetXInstance(NovelReadStatusController());

  @override
  void initState() {
    super.initState();

    _bookId = widget.arguments['bookId'];
  }

  @override
  dispose() {
    super.dispose();
  }

  ///添加到书架
  addBookToLibrary(int dataType) async {
    await CommonManager.instance.addBookToLibrary(_bookId, dataType, (isSuccess) {
      if (isSuccess) {
        if (dataType == 1) {
          novelReadStatusController.setAddLibrary(true);
          EventReportManager.eventReportOfFirebase(clickReadLibrary);
        } else {
          if (dataType == 2) {
            novelReadStatusController.setLiked(true);
            novelReadStatusController.updateLikes(true);
            EventReportManager.eventReportOfFirebase(clickReadLike);
          }
        }
      }
    });
  }

  ///从书架移除
  cancelBookFromLibrary(int dataType) async {
    await CommonManager.instance.cancelBookFromLibrary(_bookId, dataType, (isSuccess) {
      if (isSuccess) {
        if (dataType == 1) {
          novelReadStatusController.setAddLibrary(false);
        } else {
          if (dataType == 2) {
            novelReadStatusController.setLiked(false);
            novelReadStatusController.updateLikes(false);
          }
        }
      }
    });

    EventReportManager.eventReportOfFirebase(clickReadRemove);
  }

  void bottomBarTap(NovelBottomBarType type) {
    //todo: bar点击事件
    //todo: 有弹窗的类型 需要先移除之前的弹窗
    if (currentBottomBarType == NovelBottomBarType.chapters ||
        currentBottomBarType == NovelBottomBarType.luminance ||
        currentBottomBarType == NovelBottomBarType.character) {
      if (currentBottomBarType == NovelBottomBarType.character) {
        if (_characterKey.currentState != null) {
          if (_characterKey.currentState!.getIsOtherSheetShow()) {
            return;
          } else {
            Get.back();
          }
        }
      } else {
        Get.back();
      }
    }
    if (currentBottomBarType == type) {
      if (type != NovelBottomBarType.add && type != NovelBottomBarType.like) {
        currentBottomBarType = NovelBottomBarType.none;
      }
    } else {
      currentBottomBarType = type;
    }
    setState(() {
      switch (currentBottomBarType) {
        case NovelBottomBarType.chapters:
          showBookBottomSheet(
            context,
            NovelChaptersSheet(
                detailInfoResultModel: widget.detailInfoResultModel,
                currentChapterId: widget.currentChapterId,
                onBookHeaderTap: () {
                  Get.back();
                  widget.onItemPressed?.call();
                },
                onChaptersTap: widget.onChaptersTap),
            HexColor("#F4F5F6"),
            isScrollControlled: true,
            onDismiss: () {
              dismiss(type);
            },
          );
          break;
        case NovelBottomBarType.add:
          if (novelReadStatusController.isAddLibrary.value) {
            cancelBookFromLibrary(1);
          } else {
            addBookToLibrary(1);
          }
          break;
        case NovelBottomBarType.like:
          if (novelReadStatusController.isLiked.value) {
            cancelBookFromLibrary(2);
          } else {
            addBookToLibrary(2);
          }

          break;
        case NovelBottomBarType.luminance:
          showBookBottomSheet(
              context,
              NovelLuminanceSheet(onBgColorElementOnTap: (color, index) {
                //todo: 颜色选择事件
                widget.preferencesController.setThemeColor(color, index);
              }),
              HexColor("#FFFFFF"),
              barrierColor: Colors.transparent,
              isScrollControlled: false,
              onDismiss: () {
                dismiss(type);
              });
          break;
        case NovelBottomBarType.character:
          showBookBottomSheet(
            context,
            NovelCharacterSheet(
              key: _characterKey,
              preferencesController: widget.preferencesController,
              onReadingModelChanged: widget.onReadingModelChanged,
            ),
            HexColor("#FFFFFF"),
            barrierColor: null,
            isScrollControlled: false,
            onDismiss: () {
              dismiss(type);
            },
          );
          break;
        case NovelBottomBarType.none:
          break;
      }
    });
  }

  void dismiss(NovelBottomBarType type) {
    if (currentBottomBarType == NovelBottomBarType.none || currentBottomBarType == type) {
      widget.onDismiss?.call();
    }
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    return Obx(() => Container(
        width: double.infinity,
        color: HexColor(widget.preferencesController.themeColor.value),
        padding: EdgeInsets.only(bottom: mediaQuery.padding.bottom, left: 27, right: 27),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            IconButton(
              icon: Image.asset(
                  currentBottomBarType == NovelBottomBarType.chapters
                      ? "assets/images/novelReading/icon_chapters_selected.png"
                      : "assets/images/novelReading/icon_chapters_normal.png",
                  width: 22,
                  height: 20,
                  fit: BoxFit.cover),
              // 使用默认的返回箭头图标
              onPressed: () {
                bottomBarTap(NovelBottomBarType.chapters);
              },
            ),
            Obx(() => IconButton(
                  icon: Image.asset(
                    novelReadStatusController.isAddLibrary.value
                        ? "assets/images/novelReading/icon_added.png"
                        : "assets/images/novelReading/icon_add.png",
                    width: 26,
                    height: 24,
                    fit: BoxFit.cover,
                  ),
                  onPressed: () {
                    bottomBarTap(NovelBottomBarType.add);
                  },
                )),
            Obx(() => IconButton(
                  icon: Image.asset(
                    novelReadStatusController.isLiked.value
                        ? "assets/images/novelReading/icon_like_selected.png"
                        : "assets/images/novelReading/icon_like_normal.png",
                    width: 23,
                    height: 22,
                    fit: BoxFit.contain,
                  ),
                  onPressed: () {
                    bottomBarTap(NovelBottomBarType.like);
                  },
                )),
            IconButton(
              icon: Image.asset(
                currentBottomBarType == NovelBottomBarType.luminance
                    ? "assets/images/novelReading/icon_luminance_selected.png"
                    : "assets/images/novelReading/icon_luminance_normal.png",
                width: 25,
                height: 25,
                fit: BoxFit.contain,
              ),
              onPressed: () {
                bottomBarTap(NovelBottomBarType.luminance);
              },
            ),
            IconButton(
              icon: Image.asset(
                currentBottomBarType == NovelBottomBarType.character
                    ? "assets/images/novelReading/icon_character_selected.png"
                    : "assets/images/novelReading/icon_character_normal.png",
                width: 19,
                height: 21,
                fit: BoxFit.contain,
              ),
              onPressed: () {
                bottomBarTap(NovelBottomBarType.character);
              },
            ),
          ],
        )));
  }
}
