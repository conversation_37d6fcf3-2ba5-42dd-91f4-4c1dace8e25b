import 'dart:math' as math;

import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Util/Extensions/colorUtil.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../Util/Common/CommonManager/common_manager.dart';
import '../../../../Util/StatusManagement/status_management.dart';

class NovelTopBar extends BaseFulWidget {
  final NovelPreferencesSettingController preferencesController;
  final Function(int) onItemPressed;

  NovelTopBar(
      {super.key,
      required this.preferencesController,
      required this.onItemPressed});

  @override
  State<NovelTopBar> createState() => _NovelTopBarStateState();
}

class _NovelTopBarStateState extends State<NovelTopBar> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Container(
        width: double.infinity,
        color: HexColor(widget.preferencesController.themeColor.value),
        padding: const EdgeInsets.only(top: 26),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 11),
              child: IconButton(
                icon: Transform.rotate(
                  angle: CommonManager.instance.isReverse() ? math.pi : 0,
                  child: Image.asset("assets/images/common/icon_back_black.png",
                      width: 13, height: 21),
                ),
                // 使用默认的返回箭头图标
                onPressed: () {
                  widget.onItemPressed(0);
                },
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(right: 16),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: Image.asset(
                      'assets/images/bookDetails/icon_download.png',
                      width: 28,
                      height: 24,
                      fit: BoxFit.contain,
                    ),
                    onPressed: () {
                      // TODO: Download book
                      widget.onItemPressed(1);
                    },
                  ),
                  IconButton(
                    icon: Image.asset(
                      'assets/images/bookDetails/icon_share.png',
                      width: 24,
                      height: 24,
                      fit: BoxFit.contain,
                    ),
                    onPressed: () {
                      // TODO: Share book
                      widget.onItemPressed(2);
                    },
                  ),
                  IconButton(
                    icon: Image.asset(
                      'assets/images/bookDetails/icon_error.png',
                      width: 26,
                      height: 26,
                      fit: BoxFit.contain,
                    ),
                    onPressed: () {
                      widget.onItemPressed(3);
                    },
                  ),
                ],
              ),
            )
          ],
        )));
  }
}
