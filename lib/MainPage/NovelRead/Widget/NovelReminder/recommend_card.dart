import 'package:flutter/cupertino.dart';
import 'package:UrNovel/BaseWidget/base_less_widget.dart';
import 'package:UrNovel/Util/Common/network_image_util.dart';

import '../../../../Util/Extensions/colorUtil.dart';
import '../../../Read/Model/ForYou/home_read_model.dart';

class RecommendCard extends BaseLessWidget {
  final Size imgSize;
  final HomeReadBookItem? bookItem;

  RecommendCard(
      {super.key, required this.imgSize, required this.bookItem});

  @override
  Widget build(BuildContext context) {
    return Row(children: [
      NetworkImageUtil(
          imageUrl: bookItem?.cover ?? '',
          width: imgSize.width,
          height: imgSize.height),
      SizedBox(width: 7),
      Expanded(
          child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
            SizedBox(height: 8),
            Text(bookItem?.title?? '',
                style: TextStyle(
                    fontSize: 15,
                    color: HexColor('#000000'),
                    fontWeight: FontWeight.bold),
                maxLines: 3,
                overflow: TextOverflow.ellipsis),
            Spacer(),
            Text(bookItem?.authorName ?? '',
                style: TextStyle(
                    fontSize: 14,
                    color: HexColor('#555A65'),
                    fontWeight: FontWeight.bold),
                maxLines: 1,
                overflow: TextOverflow.ellipsis),
            SizedBox(height: 6),
          ])),
      SizedBox(width: 9),
    ]);
  }
}
