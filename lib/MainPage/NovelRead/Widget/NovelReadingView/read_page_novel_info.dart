import 'package:UrNovel/BaseWidget/base_less_widget.dart';
import 'package:UrNovel/Util/Common/network_image_util.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../Util/Common/CommonManager/common_manager.dart';
import '../../../../Util/DataReportManager/event_name_config.dart';
import '../../../../Util/DataReportManager/event_report_manager.dart';
import '../../../../Util/Extensions/colorUtil.dart';
import '../../../../Util/ReaderUtils/readerUtil.dart';
import '../../../../Util/StatusManagement/status_management.dart';
import '../../../../Util/genericUtil.dart';
import '../../Model/book_detailInfo_model.dart';

class ReadPageNovelInfo extends BaseLessWidget {
  final BookDetailInfoResultModel? bookDetailInfo;
  final bool isHideSwipeNotification;
  final NovelPreferencesSettingController preferencesController;

  ReadPageNovelInfo(
      {super.key,
      required this.bookDetailInfo,
      this.isHideSwipeNotification = false,
      required this.preferencesController});

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var tagList = bookDetailInfo?.tagList;
    var tagListCount = tagList?.length ?? 0;
    return SingleChildScrollView(
      child: Padding(
          padding: EdgeInsets.only(top: mediaQuery.padding.top + 20),
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 26),
                child: Align(
                  alignment: CommonManager.instance.isContentReverse()
                      ? Alignment.centerRight
                      : Alignment.centerLeft,
                  child: Obx(() => Text.rich(
                        TextSpan(
                          text: bookDetailInfo?.title ?? '--',
                          style: TextStyle(
                              color: HexColor(
                                  preferencesController.titleColor.value),
                              fontSize: 21,
                              fontWeight: FontWeight.bold),
                        ),
                      )),
                ),
              ),
              SizedBox(height: 20),
              Padding(
                  padding: EdgeInsets.symmetric(horizontal: 26),
                  child: GestureDetector(
                      onTap: () async {
                        EventReportManager.eventReportOfFirebase(openAuthor);
                        await Get.toNamed('/bookAuthorPage',
                            arguments: {"authorId": bookDetailInfo?.authorId});
                      },
                      child: Row(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(11),
                            child: NetworkImageUtil(
                                imageUrl: bookDetailInfo?.authorCover,
                                w: 44,
                                h: 44,
                                width: 22,
                                height: 22),
                          ),
                          SizedBox(width: 9),
                          Text(bookDetailInfo?.authorName ?? '--',
                              style: TextStyle(
                                  fontSize: 15,
                                  color: HexColor('#176DE4'),
                                  fontWeight: FontWeight.bold),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis)
                        ],
                      ))),
              SizedBox(height: 27),
              Padding(
                  padding: EdgeInsets.symmetric(horizontal: 30),
                  child: ReadPageNovelDataInfo(bookDetailInfo: bookDetailInfo)),
              SizedBox(height: 15),
              Padding(
                  padding: EdgeInsets.symmetric(horizontal: 28),
                  child: SizedBox(
                    height: 24,
                    child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: tagListCount,
                        itemBuilder: (context, index) {
                          if (index < tagListCount) {
                            String? tagStr = tagList?[index];
                            return Padding(
                              padding: EdgeInsets.only(
                                  right: index != tagListCount - 1 ? 10 : 0),
                              child: DecoratedBox(
                                decoration: BoxDecoration(
                                  color: HexColor('#EAEDEE'),
                                  borderRadius: const BorderRadius.all(
                                      Radius.circular(12)),
                                ),
                                child: Padding(
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 16),
                                    child: Center(
                                      child: Text(
                                        tagStr ?? '--',
                                        style: TextStyle(
                                            color: HexColor("#7F8185"),
                                            fontSize: 13,
                                            fontWeight: FontWeight.bold),
                                      ),
                                    )),
                              ),
                            );
                          }

                          return Container();
                        }),
                  )),
              SizedBox(height: 18),
              Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    children: [
                      DecoratedBox(
                        decoration: BoxDecoration(
                            color: HexColor('#FFFFFF'),
                            borderRadius:
                                const BorderRadius.all(Radius.circular(12))),
                        child: Stack(
                          children: [
                            Positioned(
                              right: 0,
                              bottom: 0,
                              width: 100,
                              height: 92,
                              child: Image.asset(
                                  'assets/images/novelReading/icon_info_synopsis_bg.png'),
                            ),
                            Column(
                              children: [
                                Padding(
                                  padding: EdgeInsets.only(
                                      top: 13, left: 9, right: 9),
                                  child: Row(
                                    children: [
                                      Image.asset(
                                          'assets/images/novelReading/icon_info_synopsis.png',
                                          width: 16,
                                          height: 14),
                                      SizedBox(width: 11),
                                      Text('synopsis'.tr,
                                          style: TextStyle(
                                              fontSize: 17,
                                              color: HexColor('#000000'),
                                              fontWeight: FontWeight.bold))
                                    ],
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(
                                      top: 14, left: 14, right: 6, bottom: 14),
                                  child: Obx(
                                    () => Text(
                                      bookDetailInfo?.description ?? '--',
                                      style: TextStyle(
                                          fontSize:
                                              ReaderUtil.instance.fontSize,
                                          color: HexColor(
                                              ReaderUtil.instance.titleColor),
                                          fontWeight:
                                              ReaderUtil.instance.fontWeight,
                                          height: ReaderUtil.instance.lineSpace,
                                          fontFamily: preferencesController
                                              .fontFamily.value),
                                    ),
                                  ),
                                )
                              ],
                            )
                          ],
                        ),
                      )
                    ],
                  )),
              if (!isHideSwipeNotification)
                Padding(
                    padding: EdgeInsets.only(top: 32, bottom: 32),
                    child: DecoratedBox(
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                                color: HexColor('#676769'), width: 1)),
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: 20, vertical: 10),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text('swipe_left'.tr,
                                  style: TextStyle(
                                      fontSize: 14,
                                      color: HexColor('#676769'),
                                      fontWeight: FontWeight.bold)),
                              SizedBox(width: 10),
                              Image.asset(
                                  'assets/images/novelReading/icon_swipe_left.png',
                                  width: 15,
                                  height: 11)
                            ],
                          ),
                        )))
            ],
          )),
    );
  }
}

class ReadPageNovelDataInfo extends StatefulWidget {
  final BookDetailInfoResultModel? bookDetailInfo;

  const ReadPageNovelDataInfo({super.key, required this.bookDetailInfo});

  @override
  State<ReadPageNovelDataInfo> createState() => _ReadPageNovelDataInfoState();
}

class _ReadPageNovelDataInfoState extends State<ReadPageNovelDataInfo> {
  @override
  Widget build(BuildContext context) {
    //阅读状态管理器
    final NovelReadStatusController novelReadStatusController =
        findGetXInstance(NovelReadStatusController());

    int likesValue = int.parse(widget.bookDetailInfo?.likes ?? 0.toString());
    int readsValue = int.parse(widget.bookDetailInfo?.reads ?? 0.toString());
    int timeValue = int.parse(widget.bookDetailInfo?.time ?? 0.toString());

    int hour = timeValue ~/ 3600;
    int minute = (timeValue % 3600) ~/ 60;

    Widget timeWidget() {
      if (0 < hour && 0 < minute) {
        return Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text('$hour',
                style: TextStyle(
                    fontSize: 13,
                    color: HexColor('#555A65'),
                    fontWeight: FontWeight.bold,
                    height: 0.8)),
            Text('h',
                style: TextStyle(
                    fontSize: 10,
                    color: HexColor('#555A65'),
                    fontWeight: FontWeight.bold,
                    height: 0.8)),
            Text('$minute',
                style: TextStyle(
                    fontSize: 13,
                    color: HexColor('#555A65'),
                    fontWeight: FontWeight.bold,
                    height: 0.8)),
            Text('m',
                style: TextStyle(
                    fontSize: 10,
                    color: HexColor('#555A65'),
                    fontWeight: FontWeight.bold,
                    height: 0.8)),
          ],
        );
      } else if (0 < hour) {
        return Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text('$hour',
                style: TextStyle(
                    fontSize: 13,
                    color: HexColor('#555A65'),
                    fontWeight: FontWeight.bold,
                    height: 0.8)),
            Text('h',
                style: TextStyle(
                    fontSize: 10,
                    color: HexColor('#555A65'),
                    fontWeight: FontWeight.bold,
                    height: 0.8)),
          ],
        );
      } else if (0 < minute) {
        return Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text('$minute',
                style: TextStyle(
                    fontSize: 13,
                    color: HexColor('#555A65'),
                    fontWeight: FontWeight.bold,
                    height: 0.8)),
            Text('m',
                style: TextStyle(
                    fontSize: 10,
                    color: HexColor('#555A65'),
                    fontWeight: FontWeight.bold,
                    height: 0.8)),
          ],
        );
      } else {
        return Text('0',
            style: TextStyle(
                fontSize: 13,
                color: HexColor('#555A65'),
                fontWeight: FontWeight.bold,
                height: 0.8));
      }
    }

    Widget otherWidget(int count) {
      if (count < 1000) {
        return Text('$count',
            style: TextStyle(
                fontSize: 13,
                color: HexColor('#555A65'),
                fontWeight: FontWeight.bold,
                height: 0.8));
      } else {
        return Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text((count / 1000).toStringAsFixed(1),
                style: TextStyle(
                    fontSize: 13,
                    color: HexColor('#555A65'),
                    fontWeight: FontWeight.bold,
                    height: 0.8)),
            Text('k',
                style: TextStyle(
                    fontSize: 10,
                    color: HexColor('#555A65'),
                    fontWeight: FontWeight.bold,
                    height: 0.8))
          ],
        );
      }
    }

    return Row(
      children: [
        Row(
          children: [
            Image.asset('assets/images/novelReading/icon_info_view.png',
                width: 15, height: 11),
            SizedBox(width: 5),
            otherWidget(readsValue)
          ],
        ),
        SizedBox(width: 16),
        Row(
          children: [
            Image.asset('assets/images/novelReading/icon_like_selected.png',
                width: 15, height: 11),
            SizedBox(width: 5),
            Obx(() => otherWidget(novelReadStatusController.likes.value))
          ],
        ),
        SizedBox(width: 16),
        Row(
          children: [
            Image.asset('assets/images/novelReading/icon_info_time.png',
                width: 15, height: 11),
            SizedBox(width: 5),
            timeWidget()
          ],
        )
      ],
    );
  }
}
