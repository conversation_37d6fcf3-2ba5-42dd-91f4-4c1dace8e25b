import 'dart:math';

import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/MainPage/NovelRead/Widget/NovelReadingView/read_chapter_end.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:photo_view/photo_view.dart';

import '../../../../Util/Common/CommonManager/common_manager.dart';
import '../../../../Util/Common/network_image_util.dart';
import '../../../../Util/Extensions/colorUtil.dart';
import '../../../../Util/ReaderUtils/novel_page_agent.dart';
import '../../../../Util/ReaderUtils/readerUtil.dart';
import '../../../../Util/StatusManagement/status_management.dart';
import '../../../../Util/enum.dart';
import '../../Model/content_list_model.dart';
import '../../Model/illustration_rule_model.dart';
import '../../ViewModel/ViewModel.dart';

class NovelContentView extends BaseFulWidget {
  final int? bookId;
  final ContentModel? contentModel;
  final int page;
  final NovelPreferencesSettingController preferencesController;
  final NovelReadStatusController novelReadStatusController;
  final VoidCallback? onGiftsTap;
  final Function(Map<String, dynamic>? purchaseInfo)? onRefreshDataBack;

  NovelContentView(
      {super.key,
      required this.bookId,
      required this.contentModel,
      required this.page,
      required this.preferencesController,
      required this.novelReadStatusController,
      required this.onGiftsTap,
      required this.onRefreshDataBack,
      required super.arguments});

  @override
  State<NovelContentView> createState() => _NovelReadingViewState();
}

class _NovelReadingViewState extends State<NovelContentView> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var content = widget.contentModel?.getPageContent(widget.page);
    if (isAvailable(content)) {
      int lastPage = widget.contentModel!.pageCount - 1;
      bool isShowPremium = isAvailable(widget.contentModel?.firstShowLineHeight) &&
          0 < widget.contentModel!.firstShowLineHeight!;
      bool isShowEnd = false;
      if (widget.page == lastPage && !isShowPremium) {
        //非最后一章，最后一页
        isShowEnd = true;
      }
      return SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.page == lastPage)
              NovelContentPart(
                  bookId: widget.bookId,
                  chapterId: widget.contentModel?.id,
                  content: content ?? "",
                  preferencesController: widget.preferencesController,
                  novelReadStatusController: widget.novelReadStatusController)
            else
              Expanded(
                  child: NovelContentPart(
                      bookId: widget.bookId,
                      chapterId: widget.contentModel?.id,
                      content: content ?? "",
                      preferencesController: widget.preferencesController,
                      novelReadStatusController: widget.novelReadStatusController)),
            if (isShowEnd)
              Padding(
                  padding: EdgeInsets.only(top: 30),
                  child: NovelReadingChapterEnd(
                      onGiftsTap: () {
                        widget.onGiftsTap?.call();
                      },
                      onRefreshDataBack: (info) {
                        widget.onRefreshDataBack?.call(info);
                      },
                      arguments: widget.arguments))
          ],
        ),
      );
    } else {
      return Padding(
          padding: const EdgeInsets.only(top: 30),
          child: NovelReadingChapterEnd(
            onGiftsTap: () {
              widget.onGiftsTap?.call();
            },
            onRefreshDataBack: (info) {
              widget.onRefreshDataBack?.call(info);
            },
            arguments: widget.arguments,
          ));
    }
  }
}

class NovelContentPart extends StatefulWidget {
  final int? bookId;
  final int? chapterId;
  final String content;
  final NovelPreferencesSettingController preferencesController;
  final NovelReadStatusController novelReadStatusController;

  const NovelContentPart(
      {super.key,
      required this.bookId,
      required this.chapterId,
      required this.content,
      required this.preferencesController,
      required this.novelReadStatusController});

  @override
  State<NovelContentPart> createState() => _NovelContentPartState();
}

class _NovelContentPartState extends State<NovelContentPart> {
  late bool _illustrationLiked;
  final _urlReg = RegExp(ruleString);
  late bool _isHorizontal;

  @override
  void initState() {
    super.initState();
    _illustrationLiked = false;
    _isHorizontal = widget.preferencesController.readingMode.value == ReadingMode.horizontalScroll;
  }

  @override
  void dispose() {
    super.dispose();
  }

  List<InlineSpan> _initWidget() {
    int lastEnd = 0;
    final List<InlineSpan> spans = [];
    final matches = _urlReg.allMatches(widget.content);
    for (final match in matches) {
      if (match.start > lastEnd) {
        spans.add(TextSpan(text: widget.content.substring(lastEnd, match.start)));
      }
      final matchStr = match.group(0)!;
      final urlMatch = _urlReg.firstMatch(matchStr);
      if (isAvailable(urlMatch)) {
        //尽可能保持差异化，避免tag重复
        String illustrationTag = "illustration_${getCurrentTimeMillis()}_${Random().nextInt(1000)}";

        final ruleString = urlMatch!.group(0)!.replaceAll(' ', '');
        IllustrationRuleModel? model = NovelPageAgent.parseRuleString(ruleString);
        _illustrationLiked = widget.novelReadStatusController.illustrationList.contains(model?.id);

        ///插图浏览上报
        _illustrationEvent(IllustrationEventType.VIEW, model);

        late double hPadding = 20;
        if (isAvailable(model?.w) && isAvailable(model?.h)) {
          illustrationRatio = model!.w! / model.h!;

          if (illustrationRatio < contentSize.width / contentSize.height) {
            illustrationRatio = contentSize.width / contentSize.height;

            hPadding = contentSize.width / contentSize.height * 70;
          }
        }

        spans.add(WidgetSpan(
            alignment: PlaceholderAlignment.middle,
            child: Padding(
              padding:
                  EdgeInsets.only(top: _isHorizontal ? 10 : 20, bottom: _isHorizontal ? 5 : 20),
              child: SizedBox(
                  child: Stack(
                children: [
                  Positioned(
                      child: Hero(
                          tag: illustrationTag,
                          child: AspectRatio(
                            aspectRatio: illustrationRatio,
                            child: NetworkImageUtil(
                              imageUrl: model?.url,
                              fit: BoxFit.contain,
                            ),
                          ))),
                  Positioned(
                    bottom: 12,
                    right: hPadding,
                    child: Column(
                      children: [
                        SizedBox(
                          width: 40,
                          height: 40,
                          child: IconButton(
                            icon: Obx(() {
                              _illustrationLiked = widget.novelReadStatusController.illustrationList
                                  .contains(model?.id);
                              return Image.asset(_illustrationLiked
                                  ? "assets/images/novelReading/icon_novel_illustration_liked.png"
                                  : "assets/images/novelReading/icon_novel_illustration_unLike.png");
                            }),
                            style: IconButton.styleFrom(padding: EdgeInsets.zero),
                            onPressed: () async {
                              //插图喜欢点击上报
                              await _illustrationEvent(
                                  _illustrationLiked
                                      ? IllustrationEventType.UNLIKE
                                      : IllustrationEventType.LIKE,
                                  model);
                            },
                          ),
                        ),
                        const SizedBox(height: 17),
                        SizedBox(
                          width: 40,
                          height: 40,
                          child: IconButton(
                            icon: Image.asset(
                                "assets/images/novelReading/icon_novel_illustration_scale.png"),
                            iconSize: 40,
                            style: IconButton.styleFrom(padding: EdgeInsets.zero),
                            onPressed: () {
                              showDialog(
                                context: context,
                                barrierColor: Colors.black,
                                builder: (_) => GestureDetector(
                                  onTap: () => Get.back(),
                                  child: Center(
                                    child: PhotoView.customChild(
                                      backgroundDecoration:
                                          BoxDecoration(color: Colors.transparent),
                                      heroAttributes: PhotoViewHeroAttributes(tag: illustrationTag),
                                      child: NetworkImageUtil(
                                        imageUrl: model?.url,
                                        fit: BoxFit.contain,
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        )
                      ],
                    ),
                  )
                ],
              )),
            )));
      } else {
        spans.add(TextSpan(text: matchStr));
      }
      lastEnd = match.end;
    }
    if (lastEnd < widget.content.length) {
      spans.add(TextSpan(text: widget.content.substring(lastEnd)));
    }

    return spans;
  }

  ///添加到书架
  Future<void> _illustrationEvent(
      IllustrationEventType eventType, IllustrationRuleModel? model) async {
    Map<String, dynamic> params = {
      "type": eventType.name,
      "illustrationId": model?.id,
      "illustrationUrl": model?.url,
      "bookId": widget.bookId,
      "chapterId": widget.chapterId
    };
    await NovelReadViewModel.illustrationEvent(params, (isSuccess) {
      if (isSuccess) {
        if (eventType == IllustrationEventType.VIEW) {
          return;
        }

        eventBusFire(model);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final List<InlineSpan> spans = _initWidget();
    return Obx(() {
      return Padding(
          padding: EdgeInsets.symmetric(
              horizontal: _isHorizontal ? widget.preferencesController.horizontalPadding.value : 0),
          child: RichText(
              text: TextSpan(
                style: TextStyle(
                    color: HexColor(widget.preferencesController.titleColor.value),
                    fontSize: widget.preferencesController.fontSize.value,
                    fontWeight: ReaderUtil.instance.fontWeight,
                    height: widget.preferencesController.lineSpace.value,
                    fontFamily: widget.preferencesController.fontFamily.value),
                children: spans,
              ),
              textAlign:
                  CommonManager.instance.isContentReverse() ? TextAlign.right : TextAlign.left));
    });
  }
}
