import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Util/Common/CommonManager/common_manager.dart';
import 'package:flutter/cupertino.dart';

import '../../../../../Launch&Login/Model/user_model.dart';
import '../../../../../Util/Common/Animation/lottie_animation_view.dart';
import '../../../../../Util/StatusManagement/status_management.dart';
import '../../../../../Util/enum.dart';
import '../../../../../Util/logUtil.dart';
import '../../../../../Util/tools.dart';
import '../../../../BookInfo/Model/book_author_info.dart';
import '../../../../Profile/Model/goods_info_model.dart';
import '../../../Model/book_detailInfo_model.dart';
import '../../../Model/content_list_model.dart';
import '../../../ViewModel/ViewModel.dart';
import '../read_page_next_novel_info.dart';
import '../read_page_view.dart';

class ReadPageHorizontal extends BaseFulWidget {
  final ChapterVoModel? currentChapter;
  final ContentModel? preArticle;
  final ContentModel? currentArticle;
  final ContentModel? nextArticle;
  final PageController pageController;
  final BookDetailInfoResultModel? bookDetailInfo;
  final int chapterListLength;
  final BookDetailJumpType? pageJumpType;
  final List<BookDetailInfoResultModel?>? nextBookInfoList;
  final NovelPreferencesSettingController preferencesController;
  final NovelReadStatusController novelReadStatusController;
  final UserInfoModel? userInfoModel;
  final GoodsListItem? premiumGoodItem;
  final List<GoodsListItem>? subCoinGoodList;
  final Function(int? chapterId)? onRechargeCallBack;
  final Function(ScrollNotification notification)? onScrollCallBack;
  final VoidCallback? onJumpCallBack;
  final Function(Map<String, dynamic>? purchaseInfo)? onRefreshDataBack;
  final Function(int) onPageChanged;
  final Function(int? currentIndex, List<BookDetailInfoResultModel?>? nextBookInfoList)?
      onNextBookCallBack;
  final Function(BookDetailInfoResultModel? bookDetailInfo) onNextBookTap;

  ReadPageHorizontal({
    super.key,
    required this.currentChapter,
    required this.preArticle,
    required this.currentArticle,
    required this.nextArticle,
    required this.pageController,
    required this.bookDetailInfo,
    required this.chapterListLength,
    required this.pageJumpType,
    required this.nextBookInfoList,
    required this.preferencesController,
    required this.novelReadStatusController,
    required this.userInfoModel,
    required this.premiumGoodItem,
    required this.subCoinGoodList,
    required this.onRechargeCallBack,
    required this.onScrollCallBack,
    required this.onJumpCallBack,
    required this.onRefreshDataBack,
    required this.onPageChanged,
    required this.onNextBookCallBack,
    required this.onNextBookTap,
  });

  @override
  State<ReadPageHorizontal> createState() => ReadPageHorizontalState();
}

class ReadPageHorizontalState extends State<ReadPageHorizontal> {
  late int _currentIndex; //当前页面索引
  late int? _nextBookId; //下一本书id
  late List<BookDetailInfoResultModel?>? _nextBookInfoList; // 下一本书集合

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    onInitData(widget.bookDetailInfo);
  }

  @override
  void dispose() {
    super.dispose();
  }

  onInitData(BookDetailInfoResultModel? bookDetailInfo) {
    _currentIndex = 0;
    _nextBookId = null;
    _nextBookInfoList = [];
    if (isAvailable(bookDetailInfo)) {
      if (isAvailable(widget.nextBookInfoList)) {
        _nextBookInfoList = widget.nextBookInfoList;
        if (isAvailable(_nextBookInfoList?.last)) {
          _nextBookId = _nextBookInfoList?.last?.nextBookId;
        } else {
          if (_nextBookInfoList!.length <= 1) {
            _nextBookId = bookDetailInfo?.nextBookId;
          } else {
            _nextBookId = _nextBookInfoList?[_nextBookInfoList!.length - 2]?.nextBookId;
          }
        }
      } else {
        _nextBookId = bookDetailInfo?.nextBookId;
        _nextBookInfoList = isAvailable(_nextBookId) ? [null] : [];
      }
    }
  }

  Future<void> _onGetNextBookInfo(int? bookId) async {
    if (!isAvailable(bookId)) {
      return;
    }

    BookDetailInfoResultModel? bookDetailInfo =
        await NovelReadViewModel.getBookDetailInfo(bookId, widget.pageJumpType, isAll: false);
    _nextBookId = bookDetailInfo?.nextBookId;
    _nextBookInfoList?.removeLast();
    if (isAvailable(bookDetailInfo)) {
      _nextBookInfoList?.add(bookDetailInfo);
      if (isAvailable(bookDetailInfo?.nextBookId)) {
        _nextBookInfoList?.add(null);
      }
    }

    if (mounted) {
      setState(() {});
    }
  }

  ChapterVoModel? _onGetCurrentChapter(int? chapterId) {
    ChapterVoModel? chapterModel = widget.currentChapter;
    if (isAvailable(chapterId)) {
      if (isAvailable(widget.bookDetailInfo?.chapterVoList)) {
        for (var chapter in widget.bookDetailInfo!.chapterVoList!) {
          if (chapter?.id == chapterId) {
            chapterModel = chapter;
            break;
          }
        }
      }
    }

    return chapterModel;
  }

  _onPageChanged(int index) {
    _currentIndex = index;
    widget.onPageChanged(index);
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    int chapterCount = 0;
    if (isAvailable(widget.preArticle)) {
      chapterCount += widget.preArticle!.pageCount;
    }
    if (isAvailable(widget.currentArticle)) {
      chapterCount += widget.currentArticle!.pageCount;
    }
    if (isAvailable(widget.nextArticle)) {
      chapterCount += widget.nextArticle!.pageCount;
    }

    var nextBookCount = _nextBookInfoList?.length ?? 0;
    int itemCount = chapterCount + nextBookCount;

    return NotificationListener(
        onNotification: (ScrollNotification notification) {
          widget.onScrollCallBack?.call(notification);

          if (notification is ScrollEndNotification) {
            // 获取滚动视图的滚动指标
            final metrics = notification.metrics;
            // 检查是否滑到了底部
            if (metrics.pixels == metrics.maxScrollExtent) {
              logP('滑到了底部');
              if (isAvailable(_nextBookId)) {
                _onGetNextBookInfo(_nextBookId);
              }
            }

            widget.onNextBookCallBack?.call(
                chapterCount <= _currentIndex ? _currentIndex - chapterCount : null,
                _nextBookInfoList);
          }
          return true;
        },
        child: PageView.builder(
            pageSnapping: true,
            physics: const BouncingScrollPhysics(),
            scrollDirection:
                widget.preferencesController.readingMode.value == ReadingMode.verticalScroll
                    ? Axis.vertical
                    : Axis.horizontal,
            controller: widget.pageController,
            itemCount: itemCount,
            onPageChanged: _onPageChanged,
            itemBuilder: (context, int index) {
              if (index < chapterCount) {
                var page = index - (widget.preArticle?.pageCount ?? 0);
                ContentModel? contentModel = widget.currentArticle;
                if (page >= widget.currentArticle!.pageCount) {
                  // 到达下一章了
                  contentModel = widget.nextArticle;
                  page = 0;
                } else if (page < 0) {
                  // 到达上一章了
                  contentModel = widget.preArticle;
                  page = widget.preArticle!.pageCount - 1;
                } else {
                  contentModel = widget.currentArticle;
                }
                ChapterVoModel? chapterModel = _onGetCurrentChapter(contentModel?.id);
                return Directionality(
                  textDirection: CommonManager.instance.isContentReverse()
                      ? TextDirection.rtl
                      : TextDirection.ltr, // 强制文本方向为从右到左
                  child: NovelReadingView(
                      bookDetailInfo: widget.bookDetailInfo,
                      currentChapter: chapterModel,
                      contentModel: contentModel,
                      page: page,
                      userInfoModel: widget.userInfoModel,
                      premiumGoodItem: widget.premiumGoodItem,
                      subCoinGoodList: widget.subCoinGoodList,
                      preferencesController: widget.preferencesController,
                      novelReadStatusController: widget.novelReadStatusController,
                      onRechargeCallBack: widget.onRechargeCallBack,
                      onJumpCallBack: () {
                        widget.onJumpCallBack?.call();
                      },
                      onRefreshDataBack: (info) async {
                        widget.onRefreshDataBack?.call(info);
                        setState(() {});
                      },
                      arguments: {
                        "bookId": widget.bookDetailInfo?.bookId,
                        "bookName": widget.bookDetailInfo?.title,
                        "bookAuthorInfo": BookAuthorResultModel(
                          id: widget.bookDetailInfo?.authorId,
                          name: widget.bookDetailInfo?.authorName,
                          cover: widget.bookDetailInfo?.authorCover,
                          description: widget.bookDetailInfo?.authorDescription,
                        ),
                        "bookGoldCount": widget.bookDetailInfo?.bookGoldCount,
                        "description": widget.bookDetailInfo?.description,
                        "likes": widget.bookDetailInfo?.likes,
                        "shares": "",
                      }),
                );
              } else {
                //todo: 如果是最后一章节，显示下一本书
                var bookInfo = _nextBookInfoList?[index - chapterCount];
                if (isAvailable(bookInfo)) {
                  return NovelReadingViewNextNovelInfo(
                    bookDetailInfo: bookInfo,
                    readingMode: ReadingMode.horizontalScroll,
                    preferencesController: widget.preferencesController,
                    onNextBookTap: widget.onNextBookTap,
                  );
                } else {
                  if (chapterCount == 0) {
                    return SizedBox();
                  }

                  return SizedBox(
                      width: mediaQuery.size.width,
                      height: widget.preferencesController.readingMode.value ==
                              ReadingMode.horizontalScroll
                          ? mediaQuery.size.height
                          : 100,
                      child: LottieAnimationView());
                }
              }
            }));
  }
}
