import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/MainPage/NovelRead/Widget/NovelReadingView/read_chapter_end.dart';
import 'package:UrNovel/MainPage/NovelRead/Widget/NovelReadingView/read_page_next_novel_info.dart';
import 'package:UrNovel/MainPage/NovelRead/Widget/NovelReadingView/read_page_novel_info.dart';
import 'package:UrNovel/Util/Common/CommonManager/common_manager.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

import '../../../../../Launch&Login/Model/user_model.dart';
import '../../../../../Util/Common/Animation/lottie_animation_view.dart';
import '../../../../../Util/Extensions/colorUtil.dart';
import '../../../../../Util/StatusManagement/status_management.dart';
import '../../../../../Util/deviceScreenUtil.dart';
import '../../../../../Util/enum.dart';
import '../../../../../Util/logUtil.dart';
import '../../../../BookInfo/Model/book_author_info.dart';
import '../../../../Profile/Model/goods_info_model.dart';
import '../../../Model/book_detailInfo_model.dart';
import '../../../Model/content_list_model.dart';
import '../../../Page/ChapterUnlock/chapter_unlock_view.dart';
import '../../../ViewModel/ViewModel.dart';
import '../read_content_view.dart';

class ReadPageVertical extends BaseFulWidget {
  final BookDetailInfoResultModel? bookDetailInfo;
  final int chapterIndex;
  final int page;
  final BookDetailJumpType? pageJumpType;
  final int? currentNextBookIndex;
  final List<BookDetailInfoResultModel?>? nextBookInfoList;
  final UserInfoModel? userInfoModel;
  final GoodsListItem? premiumGoodItem;
  final List<GoodsListItem>? subCoinGoodList;

  // 偏好设置控制器
  final NovelPreferencesSettingController preferencesController;
  final NovelReadStatusController novelReadStatusController;
  final Function(int? chapterId)? onRechargeCallBack;
  final Function(ScrollNotification notification)? onScrollCallBack;
  final VoidCallback? onJumpCallBack;
  final Function(Map<String, dynamic>? purchaseInfo)? onRefreshDataBack;
  final Function(int? chapterId, int? chapterIndex, int pageIndex, int? currentIndex,
      List<BookDetailInfoResultModel?>? nextBookInfoList)? onScrollPageOffset;
  final Function(BookDetailInfoResultModel? bookDetailInfo)? onNextBookTap;

  ReadPageVertical({
    super.key,
    required this.bookDetailInfo,
    required this.chapterIndex,
    required this.page,
    required this.pageJumpType,
    required this.currentNextBookIndex,
    required this.nextBookInfoList,
    required this.userInfoModel,
    required this.premiumGoodItem,
    required this.subCoinGoodList,
    required this.preferencesController,
    required this.novelReadStatusController,
    required this.onRechargeCallBack,
    required this.onScrollCallBack,
    required this.onJumpCallBack,
    required this.onRefreshDataBack,
    required this.onScrollPageOffset,
    required this.onNextBookTap,
  });

  @override
  State<ReadPageVertical> createState() => ReadPageVerticalState();
}

class ReadPageVerticalState extends State<ReadPageVertical> {
  final ItemScrollController _scrollController = ItemScrollController();
  final ScrollOffsetController _offsetController = ScrollOffsetController();
  final ItemPositionsListener _itemPositionsListener = ItemPositionsListener.create();

  late int _currentIndex; //当前章节索引
  late int _currentPageOffset; //当前页偏移量-页数
  late int? _nextBookId; //下一本书id
  late List<BookDetailInfoResultModel?>? _nextBookInfoList; // 下一本书集合
  late GlobalKey<PremiumPurchaseViewState> _chapterUnlockKey;

  @override
  void initState() {
    super.initState();
    onInitData(widget.bookDetailInfo);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      checkCurrentChapter();
    });
  }

  @override
  void dispose() {
    _itemPositionsListener.itemPositions.removeListener(_onItemScroll);
    super.dispose();
  }

  Future<void> onInitData(BookDetailInfoResultModel? bookDetailInfo) async {
    _itemPositionsListener.itemPositions.addListener(_onItemScroll);
    _currentIndex = 0;
    _currentPageOffset = 0;
    _nextBookId = null;
    _nextBookInfoList = [];
    _chapterUnlockKey = GlobalKey<PremiumPurchaseViewState>();
    if (isAvailable(bookDetailInfo)) {
      if (isAvailable(widget.nextBookInfoList)) {
        _nextBookInfoList = widget.nextBookInfoList;
        if (isAvailable(_nextBookInfoList?.last)) {
          _nextBookId = _nextBookInfoList?.last?.nextBookId;
        } else {
          if (_nextBookInfoList!.length <= 1) {
            _nextBookId = bookDetailInfo?.nextBookId;
          } else {
            _nextBookId = _nextBookInfoList?[_nextBookInfoList!.length - 2]?.nextBookId;
          }
        }
      } else {
        _nextBookId = bookDetailInfo?.nextBookId;
        _nextBookInfoList = isAvailable(_nextBookId) ? [null] : [];
      }
    }
  }

  void checkCurrentChapter() {
    Future.delayed(Duration(milliseconds: 300)).then((value) {
      if (isAvailable(widget.bookDetailInfo?.chapterVoList) &&
          isAvailable(widget.currentNextBookIndex)) {
        int page =
            (widget.bookDetailInfo?.chapterVoList?.length ?? 0) + widget.currentNextBookIndex!;
        onItemTap(page);
      } else {
        if (isAvailable(widget.chapterIndex) && 0 < widget.chapterIndex) {
          int page = widget.chapterIndex - 1;
          onItemTap(page, page: widget.page);
        }
      }
    });
  }

  Future<void> _onGetNextBookInfo(int? bookId) async {
    if (!isAvailable(bookId)) {
      return;
    }

    BookDetailInfoResultModel? bookDetailInfo =
        await NovelReadViewModel.getBookDetailInfo(bookId, widget.pageJumpType, isAll: false);
    _nextBookId = bookDetailInfo?.nextBookId;
    _nextBookInfoList?.removeLast();
    if (isAvailable(bookDetailInfo)) {
      _nextBookInfoList?.add(bookDetailInfo);
      if (isAvailable(bookDetailInfo?.nextBookId)) {
        _nextBookInfoList?.add(null);
      }
    }

    setState(() {});
  }

  void _onItemScroll() {
    final positions = _itemPositionsListener.itemPositions.value;
    if (positions.isNotEmpty) {
      final firstVisibleItem = positions.first;
      final lastVisibleItem = positions.last;

      if (firstVisibleItem.index == lastVisibleItem.index) {
        _currentIndex = firstVisibleItem.index;
        _currentPageOffset = onVerticalToHorizontal(firstVisibleItem.itemLeadingEdge, 0.8);
      } else {
        if (lastVisibleItem.index < firstVisibleItem.index) {
          if (firstVisibleItem.itemLeadingEdge <= 0.5) {
            _currentIndex = firstVisibleItem.index;
            _currentPageOffset = onVerticalToHorizontal(firstVisibleItem.itemLeadingEdge, 0.8);
          } else {
            _currentIndex = lastVisibleItem.index;
            _currentPageOffset = onVerticalToHorizontal(lastVisibleItem.itemLeadingEdge, 0.8);
          }
        } else {
          if (lastVisibleItem.itemLeadingEdge <= 0.5) {
            _currentIndex = lastVisibleItem.index;
            _currentPageOffset = onVerticalToHorizontal(lastVisibleItem.itemLeadingEdge, 0.8);
          } else {
            _currentIndex = firstVisibleItem.index;
            _currentPageOffset = onVerticalToHorizontal(firstVisibleItem.itemLeadingEdge, 0.8);
          }
        }
      }
    }
  }

  //上下滑动到左右滑动页数转换
  int onVerticalToHorizontal(double value, double scale) {
    int page = 0;
    if (1 - scale <= value) {
      page = 0;
    } else {
      page = (value / scale).toInt().abs();
      if (0 < value % scale) {
        page += 1;
      }
    }

    return page;
  }

  // 章节列表点击，直接切到对应的章节
  void onItemTap(int? index, {int page = 0}) {
    var pageCount =
        (widget.bookDetailInfo?.chapterVoList?.length ?? 0) + (_nextBookInfoList?.length ?? 0);
    if (isAvailable(index) && 0 <= index! && index < pageCount) {
      final positions = _itemPositionsListener.itemPositions.value;
      if (positions.isNotEmpty) {
        _scrollController.jumpTo(index: index);
        _currentIndex = index;
        if (0 < page) {
          _offsetController.animateScroll(
              offset: page * DeviceScreenUtil.instance.height * 0.8,
              duration: Duration(milliseconds: 300));
        }
        if (isAvailable(widget.bookDetailInfo?.chapterVoList) &&
            index < widget.bookDetailInfo!.chapterVoList!.length) {
          ChapterVoModel? item = widget.bookDetailInfo?.chapterVoList?[index];
          //已经解锁了，但是还没有刷新数据
          if (item?.lock == 1 && isAvailable(item?.chapterUnLockKey)) {
            Future.delayed(Duration(milliseconds: 200)).then((value) {
              _onChapterUnlock(item?.chapterUnLockKey);
            });
          }
        }
      }
    }
  }

  // 章节解锁
  void _onChapterUnlock(GlobalKey<PremiumPurchaseViewState>? key) {
    _chapterUnlockKey = key!;
    _chapterUnlockKey.currentState?.onChapterUnlock();
    _chapterUnlockKey.currentState?.showUnlockGoodsAlter();
  }

  //更新自动解锁状态
  void _onUpdateAutoUnlockState(int value) {
    widget.bookDetailInfo?.chapterVoList?.forEach((element) async {
      if (isAvailable(element?.chapterUnLockKey)) {
        if (element?.chapterUnLockKey?.currentState?.autoUnlock != value) {
          await element?.chapterUnLockKey?.currentState?.updateAutoUnlock(value);
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var contentListCount = widget.bookDetailInfo?.contentList?.length ?? 0;
    var nextBookCount = _nextBookInfoList?.length ?? 0;
    var itemCount = contentListCount + nextBookCount;
    return Scaffold(
      backgroundColor: HexColor(widget.preferencesController.themeColor.value),
      body: NotificationListener(
        onNotification: (ScrollNotification notification) {
          widget.onScrollCallBack?.call(notification);

          if (notification is ScrollEndNotification) {
            try {
              ChapterVoModel? item;
              if (isAvailable(widget.bookDetailInfo?.chapterVoList)) {
                if (_currentIndex < contentListCount) {
                  item = widget.bookDetailInfo?.chapterVoList?[_currentIndex];
                } else {
                  item = widget.bookDetailInfo?.chapterVoList?.last;
                }
              }

              if (item?.lock == 1) {
                _currentPageOffset = 0;
                if (isAvailable(item?.chapterUnLockKey)) {
                  _onChapterUnlock(item?.chapterUnLockKey);
                }
              } else {
                if (isAvailable(_chapterUnlockKey.currentState)) {
                  //已经解锁了，但是还没有刷新数据
                  widget.onRechargeCallBack?.call(item?.id);
                }
              }

              widget.onScrollPageOffset?.call(
                  item?.id,
                  item?.chapterIndex,
                  _currentPageOffset,
                  contentListCount <= _currentIndex ? _currentIndex - contentListCount : null,
                  _nextBookInfoList);

              // 获取滚动视图的滚动指标
              final metrics = notification.metrics;
              // 检查是否滑到了底部
              if (metrics.pixels == metrics.maxScrollExtent) {
                logP('滑到了底部');
                _onGetNextBookInfo(_nextBookId);
              }
            } catch (e) {
              logP('onScrollPageOffset error: $e');
            }
          }
          return true;
        },
        child: ScrollablePositionedList.builder(
          itemCount: itemCount,
          itemScrollController: _scrollController,
          scrollOffsetController: _offsetController,
          itemPositionsListener: _itemPositionsListener,
          itemBuilder: (context, index) {
            if (index < contentListCount) {
              ChapterVoModel? item = widget.bookDetailInfo?.chapterVoList?[index];
              ContentModel? contentModel = widget.bookDetailInfo?.contentList?[index];
              return Directionality(
                  textDirection: CommonManager.instance.isContentReverse()
                      ? TextDirection.rtl
                      : TextDirection.ltr, // 强制文本方向为从右到左
                  child: Column(
                    children: [
                      if (index == 0)
                        ReadPageNovelInfo(
                          bookDetailInfo: widget.bookDetailInfo,
                          isHideSwipeNotification: true,
                          preferencesController: widget.preferencesController,
                        ),
                      SizedBox(
                        width: DeviceScreenUtil.instance.width,
                        child: Column(children: [
                          Obx(() => Padding(
                              padding: EdgeInsets.symmetric(
                                  horizontal: widget.preferencesController.horizontalPadding.value),
                              child: Column(
                                children: [
                                  //章节标题
                                  Padding(
                                    padding: EdgeInsets.only(top: 40),
                                    child: Row(
                                      children: [
                                        SizedBox(
                                            width: 5,
                                            height: 31,
                                            child: DecoratedBox(
                                                decoration:
                                                    BoxDecoration(color: HexColor('#820F15')))),
                                        SizedBox(width: 10),
                                        Text(
                                          '${index + 1}'.padLeft(2, '0'),
                                          style: TextStyle(
                                              color: HexColor('#820F15'),
                                              fontSize: widget.preferencesController.fontSize.value,
                                              fontWeight: FontWeight.bold,
                                              fontFamily:
                                                  widget.preferencesController.fontFamily.value,
                                              height: 1.0),
                                        )
                                      ],
                                    ),
                                  ),
                                  SizedBox(height: 10),
                                  if (item?.lock == 1)
                                    NovelReadingChapterUnlock(
                                        chapterUnlockKey: item?.chapterUnLockKey,
                                        bookDetailInfo: widget.bookDetailInfo,
                                        item: item,
                                        model: contentModel,
                                        isCurrentChapter: _currentIndex == index,
                                        preferencesController: widget.preferencesController,
                                        novelReadStatusController: widget.novelReadStatusController,
                                        currentChapterIndex: _currentIndex,
                                        userInfoModel: widget.userInfoModel,
                                        premiumGoodItem: widget.premiumGoodItem,
                                        subCoinGoodList: widget.subCoinGoodList,
                                        onRechargeCallBack: (chapterId) async {
                                          widget.onRechargeCallBack?.call(chapterId);
                                          onItemTap(index);
                                        },
                                        onJumpCallBack: widget.onJumpCallBack,
                                        onRefreshDataBack: widget.onRefreshDataBack,
                                        onLockStateChanged: _onUpdateAutoUnlockState)
                                  else
                                    NovelContent(
                                      item: item,
                                      model: contentModel,
                                      preferencesController: widget.preferencesController,
                                      novelReadStatusController: widget.novelReadStatusController,
                                      isNextBook: false,
                                    ),
                                  //章节结束模块
                                  if (item?.lock != 1)
                                    Padding(
                                      padding: EdgeInsets.only(top: 30, bottom: 80),
                                      child: NovelReadingChapterEnd(
                                        onGiftsTap: () {
                                          widget.onJumpCallBack?.call();
                                        },
                                        onRefreshDataBack: (info) {
                                          widget.onRefreshDataBack?.call(info);
                                        },
                                        arguments: {
                                          "bookId": widget.bookDetailInfo?.bookId,
                                          "bookName": widget.bookDetailInfo?.title,
                                          "bookAuthorInfo": BookAuthorResultModel(
                                            id: widget.bookDetailInfo?.authorId,
                                            name: widget.bookDetailInfo?.authorName,
                                            cover: widget.bookDetailInfo?.authorCover,
                                            description: widget.bookDetailInfo?.authorDescription,
                                          ),
                                          "bookGoldCount": widget.bookDetailInfo?.bookGoldCount,
                                          "description": widget.bookDetailInfo?.description,
                                          "likes": widget.bookDetailInfo?.likes,
                                          "shares": "",
                                        },
                                      ),
                                    )
                                ],
                              ))),
                        ]),
                      )
                    ],
                  ));
            } else if (index - contentListCount < nextBookCount) {
              var bookInfo = _nextBookInfoList?[index - contentListCount];
              if (isAvailable(bookInfo)) {
                if (isAvailable(bookInfo)) {
                  return NovelReadingViewNextNovelInfo(
                    bookDetailInfo: _nextBookInfoList?[index - contentListCount],
                    preferencesController: widget.preferencesController,
                    onNextBookTap: widget.onNextBookTap,
                  );
                }
              } else {
                if (contentListCount == 0) {
                  return SizedBox();
                }

                return SizedBox(
                    width: mediaQuery.size.width,
                    height: widget.preferencesController.readingMode.value ==
                            ReadingMode.horizontalScroll
                        ? mediaQuery.size.height
                        : 100,
                    child: LottieAnimationView());
              }
            }

            return Container();
          },
        ),
      ),
    );
  }
}

///章节需要解锁模块
class NovelReadingChapterUnlock extends StatefulWidget {
  final GlobalKey<PremiumPurchaseViewState>? chapterUnlockKey;
  final BookDetailInfoResultModel? bookDetailInfo;
  final ChapterVoModel? item;
  final ContentModel? model;
  final bool isCurrentChapter;
  final NovelPreferencesSettingController preferencesController;
  final NovelReadStatusController novelReadStatusController;
  final int? currentChapterIndex;
  final UserInfoModel? userInfoModel;
  final GoodsListItem? premiumGoodItem;
  final List<GoodsListItem>? subCoinGoodList;
  final Function(int? chapterId)? onRechargeCallBack;
  final VoidCallback? onJumpCallBack;
  final Function(Map<String, dynamic>? purchaseInfo)? onRefreshDataBack;
  final Function(int value)? onLockStateChanged;

  const NovelReadingChapterUnlock({
    super.key,
    required this.chapterUnlockKey,
    required this.bookDetailInfo,
    required this.item,
    required this.model,
    required this.isCurrentChapter,
    required this.preferencesController,
    required this.novelReadStatusController,
    required this.currentChapterIndex,
    required this.userInfoModel,
    required this.premiumGoodItem,
    required this.subCoinGoodList,
    required this.onRechargeCallBack,
    required this.onJumpCallBack,
    required this.onRefreshDataBack,
    required this.onLockStateChanged,
  });

  @override
  State<NovelReadingChapterUnlock> createState() => NovelReadingChapterUnlockState();
}

class NovelReadingChapterUnlockState extends State<NovelReadingChapterUnlock> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    bool isGoldCoinEnabled = (widget.item?.cost ?? 0) <= (widget.userInfoModel?.goldCoin ?? 0);
    return SizedBox(
      width: double.infinity,
      height: mediaQuery.size.height - (isGoldCoinEnabled ? 160 : 50),
      child: Stack(
        children: [
          Positioned(
            child: NovelContent(
              item: widget.item,
              model: widget.model,
              preferencesController: widget.preferencesController,
              novelReadStatusController: widget.novelReadStatusController,
              isNextBook: false,
            ),
          ),
          Positioned(
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              child: ChapterUnlockView(
                  key: widget.chapterUnlockKey,
                  chapterId: widget.item?.id,
                  cost: widget.item?.cost,
                  userInfoModel: widget.userInfoModel,
                  premiumGoodItem: widget.premiumGoodItem,
                  subCoinGoodList: widget.subCoinGoodList,
                  isGoldCoinEnabled: isGoldCoinEnabled,
                  preferencesController: widget.preferencesController,
                  onRechargeCallBack: widget.onRechargeCallBack,
                  onJumpCallBack: widget.onJumpCallBack,
                  onRefreshDataBack: widget.onRefreshDataBack,
                  onLockStateChanged: widget.onLockStateChanged,
                  topMaskHeight: 90,
                  arguments: {'bookId': widget.bookDetailInfo?.bookId}))
        ],
      ),
    );
  }
}

///章节内容模块
class NovelContent extends StatelessWidget {
  final ChapterVoModel? item;
  final ContentModel? model;
  final NovelPreferencesSettingController preferencesController;
  final NovelReadStatusController novelReadStatusController;
  final bool isNextBook;

  const NovelContent(
      {super.key,
      required this.item,
      required this.model,
      required this.preferencesController,
      required this.novelReadStatusController,
      required this.isNextBook});

  String? _getContent(int? length) {
    String? content = item?.getContent(length: length);
    if (!isAvailable(content)) {
      content = model?.getContent(length: item?.lock == 1 ? 188 : length);
    }

    return content;
  }

  @override
  Widget build(BuildContext context) {
    return NovelContentPart(
      bookId: item?.bookId,
      chapterId: item?.id,
      content: _getContent(isNextBook ? 200 : null)!,
      preferencesController: preferencesController,
      novelReadStatusController: novelReadStatusController,
    );
  }
}
