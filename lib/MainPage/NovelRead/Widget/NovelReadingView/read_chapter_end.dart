import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Util/Common/Animation/lottie_animation_view.dart';
import 'package:UrNovel/Util/Common/CommonManager/common_manager.dart';
import 'package:UrNovel/Util/DataReportManager/event_report_manager.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../Util/Common/Animation/Spine/spine_animation_view.dart';
import '../../../../Util/Common/Animation/Spine/spine_thanks_view.dart';
import '../../../../Util/Common/Model/reward_item.dart';
import '../../../../Util/Common/Model/share_channel_model.dart';
import '../../../../Util/DataReportManager/event_name_config.dart';
import '../../../../Util/Extensions/colorUtil.dart';
import '../../../../Util/ReaderUtils/readerUtil.dart';
import '../../../../Util/ShareManager/share_manager.dart';
import '../../../../Util/SheetAndAlter/bottom_sheet.dart';
import '../../../../Util/StatusManagement/status_management.dart';
import '../../../../Util/enum.dart';
import '../../../../Util/genericUtil.dart';
import '../../../BookInfo/Model/book_author_info.dart';
import '../../Model/author_reward_model.dart';
import '../../ViewModel/ViewModel.dart';

class NovelReadingChapterEnd extends BaseFulWidget {
  final VoidCallback? onGiftsTap;
  final Function(Map<String, dynamic>? purchaseInfo)? onRefreshDataBack;

  NovelReadingChapterEnd(
      {super.key,
      required this.onGiftsTap,
      required this.onRefreshDataBack,
      required super.arguments});

  @override
  State<NovelReadingChapterEnd> createState() => _NovelReadingChapterEndState();
}

class _NovelReadingChapterEndState extends State<NovelReadingChapterEnd> {
  //阅读状态管理器
  final NovelReadStatusController novelReadStatusController =
      findGetXInstance(NovelReadStatusController());
  late int _bookId;
  late bool _isLoading;
  late BookAuthorResultModel? _bookAuthorInfo;
  late OverlayEntry? _overlayEntry;
  late bool _isRewardLoading;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _isLoading = false;
    _bookId = widget.arguments['bookId'] ?? 0;
    _bookAuthorInfo = widget.arguments['bookAuthorInfo'];
    _isRewardLoading = false;
    _initSpineOverlayEntry(SpineAnimationType.none);
  }

  @override
  void dispose() {
    _removeSpineOverlayEntry();

    super.dispose();
  }

  //作者打赏
  Future<void> _authorReward(RewardItem rewardItem, SpineAnimationType animationType) async {
    if (_isRewardLoading) return;

    _isRewardLoading = true;

    AuthorRewardResultModel? model =
        await NovelReadViewModel.authorReward(_bookId, _bookAuthorInfo?.id, rewardItem.coin);

    _isRewardLoading = false;
    Get.back();
    //打赏状态（0：成功；1：金币不足；2：失败）
    if (model?.rewardStatus == 0) {
      widget.onRefreshDataBack?.call(null);
      _initSpineOverlayEntry(animationType);
    } else if (model?.rewardStatus == 1) {
      await Get.toNamed("/accountPage", arguments: {});
    }
  }

  void _initSpineOverlayEntry(SpineAnimationType animationType) {
    _overlayEntry = OverlayEntry(
      builder: (context) {
        return SpineAnimationView(
          animationType: animationType,
          onAnimationComplete: () {
            _initThanksOverlayEntry();
          },
        );
      },
    );
    if (animationType != SpineAnimationType.none) {
      Overlay.of(context).insert(_overlayEntry!);
    }
  }

  void _initThanksOverlayEntry() {
    _removeSpineOverlayEntry();
    _overlayEntry = OverlayEntry(
      builder: (context) {
        return SpineThanksView(
          onAnimationComplete: () {
            _removeSpineOverlayEntry();
          },
        );
      },
    );
    Overlay.of(context).insert(_overlayEntry!);
  }

  void _removeSpineOverlayEntry() {
    _isRewardLoading = false;
    if (isAvailable(_overlayEntry) && _overlayEntry!.mounted) {
      _overlayEntry?.remove();
    }
    _overlayEntry?.dispose();
    _overlayEntry = null;
  }

  ///添加到书架
  Future<void> addBookToLibrary(int dataType) async {
    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }
    await CommonManager.instance.addBookToLibrary(_bookId, dataType, (isSuccess) {
      if (isSuccess) {
        if (dataType == 1) {
          novelReadStatusController.setAddLibrary(true);
          EventReportManager.eventReportOfFirebase(clickReadLibrary);
        } else if (dataType == 2) {
          novelReadStatusController.setLiked(true);
          novelReadStatusController.updateLikes(true);
          EventReportManager.eventReportOfFirebase(clickReadLike);
        }
      }
    });

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  ///从书架移除
  Future<void> cancelBookFromLibrary(int dataType) async {
    audioBookController.isLoading.value = true;
    await CommonManager.instance.cancelBookFromLibrary(_bookId, dataType, (isSuccess) {
      if (isSuccess) {
        if (dataType == 1) {
          novelReadStatusController.setAddLibrary(false);
        } else if (dataType == 2) {
          novelReadStatusController.setLiked(false);
          novelReadStatusController.updateLikes(false);
        }
      }
    });

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }

    EventReportManager.eventReportOfFirebase(clickReadRemove);
  }

  Future<void> _shareNovel(ShareType type) async {
    // TODO: 获取分享链接
    ShareBookUrlModel? model = await CommonManager.getShareBookUrl(_bookId);
    ShareManager.instance.shareNovel(
        widget.arguments['bookName'],
        widget.arguments['description'],
        widget.arguments['cover'], // 传递封面图URL
        model,
        type,
        context: context); // 传递context
  }

  @override
  Widget build(BuildContext context) {
    var width = 140.0;
    var height = 44.0;
    return Stack(
      children: [
        SizedBox(
            width: double.infinity,
            height: ReaderUtil.chapterEndViewHeight,
            child: Column(mainAxisSize: MainAxisSize.min, children: [
              Row(
                textDirection: TextDirection.ltr,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  GestureDetector(
                    onTap: () {
                      if (novelReadStatusController.isLiked.value) {
                        cancelBookFromLibrary(2);
                      } else {
                        addBookToLibrary(2);
                      }
                    },
                    child: Obx(() => Container(
                          width: width,
                          height: height,
                          decoration: BoxDecoration(
                              image: DecorationImage(
                            image: AssetImage(novelReadStatusController.isLiked.value
                                ? 'assets/images/novelReading/icon_box_pink.png'
                                : 'assets/images/novelReading/icon_box_gray.png'),
                          )),
                          child: Row(
                              textDirection: TextDirection.ltr,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Image.asset(
                                  novelReadStatusController.isLiked.value
                                      ? 'assets/images/novelReading/icon_like_selected.png'
                                      : 'assets/images/novelReading/icon_like_normal.png',
                                  width: 17,
                                  height: 15,
                                ),
                                const SizedBox(width: 7),
                                Obx(() => Text('${novelReadStatusController.likes.value}',
                                    style: TextStyle(
                                        fontSize: 12,
                                        color: novelReadStatusController.isLiked.value
                                            ? HexColor('#FD6178')
                                            : HexColor('#5B6571'))))
                              ]),
                        )),
                  ),
                  SizedBox(width: 20),
                  GestureDetector(
                      onTap: () {
                        setState(() {
                          // TODO: Share book
                          showBookBottomSheet(context, BookShareSheet(onShareTap: (type) {
                            _shareNovel(type);
                          }), HexColor('#F4F5F6'));
                        });
                      },
                      child: Container(
                        width: width,
                        height: height,
                        alignment: Alignment.center,
                        decoration: const BoxDecoration(
                            image: DecorationImage(
                          image: AssetImage('assets/images/novelReading/icon_box_gold.png'),
                        )),
                        child: Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                          Image.asset(
                            'assets/images/novelReading/icon_share_gold.png',
                            width: 16,
                            height: 16,
                          ),
                        ]),
                      ))
                ],
              ),
              const SizedBox(height: 8),
              GestureDetector(
                onTap: () {
                  widget.onGiftsTap?.call();
                  showBookBottomSheet(
                      context,
                      AuthorRewardSheet(
                        bookAuthorInfo: _bookAuthorInfo,
                        onRewardSelected: (item, type) async {
                          await _authorReward(item, type);
                        },
                      ),
                      HexColor('#F4F5F6'));
                },
                child: SizedBox(
                  width: 155,
                  height: 44,
                  child: DecoratedBox(
                    decoration: BoxDecoration(
                      border: Border.all(color: HexColor('#F64E9D'), width: 1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                        textDirection: TextDirection.ltr,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset(
                            'assets/images/novelReading/icon_author_gift.png',
                            width: 19,
                            height: 18,
                          ),
                          const SizedBox(width: 9),
                          Text('send_gifts'.tr,
                              style: TextStyle(
                                  fontSize: 12,
                                  color: HexColor('#F64E9D'),
                                  fontWeight: FontWeight.bold))
                        ]),
                  ),
                ),
              ),
              const SizedBox(height: 58),
              Obx(() => !novelReadStatusController.isAddLibrary.value
                  ? SizedBox(
                      height: 40,
                      child: TextButton(
                        onPressed: () {
                          if (novelReadStatusController.isAddLibrary.value) {
                            cancelBookFromLibrary(1);
                          } else {
                            addBookToLibrary(1);
                          }
                        },
                        child: RichText(
                            text: TextSpan(
                          text: "read_add_library".tr,
                          style: TextStyle(
                              fontSize: 12,
                              color: HexColor('#5B6571'),
                              decoration: TextDecoration.underline),
                        )),
                      ),
                    )
                  : SizedBox())
            ])),
        if (_isLoading) LottieAnimationView()
      ],
    );
  }
}
