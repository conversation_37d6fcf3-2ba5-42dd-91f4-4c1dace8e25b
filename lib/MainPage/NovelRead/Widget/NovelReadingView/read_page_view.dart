import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/MainPage/NovelRead/Widget/NovelReadingView/read_chapter_title.dart';
import 'package:UrNovel/MainPage/NovelRead/Widget/NovelReadingView/read_content_view.dart';
import 'package:UrNovel/MainPage/NovelRead/Widget/NovelReadingView/read_page_index.dart';
import 'package:UrNovel/MainPage/NovelRead/Widget/NovelReadingView/read_page_novel_info.dart';
import 'package:flutter/material.dart';

import '../../../../Launch&Login/Model/user_model.dart';
import '../../../../Util/ReaderUtils/readerUtil.dart';
import '../../../../Util/StatusManagement/status_management.dart';
import '../../../../Util/deviceScreenUtil.dart';
import '../../../Profile/Model/goods_info_model.dart';
import '../../Model/book_detailInfo_model.dart';
import '../../Model/content_list_model.dart';
import '../../Page/ChapterUnlock/chapter_unlock_view.dart';

class NovelReadingView extends BaseFulWidget {
  final BookDetailInfoResultModel? bookDetailInfo;
  final ChapterVoModel? currentChapter;
  final ContentModel? contentModel;
  final int page;
  final UserInfoModel? userInfoModel;
  final GoodsListItem? premiumGoodItem;
  final List<GoodsListItem>? subCoinGoodList;
  final NovelPreferencesSettingController preferencesController;
  final NovelReadStatusController novelReadStatusController;
  final Function(int? chapterId)? onRechargeCallBack;
  final VoidCallback? onJumpCallBack;
  final Function(Map<String, dynamic>? purchaseInfo)? onRefreshDataBack;

  NovelReadingView(
      {super.key,
      required this.bookDetailInfo,
      required this.currentChapter,
      required this.contentModel,
      required this.page,
      required this.userInfoModel,
      required this.premiumGoodItem,
      required this.subCoinGoodList,
      required this.preferencesController,
      required this.novelReadStatusController,
      required this.onRechargeCallBack,
      required this.onJumpCallBack,
      required this.onRefreshDataBack,
      required super.arguments});

  @override
  State<NovelReadingView> createState() => NovelReadingViewState();
}

class NovelReadingViewState extends State<NovelReadingView> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    bool isGoldCoinEnabled =
        (widget.currentChapter?.cost ?? 0) <= (widget.userInfoModel?.goldCoin ?? 0);
    bool isShowPremium = widget.currentChapter?.lock == 1;
    int pageCount = widget.contentModel?.pageCount ?? 0;
    bool isFirstContent =
        widget.contentModel?.id == widget.bookDetailInfo?.chapterVoList?.first?.id;
    if (isFirstContent) {
      pageCount -= 1;
    }

    if (isFirstContent && widget.page == 0) {
      return ReadPageNovelInfo(
          bookDetailInfo: widget.bookDetailInfo,
          preferencesController: widget.preferencesController);
    } else {
      return Stack(
        children: [
          // todo:章节标题
          Positioned(
              top: DeviceScreenUtil.instance.topSafeHeight,
              left: 20,
              right: 20,
              height: ReaderUtil.pageTopOffset,
              child: Container(
                  alignment: Alignment.centerLeft,
                  child: ReadChapterTitle(
                    currentChapter: widget.currentChapter,
                    page: widget.page - (isFirstContent ? 1 : 0),
                    preferencesController: widget.preferencesController,
                  ))),
          //todo: 章节内容
          Positioned(
              top: DeviceScreenUtil.instance.topSafeHeight + ReaderUtil.pageTopOffset,
              left: 0,
              right: 0,
              bottom: 0,
              child: NovelContentView(
                  bookId: widget.bookDetailInfo?.bookId,
                  contentModel: widget.contentModel,
                  page: widget.page,
                  preferencesController: widget.preferencesController,
                  novelReadStatusController: widget.novelReadStatusController,
                  onRefreshDataBack: widget.onRefreshDataBack,
                  onGiftsTap: () {
                    widget.onJumpCallBack?.call();
                  },
                  arguments: widget.arguments)),
          //todo: 章节页码
          Positioned(
              left: 20,
              right: 20,
              bottom: ReaderUtil.pageNumberPaddingBottom,
              height: ReaderUtil.pageNumberHeight,
              child: ReadPageIndex(
                index: widget.page - (isFirstContent ? 1 : 0),
                pageCount: pageCount,
                preferencesController: widget.preferencesController,
              )),
          //todo: 章节收租啦
          if (isShowPremium && widget.page == 0)
            Positioned(
                top: DeviceScreenUtil.instance.topSafeHeight + ReaderUtil.pageTopOffset,
                left: 0,
                right: 0,
                bottom: 0,
                child: ChapterUnlockView(
                    chapterId: widget.contentModel?.id,
                    cost: widget.currentChapter?.cost,
                    userInfoModel: widget.userInfoModel,
                    premiumGoodItem: widget.premiumGoodItem,
                    subCoinGoodList: widget.subCoinGoodList,
                    isGoldCoinEnabled: isGoldCoinEnabled,
                    preferencesController: widget.preferencesController,
                    onRechargeCallBack: widget.onRechargeCallBack,
                    onJumpCallBack: widget.onJumpCallBack,
                    onRefreshDataBack: widget.onRefreshDataBack,
                    topMaskHeight: widget.contentModel!.firstShowLineHeight! / 3 + 50,
                    arguments: widget.arguments)),
        ],
      );
    }
  }
}
