import 'package:UrNovel/BaseWidget/base_less_widget.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

import '../../../../Util/Extensions/colorUtil.dart';
import '../../../../Util/ReaderUtils/readerUtil.dart';
import '../../../../Util/StatusManagement/status_management.dart';
import '../../../../Util/deviceScreenUtil.dart';
import '../../Model/book_detailInfo_model.dart';

class ReadChapterTitle extends BaseLessWidget {
  final ChapterVoModel? currentChapter;
  final int page;
  final NovelPreferencesSettingController preferencesController;

  ReadChapterTitle(
      {super.key,
      required this.currentChapter,
      required this.page,
      required this.preferencesController});

  @override
  Widget build(BuildContext context) {
    String chapterIndexStr = '00';
    if (isAvailable(currentChapter?.chapterIndex)) {
      if (currentChapter!.chapterIndex! < 10) {
        chapterIndexStr = '0${currentChapter!.chapterIndex!}';
      } else {
        chapterIndexStr = '${currentChapter!.chapterIndex!}';
      }
    }

    double getTitleFontSize() {
      if (isAvailable(currentChapter?.title)) {
        if (currentChapter!.title!.length > 20) {
          if (preferencesController.fontSize.value > 24) {
            return 24;
          }

          return preferencesController.fontSize.value;
        } else {
          return preferencesController.fontSize.value;
        }
      }

      return ReaderUtil.instance.fontSize;
    }

    return SizedBox(
        width: DeviceScreenUtil.instance.width,
        child: page == 0
            ? Stack(
                children: [
                  Positioned(
                      top: 0,
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: SizedBox(
                        child: Image.asset(
                            "assets/images/novelReading/icon_chapter_bg.png",
                            fit: BoxFit.fill),
                      )),
                  Padding(
                      padding: const EdgeInsets.only(left: 15, bottom: 5),
                      child: Text(
                        currentChapter?.title ?? "",
                        style: TextStyle(
                          color: HexColor('#820F15'),
                          fontSize: getTitleFontSize(),
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ))
                ],
              )
            : Obx(() => Text(
                  chapterIndexStr,
                  style: TextStyle(
                    color: HexColor('#676769'),
                    fontSize: preferencesController.fontSize.value,
                    fontWeight: FontWeight.bold,
                  ),
                )));
  }
}
