import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:UrNovel/BaseWidget/base_less_widget.dart';
import '../../../../Util/Extensions/colorUtil.dart';
import '../../../../Util/StatusManagement/status_management.dart';

class ReadPageIndex extends BaseLessWidget {
  final int index;
  final int pageCount;
  final NovelPreferencesSettingController preferencesController;
  ReadPageIndex({super.key, this.index = 0, this.pageCount = 0, required this.preferencesController});


  @override
  Widget build(BuildContext context) {
    var pageCount = this.pageCount;
    return Container(
      width: double.infinity,
      alignment: Alignment.bottomRight,
      child: Obx(() => Text("${index + 1}/$pageCount",
          style: TextStyle(fontSize: 12, color: HexColor(preferencesController.titleColor.value)))),
    );
  }
}
