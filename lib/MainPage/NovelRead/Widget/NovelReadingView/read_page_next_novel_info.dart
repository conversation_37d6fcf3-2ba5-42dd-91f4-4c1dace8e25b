import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/MainPage/NovelRead/Widget/NovelReadingView/ReadPageModel/read_page_vertical.dart';
import 'package:UrNovel/MainPage/NovelRead/Widget/NovelReadingView/read_page_novel_info.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../Util/Common/CommonManager/common_manager.dart';
import '../../../../Util/Extensions/colorUtil.dart';
import '../../../../Util/StatusManagement/status_management.dart';
import '../../../../Util/enum.dart';
import '../../../../Util/genericUtil.dart';
import '../../Model/book_detailInfo_model.dart';

class NovelReadingViewNextNovelInfo extends BaseFulWidget {
  final BookDetailInfoResultModel? bookDetailInfo;
  final ReadingMode readingMode;
  final NovelPreferencesSettingController preferencesController;
  final Function(BookDetailInfoResultModel? bookDetailInfo)? onNextBookTap;

  NovelReadingViewNextNovelInfo(
      {super.key,
      required this.bookDetailInfo,
      this.readingMode = ReadingMode.verticalScroll,
      required this.preferencesController,
      required this.onNextBookTap});

  @override
  State<NovelReadingViewNextNovelInfo> createState() => _NovelReadingViewNextNovelInfoState();
}

class _NovelReadingViewNextNovelInfoState extends State<NovelReadingViewNextNovelInfo> {
  //阅读状态管理器
  final NovelReadStatusController _novelReadStatusController =
      findGetXInstance(NovelReadStatusController());

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    _novelReadStatusController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
        textDirection: CommonManager.instance.isContentReverse()
            ? TextDirection.rtl
            : TextDirection.ltr, // 强制文本方向为从右到左
        child: SingleChildScrollView(
            physics: widget.readingMode == ReadingMode.horizontalScroll
                ? NeverScrollableScrollPhysics()
                : ScrollPhysics(),
            child: NovelInfoIntroduceView(
              bookDetailInfo: widget.bookDetailInfo,
              preferencesController: widget.preferencesController,
              novelReadStatusController: _novelReadStatusController,
              readingMode: widget.readingMode,
              onNextBookTap: widget.onNextBookTap,
            )));
  }
}

class NovelInfoIntroduceView extends StatelessWidget {
  final BookDetailInfoResultModel? bookDetailInfo;
  final NovelPreferencesSettingController preferencesController;
  final NovelReadStatusController novelReadStatusController;
  final ReadingMode readingMode;
  final Function(BookDetailInfoResultModel? bookDetailInfo)? onNextBookTap;

  const NovelInfoIntroduceView(
      {super.key,
      required this.bookDetailInfo,
      required this.preferencesController,
      required this.novelReadStatusController,
      required this.readingMode,
      required this.onNextBookTap});

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    return Stack(
      children: [
        Column(
          children: [
            ReadPageNovelInfo(
              bookDetailInfo: bookDetailInfo,
              isHideSwipeNotification: true,
              preferencesController: preferencesController,
            ),
            Obx(() => Padding(
                padding:
                    EdgeInsets.symmetric(horizontal: preferencesController.horizontalPadding.value),
                child: Column(
                  children: [
                    //章节标题
                    Padding(
                      padding: EdgeInsets.only(top: 40),
                      child: Row(
                        children: [
                          SizedBox(
                              width: 5,
                              height: 31,
                              child: DecoratedBox(
                                  decoration: BoxDecoration(color: HexColor('#820F15')))),
                          SizedBox(width: 10),
                          Text(
                            '01',
                            style: TextStyle(
                                color: HexColor('#820F15'),
                                fontSize: preferencesController.fontSize.value,
                                fontWeight: FontWeight.bold,
                                fontFamily: preferencesController.fontFamily.value,
                                height: 1.0),
                          )
                        ],
                      ),
                    ),
                    NovelContent(
                      item: bookDetailInfo?.chapterVoList?.first,
                      model: null,
                      preferencesController: preferencesController,
                      novelReadStatusController: novelReadStatusController,
                      isNextBook: true,
                    ),
                  ],
                ))),
          ],
        ),
        Positioned(
          top: readingMode == ReadingMode.horizontalScroll ? mediaQuery.size.height - 100 : null,
          bottom: readingMode == ReadingMode.horizontalScroll ? null : 0,
          left: 0,
          right: 0,
          child: SizedBox(
            height: 100,
            child: Obx(() => DecoratedBox(
                    decoration: BoxDecoration(
                        gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                      HexColor(preferencesController.themeColor.value).withValues(alpha: 0.0),
                      HexColor(preferencesController.themeColor.value).withValues(alpha: 0.6),
                      HexColor(preferencesController.themeColor.value).withValues(alpha: 0.8),
                      HexColor(preferencesController.themeColor.value).withValues(alpha: 0.9),
                      HexColor(preferencesController.themeColor.value).withValues(alpha: 1.0),
                      HexColor(preferencesController.themeColor.value).withValues(alpha: 1.0),
                    ])))),
          ),
        ),
        Positioned(
            top: readingMode == ReadingMode.horizontalScroll ? mediaQuery.size.height - 120 : null,
            bottom: readingMode == ReadingMode.horizontalScroll ? null : 120,
            right: 12,
            child: SizedBox(
                height: 40,
                child: DecoratedBox(
                  decoration: BoxDecoration(
                      color: HexColor('#0199F8'), borderRadius: BorderRadius.circular(20)),
                  child: TextButton(
                      onPressed: () {
                        // TODO: 跳转到下一本书
                        onNextBookTap?.call(bookDetailInfo);
                      },
                      style: TextButton.styleFrom(padding: EdgeInsets.symmetric(horizontal: 20)),
                      child: Text(
                        'read_more'.tr,
                        style: TextStyle(
                            color: HexColor('#FFFFFF'), fontSize: 17, fontWeight: FontWeight.bold),
                      )),
                )))
      ],
    );
  }
}
