import 'package:UrNovel/MainPage/BookInfo/Model/BookDownLoad/download_book_detail_info_model.dart';
import 'package:UrNovel/MainPage/Library/Widget/history_list.dart';
import 'package:UrNovel/MainPage/Library/Widget/lliked_list.dart';
import 'package:UrNovel/MainPage/Library/Widget/my_list.dart';
import 'package:UrNovel/MainPage/Search/ViewModel/ViewModel.dart';
import 'package:UrNovel/Util/Common/Animation/lottie_animation_view.dart';
import 'package:UrNovel/Util/DBmanager/db_manager.dart';
import 'package:UrNovel/Util/DataReportManager/event_report_manager.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../Launch&Login/Model/language_model.dart';
import '../../../Util/Common/Model/normal_model.dart';
import '../../../Util/DataReportManager/event_name_config.dart';
import '../../../Util/Extensions/colorUtil.dart';
import '../../../Util/StatusManagement/status_management.dart';
import '../../../Util/enum.dart';
import '../../../Util/genericUtil.dart';
import '../../../Util/tools.dart';
import '../../Read/Widget/ForYou/read_search_bar.dart';
import '../../Search/Model/search_book_default_model.dart';
import '../Model/library_list_model.dart';
import '../ViewModel/ViewModel.dart';
import '../Widget/library_controll.dart';
import '../Widget/offine_list.dart';

class LibraryPage extends StatefulWidget {
  const LibraryPage({super.key});

  @override
  State<LibraryPage> createState() => _LibraryPageState();
}

class _LibraryPageState extends State<LibraryPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  LibraryListType listType = LibraryListType.myList;
  LibraryListType lastListType = LibraryListType.myList;
  late bool isEdit;
  late SearchBookDefaultItem? _searchBookDefaultItem;
  final libraryControllerKey = GlobalKey<LibraryControlState>();
  final myListKey = GlobalKey<MyListState>();
  final likedListKey = GlobalKey<LikedListState>();
  final historyListKey = GlobalKey<HistoryListState>();
  final offlineListKey = GlobalKey<OffLineListState>();

  // 实例化控制器
  final BottomBarController bottomBarController = findGetXInstance(BottomBarController());
  late bool _isLoading;

  @override
  void initState() {
    super.initState();
    isEdit = false;
    _searchBookDefaultItem = null;
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(_handleTabChange);
    _isLoading = false;

    getSearchDefaultBook();

    eventBusOn<LanguageItem>((item) {
      getSearchDefaultBook();
      myListKey.currentState?.onRefresh();
      likedListKey.currentState?.onRefresh();
      historyListKey.currentState?.getHistoryList();
      offlineListKey.currentState?.onRefresh();
    });
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  //todo: 获取搜索默认书籍
  Future<void> getSearchDefaultBook() async {
    _searchBookDefaultItem = await SearchViewModel.getSearchDefaultBook();
    if (mounted) {
      setState(() {});
    }
  }

  onTabTapped(int index) {
    switch (index) {
      case 0:
        EventReportManager.eventReportOfFirebase(libraryList);
        break;
      case 1:
        EventReportManager.eventReportOfFirebase(libraryLiked);
        break;
      case 2:
        EventReportManager.eventReportOfFirebase(libraryHistory);
        break;
      case 3:
        EventReportManager.eventReportOfFirebase(libraryOffline);
        break;
    }
  }

  //全选
  void selectAll(bool isSelectedAll) {
    switch (listType) {
      case LibraryListType.myList:
        myListKey.currentState?.selectAll(isSelectedAll);
        libraryControllerKey.currentState?.updateControlState(
            isEdit, myListKey.currentState!.controller.selectedBookList.length,
            isSelectedAll: isSelectedAll);
        break;
      case LibraryListType.liked:
        likedListKey.currentState?.selectAll(isSelectedAll);
        libraryControllerKey.currentState?.updateControlState(
            isEdit, myListKey.currentState!.controller.selectedBookList.length,
            isSelectedAll: isSelectedAll);
        break;
      case LibraryListType.history:
        historyListKey.currentState?.selectAll(isSelectedAll);
        libraryControllerKey.currentState?.updateControlState(
            isEdit, historyListKey.currentState!.getGroupsLength(),
            isSelectedAll: isSelectedAll);
        break;
      case LibraryListType.offline:
        offlineListKey.currentState?.selectAll(isSelectedAll);
        libraryControllerKey.currentState?.updateControlState(
            isEdit, myListKey.currentState!.controller.selectedBookList.length,
            isSelectedAll: isSelectedAll);
        break;
    }
  }

  //列表刷新数据，更新选择按钮状态
  void _refreshSelectBtState(bool isDataAvailable) {
    if (isDataAvailable && libraryControllerKey.currentState?.isSelectBtnShow == false ||
        !isDataAvailable && libraryControllerKey.currentState?.isSelectBtnShow == true) {
      _handleTabChange();
    }
  }

  //切换tab，更新选择按钮状态
  void _handleTabChange() {
    switch (_tabController.index) {
      case 0:
        listType = LibraryListType.myList;
        libraryControllerKey.currentState
            ?.updateSelectedBtn(isAvailable(myListKey.currentState?.bookList));
        break;
      case 1:
        listType = LibraryListType.liked;
        libraryControllerKey.currentState
            ?.updateSelectedBtn(isAvailable(likedListKey.currentState?.bookList));
        break;
      case 2:
        listType = LibraryListType.history;
        libraryControllerKey.currentState?.updateSelectedBtn(false);
        break;
      case 3:
        listType = LibraryListType.offline;
        libraryControllerKey.currentState
            ?.updateSelectedBtn(isAvailable(offlineListKey.currentState?.bookList));
        break;
    }

    restState();
    lastListType = listType;
  }

  void restState() {
    if (isEdit) {
      bottomBarController.toggleBottomBar(true);
      setState(() {
        isEdit = false;
      });

      resetListState(false);
    }
  }

  //ToDo：恢复选择默认状态
  void resetListState(bool state) {
    switch (lastListType) {
      case LibraryListType.myList:
        myListKey.currentState?.updateBookCard(state);
        break;
      case LibraryListType.liked:
        likedListKey.currentState?.updateBookCard(state);
        break;
      case LibraryListType.history:
        historyListKey.currentState?.updateBookCard(state);
        break;
      case LibraryListType.offline:
        offlineListKey.currentState?.updateBookCard(state);
        break;
    }

    libraryControllerKey.currentState?.updateControlState(state, 0);
  }

  removeBookFromLibrary() async {
    setState(() {
      _isLoading = true;
    });
    List<LibraryListItem?>? items = [];
    if (listType == LibraryListType.myList) {
      items = myListKey.currentState?.getSelectedBookList();
    } else if (listType == LibraryListType.liked) {
      items = likedListKey.currentState?.getSelectedBookList();
    } else if (listType == LibraryListType.history) {
      items = historyListKey.currentState?.getSelectedBookList();
    } else if (listType == LibraryListType.offline) {
      items = offlineListKey.currentState?.getSelectedBookList();
    }
    List<int>? ids = items?.map((e) {
      return e!.id ?? 0;
    }).toList();

    Map<String, dynamic> parameters = {
      "idList": ids,
    };
    NormalModel? model = await LibraryViewModel.removeBookFromLibrary(parameters);
    if (model?.code == 200) {
      if (listType == LibraryListType.myList) {
        myListKey.currentState?.getMyListList();
      } else if (listType == LibraryListType.liked) {
        likedListKey.currentState?.getLikedList();
      } else if (listType == LibraryListType.history) {
        historyListKey.currentState?.getHistoryList();
      } else if (listType == LibraryListType.offline) {
        offlineListKey.currentState?.getOfflineList();
        List<int>? books = items?.map((e) {
          return e!.bookId ?? 0;
        }).toList();
        deleteNovelFormDB(books);
      }
      restState();
      await refreshListData();
      setState(() {
        _isLoading = false;
      });
    } else {
      setState(() {
        _isLoading = false;
      });
    }
  }

  ///数据库里删除小说
  Future<void> deleteNovelFormDB(List<int>? books) async {
    List<DownloadBookDetailInfoModel> objects = [];
    books?.forEach((bookId) {
      if (bookId != 0) {
        DownloadBookDetailInfoModel? book = DBManager.instance.getNovel(bookId);
        if (isAvailable(book)) {
          objects.add(book!);
        }
      }
    });

    for (var object in objects) {
      if (isAvailable(object)) {
        if (isAvailable(object.chapterVoList)) {
          DBManager.instance.deleteManyObjects(object.chapterVoList);
        }
        DBManager.instance.deleteObject(object);
      }
    }
  }

  //ToDo：恢复选择默认状态
  Future<void> refreshListData() async {
    switch (listType) {
      case LibraryListType.myList:
        await myListKey.currentState?.onRefresh();
        break;
      case LibraryListType.liked:
        await likedListKey.currentState?.onRefresh();
        break;
      case LibraryListType.history:
        await historyListKey.currentState?.getHistoryList();
        break;
      case LibraryListType.offline:
        await offlineListKey.currentState?.onRefresh();
        break;
    }
  }

  //删除确认弹窗
  void showConfirmSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(17),
          topRight: Radius.circular(17),
        ),
      ),
      builder: (BuildContext context) {
        var mediaQuery = MediaQuery.of(context);
        return Stack(
          children: [
            Column(
              mainAxisSize: MainAxisSize.min, // 控制高度
              children: [
                ListTile(
                  title: Text('remove_popup'.tr,
                      style: TextStyle(fontSize: 17, color: HexColor('#7E839D')),
                      textAlign: TextAlign.center),
                ),
                Divider(height: 1, color: ColorsUtil.hexColor(0x444750, alpha: 0.1)),
                ListTile(
                  title: Text('remove'.tr,
                      style: TextStyle(fontSize: 17, color: HexColor('#F42222')),
                      textAlign: TextAlign.center),
                  onTap: () async {
                    Navigator.pop(context); // 关闭底部 sheet

                    removeBookFromLibrary();
                  },
                ),
                Divider(height: 1, color: ColorsUtil.hexColor(0x444750, alpha: 0.1)),
                ListTile(
                  title: Text('cancel'.tr,
                      style: TextStyle(fontSize: 17, color: HexColor('#4D4F56')),
                      textAlign: TextAlign.center),
                  onTap: () {
                    restState();
                    Navigator.pop(context); // 关闭底部 sheet
                  },
                ),
                SizedBox(height: mediaQuery.padding.bottom + 20),
              ],
            ),
            if (_isLoading) const LottieAnimationView()
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    return Scaffold(
      body: Stack(
        children: [
          SizedBox(
            width: double.infinity,
            height: mediaQuery.size.height,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ReadSearchBar(
                    defaultItem: _searchBookDefaultItem,
                    isShowLanguage: false,
                    isShowSignIn: isSignInSwitchOn,
                    onSearchTap: () async {
                      await Get.toNamed('/searchPage',
                          arguments: {"defaultItem": _searchBookDefaultItem});
                    }),
                const SizedBox(height: 17),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: LibraryControl(
                      key: libraryControllerKey,
                      onSelectAll: (isSelectAll) {
                        selectAll(isSelectAll);
                      },
                      onEdit: () {
                        setState(() {
                          isEdit = !isEdit;
                        });
                        resetListState(isEdit);

                        bottomBarController.toggleBottomBar(!isEdit);

                        if (isEdit) {
                          EventReportManager.eventReportOfFirebase(clickLibraryEdit);
                        }
                      }),
                ),
                Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 18),
                    child: Divider(
                        height: 1,
                        thickness: 1,
                        color: ColorsUtil.hexColor(0x444750, alpha: 0.06))),
                const SizedBox(height: 2),
                TabBar(
                  controller: _tabController,
                  padding: const EdgeInsets.only(left: 5),
                  //隐藏下划线
                  dividerHeight: 0,
                  indicatorWeight: 0.0,
                  indicator: const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: Colors.transparent, // 将下边线的颜色设置为透明
                        width: 0.0, // 将下边线的宽度设置为0
                      ),
                    ),
                  ),
                  labelColor: HexColor('#1B86FF'),
                  unselectedLabelColor: HexColor('#787C87'),
                  isScrollable: true,
                  tabAlignment: TabAlignment.start,
                  onTap: onTabTapped,
                  tabs: [
                    Tab(child: Text('mylist'.tr, style: const TextStyle(fontSize: 14))),
                    Tab(child: Text('liked'.tr, style: const TextStyle(fontSize: 14))),
                    Tab(child: Text('history'.tr, style: const TextStyle(fontSize: 14))),
                    Tab(child: Text('offine'.tr, style: const TextStyle(fontSize: 14))),
                  ],
                ),
                Expanded(
                  child: Padding(
                      padding: const EdgeInsets.only(top: 10, bottom: 10),
                      child: TabBarView(
                        controller: _tabController,
                        children: [
                          MyList(
                              key: myListKey,
                              onBookSelected: (selectedNum, isSelectedAll) {
                                libraryControllerKey.currentState?.updateControlState(
                                    isEdit, selectedNum,
                                    isSelectedAll: isSelectedAll);
                              },
                              onDataLoaded: (isDataAvailable) {
                                _refreshSelectBtState(isDataAvailable);
                              },
                              onTap: () {
                                restState();
                                bottomBarController.changeIndex(0, true);
                              }),
                          LikedList(
                              key: likedListKey,
                              onBookSelected: (selectedNum, isSelectedAll) {
                                libraryControllerKey.currentState?.updateControlState(
                                    isEdit, selectedNum,
                                    isSelectedAll: isSelectedAll);
                              },
                              onDataLoaded: (isDataAvailable) {
                                _refreshSelectBtState(isDataAvailable);
                              }),
                          HistoryList(
                              key: historyListKey,
                              onBookSelected: (selectedNum, isSelectedAll) {
                                libraryControllerKey.currentState?.updateControlState(
                                    isEdit, selectedNum,
                                    isSelectedAll: isSelectedAll);
                              },
                              onDataLoaded: (isDataAvailable) {
                                _refreshSelectBtState(isDataAvailable);
                              }),
                          OffLineList(
                              key: offlineListKey,
                              onBookSelected: (selectedNum, isSelectedAll) {
                                libraryControllerKey.currentState?.updateControlState(
                                    isEdit, selectedNum,
                                    isSelectedAll: isSelectedAll);
                              },
                              onDataLoaded: (isDataAvailable) {
                                _refreshSelectBtState(isDataAvailable);
                              }),
                        ],
                      )),
                ),
              ],
            ),
          ),
          if (isEdit)
            Positioned(
                left: 0,
                right: 0,
                bottom: 0,
                child: Container(
                    color: HexColor('#FFFFFF'),
                    alignment: Alignment.center,
                    child: SizedBox(
                      width: mediaQuery.size.width,
                      height: mediaQuery.size.width / 5.71,
                      child: TextButton(
                          onPressed: () {
                            showConfirmSheet(context);
                          },
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Image.asset(
                                'assets/images/library/icon_delete.png',
                                width: 22,
                                height: 20,
                              ),
                              Text('delete'.tr,
                                  style: TextStyle(fontSize: 11, color: HexColor('#FA3939'))),
                            ],
                          )),
                    )))
        ],
      ),
    );
  }
}
