import 'package:UrNovel/Util/AlterManager/alter_manager.dart';
import 'package:UrNovel/Util/NetWorkManager/net_work_manager.dart';
import 'package:UrNovel/Util/api_config.dart';
import 'package:UrNovel/Util/enum.dart';
import 'package:get/get.dart';

import '../../../Util/Common/Model/normal_model.dart';
import '../../../Util/SheetAndAlter/toast.dart';
import '../Model/library_list_model.dart';

class LibraryViewModel {
  /// Get the list of books from the server.
  /// url is the API endpoint to get the list of books.
  /// pageIndex and pageSize are used for pagination.
  static Future getLibraryList(String url, {int? pageIndex, int? pageSize}) async {
    // TODO: Implement getBooks method
    Map<String, dynamic> parameters = {};
    if (pageIndex != null) {
      parameters['page'] = pageIndex;
    }
    if (pageSize != null) {
      parameters['size'] = pageSize;
    }

    var response = await NetWorkManager.instance.post(url, parameters: parameters);

    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      if (url == apiLibraryGetHistory) {
        LibraryHistoryModel model = LibraryHistoryModel.fromJson(response.data);

        return model.result;
      } else {
        LibraryListModel model = LibraryListModel.fromJson(response.data);

        return model.result;
      }
    }

    return null;
  }

  ///remove a book from the library.
  ///dataType:类型,选择对应的数字(1:MyList, 2:Liked, 3:Offline)
  static Future<NormalModel?> addBookToLibrary(Map<String, dynamic> parameters) async {
    var response = await NetWorkManager.instance.post(apiLibraryAdd, parameters: parameters);

    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      int? dataType = parameters['dataType'];
      if (dataType == 1) {
        showToast('library_added'.tr);
        //TODO:推送授权
        await AlterManager.instance
            .showNotificationAlter(NotificationAlterSource.addToLibrary);
      } else if (dataType == 2) {
        showToast('add_favorites_toast'.tr);
        //TODO:推送授权
        await AlterManager.instance
            .showNotificationAlter(NotificationAlterSource.firstClickCollection);
      }

      var model = NormalModel.fromJson(response.data);

      return model;
    } else {
      showToast(response.data['msg'] ?? 'Failed to add to library or already in library');
    }

    return null;
  }

  ///用户删除书架中书籍
  static Future<NormalModel?> removeBookFromLibrary(Map<String, dynamic> parameters) async {
    var response = await NetWorkManager.instance.post(apiLibraryRemove, parameters: parameters);

    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      showToast('library_removed'.tr);
      var model = NormalModel.fromJson(response.data);

      return model;
    } else {
      showToast(response.data['msg'] ?? "Failed to remove books from library");
    }

    return null;
  }

  ///用户取消书架中书籍
  static Future<NormalModel?> cancelBookFromLibrary(Map<String, dynamic> parameters) async {
    var response = await NetWorkManager.instance.post(apiLibraryCancel, parameters: parameters);

    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      showToast('library_removed'.tr);
      var model = NormalModel.fromJson(response.data);

      return model;
    } else {
      showToast(response.data['msg'] ?? "Failed to remove books from library");
    }

    return null;
  }
}
