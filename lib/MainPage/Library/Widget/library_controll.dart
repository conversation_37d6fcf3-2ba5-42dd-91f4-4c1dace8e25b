import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../Util/Extensions/colorUtil.dart';

class LibraryControl extends StatefulWidget {
  final void Function(bool isSelectAll) onSelectAll; //全选 || 全删
  final void Function() onEdit; //编辑状态回调
  const LibraryControl(
      {super.key, required this.onSelectAll, required this.onEdit});

  @override
  State<LibraryControl> createState() => LibraryControlState();
}

class LibraryControlState extends State<LibraryControl> {
  late bool isEdited; //选中状态标志位
  late int selectedNum; //选中数量
  late bool isSelectedAll; //全选状态
  late bool isSelectBtnShow; //是否显示选择按钮

  void updateControlState(bool isEdited, int num,
      {bool isSelectedAll = false}) {
    if (mounted) {
      setState(() {
        this.isEdited = isEdited;
        selectedNum = num;
        this.isSelectedAll = isSelectedAll;
      });
    }
  }

  @override
  initState() {
    super.initState();
    isEdited = false;
    selectedNum = 0;
    isSelectedAll = false;
    isSelectBtnShow = true;
  }

  void updateSelectedBtn(bool isShow) {
    if (mounted) {
      setState(() {
        isSelectBtnShow = isShow;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 50,
      child: Stack(
        children: [
          Positioned(top: 0, left: 0, bottom: 0, child: TextButton(
            onPressed: () {
              if (isEdited) {
                //select all
                setState(() {
                  isSelectedAll = !isSelectedAll;
                  widget.onSelectAll(isSelectedAll);
                });
              }
            },
            child: Text(
                isEdited
                    ? (!isSelectedAll ? 'selected_all'.tr : 'deselected_all'.tr)
                    : 'Library'.tr,
                style: TextStyle(
                    fontSize: isEdited ? 14 : 17,
                    color: isEdited ? HexColor('#555A65') : HexColor('#1B86FF'),
                    fontWeight: isEdited ? FontWeight.normal : FontWeight.bold)),
          )),
          if (isEdited)
            Align(alignment: Alignment.center, child:  Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text('edit'.tr,
                    style: TextStyle(
                        fontSize: 18,
                        color: HexColor('#000000'),
                        fontWeight: FontWeight.bold)),
                if (0 < selectedNum)
                  Text('selected_number'.trParams({"param": '$selectedNum'}),
                      style: TextStyle(
                          fontSize: 12,
                          color: HexColor('#000000'),
                          fontWeight: FontWeight.bold)),
              ],
            )),
          if (isSelectBtnShow && !isEdited)
            Positioned(top: 0, right: 0, bottom: 0, child: TextButton.icon(
              onPressed: editAcation,
              icon: Image.asset('assets/images/library/icon_select.png',
                  width: 14, height: 14),
              label: Text('select'.tr,
                  style: TextStyle(fontSize: 14, color: HexColor('#555A65'))),
            )),
          if (isSelectBtnShow && isEdited)
            Positioned(right: 0, child: TextButton(
              onPressed: editAcation,
              child: Text('cancel'.tr,
                  style: TextStyle(fontSize: 14, color: HexColor('#555A65'))),
            )),
        ],
      ),
    );
  }

  void editAcation() {
    widget.onEdit();
  }
}
