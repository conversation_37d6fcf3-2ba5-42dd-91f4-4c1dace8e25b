import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Util/RefreshLoad/refresh_load.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../../../Util/Common/Animation/lottie_animation_view.dart';
import '../../../Util/Extensions/colorUtil.dart';
import '../../../Util/LocalNotificationManager/local_notification_manager.dart';
import '../../../Util/StatusManagement/status_management.dart';
import '../../../Util/api_config.dart';
import '../../../Util/enum.dart';
import '../../../Util/genericUtil.dart';
import '../../../Util/tools.dart';
import '../Model/library_list_model.dart';
import '../ViewModel/ViewModel.dart';
import 'book_card.dart';
import 'no_data.dart';

class HistoryList extends BaseFulWidget {
  final void Function(int selectedNum, bool isSelectedAll) onBookSelected;
  final Function(bool isDataAvailable) onDataLoaded;

  HistoryList(
      {super.key, required this.onBookSelected, required this.onDataLoaded});

  @override
  State<HistoryList> createState() => HistoryListState();
}

class HistoryListState extends State<HistoryList>
    with AutomaticKeepAliveClientMixin {
  late bool isEdited;
  final BookSelectionController controller =
      findGetXInstance(BookSelectionController());
  late bool isLoadFinished; //判断是否已经加载完成

  late List<LibraryHistoryListItem>? bookList;

  @override
  bool get wantKeepAlive => true; // 保持状态

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    isEdited = false;
    isLoadFinished = false;
    bookList = [];

    getHistoryList();
  }

  Future<void> getHistoryList({bool isLoading = true}) async {
    if (isLoading) {
      if (mounted) {
        setState(() {
          isLoadFinished = false;
        });
      }
    }

    await LibraryViewModel.getLibraryList(apiLibraryGetHistory)
        .then((list) async {
      widget.dealRefreshState(1, 1, list);
      setState(() {
        isLoadFinished = true;
        bookList = list;
      });

      widget.onDataLoaded(isAvailable(bookList));
      await widget.dealExposureList(bookList?.first.libraryBooKVoList, 6);
    }).catchError((error) {
      widget.dealRefreshState(1, 1, null);
      setState(() {
        isLoadFinished = true;
      });

      widget.onDataLoaded(isAvailable(bookList));
    });
  }

  void updateBookCard(bool isEdited) {
    setState(() {
      this.isEdited = isEdited;

      //清理状态
      if (!isEdited) {
        controller.clearSelection();
      }
    });
  }

  void selectAll(bool isSelectAll) {
    setState(() {
      controller.clearSelection();
      if (isSelectAll) {
        //全选
        if (isAvailable(bookList)) {
          for (var i = 0; i < bookList!.length; i++) {
            if (isAvailable(bookList![i].libraryBooKVoList)) {
              for (var j = 0; j < bookList![i].libraryBooKVoList!.length; j++) {
                controller.bookSelection(i, j);
              }
            }
          }
        }
      }
    });
  }

  List<LibraryListItem?> getSelectedBookList() {
    if (isAvailable(bookList)) {
      var selectedBookList = <LibraryListItem?>[];
      for (var map in controller.selectedBookList) {
        int i = int.parse(map.keys.first);
        if (i < bookList!.length) {
          LibraryHistoryListItem? item = bookList?[i];
          LibraryListItem? book =
              item?.libraryBooKVoList?[int.parse(map.values.first)];
          if (isAvailable(book)) {
            selectedBookList.add(book);
          }
        }
      }

      return selectedBookList;
    }

    return [];
  }

  bookSelected(int section, int index, LibraryListItem? item) async {
    if (isEdited) {
      controller.bookSelection(section, index);
      setState(() {
        //更新数量
        widget.onBookSelected(controller.selectedBookList.length,
            controller.selectedBookList.length == getGroupsLength());
      });
    } else {
      //todo:去往阅读页
      await LocalNotificationManager.toNovelReadPage({
        "bookId": item?.bookId,
        'jumpType': BookDetailJumpType.other
      });
    }
  }

  String getSectionTitle(String? title) {
    if (title == 'Today') {
      return "time1".tr;
    } else if (title == 'Yesterday') {
      return "time2".tr;
    }

    return "earlier".tr;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    var media = MediaQuery.of(context);
    var cardWidth = (media.size.width - 68) / 3.0;
    var cardImgHeight = cardWidth / 0.752;
    var cardHeight = cardImgHeight + 10 + 26;
    var groupCount = bookList?.length ?? 0;

    return Stack(
      children: [
        Container(
            margin: const EdgeInsets.only(left: 18, right: 18),
            height: double.infinity,
            width: double.infinity,
            child: MediaQuery.removePadding(
              context: context,
              removeTop: true,
              removeBottom: true,
              removeLeft: true,
              removeRight: true,
              child: NotificationListener(
                onNotification: (ScrollNotification notification) {
                  if (notification is ScrollStartNotification) {
                    widget.exposureList
                        .removeRange(0, widget.exposureList.length);
                  } else if (notification is ScrollEndNotification) {
                    widget.exposureReport();
                  }
                  return true;
                },
                child: RefreshLoadUtil(
                    onRefresh: () {
                      getHistoryList(isLoading: false);
                    },
                    onLoading: () {},
                    controller: widget.refreshController,
                    enablePullUp: false,
                    child: ListView.builder(
                      itemCount: groupCount,
                      itemBuilder: (context, index) {
                        if (index < groupCount) {
                          var titlePaddingTop = 18.0;
                          if (index == 0) {
                            titlePaddingTop = 0.0;
                          }

                          var item = bookList![index];
                          var listCount = item.libraryBooKVoList?.length ?? 0;
                          return Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: EdgeInsets.only(
                                    top: titlePaddingTop, bottom: 10),
                                child: Text(
                                  getSectionTitle(item.title),
                                  style: TextStyle(
                                      fontSize: 16,
                                      color: HexColor('#000000'),
                                      fontWeight: FontWeight.bold),
                                ),
                              ),
                              GridView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                padding: EdgeInsets.zero,
                                gridDelegate:
                                    const SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 3,
                                  childAspectRatio: 0.53,
                                  mainAxisSpacing: 14,
                                  crossAxisSpacing: 16,
                                ),
                                itemCount: listCount,
                                itemBuilder: (context, itemIndex) {
                                  var listItem =
                                      item.libraryBooKVoList?[itemIndex];
                                  if (itemIndex < listCount) {
                                    return VisibilityDetector(
                                        key: Key('historyList_$index'),
                                        onVisibilityChanged: (visibilityInfo) {
                                          if (0.8 <=
                                              visibilityInfo.visibleFraction) {
                                            if (listItem?.id is int) {
                                              widget.exposureList
                                                  .add(listItem!.id!);
                                            }
                                          }
                                        },
                                        child: GestureDetector(
                                          onTap: () {
                                            bookSelected(
                                                index, itemIndex, listItem);
                                          },
                                          child: BookCard(
                                              cardWidth: cardWidth,
                                              cardHeight: cardHeight,
                                              cardImgHeight: cardImgHeight,
                                              listItem: listItem,
                                              isEdited: isEdited,
                                              isSelected: controller.isSelected(
                                                  index, itemIndex),
                                              onSelectAcation: () {
                                                //todo:car片选中
                                                bookSelected(
                                                    index, itemIndex, listItem);
                                              }),
                                        ));
                                  } else {
                                    return null;
                                  }
                                },
                              )
                            ],
                          );
                        } else {
                          return Container();
                        }
                      },
                    )),
              ),
            )),
        if (!isAvailable(bookList) && isLoadFinished)
          NoDataWidget()
        else if (!isLoadFinished)
          const LottieAnimationView()
      ],
    );
  }

  int getGroupsLength() {
    var length = 0;
    if (isAvailable(bookList)) {
      for (var i = 0; i < bookList!.length; i++) {
        if (isAvailable(bookList![i].libraryBooKVoList)) {
          for (var j = 0; j < bookList![i].libraryBooKVoList!.length; j++) {
            length += 1;
          }
        }
      }
    }

    return length;
  }
}
