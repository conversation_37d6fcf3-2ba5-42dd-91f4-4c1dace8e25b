import 'package:flutter/material.dart';
import 'package:UrNovel/Util/Common/network_image_util.dart';
import '../../../Util/Extensions/colorUtil.dart';
import '../Model/library_list_model.dart';

class BookCard extends StatefulWidget {
  final double cardWidth;
  final double cardHeight;
  final double cardImgHeight;
  final LibraryListItem? listItem;
  final bool isEdited;
  final bool isSelected;
  final Function() onSelectAcation;

  const BookCard(
      {super.key,
      required this.cardWidth,
      required this.cardHeight,
      required this.cardImgHeight,
      required this.listItem,
      required this.isEdited,
      required this.isSelected,
      required this.onSelectAcation});

  @override
  State<BookCard> createState() => BookCardState();
}

class BookCardState extends State<BookCard> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.cardWidth,
      height: widget.cardHeight,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(
            children: [
              NetworkImageUtil(
                  imageUrl: widget.listItem?.cover ?? "",
                  width: widget.cardWidth,
                  height: widget.cardImgHeight,
                  fit: BoxFit.cover),
              if (widget.isEdited)
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: HexColor('#000000').withValues(alpha: 0.1),
                          spreadRadius: 5, //阴影半径 正值会使阴影扩大，负值会使阴影缩小
                          blurRadius: 10, //阴影模糊程度 数值越大，阴影越模糊
                          offset: const Offset(0, 3), // 阴影偏移量
                        ),
                      ],
                    ),
                    child: IconButton(
                      onPressed: () {
                        widget.onSelectAcation();
                      },
                      icon: widget.isSelected
                          ? Image.asset(
                              'assets/images/library/icon_selected.png')
                          : Image.asset(
                              'assets/images/library/icon_unSelect.png'),
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 10),
          Expanded(
            child: Stack(
              children: [
                Text(
                  widget.listItem?.title ?? "",
                  style: TextStyle(
                    fontSize: 12,
                    color: HexColor('#000000'),
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                Positioned(
                  left: 0,
                  bottom: 0,
                  child: Text(
                    widget.listItem?.chapterProgress ?? "",
                    style: TextStyle(
                      fontSize: 11,
                      color: HexColor('#888C94'),
                    ),
                    textAlign: TextAlign.left,
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}
