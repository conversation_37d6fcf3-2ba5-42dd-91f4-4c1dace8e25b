import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/MainPage/Library/Widget/no_data.dart';
import 'package:flutter/cupertino.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../../../Util/Common/Animation/lottie_animation_view.dart';
import '../../../Util/LocalNotificationManager/local_notification_manager.dart';
import '../../../Util/RefreshLoad/refresh_load.dart';
import '../../../Util/StatusManagement/status_management.dart';
import '../../../Util/api_config.dart';
import '../../../Util/enum.dart';
import '../../../Util/genericUtil.dart';
import '../../../Util/tools.dart';
import '../Model/library_list_model.dart';
import '../ViewModel/ViewModel.dart';
import 'book_card.dart';

class OffLineList extends BaseFulWidget {
  final void Function(int selectedNum, bool isSelectedAll) onBookSelected;
  final Function(bool isDataAvailable) onDataLoaded;

  OffLineList(
      {super.key, required this.onBookSelected, required this.onDataLoaded});

  @override
  State<OffLineList> createState() => OffLineListState();
}

class OffLineListState extends State<OffLineList>
    with AutomaticKeepAliveClientMixin {
  late List<LibraryListItem>? bookList;
  late bool isEdited;
  final BookSelectionController controller =
      findGetXInstance(BookSelectionController());
  late bool isLoadFinished; //判断是否已经加载完成
  int pageIndex = 1; //page默认从1开始
  int pageSize = 12; //每页加载数量

  @override
  bool get wantKeepAlive => true; // 保持状态

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    isEdited = false;
    isLoadFinished = false;
    bookList = [];

    onRefresh(isLoading: true);
  }

  Future<void> onRefresh({bool isLoading = false}) async {
    pageIndex = 1;
    if (isLoading) {
      if (mounted) {
        setState(() {
          isLoadFinished = false;
        });
      }
    }
    await getOfflineList(true);
  }

  Future<void> onLoading() async {
    pageIndex++;
    await getOfflineList(false);
  }

  Future<void> getOfflineList([bool isRefresh = true]) async {
    await LibraryViewModel.getLibraryList(apiLibraryGetOffline,
            pageIndex: pageIndex, pageSize: pageSize)
        .then((model) async {
      List<LibraryListItem>? list = model?.list;
      isLoadFinished = true;
      if (isAvailable(list)) {
        if (isRefresh) {
          bookList = list;
        } else {
          bookList?.addAll(list!);
        }
      }
      if (mounted) {
        setState(() {});
      }
      widget.dealRefreshState(pageIndex, pageSize, list);
      widget.onDataLoaded(isAvailable(bookList));
      await widget.dealExposureList(bookList, 6);
    }).catchError((error) {
      widget.dealRefreshState(pageIndex, pageSize, null);
      if (mounted) {
        setState(() {
          isLoadFinished = true;
        });
      }
      widget.onDataLoaded(isAvailable(bookList));
    });
  }

  void assignBookList(List<LibraryListItem>? list) {
    if (mounted) {
      setState(() {
        bookList = list;
      });
    }
  }

  void updateBookCard(bool isEdited) {
    if (mounted) {
      setState(() {
        this.isEdited = isEdited;

        //清理状态
        if (!isEdited) {
          controller.clearSelection();
        }
      });
    }
  }

  void selectAll(bool isSelectAll) {
    if (isAvailable(bookList)) {
      if (mounted) {
        setState(() {
          controller.clearSelection();
          if (isSelectAll) {
            for (var i = 0; i < bookList!.length; i++) {
              controller.bookSelection(i, i);
            }
          }
        });
      }
    }
  }

  List<LibraryListItem?> getSelectedBookList() {
    if (isAvailable(bookList)) {
      var selectedBookList = <LibraryListItem?>[];
      for (var map in controller.selectedBookList) {
        int i = int.parse(map.keys.first);
        if (i < bookList!.length) {
          LibraryListItem? item = bookList?[i];
          if (isAvailable(item)) {
            selectedBookList.add(item!);
          }
        }
      }

      return selectedBookList;
    }

    return [];
  }

  bookSelected(int index, LibraryListItem? item) async {
    if (isEdited) {
      controller.bookSelection(index, index);
      if (mounted) {
        setState(() {
          //更新数量
          widget.onBookSelected(controller.selectedBookList.length,
              controller.selectedBookList.length == bookList?.length);
        });
      }
    } else {
      //todo:去往阅读页
      await LocalNotificationManager.toNovelReadPage({
        "bookId": item?.bookId,
        'jumpType': BookDetailJumpType.other
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    var media = MediaQuery.of(context);
    var cardWidth = (media.size.width - 68) / 3.0;
    var cardImgHeight = cardWidth / 0.752;
    var cardHeight = cardImgHeight + 10 + 26;
    var count = bookList?.length ?? 0;

    return Stack(
      children: [
        Container(
          margin: const EdgeInsets.only(left: 18, right: 18),
          height: double.infinity,
          width: double.infinity,
          child: NotificationListener(
              onNotification: (ScrollNotification notification) {
                if (notification is ScrollStartNotification) {
                  widget.exposureList
                      .removeRange(0, widget.exposureList.length);
                } else if (notification is ScrollEndNotification) {
                  widget.exposureReport();
                }
                return true;
              },
              child: RefreshLoadUtil(
                onRefresh: (){
                  onRefresh(isLoading: false);
                },
                onLoading: onLoading,
                controller: widget.refreshController,
                enablePullUp: true,
                child: GridView.builder(
                  padding: EdgeInsets.zero,
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    childAspectRatio: 0.53,
                    mainAxisSpacing: 14,
                    crossAxisSpacing: 16,
                  ),
                  itemCount: count,
                  itemBuilder: (context, index) {
                    if (index < count) {
                      var item = bookList?[index];
                      return VisibilityDetector(
                          key: Key('offlineList_$index'),
                          onVisibilityChanged: (visibilityInfo) {
                            if (0.8 <= visibilityInfo.visibleFraction) {
                              if (item?.id is int) {
                                widget.exposureList.add(item!.id!);
                              }
                            }
                          },
                          child: GestureDetector(
                            onTap: () {
                              bookSelected(index, item);
                            },
                            child: BookCard(
                                cardWidth: cardWidth,
                                cardHeight: cardHeight,
                                cardImgHeight: cardImgHeight,
                                listItem: item,
                                isEdited: isEdited,
                                isSelected: controller.isSelected(index, index),
                                onSelectAcation: () {
                                  //todo:car片选中
                                  bookSelected(index, item);
                                }),
                          ));
                    } else {
                      return Container();
                    }
                  },
                ),
              )),
        ),
        if (!isAvailable(bookList) && isLoadFinished)
          NoDataWidget()
        else if (!isLoadFinished)
          const LottieAnimationView()
      ],
    );
  }
}
