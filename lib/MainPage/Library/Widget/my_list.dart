import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Util/Common/Animation/lottie_animation_view.dart';
import 'package:UrNovel/Util/RefreshLoad/refresh_load.dart';
import 'package:UrNovel/Util/api_config.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:flutter/cupertino.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../../../Util/LocalNotificationManager/local_notification_manager.dart';
import '../../../Util/StatusManagement/status_management.dart';
import '../../../Util/enum.dart';
import '../../../Util/genericUtil.dart';
import '../Model/library_list_model.dart';
import '../ViewModel/ViewModel.dart';
import 'book_card.dart';

class MyList extends BaseFulWidget {
  //更新数量
  final void Function(int selectedNum, bool isSelectedAll) onBookSelected;
  final Function(bool isDataAvailable) onDataLoaded;
  final VoidCallback onTap;

  MyList(
      {super.key, required this.onBookSelected, required this.onDataLoaded, required this.onTap});

  @override
  State<MyList> createState() => MyListState();
}

class MyListState extends State<MyList> with AutomaticKeepAliveClientMixin {
  late List<LibraryListItem>? bookList;
  late bool isEdited;
  final BookSelectionController controller = findGetXInstance(BookSelectionController());
  late bool isLoadFinished; //判断是否已经加载完成
  int pageIndex = 1; //page默认从1开始
  int pageSize = 12; //每页加载数量

  @override
  bool get wantKeepAlive => true; // 保持状态

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    isEdited = false;
    isLoadFinished = false;
    bookList = [];

    onRefresh();
  }

  Future<void> onRefresh({bool isLoading = true}) async {
    pageIndex = 1;
    if (isLoading) {
      if (mounted) {
        setState(() {
          isLoadFinished = false;
        });
      }
    }

    await getMyListList(true);
  }

  Future<void> onLoading() async {
    pageIndex++;
    await getMyListList(false);
  }

  Future<void> getMyListList([bool isRefresh = true]) async {
    await LibraryViewModel.getLibraryList(apiLibraryMyList,
            pageIndex: pageIndex, pageSize: pageSize)
        .then((model) async {
      List<LibraryListItem>? list = model?.list;
      isLoadFinished = true;
      if (isAvailable(list)) {
        if (isRefresh) {
          bookList = list;
        } else {
          bookList?.addAll(list!);
        }
      }
      if (mounted) {
        setState(() {});
      }

      widget.dealRefreshState(pageIndex, pageSize, list);
      widget.onDataLoaded(isAvailable(bookList));
      await widget.dealExposureList(bookList, 6);
    }).catchError((error) {
      widget.dealRefreshState(pageIndex, pageSize, null);
      if (mounted) {
        setState(() {
          isLoadFinished = true;
        });
      }
      widget.onDataLoaded(isAvailable(bookList));
    });
  }

  void updateBookCard(bool isEdited) {
    if (mounted) {
      setState(() {
        this.isEdited = isEdited;

        //清理状态
        if (!isEdited) {
          controller.clearSelection();
        }
      });
    }
  }

  void selectAll(bool isSelectAll) {
    if (isAvailable(bookList)) {
      if (mounted) {
        setState(() {
          controller.clearSelection();
          if (isSelectAll) {
            for (var i = 0; i < bookList!.length; i++) {
              controller.bookSelection(i, i);
            }
          }
        });
      }
    }
  }

  List<LibraryListItem?> getSelectedBookList() {
    if (isAvailable(bookList)) {
      var selectedBookList = <LibraryListItem?>[];
      for (var map in controller.selectedBookList) {
        int i = int.parse(map.keys.first);
        if (i < bookList!.length) {
          LibraryListItem? item = bookList?[i];
          if (isAvailable(item)) {
            selectedBookList.add(item);
          }
        }
      }

      return selectedBookList;
    }

    return [];
  }

  bookSelected(int index, LibraryListItem? item) async {
    if (isEdited) {
      controller.bookSelection(index, index);
      if (mounted) {
        setState(() {
          //更新数量
          widget.onBookSelected(controller.selectedBookList.length,
              controller.selectedBookList.length == bookList?.length);
        });
      }
    } else {
      //todo:去往阅读页
      await LocalNotificationManager.toNovelReadPage(
          {"bookId": item?.bookId, 'jumpType': BookDetailJumpType.other});
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    var media = MediaQuery.of(context);
    var cardWidth = (media.size.width - 68) / 3.0;
    var cardImgHeight = cardWidth / 0.752;
    var cardHeight = cardImgHeight + 10 + 26;

    // 书籍列表 + 添加按钮的总数
    var count = (bookList?.length ?? 0) + 1;

    return Stack(
      children: [
        Container(
          margin: const EdgeInsets.only(left: 18, right: 18),
          height: double.infinity,
          width: double.infinity,
          child: NotificationListener(
              onNotification: (ScrollNotification notification) {
                if (notification is ScrollStartNotification) {
                  widget.exposureList.removeRange(0, widget.exposureList.length);
                } else if (notification is ScrollEndNotification) {
                  widget.exposureReport();
                }
                return true;
              },
              child: RefreshLoadUtil(
                  onRefresh: () {
                    onRefresh(isLoading: false);
                  },
                  onLoading: onLoading,
                  enablePullUp: true,
                  controller: widget.refreshController,
                  child: GridView.builder(
                    padding: EdgeInsets.zero,
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 3,
                      childAspectRatio: 0.53,
                      mainAxisSpacing: 14,
                      crossAxisSpacing: 16,
                    ),
                    itemCount: count,
                    itemBuilder: (context, index) {
                      // 添加按钮总是在最后一个位置
                      if (index == count - 1) {
                        return GestureDetector(
                          onTap: widget.onTap,
                          child: Padding(
                            padding: EdgeInsets.only(bottom: cardHeight - cardImgHeight + 10),
                            child: Image.asset('assets/images/library/icon_addBook.png',
                                width: cardWidth, height: cardImgHeight, fit: BoxFit.cover),
                          ),
                        );
                      } else if (index < (bookList?.length ?? 0)) {
                        // 显示书籍
                        var item = bookList?[index];
                        return VisibilityDetector(
                            key: Key('myList_$index'),
                            onVisibilityChanged: (visibilityInfo) {
                              if (0.8 <= visibilityInfo.visibleFraction) {
                                if (item?.id is int) {
                                  widget.exposureList.add(item!.id!);
                                }
                              }
                            },
                            child: GestureDetector(
                              onTap: () {
                                bookSelected(index, item);
                              },
                              child: BookCard(
                                  cardWidth: cardWidth,
                                  cardHeight: cardHeight,
                                  cardImgHeight: cardImgHeight,
                                  listItem: item,
                                  isEdited: isEdited,
                                  isSelected: controller.isSelected(index, index),
                                  onSelectAcation: () {
                                    //todo:car片选中
                                    bookSelected(index, item);
                                  }),
                            ));
                      } else {
                        return Container();
                      }
                    },
                  ))),
        ),
        if (!isLoadFinished) const LottieAnimationView()
      ],
    );
  }
}
