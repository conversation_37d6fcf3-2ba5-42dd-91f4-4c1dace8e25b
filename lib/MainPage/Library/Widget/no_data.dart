import 'package:UrNovel/BaseWidget/base_less_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../Util/Extensions/colorUtil.dart';
import '../../../Util/StatusManagement/status_management.dart';
import '../../../Util/genericUtil.dart';

class NoDataWidget extends BaseLessWidget {
  NoDataWidget({super.key});

  // 实例化控制器
  final BottomBarController bottomBarController =
      findGetXInstance(BottomBarController());

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'favorites_title'.tr + 'liked_desc'.tr,
              style: TextStyle(
                color: HexColor("#000000"),
                fontSize: 14,
                fontWeight: FontWeight.bold,
                height: 1.5,
              ),
              maxLines: 2,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 44),
            Container(
              width: double.infinity,
              height: 40,
              margin: const EdgeInsets.symmetric(horizontal: 50),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                    colors: [HexColor("#0087FB"), HexColor("#0099F8")],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight),
                borderRadius: BorderRadius.circular(12),
              ),
              child: TextButton(
                  onPressed: () {
                    bottomBarController.changeIndex(0, true);
                  },
                  child: Text(
                    "liked_button".tr,
                    style: TextStyle(
                      color: HexColor("#FFFFFF"),
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  )),
            )
          ],
        ),
      ),
    );
  }
}
