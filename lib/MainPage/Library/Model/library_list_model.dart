import 'package:json_annotation/json_annotation.dart';

part 'library_list_model.g.dart';

///书架除了历史其它的列表数据模型
@JsonSerializable()
class LibraryListModel {
  int? code;
  LibraryBooKVoList? result; //书架书籍列表
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  LibraryListModel(
      {this.code, this.msg, this.sysAt, this.cost, this.traceId, this.result});

  factory LibraryListModel.fromJson(Map<String, dynamic> json) =>
      _$LibraryListModelFromJson(json);

  Map<String, dynamic> toJson() => _$LibraryListModelToJson(this);
}

@JsonSerializable()
class LibraryBooKVoList {
  @JsonKey(name: "libraryBooKVoList")
  List<LibraryListItem>? list;

  LibraryBooKVoList({this.list});

  factory LibraryBooKVoList.fromJson(Map<String, dynamic> json) =>
      _$LibraryBooKVoListFromJson(json);

  Map<String, dynamic> toJson() => _$LibraryBooKVoListToJson(this);
}

///书架历史列表数据模型
@JsonSerializable()
class LibraryHistoryModel {
  int? code;
  List<LibraryHistoryListItem>? result;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  LibraryHistoryModel(
      {this.code, this.msg, this.sysAt, this.cost, this.traceId, this.result});

  factory LibraryHistoryModel.fromJson(Map<String, dynamic> json) =>
      _$LibraryHistoryModelFromJson(json);

  Map<String, dynamic> toJson() => _$LibraryHistoryModelToJson(this);
}

@JsonSerializable()
class LibraryHistoryListItem {
  String? title; //标题
  List<LibraryListItem>? libraryBooKVoList; //列表

  LibraryHistoryListItem({this.title, this.libraryBooKVoList});

  factory LibraryHistoryListItem.fromJson(Map<String, dynamic> json) =>
      _$LibraryHistoryListItemFromJson(json);

  Map<String, dynamic> toJson() => _$LibraryHistoryListItemToJson(this);
}

///书架书籍列表数据模型
@JsonSerializable()
class LibraryListItem {
  int? id; //书架id
  int? bookId; //书籍id
  String? authorName; //作者名称
  String? authorCover; //作者头像
  String? title; //标题
  String? cover; //封面
  String? description; //描述
  int? chapterIndex; //当前章节位置
  int? chapterCount; //书籍所有章节数
  String? chapterProgress; //书籍章节阅读进度
  String? historyDateCode; //历史时间code
  int? updateTime; //最后阅读时间
  bool? liked; //是否加入收藏
  bool? library; //是否加入书架
  int? bookGoldCount; //书本金币数

  LibraryListItem(
      {this.id,
      this.bookId,
      this.authorName,
      this.authorCover,
      this.title,
      this.cover,
      this.description,
      this.chapterIndex,
      this.chapterCount,
      this.chapterProgress,
      this.historyDateCode,
      this.updateTime,
      this.liked,
      this.library,
      this.bookGoldCount});

  factory LibraryListItem.fromJson(Map<String, dynamic> json) =>
      _$LibraryListItemFromJson(json);

  Map<String, dynamic> toJson() => _$LibraryListItemToJson(this);
}
