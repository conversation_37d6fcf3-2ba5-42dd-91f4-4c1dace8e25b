import 'dart:core';
import 'dart:math' as math;

import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Util/logUtil.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../Util/Extensions/colorUtil.dart';
import '../../../../Util/StatusManagement/status_management.dart';
import '../../../../Util/enum.dart';
import '../../../Launch&Login/Model/user_model.dart';
import '../../../Util/Common/CommonManager/common_manager.dart';
import '../../../Util/Common/network_image_util.dart';
import '../../../Util/DataReportManager/event_name_config.dart';
import '../../../Util/DataReportManager/event_report_manager.dart';
import '../../../Util/SharedPreferences/shared_preferences.dart';
import '../../../Util/genericUtil.dart';
import '../../Profile/Page/AccountPage/account_page.dart';
import '../ViewModel/ViewModel.dart';
import '../Widget/History/red_point.dart';
import 'ForYou/chat_forYou_page.dart';
import 'History/chat_history_page.dart';

class ChatNavBarPage extends BaseFulWidget {
  ChatNavBarPage({super.key, super.arguments});

  @override
  State<ChatNavBarPage> createState() => ChatNavBarPageState();
}

class ChatNavBarPageState extends State<ChatNavBarPage>
    with AutomaticKeepAliveClientMixin {
  late List<Widget> _pages;
  static const double width = 45.0;
  static const double height = 45.0;

  final ValueNotifier<int> _selectedIndex = ValueNotifier(0);

  // 实例化控制器
  final ChatBarController _chatBarController =
      findGetXInstance(ChatBarController());
  static const double offsetNavigationBar = 12;

  late int? _bookId;
  late UserInfoModel? _userInfoModel;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _chatBarController.changeIndex(1);
    _bookId = widget.arguments['bookId'];
    _userInfoModel = null;

    _initPage();
    fetchData();
  }

  @override
  void dispose() {
    setSystemUiDark();
    try {
      _selectedIndex.dispose();
      _chatBarController.dispose();
    } catch (e) {
      logP(e.toString());
    }
    super.dispose();
  }

  _initPage() {
    _pages = [
      ChatHistoryPage(arguments: {"parentKey": widget.key, "bookId": _bookId}),
      ChatForYouPage(
        arguments: {"parentKey": widget.key, "bookId": _bookId},
      ),
      AccountPage(arguments: {"goodsType": GoodsType.purchaseDiamonds}),
    ];
  }

  Future<void> fetchData() async {
    await _getLocalUserInfo();
    await EventReportManager.eventReportOfFirebase(chat, parameters: null);
  }

  _getLocalUserInfo() async {
    _userInfoModel = await SpUtil.spGetUserInfo();
    if (mounted) {
      setState(() {});
    }
  }

  void setForYouActionsToggle(int index) {
    logD(index);
    _selectedIndex.value = index;
  }

  BottomNavigationBarItem getBarItem(String s) {
    return BottomNavigationBarItem(
      icon: Transform.translate(
        offset: Offset(0, offsetNavigationBar), // 下移 8 像素
        child: Image.asset(
          '${s}1.png',
          width: width,
          height: height,
        ),
      ),
      activeIcon: Transform.translate(
        offset: Offset(0, offsetNavigationBar), // 下移 8 像素
        child: Image.asset(
          '${s}2.png',
          width: width,
          height: height,
        ),
      ),
      label: '',
    );
  }

  ///BarItems
  List<BottomNavigationBarItem> _getBarItems() {
    return [
      getBarItem("assets/images/chat/chat_icon_chat0"),
      getBarItem("assets/images/chat/chat_icon_love0"),
      getBarItem("assets/images/chat/chat_icon_shop0"),
    ];
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    var avatar = getUserAvatar(_userInfoModel?.avatar, _userInfoModel?.sex);
    return Scaffold(
        resizeToAvoidBottomInset: false,
        // 防止键盘弹出导致重建
        backgroundColor: HexColor('#101011'),
        appBar: _chatBarController.getIndex() == 2
            ? null
            : AppBar(
                backgroundColor: HexColor('#101011'),
                leading: IconButton(
                  icon: Transform.rotate(
                    angle: CommonManager.instance.isReverse() ? math.pi : 0,
                    child: Image.asset(
                        "assets/images/common/icon_back_white.png",
                        width: 12,
                        height: 20),
                  ),
                  // 使用默认的返回箭头图标
                  onPressed: () async {
                    Get.back();
                    setSystemUiDark();
                  },
                ),
                actions: [
                  if (_chatBarController.getIndex() == 1)
                    GestureDetector(
                        onTap: () async {
                          //Todo: 跳转到个人中心页面
                          await Get.toNamed('/profileEditPage', arguments: {
                            "infoModel": _userInfoModel,
                            "avatar": avatar,
                            "callback": (infoModel) {
                              if (isAvailable(infoModel)) {
                                setState(() {
                                  _userInfoModel = infoModel;
                                });
                              }
                            }
                          });
                          EventReportManager.eventReportOfFirebase(clickMeEdit);
                        },
                        child: Padding(
                            padding: EdgeInsets.only(right: 15),
                            child: NetworkImageUtil(
                                imageUrl: avatar,
                                width: 40,
                                height: 40,
                                isNeedRedirect: false,
                                isCircle: true))),
                ],
                title: Text(
                  _chatBarController.getIndex() == 2 ? 'account'.tr : "chat".tr,
                  style: TextStyle(
                    color: Colors.white, // 设置标题颜色为白色
                    fontSize: 20, // 设置标题字体大小
                    fontWeight: FontWeight.normal, // 设置标题字体加粗
                  ),
                ),
                automaticallyImplyLeading: true,
                iconTheme: IconThemeData(
                  color: Colors.white, // 设置返回按钮颜色为白色
                ),
              ),

        ///保留tab页面的状态
        body: Obx(() {
          return IndexedStack(
            index: _chatBarController.currentIndex.value,
            children: _pages,
          );
        }),
        bottomNavigationBar: Theme(
          data: Theme.of(context).copyWith(
            splashFactory: NoSplash.splashFactory,
            highlightColor: Colors.transparent, // 取消点击时的高亮效果
          ),
          child: Obx(() {
            return _chatBarController.isBottomBarVisible.value
                ? Stack(
                    children: [
                      BottomNavigationBar(
                        type: BottomNavigationBarType.fixed,
                        currentIndex: _chatBarController.currentIndex.value,
                        items: _getBarItems(),
                        onTap: (index) {
                          setState(() {
                            _chatBarController.changeIndex(index);
                          });
                        },
                        selectedItemColor: Colors.transparent,
                        unselectedItemColor: Colors.transparent,
                        // selectedFontSize: 0, // 设置为 0 来隐藏文本
                        // unselectedFontSize: 0, // 设置为 0 来隐藏文本
                        backgroundColor: Color.fromARGB(255, 27, 26, 31),
                      ),
                      Visibility(
                        visible: ChatViewModel.instance.totalRedPointCount >
                            0, //红点数量大于0
                        child: Positioned(
                          top: 12, // 调整红点位置
                          // 根据item位置调整
                          left: MediaQuery.of(context).size.width / 3 - 45,
                          child: RedPoint(count: 1, size: 16),
                        ),
                      )
                    ],
                  )
                : const SizedBox.shrink();
          }),
        ));
  }
}
