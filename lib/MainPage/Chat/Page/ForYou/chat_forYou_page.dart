import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../Util/Common/Animation/lottie_animation_view.dart';
import '../../../../Util/Common/network_image_util.dart';
import '../../../../Util/Extensions/colorUtil.dart';
import '../../Model/chat_navbar_foryou_model.dart';
import '../../ViewModel/ViewModel.dart';
import '../../Widget/expandable_text.dart';

class ChatForYouPage extends StatefulWidget {
  final Map<String, dynamic> arguments;

  @override
  ChatForYouPageState createState() => ChatForYouPageState();

  const ChatForYouPage({super.key, required this.arguments});
}

class ChatForYouPageState extends State<ChatForYouPage>
    with AutomaticKeepAliveClientMixin {
  final chatNavBarPageStateKey = GlobalKey<ChatForYouPageState>();

  late final PageController _pageController;
  late double _currentPage;
  bool isFinding = false;
  late List<ChatNavbarForYouResult>? _result;

  late int? _bookId;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _bookId = widget.arguments['bookId'];
    _currentPage = 0;
    _pageController = PageController(
      viewportFraction: 0.85, // 调整视窗比例
      initialPage: 0,
    );
    _pageController.addListener(scrollerListener);
    fetchData();
  }

  @override
  void dispose() {
    _pageController.removeListener(scrollerListener);
    _pageController.dispose();
    super.dispose();
  }

  void scrollerListener() {
    setState(() {
      _currentPage = _pageController.page ?? 0;
    });
  }

  fetchData() async {
    isFinding = true;

    setState(() {});
    _result =
        await ChatViewModel.instance.postChatNavbarForYouModel(1, 100, _bookId);
    isFinding = false;

    if (mounted) {
      setState(() {});
    }
  }

  void _handleChatTap(ChatNavbarForYouResult? data) async {
    Get.toNamed('/chatPage', arguments: {
      "chatBookId": data?.chatBookId,
      'bookId': _bookId,
      'bookCover': data?.cover
    });
  }

  Widget _buildFinding() {
    var mediaQuery = MediaQuery.of(context);
    var cardWidth = mediaQuery.size.width * 0.9;
    return Stack(
      alignment: Alignment.center, // 所有子组件默认居中对齐
      children: [
        Container(
          alignment: Alignment.center,
          width: cardWidth,
          margin: const EdgeInsets.only(top: 20, bottom: 25, left: 8, right: 8),
          decoration: BoxDecoration(
            color: const Color.fromARGB(75, 255, 255, 255),
            borderRadius: BorderRadius.circular(20),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(bottom: 150),
          child: Image.asset(
            "assets/images/chat/chat_icon_search.png",
            width: 171,
            height: 230,
          ),
        ),
        Padding(
          padding: EdgeInsets.only(top: 220),
          child: Text(
            "searching_scene".tr,
            style: TextStyle(color: Colors.white, fontSize: 20),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(top: 300),
          child: Text(
            "wait_a_moment".tr,
            style: TextStyle(color: Color(0xFF888888), fontSize: 17),
          ),
        ),
        LottieAnimationView(),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用
    return Scaffold(
      resizeToAvoidBottomInset: false, // 防止键盘弹出导致重建
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.only(bottom: 20),
          child: isFinding
              ? Center(
                  child: _buildFinding(),
                )
              : Stack(
                  clipBehavior: Clip.none, // 允许子组件超出范围
                  children: [
                    Positioned(
                        child: PageView.builder(
                      controller: _pageController,
                      itemCount: _result?.length,
                      itemBuilder: (context, index) {
                        // 计算缩放比例和位置偏移
                        double diff = index - _currentPage;
                        double scale = 1 - (diff.abs() * 0.2);

                        ChatNavbarForYouResult? data = _result?[index];
                        final String url = data?.cover ?? "";

                        return Transform(
                          transform: Matrix4.identity()
                            ..setEntry(3, 2, 0.001)
                            ..scale(scale),
                          alignment: diff >= 0 ? Alignment.centerLeft : Alignment.centerRight,
                          child: Container(
                            clipBehavior: Clip.none,
                            margin: const EdgeInsets.only(
                                top: 20, bottom: 25, left: 8, right: 8),
                            decoration: BoxDecoration(
                              color:
                              const Color.fromARGB(0, 255, 255, 255),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: ClipRRect(
                              borderRadius:
                              BorderRadius.circular(20), // 设置圆角
                              child: Stack(
                                clipBehavior: Clip.none, // 允许子组件超出范围
                                children: [
                                  //背景图
                                  NetworkImageUtil(
                                    imageUrl: url,
                                    placeholderColor: Color(0xFF343434),
                                  ),
                                  //卡片上的内容
                                  Positioned(
                                    bottom: 45, // 固定在底部
                                    left: 10,
                                    right: 10,
                                    child: Column(
                                      children: [
                                        //头像部分
                                        GestureDetector(
                                          onTap: () {
                                            Get.toNamed('/chatDetailPage',
                                                arguments: {
                                                  "personId":
                                                  data?.personId ?? 0,
                                                  "chatBookId":
                                                  data?.chatBookId ??
                                                      0,
                                                  "bookId": _bookId
                                                });
                                          },
                                          child: Row(
                                            children: [
                                              const SizedBox(width: 10),
                                              NetworkImageUtil(
                                                imageUrl: url,
                                                isCircle: true,
                                                width: 45,
                                                height: 45,
                                              ),
                                              const SizedBox(width: 10),
                                              Column(
                                                crossAxisAlignment:
                                                CrossAxisAlignment
                                                    .start,
                                                children: [
                                                  Text(
                                                    data?.personName ??
                                                        "",
                                                    textAlign:
                                                    TextAlign.left,
                                                    style: TextStyle(
                                                      color: Colors.white,
                                                      fontSize: 19,
                                                    ),
                                                  ),
                                                  Text(
                                                    data?.category ?? "",
                                                    textAlign:
                                                    TextAlign.left,
                                                    style: TextStyle(
                                                        color:
                                                        Colors.white,
                                                        fontSize: 13),
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                        SizedBox(
                                          height: 3,
                                        ),
                                        //黑色圆角区域
                                        Container(
                                          clipBehavior: Clip.none,
                                          // width: MediaQuery.of(context).size.width * 0.5,
                                          decoration: BoxDecoration(
                                              color: const Color.fromARGB(
                                                  200, 0, 0, 0),
                                              borderRadius:
                                              BorderRadius.circular(
                                                  20)),
                                          // color: const Color.fromARGB(132, 0, 0, 0),
                                          child: Column(
                                            children: [
                                              //说话内容
                                              Container(
                                                margin:
                                                const EdgeInsets.only(
                                                    top: 15,
                                                    bottom: 0,
                                                    left: 10,
                                                    right: 10),
                                                padding:
                                                EdgeInsets.symmetric(
                                                    horizontal: 10,
                                                    vertical: 5),
                                                decoration: BoxDecoration(
                                                  color: const Color
                                                      .fromARGB(
                                                      189, 56, 56, 56),
                                                  borderRadius:
                                                  BorderRadius.all(
                                                      Radius.circular(
                                                          20)),
                                                ),
                                                child: ChatViewModel
                                                    .instance
                                                    .getRichText(
                                                    data?.firstSessionContent,
                                                    4),
                                              ),
                                              //介绍
                                              Container(
                                                clipBehavior: Clip.none,
                                                padding:
                                                EdgeInsets.symmetric(
                                                    horizontal: 10,
                                                    vertical: 5),
                                                decoration: BoxDecoration(
                                                  color: const Color
                                                      .fromARGB(
                                                      0, 0, 0, 0),
                                                  borderRadius:
                                                  BorderRadius
                                                      .circular(20),
                                                ),
                                                child: ExpandableText(
                                                  text:
                                                  data?.background ??
                                                      "",
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    )),

                    //底部按钮
                    Positioned(
                      bottom: 0,
                      left: 30,
                      right: 30,
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: HexColor("#EF85B7"),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(24),
                          ),
                          padding: EdgeInsets.symmetric(
                              horizontal: 32, vertical: 12),
                        ),
                        onPressed: () {
                          int i = _pageController.page?.round() ?? 0;
                          _handleChatTap(_result?[i]);
                        },
                        child: Text('chat'.tr,
                            style:
                                TextStyle(color: Colors.white, fontSize: 22)),
                      ),
                    )
                  ],
                ),
        ),
      ),
    );
  }
}
