import 'dart:core';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/MainPage/Chat/ViewModel/ViewModel.dart';
import 'package:UrNovel/Util/Common/network_image_util.dart';

import '../../../../Util/Common/Animation/lottie_animation_view.dart';
import '../../../../Util/Common/no_load_view.dart';
import '../../../../Util/SheetAndAlter/alter.dart';
import '../../../../Util/custom_tag.dart';
import '../../Model/chat_character_detail_model.dart';
import '../../Widget/ChatDetail/chat_detail_item.dart';
import '../../Widget/expandable_text.dart';

// BaseFulWidget {
class ChatDetailPage extends BaseFulWidget {
  ChatDetailPage({super.key, super.arguments});

  @override
  State<ChatDetailPage> createState() => ChatDetailPageState();
}

class ChatDetailPageState extends State<ChatDetailPage> {
  late int personId;
  late int chatBookId;
  late ChatCharacterDetailResult? result;
  bool isLoading = false;
  int? _bookId;

  @override
  void initState() {
    super.initState();
    personId = widget.arguments['personId'] ?? 0;
    chatBookId = widget.arguments['chatBookId'] ?? 0;
    _bookId = widget.arguments['bookId'] ?? 0;
    fetchData();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> fetchData() async {
    isLoading = true;
    setState(() {});
    result = await ChatViewModel.instance
        .getChatCharacterDetailModel(personId, chatBookId);
    isLoading = false;
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return Container(color: Color(0XFF343434), child: LottieAnimationView());
    }
    if (result == null) {
      return NoLoadView();
    }

    MainPage mainPage = result!.mainPage!;

    return Stack(
      // alignment: AlignmentDirectional.center,
      children: [
        Container(
          color: Color(0xFF343434),
        ),
        // 背景图片
        NetworkImageUtil(
          imageUrl: mainPage.headImg ?? "",
          width: double.infinity,
          height: double.infinity,
          fit: BoxFit.cover, // 使图片覆盖整个屏幕
        ),

        //黑色遮罩
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.bottomCenter,
              end: Alignment.topCenter,
              stops: const [0.0, 0.4, 0.8, 1.0], // 定义渐变的停止点
              colors: [
                Colors.black, // 底部纯黑
                Colors.black, // 40%处纯黑
                Colors.transparent, // 70%处全透明
                Colors.transparent, // 顶部全透明
              ],
            ),
          ),
        ),

        // 其他内容
        Scaffold(
          resizeToAvoidBottomInset: false, // 防止键盘弹出导致重建
          backgroundColor: Color.fromARGB(0, 16, 16, 17),
          appBar: AppBar(
            backgroundColor: Color.fromARGB(0, 16, 16, 17),
            automaticallyImplyLeading: true,
            iconTheme: IconThemeData(
              color: Colors.white, // 设置返回按钮颜色为白色
            ),
          ),
          body: SafeArea(
            child: Stack(
              children: [
                ListView.builder(
                    itemCount: result!.scenes!.length + 2,
                    itemBuilder: (context, index) {
                      if (index == 0) {
                        return Column(
                          mainAxisAlignment: MainAxisAlignment.center, // 靠底部对齐
                          children: [
                            const SizedBox(height: 300),
                            Text(
                              // "Leon",
                              mainPage.personName ?? "",
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 23,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              // "25 | INFJ | idol from boy band",
                              mainPage.category ?? "",
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Tags
                            Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              children: mainPage.tags!
                                  .split(',')
                                  .map((tag) => CustomTag(label: tag))
                                  .toList(),
                            ),

                            //简介
                            Container(
                              padding:
                                  EdgeInsets.only(left: 20, right: 20, top: 5),
                              child: ExpandableText(
                                text: mainPage.description ?? "",
                                color: Colors.white,
                                fontSize: 14,
                                maxLines: 2,
                              ),
                            ),

                            // Scenes Section
                            Container(
                              padding: const EdgeInsets.only(
                                  left: 20, right: 20, top: 8),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'scenes'.tr,
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 24,
                                      // fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  // SizedBox(
                                  //   height: 210, // Adjust based on needs
                                  //   child:
                                  // ),
                                ],
                              ),
                            ),
                          ],
                        );
                      }
                      if (index == result!.scenes!.length + 2 - 1) {
                        return SizedBox(
                          height: 60,
                        );
                      }
                      return ChatDetailItem(scene: result!.scenes![index - 1]);
                    }),
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: // Start Chat Button
                      Center(
                    child: Padding(
                      // padding: const EdgeInsets.all(0),
                      padding: const EdgeInsets.only(bottom: 10),
                      child: ElevatedButton(
                        onPressed: () {
                          showAlter(StartNewChatAlert(onConfirm: () {
                            Get.toNamed('/chatPage', arguments: {
                              "chatBookId": mainPage.chatBookId,
                              "newChat": true,
                              "bookId": _bookId
                            });
                          }));
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFFEF85B7),
                          minimumSize: const Size(240, 50),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                        ),
                        child: Text(
                          'start_new_chat'.tr,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
