import 'dart:async';
import 'dart:math' as math;
import 'dart:math';

import 'package:UrNovel/Launch&Login/Model/user_model.dart';
import 'package:UrNovel/Launch&Login/ViewModel/ViewModel.dart';
import 'package:UrNovel/Util/Common/network_image_util.dart';
import 'package:UrNovel/Util/logUtil.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../BaseWidget/base_ful_widget.dart';
import '../../../../Util/Common/Animation/lottie_animation_view.dart';
import '../../../../Util/Common/CommonManager/common_manager.dart';
import '../../../../Util/LocalNotificationManager/local_notification_manager.dart';
import '../../../../Util/SharedPreferences/shared_preferences.dart';
import '../../../../Util/SheetAndAlter/alter.dart';
import '../../../../Util/enum.dart';
import '../../../../Util/tools.dart';
import '../../Model/chat_info_model.dart';
import '../../ViewModel/ViewModel.dart';

class ChatPage extends BaseFulWidget {
  ChatPage({super.key, required super.arguments});

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> with TickerProviderStateMixin {
  static const int suggestionAnimTime = 500; //seconds
  static const int scrollAnimTime = 500; //seconds
  late TextEditingController _messageController;
  late ScrollController _scrollController;
  late AnimationController _controllerLoading;
  late AnimationController _animationController;
  bool _showSuggestions = false;
  bool _isLoading = false;
  bool _isLoadingMessage = false;

  Result? result; //聊天界面信息
  List<RecordVOS>? promptList; //提示词信息

  late ValueNotifier<bool> _sendStateNotifier; // 控制图标状态

  late int? _bookId;
  late String? _bookCover;
  late UserInfoModel? _userInfoModel;

  @override
  void initState() {
    super.initState();
    _bookId = widget.arguments["bookId"];
    _bookCover = widget.arguments["bookCover"];
    _userInfoModel = null;

    _messageController = TextEditingController();
    _scrollController = ScrollController();
    _controllerLoading = AnimationController(
      vsync: this, // 使用 SingleTickerProviderStateMixin
      duration: Duration(seconds: 2), // 旋转一圈的时长
    )..repeat(); // 无限循环播放
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );
    _animationController.addListener(_linkAnimationToScroll);
    _sendStateNotifier = ValueNotifier(false); // 控制图标状态

    eventBusOn((obj) async {
      await _getUserInfo();
    });

    fetchData();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _controllerLoading.dispose();
    _sendStateNotifier.dispose();
    _animationController.removeListener(_linkAnimationToScroll);
    _animationController.dispose();
    super.dispose();
  }

  //获取聊天数据
  fetchData() async {
    _isLoading = true;
    int chatBookId = widget.arguments["chatBookId"] ?? 1;
    bool newChat = widget.arguments["newChat"] ?? false;
    result = await ChatViewModel.instance.getChatInfoModel(chatBookId, newChat);
    ChatViewModel.instance.chatItems = [...(result?.recordVOS ?? [])];
    if (_bookCover != result?.cover) {
      _bookCover = result?.cover;
    }
    await _getUserInfo();

    ChatViewModel.instance.chatItems.insert(
        0,
        RecordVOS(type: ChatItemCharacterType.TIPS.name, content: [
          Content(
              content: "all_ai_generated".tr,
              type: ChatItemTextType.NORMAL.name)
        ]));

    logD(ChatViewModel.instance.chatItems);
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
    WidgetsBinding.instance.addPostFrameCallback((t1) {
      _goodScrollToBottom();
      _scrollToBottom(milliseconds: 0);
    });
  }

  Future<void> _getUserInfo() async {
    _userInfoModel = await LoginViewModel.getUserInfo();
    if (mounted) {
      setState(() {});
    }
  }

  void _linkAnimationToScroll() {
    _scrollController.jumpTo(
      _animationController.value * _scrollController.position.maxScrollExtent,
    );
  }

  void _goodScrollToBottom() {
    _animationController.value = _scrollController.position.pixels /
        _scrollController.position.maxScrollExtent;
    _animationController.fling(velocity: 100);
  }

  // 滚动到底部的方法
  void _scrollToBottom({int milliseconds = scrollAnimTime, int offsetH = 0}) {
    if (_scrollController.hasClients) {
      if (milliseconds == 0) {
        _scrollController
            .jumpTo(_scrollController.position.maxScrollExtent + offsetH);
        return;
      }
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent + offsetH,
        duration: Duration(milliseconds: scrollAnimTime),
        curve: Curves.easeIn,
      );
    }
  }

  //切换Suggestion面板
  void _toggleSuggestions() {
    // 如果键盘是打开的，先关闭键盘
    unFocusScope(context);

    setState(() {
      _showSuggestions = !_showSuggestions;
    });

    // 如果是显示建议面板，等建议面板动画结束再滚动到底部
    if (_showSuggestions) {
      Future.delayed(const Duration(seconds: suggestionAnimTime), () {
        _scrollToBottom();
      });
    }
  }

  //点击Suggestion Item
  void _applySuggestion(String suggestion) {
    setState(() {
      _messageController.text = suggestion;
      _showSuggestions = false;
      _sendStateNotifier.value = true;
    });
    FocusScope.of(context).requestFocus(FocusNode());
  }

  Future _sendMessageSendBtn() async {
    if (_messageController.text.trim().isEmpty) return;
    if (_isLoadingMessage) return;
    await _sendMessage(false, content: _messageController.text);
  }

  _messageErrReport() {
    showAlter(ReportErrorContent(
      reportType: ReportType.comment,
      onConfirm: (content) async {
        await errorReport(ReportType.chat, content,
            chatId: result?.id, messageId: result?.messageId);
      },
    ));
  }

  Future _sendMessageContinue() async {
    if (_isLoadingMessage) return;
    await _sendMessage(true);
  }

  Future _sendMessageRetry() async {
    if (_isLoadingMessage) return;

    _isLoadingMessage = true;
    RecordVOS last = ChatViewModel.instance.chatItems.removeLast();
    var recordVo2 = RecordVOS();
    recordVo2.type = ChatItemCharacterType.LOADING.name;
    ChatViewModel.instance.chatItems.add(recordVo2);
    _scrollToBottom();
    setState(() {});
    var msg = await ChatViewModel.instance
        .reGetChatMessageSendModel(result?.id ?? "");

    ChatViewModel.instance.chatItems.removeLast(); //移除Loading
    _isLoadingMessage = false;
    if (msg != null) {
      last = msg;
    }
    ChatViewModel.instance.chatItems.add(last); //把最后一个元素加回去
    _scrollToBottom();

    if (mounted) {
      setState(() {});
    }
  }

  //发送消息
  Future _sendMessage(bool isContinue, {String content = ""}) async {
    // if (_messageController.text.isEmpty) return;
    if (_isLoadingMessage) return;
    String message = content;
    setState(() {
      if (!isContinue) {
        // Add user message
        var recordVo = RecordVOS();
        recordVo.content = message.toContentList();
        recordVo.type = ChatItemCharacterType.USER.name;
        ChatViewModel.instance.chatItems.add(recordVo);
      }

      // Add loading message for opponent
      var recordVo2 = RecordVOS();
      recordVo2.type = ChatItemCharacterType.LOADING.name;
      ChatViewModel.instance.chatItems.add(recordVo2);
    });
    _messageController.clear();
    _sendStateNotifier.value = false;

    // Scroll to bottom
    _scrollToBottom();

    _isLoadingMessage = true;
    RecordVOS? answerResult;
    if (isContinue) {
      answerResult = await ChatViewModel.instance
          .continueGetChatMessageSendModel(result?.id ?? "");
    } else {
      answerResult = await ChatViewModel.instance
          .getChatMessageSendModel(result?.id ?? "", message);
    }

    if (answerResult == null) {
      logD("answerResult == null");
      var chatItems = ChatViewModel.instance.chatItems;
      if (chatItems[chatItems.length - 1].type ==
          ChatItemCharacterType.LOADING.name) {
        chatItems.removeLast();
      }
    }

    await _getUserInfo();

    _isLoadingMessage = false;
    if (mounted) {
      setState(() {});
      _scrollToBottom();
    }
  }

  _toChatDetailPage() async {
    await Get.toNamed('/accountPage',
        arguments: {"goodsType": GoodsType.purchaseDiamonds});
  }

  List<Widget> _buildActions() {
    return [
      const SizedBox(width: 5),
      IconButton(
        onPressed: () {
          Get.back();
        },
        icon: Transform.rotate(
          angle: CommonManager.instance.isReverse() ? math.pi : 0,
          child: Image.asset("assets/images/common/icon_back_white.png",
              width: 12, height: 20),
        ),
      ),
      const SizedBox(width: 5),
      Expanded(
          child: Text(
        result?.title ?? "",
        style: TextStyle(
          color: Colors.white, // 设置标题颜色为白色
          fontSize: 20, // 设置标题字体大小
          fontWeight: FontWeight.bold, // 设置标题字体加粗
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      )),
      const SizedBox(width: 15),
      InkWell(
        borderRadius: BorderRadius.circular(10),
        onTap: () async {
          //todo: 跳转到小说阅读页面
          if (curRoutePage == RoutePage.novelReadPage) {
            // 保留首页，登录页等关键页面
            Get.back();
            Get.back();
            setSystemUiDark();
          } else {
            await LocalNotificationManager.toNovelReadPage({
              'bookId': _bookId,
              'jumpType': BookDetailJumpType.chat
            });
            setSystemUiDark();
          }
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Color.fromARGB(76, 255, 255, 255),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Row(
            children: [
              Image.asset(
                "assets/images/chat/chat_icon_book.png",
                width: 28,
                height: 24,
              ),
              SizedBox(width: 10),
              Text(
                'novel'.tr,
                style: TextStyle(color: Colors.white, fontSize: 17),
              ),
            ],
          ),
        ),
      ),
      const SizedBox(width: 8),
      InkWell(
        onTap: _toChatDetailPage,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Color.fromARGB(76, 255, 255, 255),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Row(
            children: [
              Image.asset(
                "assets/images/chat/chat_icon_diamond.png",
                width: 29,
                height: 25,
              ),
              SizedBox(width: 5),
              Text(
                '${_userInfoModel?.diamonds ?? 0}',
                style: TextStyle(color: Colors.white, fontSize: 17),
              ),
            ],
          ),
        ),
      ),
      const SizedBox(width: 12),
    ];
  }

  //第二句的介绍
  Widget _buildNarrative(RecordVOS? recordVo) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          // color: const Color.fromARGB(205, 0, 0, 0),
          color: Colors.black.withValues(alpha: 0.8),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Text(
          recordVo?.content?[0].content ?? "",
          style: TextStyle(
            color: Color(0xFFB4B4B4),
            // fontStyle: FontStyle.italic,
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  //第一句的AI提示
  Widget _buildAITips(RecordVOS? text) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 25, vertical: 8),
      child: FittedBox(
        fit: BoxFit.scaleDown,
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: const Color.fromRGBO(0, 0, 0, 0.5),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Text(
            text?.content?[0].content ?? "",
            style: const TextStyle(
              color: Color(0xFF999999),
              fontStyle: FontStyle.normal,
              fontSize: 14,
            ),
          ),
        ),
      ),
    );
  }

  //最后一条聊天下的Buttons
  Widget _buildMessageButtons() {
    return Container(
      margin: const EdgeInsets.only(top: 5, bottom: 5),
      padding: const EdgeInsets.only(bottom: 0),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 32, // 设置宽度
            height: 32, // 设置高度
            child: IconButton(
              padding: EdgeInsets.zero, // 取消内边距
              onPressed: _sendMessageRetry,
              icon: Image.asset(
                "assets/images/chat/chat_icon_refresh.png",
                width: 22,
                height: 22,
              ),
            ),
          ),
          const SizedBox(
            width: 10,
          ),
          SizedBox(
            width: 32, // 设置宽度
            height: 32, // 设置高度
            child: IconButton(
              padding: EdgeInsets.zero, // 取消内边距
              onPressed: _messageErrReport,
              icon: Image.asset(
                "assets/images/chat/chat_icon_frame04.png",
                width: 22,
                height: 22,
              ),
            ),
          ),
          Spacer(),
          SizedBox(
            width: 32, // 设置宽度
            height: 32, // 设置高度
            child: IconButton(
              padding: EdgeInsets.zero, // 取消内边距
              onPressed: _sendMessageContinue,
              icon: Image.asset(
                "assets/images/chat/chat_icon_report.png",
                width: 22,
                height: 22,
              ),
            ),
          ),
          const SizedBox(
            width: 35,
          ),
        ],
      ),
    );
  }

  //聊天消息Item
  Widget _buildMessage({
    required RecordVOS recordVOS,
    bool isLast = false,
  }) {
    bool isUser = recordVOS.type == ChatItemCharacterType.USER.name;
    final String personName = recordVOS.person ?? "";
    return Column(
      crossAxisAlignment:
          isUser ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 8, bottom: 4, left: 6, right: 6),
          child: Row(
            mainAxisAlignment:
                isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (!isUser) ...[
                GestureDetector(
                  onTap: () {
                    Get.toNamed('/chatDetailPage', arguments: {
                      "personId": recordVOS.personId ?? 0,
                      "chatBookId": result?.chatBookId ?? 0,
                      "bookId": _bookId
                    });
                  },
                  child: NetworkImageUtil(
                    imageUrl: getHeadUrl(),
                    isCircle: true,
                    width: 45,
                    height: 45,
                  ),
                ),
                const SizedBox(width: 8),
              ],
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (!isUser)
                      IntrinsicWidth(
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            // 底图
                            Container(
                              height: 25,
                              decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      Colors.black.withValues(alpha: 0.5),
                                      Colors.black.withValues(alpha: 0.0),
                                    ],
                                    begin: Alignment.centerLeft,
                                    end: Alignment.centerRight,
                                  ),
                                  borderRadius: BorderRadius.only(
                                      topLeft: Radius.circular(10))),
                            ),
                            // 名字
                            Padding(
                              padding: EdgeInsets.only(
                                  bottom: 4, left: 5, right: 40),
                              child: Text(
                                personName,
                                style: TextStyle(
                                  fontSize: 15,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    Container(
                      padding: const EdgeInsets.all(12),
                      margin: EdgeInsets.only(
                          right: isUser ? 0 : 20,
                          left: isUser ? 20 : 0,
                          top: 0,
                          bottom: 0),
                      decoration: BoxDecoration(
                        color: isUser
                            // ? Color.fromARGB(230, 205, 115, 157)
                            ? Color(0XFFCD739D).withValues(alpha: 0.9)
                            : Color(0XFF323238).withValues(alpha: 0.95),
                        borderRadius: isUser
                            ? BorderRadius.only(
                                topLeft: Radius.circular(10),
                                topRight: Radius.circular(0),
                                bottomLeft: Radius.circular(10),
                                bottomRight: Radius.circular(10))
                            : BorderRadius.only(
                                topLeft: Radius.circular(0),
                                topRight: Radius.circular(10),
                                bottomLeft: Radius.circular(10),
                                bottomRight: Radius.circular(10)),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ChatViewModel.instance.getRichText(
                              recordVOS.content, null,
                              italicColor: isUser
                                  ? Color(0xFF642C46)
                                  : Color(0xFF7B7B7B)),
                        ],
                      ),
                    ),
                    if (isLast && !isUser)
                      Padding(
                        padding: const EdgeInsets.only(left: 5, top: 0),
                        child: _buildMessageButtons(),
                      ),
                  ],
                ),
              ),
              if (isUser) ...[
                const SizedBox(width: 8),
                NetworkImageUtil(
                  imageUrl: getUserAvatar(
                      _userInfoModel?.avatar, _userInfoModel?.sex),
                  isCircle: true,
                  width: 45,
                  height: 45,
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  String getHeadUrl() {
    var list = result?.headImg;
    if (list != null && list.isNotEmpty) {
      return list[0];
    }
    return "";
  }

  void onPressedPrompt() async {
    if (!_showSuggestions) {
      promptList = await ChatViewModel.instance
          .recommendChatMessageRecommendModel(result?.id ?? "");
      if (promptList == null) return;

      _scrollToBottom(milliseconds: 0);
    }
    _toggleSuggestions();
  }

  //Loading Item
  Widget _buildLoadingMessage() {
    return Padding(
      // padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      padding: const EdgeInsets.only(top: 8, bottom: 4, left: 6, right: 6),
      child: Row(
        children: [
          GestureDetector(
            onTap: _toChatDetailPage,
            child: NetworkImageUtil(
              imageUrl: getHeadUrl(),
              isCircle: true,
              width: 45,
              height: 45,
            ),
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              // color: Colors.grey[200],
              color: Color.fromARGB(230, 0, 0, 0),
              borderRadius: BorderRadius.circular(16),
            ),
            child: AnimatedBuilder(
              animation: _controllerLoading,
              builder: (context, child) {
                return Transform.rotate(
                  angle: _controllerLoading.value * 2 * pi, // 使用 pi 常量
                  child: child,
                );
              },
              child: ClipOval(
                child: Image.asset(
                  'assets/images/chat/chat_icon_loading.png', // 替换为你的图片路径
                  width: 30,
                  height: 31,
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 提示词面板
  Widget _buildSuggestion() {
    if (promptList == null) {
      return SizedBox(
        width: 0,
      );
    }
    return AnimatedContainer(
      duration: const Duration(seconds: suggestionAnimTime),
      height: _showSuggestions ? 221 : 0,
      child: SingleChildScrollView(
        child: Container(
          // color: Color.fromARGB(204, 0, 0, 0),
          padding: const EdgeInsets.all(16),
          child: Column(
            children: getPromptItems(),
          ),
        ),
      ),
    );
  }

  // 提示词面板
  List<Widget> getPromptItems() {
    return promptList!
        .map((suggestion) => GestureDetector(
              onTap: () => _applySuggestion(
                // suggestion.content!.map((item) => item.content).join(),
                ChatViewModel.instance.getAiString(suggestion.content),
              ),
              child: Container(
                alignment: Alignment.centerLeft,
                width: double.infinity,
                // height: 55,
                // padding: const EdgeInsets.all(6),
                padding:
                    const EdgeInsets.symmetric(vertical: 6, horizontal: 10),
                margin: const EdgeInsets.only(bottom: 8),
                decoration: BoxDecoration(
                  color: const Color.fromARGB(128, 70, 69, 76),
                  // borderRadius: BorderRadius.circular(15),
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(15),
                      topRight: Radius.circular(0),
                      bottomLeft: Radius.circular(15),
                      bottomRight: Radius.circular(15)),
                ),
                child: ChatViewModel.instance
                    .getRichText(suggestion.content, null),
              ),
            ))
        .toList();
  }

  //Bottom
  Widget _buildBottom() {
    return Container(
      decoration: BoxDecoration(
        color: Color.fromARGB(204, 0, 0, 0),
        borderRadius: BorderRadius.vertical(top: Radius.circular(15)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            offset: const Offset(0, -2),
            blurRadius: 4,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding:
                const EdgeInsets.only(left: 16, right: 8, top: 8, bottom: 8),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    onChanged: (text) {
                      if (_messageController.text.isEmpty) {
                        _sendStateNotifier.value = false;
                      } else {
                        _sendStateNotifier.value = true;
                      }
                    },
                    cursorColor: Colors.white,
                    maxLines: 6,
                    minLines: 1,
                    style:
                        const TextStyle(fontSize: 16, color: Color(0xFF9D9D9D)),
                    decoration: InputDecoration(
                      hintText: 'enter_message'.tr,
                      hintStyle: const TextStyle(
                          fontSize: 16, color: Color(0xFF9D9D9D)),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(15),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: Color.fromARGB(204, 113, 113, 116),
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      suffixIcon: IconButton(
                        padding: EdgeInsets.zero,
                        icon: Image.asset(
                          "assets/images/chat/chat_icon_prompt.png",
                          width: 31,
                          height: 36,
                        ),
                        // color: Colors.amber,
                        onPressed: onPressedPrompt,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8), // 可以适当调整间距

                ValueListenableBuilder<bool>(
                  valueListenable: _sendStateNotifier,
                  builder: (context, value, child) {
                    return IconButton(
                      padding: EdgeInsets.zero,
                      // icon: const Icon(Icons.send),
                      icon: Image.asset(
                        "assets/images/chat/chat_icon_sent0${_sendStateNotifier.value ? 2 : 1}.png", //chat_icon_sent01
                        width: 40,
                        height: 40,
                      ),
                      // color: Colors.blue,
                      onPressed: () async {
                        await _sendMessageSendBtn();
                      },
                    );
                  },
                )
              ],
            ),
          ),
          _buildSuggestion(),
        ],
      ),
    );
  }

  //Center
  Widget _buildCenter() {
    return Expanded(
      child: GestureDetector(
        onTap: () {
          _toggleSuggestions();
        },
        child: ListView.builder(
          controller: _scrollController,
          padding: const EdgeInsets.only(top: 8),
          itemCount: ChatViewModel.instance.chatItems.length,
          itemBuilder: (context, index) {
            final RecordVOS item = ChatViewModel.instance.chatItems[index];
            final isLast = index == ChatViewModel.instance.chatItems.length - 1;
            if (item.type == ChatItemCharacterType.SYSTEM.name) {
              return _buildNarrative(item);
            }
            if (item.type == ChatItemCharacterType.TIPS.name) {
              return _buildAITips(item);
            }
            if (item.type == ChatItemCharacterType.LOADING.name) {
              return _buildLoadingMessage();
            }
            if (item.type == ChatItemCharacterType.USER.name ||
                item.type == ChatItemCharacterType.ASSISTANT.name) {
              return _buildMessage(
                recordVOS: item,
                isLast: isLast,
              );
            }
            return null;
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (MediaQuery.of(context).viewInsets.bottom > 0) {
        _scrollToBottom();
      }
    });

    final screenSize = MediaQuery.of(context).size;

    return PopScope(
        onPopInvokedWithResult: (didPop, result) {
          unFocusScope(context);
        },
        child: Scaffold(
          backgroundColor: Color.fromARGB(255, 27, 26, 31),
          body: Stack(
            children: [
              if (isAvailable(_bookCover))
                // 背景图片,
                NetworkImageUtil(
                  // Const.testNetImage,
                  imageUrl: _bookCover,
                  width: screenSize.width,
                  height: screenSize.height,
                  fit: BoxFit.cover,
                  showPlaceholder: false,
                ),
              // 其他内容
              SafeArea(
                  child: Column(
                children: [
                  Row(
                    children: _buildActions().toList(),
                  ),
                  _buildCenter(),
                  _buildBottom(),
                ],
                // ),
              )),
              if (_isLoading) LottieAnimationView(),
            ],
          ),
        ));
  }
}
