import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Util/Common/Animation/lottie_animation_view.dart';

import '../../../../Util/Common/no_load_view.dart';
import '../../../../Util/Extensions/colorUtil.dart';
import '../../../../Util/RefreshLoad/refresh_load.dart';
import '../../../../Util/enum.dart';
import '../../../../Util/tools.dart';
import '../../Model/chat_navbar_history_model.dart';
import '../../ViewModel/ViewModel.dart';
import '../../Widget/History/chat_history_cell.dart';

class ChatHistoryPage extends BaseFulWidget {
  ChatHistoryPage({super.key, super.arguments});

  @override
  State<ChatHistoryPage> createState() => ChatHistoryPageState();
}

class ChatHistoryPageState extends State<ChatHistoryPage>
    with AutomaticKeepAliveClientMixin {
  late List<ListElement>? bookList;
  late ChatPageType pageType;
  int pageIndex = 1; //page默认从1开始
  int pageSize = 10;
  late bool isLoading; //判断是否加载中
  late int? _bookId;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    bookList = [];
    isLoading = true;
    _bookId = widget.arguments['bookId'];
    onRefresh();
  }

  Future<void> onRefresh() async {
    pageIndex = 1;
    await getGenresAndTagsListData(true, true);
    if (isLoading) {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  Future<void> onLoading() async {
    pageIndex++;
    await getGenresAndTagsListData(true, false);
  }

  Future getGenresAndTagsListData(bool isGenres, bool isRefresh) async {
    await ChatViewModel.getChatHistoryList(pageIndex, pageSize).then((result) {
      List<ListElement>? list = result?.list;
      if (isAvailable(list)) {
        if (mounted) {
          setState(() {
            if (isRefresh) {
              bookList = list;
            } else {
              bookList?.addAll(list!);
            }
          });
        }
      }
      widget.dealRefreshState(pageIndex, pageSize, bookList);
    }).catchError((error) {
      widget.dealRefreshState(pageIndex, pageSize, bookList);
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用
    var count = bookList?.length ?? 0;
    return Scaffold(
      backgroundColor: HexColor('#101011'),
      body: Stack(
        children: [
          if (!isLoading)
            if (0 < count)
              MediaQuery.removePadding(
                context: context,
                removeTop: true,
                removeLeft: true,
                removeRight: true,
                removeBottom: true,
                child: RefreshLoadUtil(
                  onRefresh: onRefresh,
                  onLoading: onLoading,
                  controller: widget.refreshController,
                  child: ListView.builder(
                      reverse: false, // 添加这行，让列表反向显示
                      itemCount: count,
                      itemBuilder: (context, index) {
                        if (index < count) {
                          var item = bookList?[index];
                          return GestureDetector(
                            onTap: () async {
                              Get.toNamed('/chatPage', arguments: {
                                "chatBookId": item?.chatBookId,
                                'bookId': _bookId
                              });
                            },
                            child: Container(
                              margin:
                                  const EdgeInsets.only(left: 16, right: 16),
                              padding: EdgeInsets.only(
                                  top: index == 0 ? 20 : 20,
                                  bottom: index == count - 1 ? 20 : 0),
                              decoration: BoxDecoration(
                                borderRadius: index == 2
                                    ? const BorderRadius.only(
                                        topLeft: Radius.circular(12),
                                        topRight: Radius.circular(12))
                                    : index == 8
                                        ? const BorderRadius.only(
                                            bottomLeft: Radius.circular(12),
                                            bottomRight: Radius.circular(12))
                                        : null,
                              ),
                              child: ChatHistoryCell(bookItem: item),
                            ),
                          );
                        } else {
                          return Container();
                        }
                      }),
                ),
              )
            else
              NoLoadView()
          else
            const LottieAnimationView()
        ],
      ),
    );
  }
}
