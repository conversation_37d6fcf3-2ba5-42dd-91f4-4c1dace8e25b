import 'package:flutter/material.dart';
import 'package:UrNovel/Util/logUtil.dart';

import '../../../../Util/NetWorkManager/net_work_manager.dart';
import '../../../../Util/SheetAndAlter/toast.dart';
import '../../../../Util/api_config.dart';
import '../../../../Util/tools.dart';
import '../../../Util/enum.dart';
import '../Model/chat_character_detail_model.dart';
import '../Model/chat_info_model.dart';
import '../Model/chat_message_recommend_model.dart';
import '../Model/chat_message_send_model.dart';
import '../Model/chat_navbar_foryou_model.dart';
import '../Model/chat_navbar_history_model.dart';

class ChatViewModel {
  static final ChatViewModel instance = ChatViewModel._internal();

  ChatViewModel._internal();

  List<RecordVOS> chatItems = [];

  var totalRedPointCount = 0;

  List<TextSpan> chatItemRichTextDataListTextSpanList(
    List<Content>? dataList, {
    Color? normalColor = Colors.white,
    Color? italicColor = const Color(0xFF7B7B7B),
  }) {
    if (dataList == null) return [];
    List<TextSpan> retList = [];
    for (var element in dataList) {
      TextSpan ts;
      if (element.type == ChatItemTextType.NORMAL.name) {
        ts = TextSpan(
          text: element.content,
          style: TextStyle(
            fontSize: 16.0,
            color: normalColor,
            fontWeight: FontWeight.normal,
          ),
        );
        retList.add(ts);
      } else if (element.type == ChatItemTextType.ITALIC.name) {
        ts = TextSpan(
            text: "(${element.content})",
            style: TextStyle(
              fontSize: 15.0,
              color: italicColor,
              fontWeight: FontWeight.normal,
              fontStyle: FontStyle.italic,
            ));
        retList.add(ts);
      }
    }
    return retList;
  }

  RichText getRichText(
    List<Content>? list,
    int? maxLines, {
    Color? normalColor = Colors.white,
    Color? italicColor = const Color(0xFF7B7B7B),
  }) {
    return RichText(
        text: TextSpan(
            children: chatItemRichTextDataListTextSpanList(list,
                normalColor: normalColor, italicColor: italicColor)),
        textAlign: TextAlign.left,
        maxLines: maxLines,
        overflow:
            maxLines == null ? TextOverflow.visible : TextOverflow.ellipsis);
  }

  String getAiString(List<Content>? list) {
    StringBuffer sb = StringBuffer();
    for (var element in list!) {
      if (element.type == ChatItemTextType.NORMAL.name) {
        sb.write(element.content);
      } else if (element.type == ChatItemTextType.ITALIC.name) {
        sb.write("*${element.content}*");
      }
    }
    return sb.toString();
  }

  /// 获取聊天信息
  Future<Result?> getChatInfoModel(int chatBookId, bool newChat) async {
    var parameters = {"chatBookId": chatBookId, "newChat": newChat};
    var response =
        await NetWorkManager.instance.get(chatStartChat, params: parameters);
    logD("<<<===  $chatStartChat  ${response.data}");
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      ChatInfoModel infoModel = ChatInfoModel.fromJson(response.data);
      if (infoModel.code == 200) {
        return infoModel.result;
      } else {
        if (isAvailable(infoModel.msg)) {
          showToast(infoModel.msg!);
        }
        return null;
      }
    }

    return null;
  }

  /// 正常发送聊天消息
  Future<RecordVOS?> getChatMessageSendModel(
      String chatId, String message) async {
    Map<String, dynamic> parameters = {"chatId": chatId, "message": message};
    var response =
        await NetWorkManager.instance.get(chatMessageSend, params: parameters);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      logD("<<<===  $chatMessageSend  ${response.data}");
      ChatMessageSendModel infoModel =
          ChatMessageSendModel.fromJson(response.data);
      if (infoModel.code == 200) {
        var msg = infoModel.result;
        chatItems.removeLast();
        chatItems.add(msg!);
        return msg;
      } else {
        if (isAvailable(infoModel.msg)) {
          showToast(infoModel.msg!);
        }
        return null;
      }
    }
    return null;
  }

  ///重新生成
  Future<RecordVOS?> reGetChatMessageSendModel(String chatId) async {
    Map<String, dynamic> parameters = {"chatId": chatId};
    var response = await NetWorkManager.instance
        .get(chatMessageRegenerate, params: parameters);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      logD("<<<===  $chatMessageRegenerate  ${response.data}");
      ChatMessageSendModel infoModel =
          ChatMessageSendModel.fromJson(response.data);
      if (infoModel.code == 200) {
        var msg = infoModel.result;
        chatItems.removeLast();
        chatItems.add(msg!);
        return msg;
      } else {
        if (isAvailable(infoModel.msg)) {
          showToast(infoModel.msg!);
        }
        return null;
      }
    }
    return null;
  }

  ///继续生成
  Future<RecordVOS?> continueGetChatMessageSendModel(String chatId) async {
    Map<String, dynamic> parameters = {"chatId": chatId};
    var response = await NetWorkManager.instance
        .get(chatMessageContinue, params: parameters);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      logD("<<<===  $chatMessageContinue  ${response.data}");
      ChatMessageSendModel infoModel =
          ChatMessageSendModel.fromJson(response.data);
      if (infoModel.code == 200) {
        var msg = infoModel.result;
        chatItems.removeLast();
        chatItems.add(msg!);
      } else {
        if (isAvailable(infoModel.msg)) {
          showToast(infoModel.msg!);
        }
        return null;
      }
    }
    return null;
  }

  ///user回复推荐
  Future<List<RecordVOS>?> recommendChatMessageRecommendModel(
      String chatId) async {
    Map<String, dynamic> parameters = {"chatId": chatId};
    var response = await NetWorkManager.instance
        .get(chatMessageRecommend, params: parameters);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      logD("<<<===  $chatMessageRecommend  ${response.data}");
      ChatMessageRecommendModel infoModel =
          ChatMessageRecommendModel.fromJson(response.data);
      if (infoModel.code == 200) {
        var msg = infoModel.result;
        return msg;
      } else {
        if (isAvailable(infoModel.msg)) {
          showToast(infoModel.msg!);
        }
        return null;
      }
    }
    return null;
  }

  ///For You
  Future<List<ChatNavbarForYouResult>?> postChatNavbarForYouModel(
      int pageNo, int pageSize, int? bookId) async {
    Map<String, dynamic> parameters = {
      "pageNo": pageNo,
      "pageSize": pageSize,
      "bookId": bookId
    };
    var response = await NetWorkManager.instance
        .post(chatBookPage, parameters: parameters);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      logD("<<<===  $chatBookPage  ${response.data}");
      ChatNavbarForYouModel infoModel =
          ChatNavbarForYouModel.fromJson(response.data);
      if (infoModel.code == 200) {
        var result = infoModel.result;
        return result;
      } else {
        if (isAvailable(infoModel.msg)) {
          showToast(infoModel.msg!);
        }
        return null;
      }
    }
    return null;
  }

  ///History
  static Future<ChatNavbarHistoryResult?> getChatHistoryList(int pageNo, int pageSize) async {
    Map<String, dynamic> parameters = {
      "pageNo": pageNo,
      "pageSize": pageSize,
    };
    var response =
        await NetWorkManager.instance.post(chatList, parameters: parameters);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      ChatNavbarHistoryModel infoModel =
          ChatNavbarHistoryModel.fromJson(response.data);
      if (infoModel.code == 200) {
        var msg = infoModel.result;
        return msg;
      } else {
        if (isAvailable(infoModel.msg)) {
          showToast(infoModel.msg!);
        }
        return null;
      }
    }
    return null;
  }

  ///Character Detail
  Future<ChatCharacterDetailResult?> getChatCharacterDetailModel(
      int personId, int chatBookId) async {
    Map<String, dynamic> parameters = {
      "personId": personId,
      "chatBookId": chatBookId,
    };
    var response =
        await NetWorkManager.instance.get(chatPersonScene, params: parameters);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      logD("<<<===  $chatPersonScene  ${response.data}");
      ChatCharacterDetailModel infoModel =
          ChatCharacterDetailModel.fromJson(response.data);
      if (infoModel.code == 200) {
        var msg = infoModel.result;
        return msg;
      } else {
        if (isAvailable(infoModel.msg)) {
          showToast(infoModel.msg!);
        }
        return null;
      }
    }
    return null;
  }
}

extension StringToContentExt on String {
  List<Content> toContentList() {
    List<Content> result = [];
    RegExp regex = RegExp(r'\*(.*?)\*');
    String text = this;
    int lastIndex = 0;

    // 查找所有匹配项
    for (Match match in regex.allMatches(text)) {
      // 如果斜体文本前有普通文本，先添加普通文本
      if (match.start > lastIndex) {
        String normalText = text.substring(lastIndex, match.start);
        if (normalText.isNotEmpty) {
          result.add(Content()
            ..type = ChatItemTextType.NORMAL.name
            ..content = normalText);
        }
      }

      // 添加斜体文本（去掉*号）
      String italicText = match.group(1)!;
      result.add(Content()
        ..type = ChatItemTextType.ITALIC.name
        ..content = italicText);

      lastIndex = match.end;
    }

    // 添加最后剩余的普通文本
    if (lastIndex < text.length) {
      String normalText = text.substring(lastIndex);
      if (normalText.isNotEmpty) {
        result.add(Content()
          ..type = ChatItemTextType.NORMAL.name
          ..content = normalText);
      }
    }

    return result;
  }
}
