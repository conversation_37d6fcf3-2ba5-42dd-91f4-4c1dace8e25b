import 'package:flutter/material.dart';
import 'package:UrNovel/MainPage/Chat/Widget/History/red_point.dart';
import 'package:UrNovel/Util/Common/network_image_util.dart';
import 'package:UrNovel/Util/Extensions/colorUtil.dart';

import '../../Model/chat_navbar_history_model.dart';

class ChatHistoryCell extends StatefulWidget {
  final ListElement? bookItem;

  const ChatHistoryCell({super.key, this.bookItem});

  @override
  State<ChatHistoryCell> createState() => _ChatHistoryCellState();
}

class _ChatHistoryCellState extends State<ChatHistoryCell> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    ListElement data = widget.bookItem as ListElement;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        NetworkImageUtil(
            imageUrl: data.headImg![0], width: 60, height: 60, isCircle: true),
        const SizedBox(width: 5),
        Expanded(
            flex: 8,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  data.title ?? "",
                  style: TextStyle(
                    fontSize: 16,
                    color: HexColor("#FFFFFF"),
                    fontWeight: FontWeight.normal,
                    height: 1,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                SizedBox(
                  height: 36,
                  child: Align(
                    alignment: Alignment.bottomLeft,
                    child: Text(
                      textAlign: TextAlign.left, // 文本内容左对齐
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      data.recentContentTrim ?? "",
                      style: TextStyle(
                        color: HexColor("#868686"),
                        fontSize: 12,
                        fontWeight: FontWeight.normal,
                      ),
                    ),
                  ),
                )
              ],
            )),
        Visibility(
          visible: (data.unReadCount ?? 0) > 0,
          child: Stack(
            clipBehavior: Clip.none, // 允许子组件超出边界
            children: [
              Container(
                height: 25,
                margin: const EdgeInsets.only(left: 15),
                decoration: BoxDecoration(
                  color: HexColor("#F4F5F6"),
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              Positioned(
                top: 0, // 向上偏移
                right: -8, // 向右偏移
                child: RedPoint(count: data.unReadCount ?? 0),
              ),
            ],
          ),
        )
      ],
    );
  }
}
