import 'package:flutter/material.dart';

class RedPoint extends StatelessWidget {
  final int count;
  final double size;
  final Color color;
  
  const RedPoint({
    super.key,
    required this.count,
    this.size = 20,  // 固定大小
    this.color = Colors.red,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,  // 确保是圆形
      ),
      child: Center(  // 使用Center确保文字居中
        child: Text(
          count > 99 ? '99+' : count.toString(),
          style: TextStyle(
            color: Colors.white,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}