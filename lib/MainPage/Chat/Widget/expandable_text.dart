import 'package:flutter/material.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';

class ExpandableText extends BaseFulWidget {
  final String text;
  final Color color;
  final double fontSize;
  final int maxLines;

  ExpandableText({
    super.key,
    required this.text,
    this.color = const Color(0XFFB4B4B4),
    this.fontSize = 13,
    this.maxLines = 4,
  });

  @override
  _ExpandableTextState createState() => _ExpandableTextState();
}

class _ExpandableTextState extends State<ExpandableText>
    with SingleTickerProviderStateMixin {
  bool isExpanded = false;
  late AnimationController _rotationController;

  @override
  void initState() {
    super.initState();
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      final textSpan = TextSpan(
        text: widget.text,
        style: TextStyle(
          fontSize: widget.fontSize,
          color: widget.color,
          fontWeight: FontWeight.normal,
        ),
      );

      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
        maxLines: widget.maxLines,
      )..layout(maxWidth: constraints.maxWidth);

      final isTextOverflowing = textPainter.didExceedMaxLines;

      return Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
            padding:
                EdgeInsets.only(right: isTextOverflowing ? 14 : 0), // 为图标预留空间
            child: Text(
              widget.text,
              style: TextStyle(
                fontSize: widget.fontSize,
                color: widget.color,
                fontWeight: FontWeight.normal,
              ),
              textAlign: TextAlign.left,
              maxLines: !isExpanded ? widget.maxLines : null,
              overflow:
                  !isExpanded ? TextOverflow.ellipsis : TextOverflow.visible,
            ),
          ),
          if (isTextOverflowing)
            Positioned(
              right: -5,
              bottom: -10,
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    isExpanded = !isExpanded;
                    if (isExpanded) {
                      _rotationController.forward();
                    } else {
                      _rotationController.reverse();
                    }
                  });
                },
                child: Container(
                  clipBehavior: Clip.none,
                  width: 40,
                  // 增加容器宽度
                  height: 40,
                  // 增加容器高度
                  alignment: Alignment.center,
                  // 居中对齐内容
                  padding: EdgeInsets.only(left: 10.0),
                  decoration: BoxDecoration(
                    // gradient: LinearGradient(
                    //   begin: Alignment.centerLeft,
                    //   end: Alignment.centerRight,
                    //   colors: [
                    //     const Color.fromARGB(255, 0, 0, 0),
                    //     Theme.of(context).scaffoldBackgroundColor,
                    //   ],
                    // ),
                    color: const Color.fromARGB(0, 255, 255, 255), // 保持透明
                  ),
                  child: RotationTransition(
                    turns: Tween(begin: 0.0, end: 0.5)
                        .animate(_rotationController),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: widget.color,
                      size: 20,
                    ),
                  ),
                ),
              ),
            ),
        ],
      );
    });
  }
}
