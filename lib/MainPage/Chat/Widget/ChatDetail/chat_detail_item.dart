import 'package:flutter/material.dart';
import 'package:UrNovel/Util/Common/network_image_util.dart';
import '../../Model/chat_character_detail_model.dart';

class ChatDetailItem extends StatelessWidget {
  final Scene scene;

  const ChatDetailItem({
    super.key,
    required this.scene,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 110,
      child: GestureDetector(
        onTap: () async {

        },
        child: Card(
          color: Color(0XFF46454C).withValues(alpha: 0.5),
          margin: const EdgeInsets.only(bottom: 12),
          child: Padding(
            padding: const EdgeInsets.all(6),
            child: Row(
              children: [
                // 图片部分
                ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: NetworkImageUtil(
                      imageUrl: scene.cover,
                      width: 70,
                      height: 90,
                    )),

                // 间距
                const SizedBox(width: 10),

                // 文字部分
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        scene.title ?? "",
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 0),
                      Text(
                        scene.background ?? "",
                        style: TextStyle(
                          color: Colors.grey[400],
                          fontSize: 13,
                        ),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}