import 'package:json_annotation/json_annotation.dart';

import 'chat_info_model.dart';
part 'chat_message_send_model.g.dart';

@JsonSerializable()
class ChatMessageSendModel {
  int? code;
  RecordVOS? result;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  ChatMessageSendModel({
    this.code,
    this.result,
    this.msg,
    this.sysAt,
    this.cost,
    this.traceId,
  });

  factory ChatMessageSendModel.fromJson(Map<String, dynamic> json) =>
      _$ChatMessageSendModelFromJson(json);
  Map<String, dynamic> toJson() => _$ChatMessageSendModelToJson(this);
}
