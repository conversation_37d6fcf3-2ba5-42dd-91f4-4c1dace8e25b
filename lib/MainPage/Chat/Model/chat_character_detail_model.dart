import 'package:json_annotation/json_annotation.dart';

part 'chat_character_detail_model.g.dart';

@JsonSerializable()
class ChatCharacterDetailModel {
  int? code;
  ChatCharacterDetailResult? result;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  ChatCharacterDetailModel({
    this.code,
    this.result,
    this.msg,
    this.sysAt,
    this.cost,
    this.traceId,
  });

  factory ChatCharacterDetailModel.fromJson(Map<String, dynamic> json) =>
      _$ChatCharacterDetailModelFromJson(json);
  Map<String, dynamic> toJson() => _$ChatCharacterDetailModelToJson(this);
}

@JsonSerializable()
class ChatCharacterDetailResult {
  MainPage? mainPage;
  List<Scene>? scenes;

  ChatCharacterDetailResult({
    this.mainPage,
    this.scenes,
  });

  factory ChatCharacterDetailResult.fromJson(Map<String, dynamic> json) =>
      _$ChatCharacterDetailResultFromJson(json);
  Map<String, dynamic> toJson() => _$ChatCharacterDetailResultToJson(this);
}

@JsonSerializable()
class MainPage {
  int? chatBookId;
  String? headImg;
  int? personId;
  String? personName;
  String? category;
  String? tags;
  String? description;

  MainPage({
    this.chatBookId,
    this.headImg,
    this.personId,
    this.personName,
    this.category,
    this.tags,
    this.description,
  });

  factory MainPage.fromJson(Map<String, dynamic> json) =>
      _$MainPageFromJson(json);
  Map<String, dynamic> toJson() => _$MainPageToJson(this);
}

@JsonSerializable()
class Scene {
  int? chatBookId;
  String? title;
  String? cover;
  String? background;

  Scene({
    this.chatBookId,
    this.title,
    this.cover,
    this.background,
  });

  factory Scene.fromJson(Map<String, dynamic> json) => _$SceneFromJson(json);
  Map<String, dynamic> toJson() => _$SceneToJson(this);
}
