import 'package:json_annotation/json_annotation.dart';

part 'chat_navbar_history_model.g.dart';

@JsonSerializable()
class ChatNavbarHistoryModel {
  int? code;
  ChatNavbarHistoryResult? result;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  ChatNavbarHistoryModel({
    this.code,
    this.result,
    this.msg,
    this.sysAt,
    this.cost,
    this.traceId,
  });

  factory ChatNavbarHistoryModel.fromJson(Map<String, dynamic> json) =>
      _$ChatNavbarHistoryModelFromJson(json);
  Map<String, dynamic> toJson() => _$ChatNavbarHistoryModelToJson(this);
}

@JsonSerializable()
class ChatNavbarHistoryResult {
  List<ListElement>? list;
  int? total;
  dynamic extend;

  ChatNavbarHistoryResult({
    this.list,
    this.total,
    this.extend,
  });

  factory ChatNavbarHistoryResult.fromJson(Map<String, dynamic> json) =>
      _$ChatNavbarHistoryResultFromJson(json);
  Map<String, dynamic> toJson() => _$ChatNavbarHistoryResultToJson(this);
}

@JsonSerializable()
class ListElement {
  String? id;
  String? title;
  List<String>? headImg;
  String? recentContentTrim;
  int? unReadCount;
  int? lastChatTime;
  int? chatBookId;
  bool? read;
  dynamic records;

  ListElement({
    this.id,
    this.title,
    this.headImg,
    this.recentContentTrim,
    this.unReadCount,
    this.lastChatTime,
    this.chatBookId,
    this.read,
    this.records,
  });

  factory ListElement.fromJson(Map<String, dynamic> json) =>
      _$ListElementFromJson(json);
  Map<String, dynamic> toJson() => _$ListElementToJson(this);
}
