import 'package:json_annotation/json_annotation.dart';
part 'chat_info_model.g.dart';

@JsonSerializable()
class ChatInfoModel {
  int? code;
  Result? result;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  ChatInfoModel({
    this.code,
    this.result,
    this.msg,
    this.sysAt,
    this.cost,
    this.traceId,
  });

  factory ChatInfoModel.fromJson(Map<String, dynamic> json) =>
      _$ChatInfoModelFromJson(json);
  Map<String, dynamic> toJson() => _$ChatInfoModelToJson(this);
}

@JsonSerializable()
class Result {
  String? id;
  String? title;
  List<String>? headImg;
  String? cover;
  int? lastChatTime;
  String? background;
  // List<Record>? records;
  List<RecordVOS>? recordVOS;
  int? chatBookId;
  int? messageId;

  Result({
    this.id,
    this.title,
    this.headImg,
    this.cover,
    this.lastChatTime,
    this.background,
    this.recordVOS,
    this.chatBookId,
    this.messageId,
  });

  factory Result.fromJson(Map<String, dynamic> json) => _$ResultFromJson(json);
  Map<String, dynamic> toJson() => _$ResultToJson(this);
}

@JsonSerializable()
class RecordVOS{
  String? person;
  int? time;
  List<Content>? content;
  String? type;
  bool? show;
  int? personId;

  RecordVOS({
    this.person,
    this.time,
    this.content,
    this.type,
    this.show,
    this.personId,
  });

  factory RecordVOS.fromJson(Map<String, dynamic> json) =>
      _$RecordVOSFromJson(json);
  Map<String, dynamic> toJson() => _$RecordVOSToJson(this);
}

@JsonSerializable()
class Content {
  String? content;
  String? type;

  Content({
    this.content,
    this.type,
  });

  factory Content.fromJson(Map<String, dynamic> json) =>
      _$ContentFromJson(json);

  Map<String, dynamic> toJson() => _$ContentToJson(this);
}

@JsonSerializable()
class Record {
  String? person;
  int? time;
  String? content;
  String? type;
  bool? show;

  Record({
    this.person,
    this.time,
    this.content,
    this.type,
    this.show,
  });

  factory Record.fromJson(Map<String, dynamic> json) => _$RecordFromJson(json);

  Map<String, dynamic> toJson() => _$RecordToJson(this);
}
