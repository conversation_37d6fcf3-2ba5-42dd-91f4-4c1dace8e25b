import 'package:json_annotation/json_annotation.dart';

import 'chat_info_model.dart';
part 'chat_navbar_foryou_model.g.dart';

@JsonSerializable()
class ChatNavbarForYouModel {
  int? code;
  List<ChatNavbarForYouResult>? result;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  ChatNavbarForYouModel({
    this.code,
    this.result,
    this.msg,
    this.sysAt,
    this.cost,
    this.traceId,
  });

  factory ChatNavbarForYouModel.fromJson(Map<String, dynamic> json) =>
      _$ChatNavbarForYouModelFromJson(json);
  Map<String, dynamic> toJson() => _$ChatNavbarForYouModelToJson(this);
}

@JsonSerializable()
class ChatNavbarForYouResult {
  int? chatBookId;
  String? title;
  String? headImg;
  String? cover;
  int? personId;
  String? personName;
  String? category;
  String? background;
  String? firstSession;
  List<Content>? firstSessionContent;

  ChatNavbarForYouResult({
    this.chatBookId,
    this.title,
    this.headImg,
    this.cover,
    this.personId,
    this.personName,
    this.category,
    this.background,
    this.firstSession,
    this.firstSessionContent,
  });

  factory ChatNavbarForYouResult.fromJson(Map<String, dynamic> json) =>
      _$ChatNavbarForYouResultFromJson(json);
  Map<String, dynamic> toJson() => _$ChatNavbarForYouResultToJson(this);
}
