import 'package:json_annotation/json_annotation.dart';

import 'chat_info_model.dart';
part 'chat_message_recommend_model.g.dart';

@JsonSerializable()
class ChatMessageRecommendModel {
  int? code;
  List<RecordVOS>? result;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  ChatMessageRecommendModel({
    this.code,
    this.result,
    this.msg,
    this.sysAt,
    this.cost,
    this.traceId,
  });

  factory ChatMessageRecommendModel.fromJson(Map<String, dynamic> json) =>
      _$ChatMessageRecommendModelFromJson(json);
  Map<String, dynamic> toJson() => _$ChatMessageRecommendModelToJson(this);
}
