import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Util/Common/network_image_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../../../../../Util/Extensions/colorUtil.dart';
import '../../../../../Util/LocalNotificationManager/local_notification_manager.dart';
import '../../../../../Util/enum.dart';
import '../../../Model/ForYou/home_read_model.dart';

class WaterfallFlowCards extends BaseFulWidget {
  final List<HomeReadBookItem?>? bookList;
  final Function(int bookId) onExposureCallback;

  WaterfallFlowCards({super.key, required this.bookList, required this.onExposureCallback});

  @override
  State<WaterfallFlowCards> createState() => _WaterfallFlowCardsState();
}

class _WaterfallFlowCardsState extends State<WaterfallFlowCards> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var cardWidth = (mediaQuery.size.width - 24) / 2.0;
    var imageHeight = cardWidth / 0.75;
    var discoverCount = widget.bookList?.length ?? 0;
    return MasonryGridView.count(
      crossAxisCount: 2,
      mainAxisSpacing: 10,
      crossAxisSpacing: 6,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.zero,
      itemCount: discoverCount,
      itemBuilder: (context, index) {
        if (index < discoverCount) {
          HomeReadBookItem? bookItem = widget.bookList?[index];
          return VisibilityDetector(
              key: Key('waterfall_$index'),
              onVisibilityChanged: (visibilityInfo) {
                if (0.8 <= visibilityInfo.visibleFraction) {
                  if (bookItem?.id is int) {
                    widget.onExposureCallback(bookItem!.id!);
                  }
                }
              },
              child: GestureDetector(
                onTap: () async {
                  //todo:去往阅读页
                  await LocalNotificationManager.toNovelReadPage(
                      {'bookId': bookItem?.id, 'jumpType': BookDetailJumpType.other});
                },
                behavior: HitTestBehavior.opaque,
                child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: DecoratedBox(
                        decoration: BoxDecoration(
                          color: HexColor('#FFFFFF'),
                        ),
                        child: Column(
                          children: [
                            NetworkImageUtil(
                              imageUrl: (bookItem?.cover),
                              width: cardWidth,
                              height: imageHeight,
                            ),
                            Padding(
                              padding: const EdgeInsets.only(top: 10, left: 4, right: 4),
                              child: SizedBox(
                                width: double.infinity,
                                child: Text(
                                  bookItem?.title ?? '',
                                  style: TextStyle(
                                      fontSize: 14,
                                      color: HexColor('#1F1F2F'),
                                      fontWeight: FontWeight.bold),
                                  maxLines: 100,
                                ),
                              ),
                            ),
                            Padding(
                              padding:
                                  const EdgeInsets.only(top: 10, left: 4, right: 4, bottom: 10),
                              child: SizedBox(
                                width: double.infinity,
                                child: Text(
                                  bookItem?.authorName ?? '',
                                  style: TextStyle(
                                      fontSize: 12,
                                      color: HexColor('#BBBBBB'),
                                      fontWeight: FontWeight.bold),
                                  maxLines: 100,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            )
                          ],
                        ))),
              ));
        }
        return Container();
      },
    );
  }
}
