import 'dart:math' as math;

import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/MainPage/Read/Widget/Explore/TopRankings/top_rankings_cards.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../Util/Common/CommonManager/common_manager.dart';
import '../../../../../Util/DataReportManager/event_name_config.dart';
import '../../../../../Util/DataReportManager/event_report_manager.dart';
import '../../../../../Util/Extensions/colorUtil.dart';
import '../../../../../Util/enum.dart';
import '../../../Model/ForYou/home_read_model.dart';

class TopRankingsTabBar extends BaseFulWidget {
  final HomeReadBookListModel? trendingBookModel;
  final HomeReadBookListModel? newReleaseBookModel;
  final HomeReadBookListModel? hotSearchesBookModel;
  final HomeReadBookListModel? favoritesBookModel;
  final Function(bool) updateTabBarIndex;

  TopRankingsTabBar(
      {super.key,
      required this.trendingBookModel,
      required this.newReleaseBookModel,
      required this.hotSearchesBookModel,
      required this.favoritesBookModel,
      required this.updateTabBarIndex});

  @override
  State<TopRankingsTabBar> createState() => _TopRankingsTabBarState();
}

class _TopRankingsTabBarState extends State<TopRankingsTabBar>
    with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  late TabController _tabController;
  late RankingType _currentListType;
  late int _currentIndex;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(_handleTabChange);
    _currentListType = RankingType.trending;
    _currentIndex = 0;

    eventReport(top9Trending);
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  //todo: 切换tab
  void _handleTabChange() {
    onTabTapped(_tabController.index);
  }

  //todo: tab点击事件
  onTabTapped(int index) async {
    if (_currentIndex == index) {
      return;
    }

    _currentIndex = index;

    switch (index) {
      case 0:
        _currentListType = RankingType.trending;
        break;
      case 1:
        _currentListType = RankingType.newRelease;
        break;
      case 2:
        _currentListType = RankingType.hotSearches;
        break;
      case 3:
        _currentListType = RankingType.favorites;
        break;
    }

    var eventName = top9Trending;
    if (_currentListType == RankingType.trending) {
      eventName = top9Trending;
    } else if (_currentListType == RankingType.newRelease) {
      eventName = top9New;
    } else if (_currentListType == RankingType.hotSearches) {
      eventName = top9Hot;
    } else if (_currentListType == RankingType.favorites) {
      eventName = top9Favorites;
    }

    await eventReport(eventName);
  }

  Future<void> eventReport(String eventName) async {
    await EventReportManager.eventReportOfFirebase(eventName);
  }

  Future<void> onMoreDetailsTap() async {
    //todo:跳转
    await Get.toNamed('/rankingPage', arguments: {"type": _currentListType});
  }

  updateTabBarIndex(bool isScrollEndLeft) {
    if (isScrollEndLeft) {
      if (_tabController.index == 0) {
        widget.updateTabBarIndex(true);
      } else {
        _tabController.index -= 1;
      }
    } else if (!isScrollEndLeft) {
      if (_tabController.index == 3) {
        widget.updateTabBarIndex(false);
      } else {
        _tabController.index += 1;
      }
    }
  }

  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    var mediaQuery = MediaQuery.of(context);
    var width = mediaQuery.size.width - 18;
    double cardImageWidth = width * 0.128;
    double cardWidth = width * 0.555;
    double cardHeight = cardImageWidth / 0.758;
    double cardTotalHeight = cardHeight * 3 + 10 * 2 + 13;

    return Container(
      width: width,
      margin: const EdgeInsets.symmetric(horizontal: 9),
      padding: const EdgeInsets.only(
        left: 13,
        right: 13,
      ),
      decoration: BoxDecoration(
        color: HexColor('#FFFFFF'),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 18),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                  child: SizedBox(
                      height: 25,
                      child: TabBar(
                        controller: _tabController,
                        padding: const EdgeInsets.only(left: 0, right: 0),
                        labelPadding: const EdgeInsets.only(left: 0, right: 32),
                        //隐藏下划线
                        dividerHeight: 0,
                        indicator: const BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: Colors.transparent, // 将下边线的颜色设置为透明
                              width: 0.0, // 将下边线的宽度设置为0
                            ),
                          ),
                        ),
                        labelStyle: TextStyle(
                            fontSize: 18,
                            color: HexColor('#000000'),
                            fontWeight: FontWeight.bold),
                        unselectedLabelStyle: TextStyle(
                            fontSize: 15,
                            color: HexColor('#555A65'),
                            fontWeight: FontWeight.bold),
                        isScrollable: true,
                        tabAlignment: TabAlignment.start,
                        onTap: onTabTapped,
                        tabs: [
                          Tab(child: Text('tab_trending'.tr)),
                          Tab(child: Text('tab_new'.tr)),
                          Tab(child: Text('tab_hot'.tr)),
                          Tab(child: Text('tab_popular'.tr)),
                        ],
                      ))),
              const SizedBox(width: 10),
              Container(
                height: 20,
                padding: const EdgeInsets.only(left: 10, right: 10),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(9),
                  color: HexColor('#F4F5F6'),
                ),
                child: GestureDetector(
                  onTap: onMoreDetailsTap,
                  behavior: HitTestBehavior.opaque,
                  child: Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          'more'.tr,
                          style: TextStyle(
                              fontSize: 14, color: HexColor('#555A65')),
                        ),
                        const SizedBox(width: 6),
                        Transform.rotate(
                          angle:
                              CommonManager.instance.isReverse() ? math.pi : 0,
                          child: Image.asset(
                            'assets/images/common/icon_arrow_gray.png',
                            width: 5,
                            height: 9,
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 18),
          SizedBox(
              height: cardTotalHeight,
              child: TabBarView(
                controller: _tabController,
                children: [
                  TopRankingsCards(
                    cardImgWidth: cardImageWidth,
                    cardWidth: cardWidth,
                    cardHeight: cardHeight,
                    bookModel: widget.trendingBookModel,
                    onScrollEnd: updateTabBarIndex,
                  ),
                  TopRankingsCards(
                    cardImgWidth: cardImageWidth,
                    cardWidth: cardWidth,
                    cardHeight: cardHeight,
                    bookModel: widget.newReleaseBookModel,
                    onScrollEnd: updateTabBarIndex,
                  ),
                  TopRankingsCards(
                    cardImgWidth: cardImageWidth,
                    cardWidth: cardWidth,
                    cardHeight: cardHeight,
                    bookModel: widget.hotSearchesBookModel,
                    onScrollEnd: updateTabBarIndex,
                  ),
                  TopRankingsCards(
                    cardImgWidth: cardImageWidth,
                    cardWidth: cardWidth,
                    cardHeight: cardHeight,
                    bookModel: widget.favoritesBookModel,
                    onScrollEnd: updateTabBarIndex,
                  ),
                ],
              )),
          const SizedBox(height: 13),
        ],
      ),
    );
  }
}
