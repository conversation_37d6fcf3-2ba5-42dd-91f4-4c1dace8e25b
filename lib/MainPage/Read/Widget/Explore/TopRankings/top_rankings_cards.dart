import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../../../../../BaseWidget/base_less_widget.dart';
import '../../../../../Util/Common/network_image_util.dart';
import '../../../../../Util/Common/no_load_view.dart';
import '../../../../../Util/Extensions/colorUtil.dart';
import '../../../../../Util/LocalNotificationManager/local_notification_manager.dart';
import '../../../../../Util/enum.dart';
import '../../../../../Util/deviceScreenUtil.dart';
import '../../../Model/ForYou/home_read_model.dart';

class TopRankingsCards extends BaseLessWidget {
  final double cardImgWidth;
  final double cardWidth;
  final double cardHeight;
  final HomeReadBookListModel? bookModel;
  final Function(bool isScrollEndLeft) onScrollEnd;

  TopRankingsCards(
      {super.key,
      required this.cardImgWidth,
      required this.cardWidth,
      required this.cardHeight,
      required this.bookModel,
      required this.onScrollEnd});

  @override
  Widget build(BuildContext context) {
    int count = bookModel?.bookList?.length ?? 0;
    bool isScrollEnd = false;

    if (0 < count) {
      return NotificationListener<ScrollNotification>(
          onNotification: (ScrollNotification notification) {
            if (!isScrollEnd &&
                notification.metrics.pixels <=
                    notification.metrics.minScrollExtent - 50) {
              /// 当滚动到最左边时触发弹簧效果响应事件
              isScrollEnd = true;
              onScrollEnd(true);
            } else if (!isScrollEnd &&
                notification.metrics.maxScrollExtent + 50 <=
                    notification.metrics.pixels) {
              /// 当滚动到最右边时触发弹簧效果响应事件
              isScrollEnd = true;
              onScrollEnd(false);
            } else if (notification is ScrollStartNotification) {
              exposureList.removeRange(0, exposureList.length);
            } else if (notification is ScrollEndNotification) {
              ///重置状态
              isScrollEnd = false;
              /// 曝光上报
              exposureReport();
            }

            return false; // 返回 false 以允许通知继续向上传递
          },
          child: GridView.builder(
            scrollDirection: Axis.horizontal, // 设置禁止滚动
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: count <= 3 ? count : 3,
              childAspectRatio: cardHeight / cardWidth,
              mainAxisSpacing: 10,
              crossAxisSpacing: 10,
            ),
            itemCount: count,
            itemBuilder: (context, index) {
              if (index < count) {
                var item = bookModel?.bookList?[index];
                return VisibilityDetector(
                    key: Key('topRanking_$index'),
                    onVisibilityChanged: (visibilityInfo) {
                      if (0.8 <= visibilityInfo.visibleFraction) {
                        if (item?.id is int) {
                          exposureList.add(item!.id!);
                        }
                      }
                    },
                    child: GestureDetector(
                      onTap: () async {
                        //todo:去往阅读页
                        await LocalNotificationManager.toNovelReadPage({
                          'bookId': item?.id,
                          'jumpType': BookDetailJumpType.other
                        });
                      },
                      behavior: HitTestBehavior.opaque,
                      child: SizedBox(
                        width: cardWidth,
                        height: cardHeight,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            NetworkImageUtil(
                                imageUrl: item?.cover,
                                width: cardImgWidth,
                                height: cardHeight,
                                fit: BoxFit.cover),
                            const SizedBox(width: 10),
                            Expanded(
                                child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  item?.title ?? "",
                                  style: TextStyle(
                                    fontSize: 15,
                                    color: HexColor('#000000'),
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                if (Platform.isIOS ||
                                    DeviceScreenUtil.instance.bottomSafeHeight == 0)
                                  SizedBox(height: 7),
                                Expanded(
                                    child: Text(
                                  item?.authorName ?? "",
                                  style: TextStyle(
                                    fontSize: 13,
                                    color: HexColor('#888C94'),
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                )),
                              ],
                            )),
                          ],
                        ),
                      ),
                    ));
              }

              return Container();
            },
          ));
    } else {
      return NoLoadView();
    }
  }
}
