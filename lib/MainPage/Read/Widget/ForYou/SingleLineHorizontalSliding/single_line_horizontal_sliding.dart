import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Util/Common/network_image_util.dart';
import 'package:flutter/material.dart';

import '../../../../../Util/Extensions/colorUtil.dart';
import '../../../../../Util/LocalNotificationManager/local_notification_manager.dart';
import '../../../../../Util/enum.dart';
import '../../../Model/ForYou/home_read_model.dart';
import '../read_more.dart';

class SingleLineHorizontalSliding extends BaseFulWidget {
  final HomeReadBookListModel? model;
  final void Function()? onMoreTap;

  SingleLineHorizontalSliding({super.key, this.model, this.onMoreTap});

  @override
  State<SingleLineHorizontalSliding> createState() => _SingleLineHorizontalSlidingState();
}

class _SingleLineHorizontalSlidingState extends State<SingleLineHorizontalSliding> {
  final _controller = ScrollController();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context);
    var cardImageWidth = (media.size.width - 38) / 3;
    var cardImgHeight = cardImageWidth / 0.75;
    var count = widget.model?.bookList?.length ?? 0;
    var cardWidth = media.size.width * 0.852;
    var cardHeight = 0 < count ? (cardImgHeight + 10) : 0.0;

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        //todo:title
        Padding(
            padding: const EdgeInsets.only(top: 24, left: 16, right: 16, bottom: 10),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    widget.model?.title ?? '',
                    style: TextStyle(
                      fontSize: 20,
                      color: HexColor('#000000'),
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(width: 10),
                ReadMore(onMoreTap: widget.onMoreTap)
              ],
            )),

        //todo:cards
        SizedBox(
          height: cardHeight, // 提供一个明确的高度
          child: ListView.builder(
            controller: _controller,
            padding: const EdgeInsets.only(left: 6, right: 6),
            scrollDirection: Axis.horizontal,
            physics: const BouncingScrollPhysics(),
            itemCount: count,
            itemBuilder: (context, index) {
              if (index < count) {
                var item = widget.model?.bookList?[index];
                return GestureDetector(
                  onTap: () async {
                    //todo:去往阅读页
                    _controller
                        .animateTo(index * (cardWidth + 8),
                            duration: const Duration(milliseconds: 300), curve: Curves.ease)
                        .whenComplete(() async {
                      await LocalNotificationManager.toNovelReadPage(
                          {'bookId': item?.id, 'jumpType': BookDetailJumpType.other});
                    });
                  },
                  child: Padding(
                    padding: EdgeInsets.only(right: index == count - 1 ? 0 : 8),
                    child: SizedBox(
                      width: cardWidth,
                      height: cardHeight,
                      child: Stack(
                        children: [
                          Positioned(
                            top: 10,
                            left: 0,
                            bottom: 0,
                            right: 0,
                            child: DecoratedBox(
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Padding(
                                padding: EdgeInsets.only(left: 6 + cardImageWidth + 6, right: 6),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const SizedBox(height: 14),
                                    Text(
                                      item?.title ?? '',
                                      style: TextStyle(
                                        fontSize: 15,
                                        color: HexColor('#1F1F2F'),
                                        fontWeight: FontWeight.bold,
                                      ),
                                      maxLines: 3,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    const SizedBox(height: 13),
                                    Text(
                                      item?.authorName ?? '',
                                      style: TextStyle(
                                        fontSize: 13,
                                        color: HexColor('#888C94'),
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ),
                          Positioned(
                            top: 0,
                            left: 6,
                            bottom: 10,
                            child: NetworkImageUtil(
                                imageUrl: item?.cover,
                                width: cardImageWidth,
                                height: cardImgHeight),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }

              return const SizedBox.shrink();
            },
          ),
        ),

        const SizedBox(height: 22),
      ],
    );
  }
}
