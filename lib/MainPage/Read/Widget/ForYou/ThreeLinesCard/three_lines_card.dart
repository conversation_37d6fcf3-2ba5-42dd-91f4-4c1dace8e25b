import 'package:UrNovel/Util/Common/network_image_util.dart';
import 'package:flutter/material.dart';

import '../../../../../BaseWidget/base_less_widget.dart';
import '../../../../../Util/Extensions/colorUtil.dart';
import '../../../../../Util/LocalNotificationManager/local_notification_manager.dart';
import '../../../../../Util/enum.dart';
import '../../../Model/ForYou/home_read_model.dart';
import '../read_more.dart';

class ThreeLinesCard extends BaseLessWidget {
  final HomeReadBookListModel? model;
  final void Function()? onMoreTap;

  ThreeLinesCard({super.key, this.model, required this.onMoreTap});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
        child: DecoratedBox(
      decoration: BoxDecoration(
        color: HexColor('#FFFFFF'),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          //todo:title
          Padding(
              padding: const EdgeInsets.only(top: 24, left: 16, right: 16),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      model?.title ?? '',
                      style: TextStyle(
                        fontSize: 20,
                        color: HexColor('#000000'),
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  SizedBox(width: 10),
                  ReadMore(onMoreTap: onMoreTap)
                ],
              )),

          //todo:cards
          ListCards(premiumReadersFavesList: model?.bookList),
        ],
      ),
    ));
  }
}

//todo:cards
class ListCards extends StatefulWidget {
  final List<HomeReadBookItem>? premiumReadersFavesList;

  const ListCards({super.key, this.premiumReadersFavesList});

  @override
  State<ListCards> createState() => ListCardsState();
}

class ListCardsState extends State<ListCards> {
  late List<HomeReadBookItem>? dataList;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    dataList = widget.premiumReadersFavesList;
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context);
    double cardImgWidth = media.size.width * 0.177;
    double cardHeight = cardImgWidth / 0.752;
    double totalCardHeight;
    var count = dataList?.length ?? 0;
    if (count <= 5) {
      totalCardHeight = cardHeight * count + 10 * (count - 1);
    } else {
      totalCardHeight = cardHeight * 5 + 10 * (5 - 1);
    }

    return Padding(
      padding: const EdgeInsets.only(top: 14, left: 18, right: 18, bottom: 23),
      child: SizedBox(
        height: totalCardHeight, // 提供一个明确的高度
        child: ListView.builder(
            scrollDirection: Axis.vertical,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: count,
            itemBuilder: (context, index) {
              if (index < count) {
                var item = dataList?[index];
                return GestureDetector(
                  onTap: () async {
                    //todo:去往阅读页
                    await LocalNotificationManager.toNovelReadPage(
                        {'bookId': item?.id, 'jumpType': BookDetailJumpType.other});
                  },
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 10),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        NetworkImageUtil(
                            imageUrl: item?.cover, width: cardImgWidth, height: cardHeight),
                        const SizedBox(height: 10),
                        Expanded(
                          child: Padding(
                              padding: const EdgeInsets.only(top: 4, left: 10),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    item?.title ?? "",
                                    style: TextStyle(
                                        fontSize: 15,
                                        color: HexColor('#000000'),
                                        fontWeight: FontWeight.bold),
                                    maxLines: 3,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  const SizedBox(height: 12),
                                  Text(
                                    item?.authorName ?? "",
                                    style: TextStyle(
                                      fontSize: 13,
                                      color: HexColor('#888C94'),
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              )),
                        )
                      ],
                    ),
                  ),
                );
              } else {
                return SizedBox.shrink();
              }
            }),
      ),
    );
  }
}
