import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../Util/Extensions/colorUtil.dart';
import '../../../../Util/SheetAndAlter/alter.dart';
import '../../../Search/Model/search_book_default_model.dart';
import '../../../Signin/View/signin_popup_widget.dart';

class ReadSearchBar extends StatelessWidget {
  final SearchBookDefaultItem? defaultItem;
  final VoidCallback? onSearchTap;
  final VoidCallback? onLanguageTap;
  final bool isShowLanguage;
  final bool isShowSignIn;

  const ReadSearchBar(
      {super.key,
      required this.defaultItem,
      required this.onSearchTap,
      this.onLanguageTap,
      this.isShowLanguage = false,
      this.isShowSignIn = false,
      });

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context);
    var padding = media.padding;

    return Container(
        margin: EdgeInsets.fromLTRB(18, padding.top + 16, 18, 0),
        child: Row(children: [
          Expanded(
            child: GestureDetector(
              onTap: onSearchTap,
              child: Container(
                height: 40,
                decoration: BoxDecoration(
                  color: HexColor('#ECEDF1'),
                  borderRadius: BorderRadius.circular(18),
                ),
                child: Row(
                  children: [
                    const SizedBox(width: 13),
                    Image.asset(
                      'assets/images/read/icon_search.png',
                      width: 13.5,
                      height: 13,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                        child: Text(defaultItem?.title ?? 'search_id'.tr,
                            style: TextStyle(
                                fontSize: 14, color: HexColor('#787C87')))),
                  ],
                ),
              ),
            ),
          ),
          if (isShowSignIn)
            Padding(
              padding: const EdgeInsets.only(left: 7, right: 0),
              child: IconButton(
                style: ButtonStyle(
                  padding: WidgetStateProperty.all<EdgeInsets>(
                      EdgeInsets.zero), // 设置为零内边距
                ),
                onPressed: (){
                  showAlter(SignInPopUpWidget(),backgroundColor: const Color.fromARGB(0, 255, 255, 255), barrierDismissible: true);
                },
                icon: Image.asset('assets/images/read/icon_signin.png',
                    width: 23, height: 21),
              ),
            ),
          if (isShowLanguage)
            Padding(
              padding: const EdgeInsets.only(left: 0, right: 0),
              child: IconButton(
                style: ButtonStyle(
                  padding: WidgetStateProperty.all<EdgeInsets>(
                      EdgeInsets.zero), // 设置为零内边距
                ),
                onPressed: onLanguageTap,
                icon: Image.asset('assets/images/read/icon_language.png',
                    width: 20, height: 20),
              ),
            ),
        ]));
  }
}
