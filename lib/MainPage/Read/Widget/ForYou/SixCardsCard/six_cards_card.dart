import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Util/Common/network_image_util.dart';
import 'package:UrNovel/Util/deviceScreenUtil.dart';
import 'package:flutter/material.dart';

import '../../../../../Util/Extensions/colorUtil.dart';
import '../../../../../Util/LocalNotificationManager/local_notification_manager.dart';
import '../../../../../Util/enum.dart';
import '../../../Model/ForYou/home_read_model.dart';
import '../read_more.dart';

class SixCardsCard extends BaseFulWidget {
  final void Function()? onMoreTap;
  final HomeReadBookListModel? model;

  SixCardsCard({super.key, required this.model, required this.onMoreTap});

  @override
  State<SixCardsCard> createState() => _SixCardsCardState();
}

class _SixCardsCardState extends State<SixCardsCard> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var count = widget.model?.bookList?.length ?? 0;
    double cardWidth = 0;
    double cardImgHeight = 0;
    double cardHeight = 0;
    double totalCardHeight = 0;

    if (cardWidth <= 0) {
      cardWidth = (DeviceScreenUtil.instance.width - 32) / 3.0;
    }
    if (cardImgHeight <= 0) {
      cardImgHeight = cardWidth / 0.75;
    }
    if (cardHeight <= 0) {
      cardHeight = cardImgHeight + 10 + 42;
    }
    if (totalCardHeight <= 0) {
      if (count > 3) {
        totalCardHeight = cardHeight * 2;
      } else {
        totalCardHeight = cardHeight;
      }
      totalCardHeight += 15;
    }

    return SizedBox(
        child: DecoratedBox(
      decoration: BoxDecoration(
        color: HexColor('#FFFFFF'),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          //todo:title
          Padding(
            padding: const EdgeInsets.only(top: 24, left: 16, right: 16, bottom: 10),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    widget.model?.title ?? '',
                    style: TextStyle(
                      fontSize: 20,
                      color: HexColor('#000000'),
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(width: 10),
                ReadMore(onMoreTap: widget.onMoreTap)
              ],
            ),
          ),
          //todo:cards
          Padding(
            padding: const EdgeInsets.only(left: 9, right: 9),
            child: SizedBox(
              height: totalCardHeight, // 提供一个明确的高度
              child: GridView.builder(
                physics: const NeverScrollableScrollPhysics(), // 设置禁止滚动
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  childAspectRatio: cardWidth / cardHeight,
                  mainAxisSpacing: 15,
                  crossAxisSpacing: 7,
                ),
                itemCount: count,
                itemBuilder: (context, index) {
                  if (index < count) {
                    var item = widget.model?.bookList?[index];
                    return GestureDetector(
                      onTap: () async {
                        //todo:去往阅读页
                        await LocalNotificationManager.toNovelReadPage(
                            {'bookId': item?.id, 'jumpType': BookDetailJumpType.other});
                      },
                      child: SizedBox(
                        width: cardWidth,
                        height: cardHeight,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            NetworkImageUtil(
                                imageUrl: item?.cover, width: cardWidth, height: cardImgHeight),
                            const SizedBox(height: 10),
                            Expanded(
                                child: Text(
                              item?.title ?? "",
                              style: TextStyle(
                                fontSize: 13,
                                color: HexColor('#000000'),
                                fontWeight: FontWeight.bold,
                              ),
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            )),
                          ],
                        ),
                      ),
                    );
                  }

                  return null;
                },
              ),
            ),
          ),

          const SizedBox(height: 22),
        ],
      ),
    ));
  }
}
