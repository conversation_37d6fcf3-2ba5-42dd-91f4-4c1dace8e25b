import 'dart:math' as math;

import 'package:UrNovel/BaseWidget/base_less_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

import '../../../../Util/Common/CommonManager/common_manager.dart';
import '../../../../Util/Extensions/colorUtil.dart';

class ReadMore extends BaseLessWidget {
  final Function()? onMoreTap;

  ReadMore({super.key, required this.onMoreTap});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        GestureDetector(
          onTap: onMoreTap,
          behavior: HitTestBehavior.opaque,
          child: Row(
            children: [
              Text('more'.tr,
                  style: TextStyle(
                      color: HexColor('#888C94'), fontSize: 12, fontWeight: FontWeight.bold)),
              const SizedBox(width: 9),
              Transform.rotate(
                angle: CommonManager.instance.isReverse() ? math.pi : 0,
                child: Image.asset(
                  'assets/images/common/icon_arrow_gray.png',
                  width: 5,
                  height: 9,
                ),
              )
            ],
          ),
        )
      ],
    );
  }
}
