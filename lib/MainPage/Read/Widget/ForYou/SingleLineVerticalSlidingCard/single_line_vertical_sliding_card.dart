import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Util/Common/network_image_util.dart';
import 'package:flutter/material.dart';

import '../../../../../Util/Extensions/colorUtil.dart';
import '../../../../../Util/LocalNotificationManager/local_notification_manager.dart';
import '../../../../../Util/enum.dart';
import '../../../Model/ForYou/home_read_model.dart';
import '../read_more.dart';

class SingleLineVerticalSlidingCard extends BaseFulWidget {
  final HomeReadBookListModel? model;
  final Function() onMoreTap;

  SingleLineVerticalSlidingCard({super.key, required this.model, required this.onMoreTap});

  @override
  State<SingleLineVerticalSlidingCard> createState() => _SingleLineVerticalSlidingCardState();
}

class _SingleLineVerticalSlidingCardState extends State<SingleLineVerticalSlidingCard> {
  final _controller = ScrollController();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context);
    var cardWidth = media.size.width * 0.208;
    var cardImgHeight = cardWidth / 0.75;
    var cardHeight = cardImgHeight + 10 + 42;
    var itemCount = widget.model?.bookList?.length ?? 0;

    return SizedBox(
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: HexColor('#FFFFFF'),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            //todo:title
            Padding(
                padding: const EdgeInsets.only(top: 24, left: 16, right: 16, bottom: 10),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        widget.model?.title ?? '',
                        style: TextStyle(
                          fontSize: 20,
                          color: HexColor('#000000'),
                          fontWeight: FontWeight.bold,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    SizedBox(width: 10),
                    ReadMore(onMoreTap: widget.onMoreTap)
                  ],
                )),

            //todo:cards
            SizedBox(
              height: cardHeight, // 提供一个明确的高度
              child: ListView.builder(
                controller: _controller,
                padding: const EdgeInsets.only(left: 9, right: 9),
                scrollDirection: Axis.horizontal,
                physics: const BouncingScrollPhysics(),
                itemCount: itemCount,
                itemBuilder: (context, index) {
                  if (index < itemCount) {
                    var item = widget.model?.bookList?[index];
                    return GestureDetector(
                        onTap: () async {
                          _controller
                              .animateTo(index * (cardWidth + 9),
                                  duration: const Duration(milliseconds: 300), curve: Curves.ease)
                              .whenComplete(() async {
                            //todo:去往阅读页
                            await LocalNotificationManager.toNovelReadPage(
                                {'bookId': item?.id, 'jumpType': BookDetailJumpType.other});
                          });
                        },
                        child: Padding(
                          padding: EdgeInsets.only(right: (index == itemCount - 1) ? 0 : 9),
                          child: SizedBox(
                            width: cardWidth,
                            height: cardHeight,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                NetworkImageUtil(
                                    imageUrl: item?.cover, width: cardWidth, height: cardImgHeight),
                                const SizedBox(height: 10),
                                Expanded(
                                    child: Text(
                                  item?.title ?? "",
                                  style: TextStyle(
                                    fontSize: 13,
                                    color: HexColor('#000000'),
                                    fontWeight: FontWeight.bold,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                )),
                              ],
                            ),
                          ),
                        ));
                  }
                  return SizedBox.shrink();
                },
              ),
            ),

            const SizedBox(height: 22),
          ],
        ),
      ),
    );
  }
}
