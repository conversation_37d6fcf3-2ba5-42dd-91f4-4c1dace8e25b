import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Util/Common/Animation/lottie_animation_view.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:flutter/material.dart';

import '../../../../Util/Common/no_load_view.dart';
import '../../../../Util/RefreshLoad/refresh_load.dart';
import '../../../../Util/api_config.dart';
import '../../../../Util/logUtil.dart';
import '../../../../Util/stringUtil.dart';
import '../../Model/ForYou/home_read_model.dart';
import '../../ViewModel/ViewModel.dart';
import '../../Widget/Explore/TopRankings/top_rankings_tabBar.dart';
import '../../Widget/Explore/WaterfallFlow/waterfall_flow_cards.dart';

class ExplorePage extends BaseFulWidget {
  final Function(bool) updateTabBarIndex;

  ExplorePage({super.key, required this.updateTabBarIndex});

  @override
  State<ExplorePage> createState() => ExplorePageState();
}

class ExplorePageState extends State<ExplorePage> {
  final ScrollController _scrollController = ScrollController();
  late List<HomeReadBookListModel>? _homeReadModelList;
  late bool _isLoading;
  late int _pageIndex;
  late int _pageSize;
  late HomeReadBookListModel? _trendingBookModel;
  late HomeReadBookListModel? _newReleaseBookModel;
  late HomeReadBookListModel? _hotSearchesBookModel;
  late HomeReadBookListModel? _favoritesBookModel;
  late List<HomeReadBookItem?>? _discoverBookList;
  late bool _isHasRankData;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _isLoading = true;
    _pageIndex = 1; //page默认从1开始
    _pageSize = 10;
    _homeReadModelList = null;
    _trendingBookModel = null;
    _newReleaseBookModel = null;
    _hotSearchesBookModel = null;
    _favoritesBookModel = null;
    _discoverBookList = null;
    _isHasRankData = true;

    fetchData();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> onRefresh() async {
    _scrollController.jumpTo(0);
    _pageIndex = 1;
    await fetchData();
  }

  Future<void> onLoading() async {
    _pageIndex++;
    await fetchData();
  }

  //todo: 加载Explore网络数据
  Future<void> fetchData() async {
    //获取列表数据
    _homeReadModelList = await HomeReadViewModel.loadBookData(
        urlStr: apiHomeReadData,
        homeDataColumnCode: 'Discover',
        page: _pageIndex,
        pageSize: _pageSize);

    getCurrentBookList();

    // 处理刷新状态
    widget.dealRefreshState(_pageIndex, _pageSize, _discoverBookList);
  }

//todo: 获取当前tab的bookList
  void getCurrentBookList() {
    if (_pageIndex == 1) {
      _trendingBookModel = null;
      _newReleaseBookModel = null;
      _hotSearchesBookModel = null;
      _favoritesBookModel = null;
      _discoverBookList = null;
    }

    try {
      _homeReadModelList?.forEach((element) async {
        if (element.homeDataColumnCode == rankingTRENDING) {
          _trendingBookModel = element;

          /// 处理曝光
          await widget.dealExposureList(_trendingBookModel?.bookList, 3);
        } else if (element.homeDataColumnCode == rankingNEWRELEASES) {
          _newReleaseBookModel = element;
        } else if (element.homeDataColumnCode == rankingHOTSEARCHES) {
          _hotSearchesBookModel = element;
        } else if (element.homeDataColumnCode == rankingFAVORITES) {
          _favoritesBookModel = element;
        } else if (element.homeDataColumnCode == rankingDISCOVER) {
          if (_pageIndex == 1) {
            _discoverBookList = element.bookList;

            /// 处理曝光
            await widget.dealExposureList(_discoverBookList, 2);
          } else {
            if (isAvailable(element.bookList)) {
              _discoverBookList?.addAll(element.bookList!);
            }
          }
        }
      });
    } catch (e) {
      logP('error: $e');
    }

    _isHasRankData = isAvailable(_trendingBookModel?.bookList) ||
        isAvailable(_newReleaseBookModel?.bookList) ||
        isAvailable(_hotSearchesBookModel?.bookList) ||
        isAvailable(_favoritesBookModel?.bookList);

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        MediaQuery.removePadding(
          context: context,
          removeTop: true,
          removeBottom: true,
          removeLeft: true,
          removeRight: true,
          child: NotificationListener(
              onNotification: (ScrollNotification notification) {
                if (notification is ScrollStartNotification) {
                  widget.exposureList.removeRange(0, widget.exposureList.length);
                } else if (notification is ScrollEndNotification) {
                  widget.exposureReport();
                }
                return true;
              },
              child: RefreshLoadUtil(
                  onRefresh: onRefresh,
                  onLoading: onLoading,
                  controller: widget.refreshController,
                  enablePullUp: true,
                  child: ListView.builder(
                    controller: _scrollController,
                    itemCount: 2,
                    itemBuilder: (context, index) {
                      if (index == 0) {
                        if (isAvailable(_trendingBookModel?.bookList) ||
                            isAvailable(_newReleaseBookModel?.bookList) ||
                            isAvailable(_hotSearchesBookModel?.bookList) ||
                            isAvailable(_favoritesBookModel?.bookList)) {
                          return TopRankingsTabBar(
                              trendingBookModel: _trendingBookModel,
                              newReleaseBookModel: _newReleaseBookModel,
                              hotSearchesBookModel: _hotSearchesBookModel,
                              favoritesBookModel: _favoritesBookModel,
                              updateTabBarIndex: widget.updateTabBarIndex);
                        }

                        return Container();
                      }
                      return Padding(
                          padding: const EdgeInsets.only(top: 10, left: 9, bottom: 10, right: 9),
                          child: WaterfallFlowCards(
                              bookList: _discoverBookList,
                              onExposureCallback: (bookId) {
                                widget.exposureList.add(bookId);
                              }));
                    },
                  ))),
        ),
        if (_isLoading) const LottieAnimationView(),
        if (!_isLoading && !_isHasRankData && !isAvailable(_discoverBookList)) NoLoadView()
      ],
    );
  }
}
