import 'package:UrNovel/Util/tools.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../BaseWidget/base_ful_widget.dart';
import '../../../Launch&Login/Model/language_model.dart';
import '../../../Util/DataReportManager/event_name_config.dart';
import '../../../Util/DataReportManager/event_report_manager.dart';
import '../../../Util/Extensions/colorUtil.dart';
import '../../Search/Model/search_book_default_model.dart';
import '../../Search/ViewModel/ViewModel.dart';
import '../Model/ForYou/home_read_model.dart';
import '../ViewModel/ViewModel.dart';
import '../Widget/ForYou/read_search_bar.dart';
import 'Explore/explore_page.dart';
import 'ForYou/for_you_page.dart';

class ReadPage extends BaseFulWidget {
  ReadPage({super.key});

  @override
  State<ReadPage> createState() => ReadPageState();
}

class ReadPageState extends State<ReadPage> with TickerProviderStateMixin {
  SearchBookDefaultItem? _searchBookDefaultItem;
  final forYouKey = GlobalKey<ForYouPageState>();
  final exploreKey = GlobalKey<ExplorePageState>();

  late TabController _tabController;
  late int _currentIndex;
  late bool _isShowForYou;
  late List<Tab> _tabs;
  late List<Widget> _pages;

  // final femaleKey = GlobalKey<FemalePageState>();
  // final maleKey = GlobalKey<MalePageState>();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    _isShowForYou = false;
    setTabsAndPages();
    setTabControllerIndex(0);

    fetchData();

    // 添加语言变化监听
    eventBusOn<LanguageItem>((item) {
      refreshData();
    });
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  setTabsAndPages() {
    if (_isShowForYou) {
      _tabs = [Tab(child: Text('for_you'.tr)), Tab(child: Text('explore'.tr))];
      _pages = [
        ForYouPage(key: forYouKey),
        ExplorePage(key: exploreKey, updateTabBarIndex: updateTabBarIndex)
      ];
    } else {
      _tabs = [Tab(child: Text('explore'.tr))];
      _pages = [ExplorePage(key: exploreKey, updateTabBarIndex: updateTabBarIndex)];
    }
    _tabController = TabController(length: _tabs.length, vsync: this);
    _tabController.addListener(_handleTabChange);
    setTabControllerIndex(_isShowForYou ? 1 : 0);
  }

  Future<void> setTabControllerIndex(int index) async {
    _currentIndex = index;
    _tabController.animateTo(index);

    await EventReportManager.eventReportOfFirebase('Explore');
  }

  Future<void> fetchData() async {
    await getSearchDefaultBook();
    List<HomeReadBookListModel>? homeReadModelList = await loadBookData();
    _isShowForYou = isAvailable(homeReadModelList);
    setTabsAndPages();
    if (mounted) {
      setState(() {});
    }
  }

  //todo: 获取搜索默认书籍
  Future<void> getSearchDefaultBook() async {
    _searchBookDefaultItem = await SearchViewModel.getSearchDefaultBook();
  }

  //todo: 加载HomeRead网络数据
  Future<List<HomeReadBookListModel>?> loadBookData() async {
    return await HomeReadViewModel.loadBookData(page: 1, pageSize: 10);
  }

  //todo: 切换tab
  void _handleTabChange() {
    onTabTapped(_tabController.index);
  }

  //todo: tab点击事件
  Future<void> onTabTapped(int index) async {
    if (index == _currentIndex) {
      return;
    }
    _currentIndex = index;

    switch (index) {
      case 0:
        if (_isShowForYou) {
          await EventReportManager.eventReportOfFirebase('ForYou');
        } else {
          await EventReportManager.eventReportOfFirebase('Explore');
        }
        break;
      case 1:
        await EventReportManager.eventReportOfFirebase('Explore');
        break;
      // case 1:
      //   await EventReportManager.eventReportOfFirebase('Female');
      //   break;
      // case 2:
      //   await EventReportManager.eventReportOfFirebase('Male');
      //   break;
    }
  }

  void updateTabBarIndex(bool isScrollEndLeft) {
    if (isScrollEndLeft) {
      if (_tabController.index != 0) {
        _tabController.index -= 1;
      }
    } else if (!isScrollEndLeft) {
      if (_tabController.index != 2) {
        _tabController.index += 1;
      }
    }
  }

  //todo: 刷新数据
  Future<void> refreshData() async {
    await fetchData();
    await forYouKey.currentState?.onRefresh();
    await exploreKey.currentState?.onRefresh();
    // await femaleKey.currentState?.onRefresh();
    // await maleKey.currentState?.onRefresh();
  }

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ReadSearchBar(
            defaultItem: _searchBookDefaultItem,
            onSearchTap: () async {
              //搜索点击事件
              await Get.toNamed('/searchPage', arguments: {"defaultItem": _searchBookDefaultItem});
              EventReportManager.eventReportOfFirebase(clickSearch);
            },
            onLanguageTap: () async {
              /// 语言点击事件
              await Get.toNamed('/storyLanguagePage');

              EventReportManager.eventReportOfFirebase(homeLanguage);
            },
            isShowLanguage: true,
            isShowSignIn: isSignInSwitchOn,
          ),
          SizedBox(
              height: 50,
              child: TabBar(
                controller: _tabController,
                padding: EdgeInsets.only(left: 13, right: 13),
                labelPadding: EdgeInsets.symmetric(horizontal: 15),
                //隐藏下划线
                dividerHeight: 0,
                indicatorColor: HexColor('#008CFB'),
                indicatorSize: TabBarIndicatorSize.label,
                indicator: UnderlineTabIndicator(
                  borderRadius: BorderRadius.circular(2),
                  borderSide: BorderSide(width: 4.0, color: HexColor('#008CFB')),
                  insets: const EdgeInsets.only(left: 17, right: 17, bottom: 5),
                ),
                labelStyle: TextStyle(
                    fontSize: 19, color: HexColor('#0087FB'), fontWeight: FontWeight.bold),
                unselectedLabelStyle: TextStyle(
                    fontSize: 15, color: HexColor('#555A65'), fontWeight: FontWeight.bold),
                isScrollable: true,
                tabAlignment: TabAlignment.center,
                tabs: _tabs,
              )),
          Expanded(
            child: Padding(
                padding: const EdgeInsets.only(top: 10),
                child: TabBarView(
                  controller: _tabController,
                  children: _pages,
                )),
          ),
        ],
      ),
    );
  }
}
