import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/MainPage/Read/ViewModel/ViewModel.dart';
import 'package:UrNovel/Util/Common/Animation/lottie_animation_view.dart';
import 'package:UrNovel/Util/Common/no_load_view.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../Util/RefreshLoad/refresh_load.dart';
import '../../../../Util/enum.dart';
import '../../Model/ForYou/home_read_model.dart';
import '../../Widget/ForYou/SingleLineHorizontalSliding/single_line_horizontal_sliding.dart';
import '../../Widget/ForYou/SingleLineVerticalSliding/single_line_vertical_sliding.dart';
import '../../Widget/ForYou/SingleLineVerticalSlidingCard/single_line_vertical_sliding_card.dart';
import '../../Widget/ForYou/SixCardsCard/six_cards_card.dart';
import '../../Widget/ForYou/ThreeLinesCard/three_lines_card.dart';

class ForYouPage extends BaseFulWidget {
  ForYouPage({super.key});

  @override
  State<ForYouPage> createState() => ForYouPageState();
}

class ForYouPageState extends State<ForYouPage> with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();

  List<HomeReadBookListModel>? _homeReadModelList;

  late bool _isLoading;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    _isLoading = true;

    fetchData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> fetchData({bool isRefresh = false}) async {
    if (!isRefresh) {
      _homeReadModelList = await getLocalBookData();

      if (isAvailable(_homeReadModelList)) {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }

    var homeReadModelList = await loadBookData();
    if (mounted) {
      setState(() {
        _isLoading = false;
        _homeReadModelList = homeReadModelList;
      });
    }

    widget.refreshController.refreshCompleted();
    widget.refreshController.loadComplete();
  }

  Future<void> onRefresh() async {
    _scrollController.jumpTo(0);
    fetchData(isRefresh: true);
  }

  //todo: 加载HomeRead本地数据
  Future<List<HomeReadBookListModel>?> getLocalBookData() async {
    return await HomeReadViewModel.getLocalBookData();
  }

  //todo: 加载HomeRead网络数据
  Future<List<HomeReadBookListModel>?> loadBookData() async {
    List<HomeReadBookListModel>? resultList =
        await HomeReadViewModel.loadBookData(page: 1, pageSize: 10);
    return resultList;
  }

  ///二级页面
  void _onToSecondaryListTap(String? title, String? code) async {
    if (isAvailable(code)) {
      await Get.toNamed('/secondaryListPage', arguments: {
        "title": title ?? '',
        'code': code,
        'pageType': SecondaryPageType.formHomeReadSection
      });
    }
  }

  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    int? length = isAvailable(_homeReadModelList) ? _homeReadModelList?.length : 0;
    int listCount = length ?? 0;
    if (_isLoading) {
      return const LottieAnimationView();
    } else {
      return SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: Stack(
          children: [
            Column(
              children: [
                Expanded(
                  //ToDo：ListVie
                  //没有和appbar一起使用，会有padding，需要去掉，这里用了MediaQuery.removePadding
                  child: MediaQuery.removePadding(
                    context: context,
                    removeTop: true,
                    removeBottom: true,
                    removeLeft: true,
                    removeRight: true,
                    child: RefreshLoadUtil(
                        controller: widget.refreshController,
                        enablePullUp: false,
                        onRefresh: fetchData,
                        onLoading: () {},
                        child: ListView.builder(
                          controller: _scrollController,
                          itemCount: listCount,
                          itemBuilder: (context, index) {
                            if (index < listCount) {
                              var item = _homeReadModelList?[index];
                              var sectionType = getHomeSectionType(item?.showStyle);

                              widget.dealExposureList(item?.bookList, 6);

                              if (sectionType == HomeSectionType.sixCardsCard) {
                                return SixCardsCard(
                                    model: item,
                                    onMoreTap: () {
                                      _onToSecondaryListTap(item?.title, item?.homeDataColumnCode);
                                    });
                              } else if (sectionType == HomeSectionType.singleLineVerticalSliding) {
                                return SingleLineVerticalSliding(
                                    model: item,
                                    onMoreTap: () {
                                      _onToSecondaryListTap(item?.title, item?.homeDataColumnCode);
                                    });
                              } else if (sectionType == HomeSectionType.threeLinesCard) {
                                return ThreeLinesCard(
                                  model: item,
                                  onMoreTap: () {
                                    _onToSecondaryListTap(item?.title, item?.homeDataColumnCode);
                                  },
                                );
                              } else if (sectionType ==
                                  HomeSectionType.singleLineHorizontalSliding) {
                                return SingleLineHorizontalSliding(
                                    model: item,
                                    onMoreTap: () {
                                      _onToSecondaryListTap(item?.title, item?.homeDataColumnCode);
                                    });
                              } else if (sectionType ==
                                  HomeSectionType.singleLineVerticalSlidingCard) {
                                return SingleLineVerticalSlidingCard(
                                    model: item,
                                    onMoreTap: () {
                                      _onToSecondaryListTap(item?.title, item?.homeDataColumnCode);
                                    });
                              }
                            }

                            return SizedBox.shrink();
                          },
                        )),
                  ),
                ),
              ],
            ),
            if (listCount <= 0) NoLoadView(),
          ],
        ),
      );
    }
  }
}
