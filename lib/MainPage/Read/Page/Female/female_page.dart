import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Util/Common/no_load_view.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:flutter/material.dart';

import '../../../../Util/Common/Animation/lottie_animation_view.dart';
import '../../../../Util/RefreshLoad/refresh_load.dart';
import '../../../../Util/stringUtil.dart';
import '../../Model/ForYou/home_read_model.dart';
import '../../ViewModel/ViewModel.dart';
import '../../Widget/Explore/WaterfallFlow/waterfall_flow_cards.dart';

class FemalePage extends BaseFulWidget {
  FemalePage({super.key});

  @override
  State<FemalePage> createState() => FemalePageState();
}

class FemalePageState extends State<FemalePage>
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  late List<HomeReadBookListModel>? _homeReadModelList;
  List<HomeReadBookItem>? _modelList;
  late bool _isLoading;
  late int _pageIndex;
  late int _pageSize;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    _homeReadModelList = null;
    _modelList = null;
    _isLoading = true;
    _pageIndex = 1; //page默认从1开始
    _pageSize = 10;

    fetchData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> onRefresh() async {
    _scrollController.jumpTo(0);
    _pageIndex = 1;
    fetchData();
  }

  Future<void> onLoading() async {
    _pageIndex++;
    fetchData();
  }

  //todo: 加载Explore网络数据
  Future<void> fetchData() async {
    _homeReadModelList = await HomeReadViewModel.loadBookData(
        homeDataColumnCode: rankingFEMALE,
        page: _pageIndex,
        pageSize: _pageSize);
    getCurrentBookList();
    // 处理刷新状态
    widget.dealRefreshState(_pageIndex, _pageSize, _modelList);
    setState(() {
      _isLoading = false;
    });

    /// 处理曝光
    await widget.dealExposureList(_modelList, 2);
  }

  //todo: 获取当前tab的booklist
  void getCurrentBookList() {
    _homeReadModelList?.forEach((element) {
      if (element.homeDataColumnCode == rankingFEMALE) {
        if (_pageIndex == 1) {
          _modelList = element.bookList;
        } else {
          if (isAvailable(element.bookList)) {
            _modelList?.addAll(element.bookList!);
          }
        }

        return;
      }
    });
    setState(() {});
  }

  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      body: Stack(
        children: [
          MediaQuery.removePadding(
            context: context,
            removeTop: true,
            removeBottom: true,
            removeLeft: true,
            removeRight: true,
            child: NotificationListener(
                onNotification: (ScrollNotification notification) {
                  if (notification is ScrollStartNotification) {
                    widget.exposureList
                        .removeRange(0, widget.exposureList.length);
                  } else if (notification is ScrollEndNotification) {
                    widget.exposureReport();
                  }
                  return true;
                },
                child: RefreshLoadUtil(
                    onRefresh: onRefresh,
                    onLoading: onLoading,
                    controller: widget.refreshController,
                    enablePullUp: true,
                    child: ListView.builder(
                      controller: _scrollController,
                      itemCount: 1,
                      itemBuilder: (context, index) {
                        return Padding(
                            padding: const EdgeInsets.only(
                                left: 9, bottom: 10, right: 9),
                            child: WaterfallFlowCards(
                                bookList: _modelList,
                                onExposureCallback: (bookId) {
                                  widget.exposureList.add(bookId);
                                }));
                      },
                    ))),
          ),
          if (_isLoading) const LottieAnimationView(),
          if (!_isLoading && !isAvailable(_modelList)) NoLoadView()
        ],
      ),
    );
  }
}
