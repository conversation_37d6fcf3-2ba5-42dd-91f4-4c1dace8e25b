// 用于标记模型类的注解
import 'package:json_annotation/json_annotation.dart';

part 'home_read_model.g.dart'; // 确保这部分正确

@JsonSerializable()
class HomeReadModel {
  int? code;
  List<HomeReadBookListModel>? result;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  HomeReadModel({this.result, this.msg, this.sysAt, this.cost, this.traceId});

  factory HomeReadModel.fromJson(Map<String, dynamic> json) =>
      _$HomeReadModelFromJson(json);

  Map<String, dynamic> toJson() => _$HomeReadModelToJson(this);
}

@JsonSerializable()
class HomeReadBookListModel {
  String? homeDataColumnCode;
  String? title;
  int? sort;
  String? showStyle;
  List<HomeReadBookItem>? bookList;

  HomeReadBookListModel(
      {this.homeDataColumnCode,
      this.title,
      this.sort,
      this.showStyle,
      this.bookList});

  factory HomeReadBookListModel.fromJson(Map<String, dynamic> json) =>
      _$HomeReadBookListModelFromJson(json);

  Map<String, dynamic> toJson() => _$HomeReadBookListModelToJson(this);
}

@JsonSerializable()
class HomeReadBookItem {
  int? id; // 书籍id
  String? authorName; // 作者名
  String? title; // 书名
  String? cover; // 封面图
  int? viewCount; // 阅读数
  String? lang; // 语言
  String? description; // 简介

  HomeReadBookItem(
      {this.id,
      this.authorName,
      this.title,
      this.cover,
      this.viewCount,
      this.lang,
      this.description});

  factory HomeReadBookItem.fromJson(Map<String, dynamic> json) =>
      _$HomeReadBookItemFromJson(json);

  Map<String, dynamic> toJson() => _$HomeReadBookItemToJson(this);
}
