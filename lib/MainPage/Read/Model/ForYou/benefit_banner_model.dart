import 'package:json_annotation/json_annotation.dart';

part 'benefit_banner_model.g.dart';

@JsonSerializable()
class BenefitBannerModel {
  int? code;
  BenefitBannerResultModel? result;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  BenefitBannerModel({
    this.code,
    this.result,
    this.msg,
    this.sysAt,
    this.cost,
    this.traceId,
  });

  factory BenefitBannerModel.fromJson(Map<String, dynamic> json) =>
      _$BenefitBannerModelFromJson(json);

  Map<String, dynamic> toJson() => _$BenefitBannerModelToJson(this);
}

@JsonSerializable()
class BenefitBannerResultModel {
  BenefitVoModel? benefitVo; //权益对象
  List<BannerVoListItem>? bannerVoList; //banner列表

  BenefitBannerResultModel({this.benefitVo, this.bannerVoList});

  factory BenefitBannerResultModel.fromJson(Map<String, dynamic> json) =>
      _$BenefitBannerResultModelFromJson(json);

  Map<String, dynamic> toJson() => _$BenefitBannerResultModelToJson(this);
}

@JsonSerializable()
class BenefitVoModel {
  int? rewardPackageId; //权益包id
  String? benefitName; //权益名称
  String? benefitDays; //权益天数
  String? benefitContent; //权益内容

  BenefitVoModel({this.rewardPackageId, this.benefitName, this.benefitContent});

  factory BenefitVoModel.fromJson(Map<String, dynamic> json) =>
      _$BenefitVoModelFromJson(json);

  Map<String, dynamic> toJson() => _$BenefitVoModelToJson(this);
}

@JsonSerializable()
class BannerVoListItem {
  int? bannerId;
  int? bookId;
  String? cover;

  BannerVoListItem({this.bannerId, this.bookId, this.cover});

  factory BannerVoListItem.fromJson(Map<String, dynamic> json) =>
      _$BannerVoListItemFromJson(json);

  Map<String, dynamic> toJson() => _$BannerVoListItemToJson(this);
}
