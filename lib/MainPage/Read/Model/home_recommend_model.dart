import 'package:json_annotation/json_annotation.dart';
import '../../../Launch&Login/Model/promotion_content_model.dart';
part 'home_recommend_model.g.dart';

@JsonSerializable()
class HomeRecommendModel {
  int? code;
  List<PromotionContentItem>? result;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  HomeRecommendModel(
      {this.code, this.result, this.msg, this.sysAt, this.cost, this.traceId});

  factory HomeRecommendModel.fromJson(Map<String, dynamic> json) =>
      _$HomeRecommendModelFromJson(json);

  Map<String, dynamic> toJson() => _$HomeRecommendModelToJson(this);
}
