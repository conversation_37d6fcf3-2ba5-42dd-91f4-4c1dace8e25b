import 'dart:convert';

import 'package:UrNovel/MainPage/Read/Model/ForYou/home_read_model.dart';
import 'package:UrNovel/Util/stringUtil.dart';
import 'package:UrNovel/Util/tools.dart';

import '../../../Launch&Login/Model/promotion_content_model.dart';
import '../../../Util/NetWorkManager/net_work_manager.dart';
import '../../../Util/api_config.dart';
import '../Model/home_recommend_model.dart';

class HomeReadViewModel {
  static bool _isGotHomeBookData = false;

  //todo: HomeReadBookData本地缓存文件
  static Future<List<HomeReadBookListModel>?> getLocalBookData() async {
    //获取本地数据
    if (!_isGotHomeBookData) {
      _isGotHomeBookData = !_isGotHomeBookData;
      var resp = await readStringFormFile(homeReadBookFilePath);
      if (isAvailable(resp)) {
        Map<String, dynamic> respMap = await jsonDecode(resp!);
        HomeReadModel homeReadModel = HomeReadModel.fromJson(respMap);

        return homeReadModel.result;
      }
    }

    return null;
  }

  //todo: HomeReadBookData网络请求
  static Future<List<HomeReadBookListModel>?> loadBookData(
      {String urlStr = apiHomeReadColumnData,
      String homeDataColumnCode = "All",
      int page = 1,
      int pageSize = 10,
      bool secondaryList = false}) async {
    //获取网络数据
    var parameters = {
      "homeDataColumnCode": homeDataColumnCode,
      "page": page,
      "pageSize": pageSize,
      "secondaryList": secondaryList,
    };
    var response = await NetWorkManager.instance.post(urlStr, parameters: parameters);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      if (homeDataColumnCode == 'All') {
        saveStringToFile(homeReadBookFilePath, jsonEncode(response.data));
      }
      HomeReadModel homeReadModel = HomeReadModel.fromJson(response.data);

      return homeReadModel.result;
    }

    return null;
  }

  //todo: 获取首页弹窗推荐内容
  static Future<List<PromotionContentItem>?> getHomeRecommendContent() async {
    var response = await NetWorkManager.instance.post(apiIndexRecommendBook, parameters: {});
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      HomeRecommendModel homeRecommendModel = HomeRecommendModel.fromJson(response.data);

      return homeRecommendModel.result;
    }

    return null;
  }
}
