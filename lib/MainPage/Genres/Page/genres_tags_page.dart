import 'dart:async';

import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Util/DataReportManager/event_report_manager.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

import '../../../Launch&Login/Model/language_model.dart';
import '../../../Util/Common/Animation/lottie_animation_view.dart';
import '../../../Util/Common/no_load_view.dart';
import '../../../Util/DataReportManager/event_name_config.dart';
import '../../../Util/Extensions/colorUtil.dart';
import '../../../Util/logUtil.dart';
import '../../../Util/tools.dart';
import '../../Read/Widget/ForYou/read_search_bar.dart';
import '../../Search/Model/search_book_default_model.dart';
import '../../Search/ViewModel/ViewModel.dart';
import '../Model/genres_model.dart';
import '../Model/tag_groups_model.dart';
import '../Model/tags_model.dart';
import '../ViewModel/ViewModel.dart';
import '../Widget/Tags/tags_list.dart';

class GenresTagsPage extends BaseFulWidget {
  GenresTagsPage({super.key, super.arguments});

  @override
  State<GenresTagsPage> createState() => _GenresTagsPageState();
}

class _GenresTagsPageState extends State<GenresTagsPage>
    with AutomaticKeepAliveClientMixin, SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<TagGroupItem>? _tagGroups;
  int _selectedTagGroupId = -1;
  bool _showGenres = true;

  bool get showGenres => _showGenres;
  SearchBookDefaultItem? _searchBookDefaultItem;

  // 添加全局刷新状态标记
  late bool _isLoading;

  // 标记是否是通过点击标签组切换的
  bool _isClickingTagGroup = false;

  // 缓存已加载的标签数据，避免重复请求
  final Map<int, TagsGroupModel?> _cachedTagData = {};

  // 缓存Genres数据
  GenresListResultModel? _genresModel;

  // 左侧滚动视图控制器
  final ScrollController _leftScrollController = ScrollController();

  // 右侧滚动视图控制器（替换为ItemScrollController和ItemPositionsListener）
  final ItemScrollController _itemScrollController = ItemScrollController();
  final ItemPositionsListener _itemPositionsListener = ItemPositionsListener.create();

  // 当前选中的部分索引
  int _currentSectionIndex = 0;

  // 防止频繁更新
  bool _isUpdatingSection = false;
  Timer? _updateSectionTimer;

  // 为每个section标题创建一个GlobalKey，用于定位
  final Map<String, GlobalKey> _sectionKeys = {};

  // 右侧各部分的区域信息
  final List<SectionInfo> _sections = [];

  // 添加一个正在加载的标记集合
  final Set<int> _loadingTagData = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // 强制设置为Genres选项
    _showGenres = true;
    _currentSectionIndex = 0;
    _selectedTagGroupId = -1;

    // 强制设置TabController索引为0（Genres）
    int initialIndex = 0;
    _tabController.index = initialIndex;

    // 添加滚动位置监听
    _itemPositionsListener.itemPositions.addListener(_onRightScroll);

    eventBusOn<LanguageItem>((item) async {
      await _fetchData();
    });

    _fetchData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _itemPositionsListener.itemPositions.removeListener(_onRightScroll);
    _leftScrollController.dispose();

    // 取消所有活动计时器
    _updateSectionTimer?.cancel();

    super.dispose();
  }

  Future<void> _fetchData() async {
    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }
    // 获取搜索默认书籍
    await getSearchDefaultBook();
    await _loadGenresData();
    await _loadTagGroups();

    // 加载标签组后重新初始化各部分
    _initSections();
  }

  // 加载Genres数据
  Future<void> _loadGenresData() async {
    _genresModel = await GenresViewModel.getGenresList();
  }

  Future<void> _loadTagGroups() async {
    _tagGroups = await GenresViewModel.getTagGroupList();
    // 为每个标签组创建一个GlobalKey
    if (isAvailable(_tagGroups)) {
      for (var group in _tagGroups!) {
        if (isAvailable(group.groupName)) {
          _sectionKeys[group.groupName!] = GlobalKey();
        }
      }
    }
  }

  // 预加载标签数据
  Future<TagsGroupModel?> _getTagListData(int? groupId) async {
    if (groupId == null || groupId < 0) return null;

    // 如果已经缓存了该组的数据，则直接返回缓存
    if (_cachedTagData.containsKey(groupId)) {
      return _cachedTagData[groupId];
    }

    // 如果正在加载中，则跳过
    if (_loadingTagData.contains(groupId)) {
      return null;
    }

    // 标记为正在加载
    _loadingTagData.add(groupId);
    try {
      // 请求数据并缓存
      final data = await GenresViewModel.getTagList(groupId);
      _cachedTagData[groupId] = data;

      return data;
    } catch (e) {
      return null;
    } finally {
      // 无论成功失败，都移除加载标记
      _loadingTagData.remove(groupId);
    }
  }

  // 初始化各个部分信息
  Future<void> _initSections() async {
    _sections.clear();

    // 添加Genres部分
    // 确保为每个标签组创建一个GlobalKey
    if (!_sectionKeys.containsKey(_genresModel?.genres)) {
      _sectionKeys[_genresModel?.genres ?? 'Genres'] = GlobalKey();
    }
    _sections.add(SectionInfo(
      index: 0,
      name: _genresModel?.genres ?? '',
      type: SectionType.genres,
      id: -1,
    ));

    // 添加各个Tag组部分
    if (_tagGroups != null) {
      int index = 1;
      for (var group in _tagGroups!) {
        if (group.groupName != null && group.groupId != null) {
          final tagName = group.groupName!;

          // 确保为每个标签组创建一个GlobalKey
          if (!_sectionKeys.containsKey(tagName)) {
            _sectionKeys[tagName] = GlobalKey();
          }

          _sections.add(SectionInfo(
            index: index,
            name: tagName,
            type: SectionType.tag,
            id: group.groupId!,
          ));
          index++;
        }
      }
    }

    // 懒加载标签组数据，只加载前两个标签组，减轻初始负担
    if (_tagGroups != null && _tagGroups!.isNotEmpty) {
      for (int i = 0; i < _tagGroups!.length; i++) {
        final group = _tagGroups![i];
        if (group.groupId != null) {
          await _getTagListData(group.groupId);
        }
      }
    }

    // 确保初始显示正确的选中项（总是Genres）
    if (mounted) {
      setState(() {
        _isLoading = false;
        _showGenres = true;
        _currentSectionIndex = 0;
        _selectedTagGroupId = -1;
      });
    }

    // 减少首次加载时间，加快初始响应速度
    final preloadDelay = 500;

    // 轻量级的预加载，只尝试一次滚动到顶部
    Future.delayed(Duration(milliseconds: preloadDelay), () {
      if (mounted && _itemScrollController.isAttached) {
        try {
          _itemScrollController.jumpTo(index: 0);
        } catch (e) {
          logD("预加载布局错误: $e");
        }
      }
    });
  }

  Future<void> _refreshData() async {
    if (_isLoading) {
      return;
    }

    await _fetchData();
  }

  // 右侧滚动事件处理
  void _onRightScroll() {
    // 如果正在点击标签组切换，不处理滚动事件
    if (_isClickingTagGroup) return;

    // 获取当前可见项的位置
    final positions = _itemPositionsListener.itemPositions.value;
    if (positions.isEmpty) return;

    // 按照索引排序
    final visibleItems = positions.toList()..sort((a, b) => a.index.compareTo(b.index));

    // 获取最合适的可见项索引，基于标题是否接近顶部
    int bestVisibleIndex = _getBestVisibleIndex(visibleItems);

    // 仅当找到了新的"最佳"索引，且与当前选中不同时才更新
    if (bestVisibleIndex != _currentSectionIndex && bestVisibleIndex != -1) {
      // 使用节流技术，控制更新频率
      if (_updateSectionTimer == null || !_updateSectionTimer!.isActive) {
        // 设置短延迟，避免滚动过程中频繁更新
        _updateSectionTimer = Timer(const Duration(milliseconds: 100), () {
          if (mounted) {
            // 再次检查是否需要更新，以防延迟期间状态已改变
            if (bestVisibleIndex != _currentSectionIndex) {
              // 更新当前选中部分
              _updateCurrentSectionAfterScroll(bestVisibleIndex);
            }
          }
        });
      }
    }
  }

  // 获取最合适的可见项索引，现在只考虑标题接近顶部的项
  int _getBestVisibleIndex(List<ItemPosition> visibleItems) {
    if (visibleItems.isEmpty) {
      return _currentSectionIndex;
    }

    // 如果只有一个项目可见，则选择它
    if (visibleItems.length == 1) {
      return visibleItems.first.index;
    }

    // 新逻辑：寻找最接近顶部的项
    // itemLeadingEdge为0表示该项正好位于视口顶部
    // 负值表示项已经往上滚动超出了视口顶部的距离
    // 正值表示项还未到达视口顶部的距离
    // 首先寻找接近顶部的项（leading edge在-0.1到0.1之间的项）
    for (var position in visibleItems) {
      // 只有当项目的顶部非常接近视口顶部时才将其视为"当前项"
      if (position.itemLeadingEdge >= -0.01 && position.itemLeadingEdge <= 0.01) {
        logD("选中靠近顶部的项: ${position.index}, leading: ${position.itemLeadingEdge}");
        return position.index;
      }
    }

    // 如果没有找到接近顶部的项，则检查第一个可见项是否已经部分滚出视口顶部
    // 这表示用户正在向下滚动，应该将该项视为当前项
    if (visibleItems.first.itemLeadingEdge < 0 && visibleItems.first.itemTrailingEdge > 0) {
      return visibleItems.first.index;
    }

    // 如果没有任何项目符合条件，则保持当前选中状态不变
    return _currentSectionIndex;
  }

  // 点击左侧菜单滚动到指定区块
  void _scrollToSection(int index) {
    // 提前检查，避免不必要的状态更新
    if (_isClickingTagGroup || index < 0 || index >= _sections.length) return;

    // 立即记录当前将要选中的项，避免中间状态
    final section = _sections[index];
    final bool willShowGenres = section.type == SectionType.genres;
    final int willSelectTagGroupId = willShowGenres ? -1 : section.id;

    // 一次性更新状态，避免多次重绘
    setState(() {
      _currentSectionIndex = index;
      _isClickingTagGroup = true;

      // 直接设置最终状态，不用中间状态
      _showGenres = willShowGenres;
      _selectedTagGroupId = willSelectTagGroupId;
    });

    logD("滚动到section: ${section.name}, index: $index, type: ${section.type}");

    // 直接跳转到目标位置
    _jumpToSection(willShowGenres ? 0 : index);
  }

  // 直接跳转到指定位置，加强健壮性
  void _jumpToSection(int index) {
    if (!mounted || !_itemScrollController.isAttached) {
      _resetClickingFlag();
      return;
    }

    try {
      // 直接跳转到目标位置，无动画
      _itemScrollController.jumpTo(index: index);

      _resetClickingFlag();
    } catch (e) {
      logD("跳转错误: $e");
      _resetClickingFlag();
    }
  }

  // 重置点击标识的辅助方法，确保完全重置
  void _resetClickingFlag() {
    // 增加延迟时间，确保跳转完成后再重置标志
    Future.delayed(const Duration(seconds: 1), () {
      _isClickingTagGroup = false;
    });
  }

  //获取搜索默认书籍
  Future<void> getSearchDefaultBook() async {
    _searchBookDefaultItem = await SearchViewModel.getSearchDefaultBook();
  }

  onTabTapped(int index) {
    switch (index) {
      case 0:
        EventReportManager.eventReportOfFirebase(clickGenresGenres);
        break;
      case 1:
        EventReportManager.eventReportOfFirebase(clickGenresTags);
        break;
      default:
        break;
    }
  }

  // 滚动后更新选中的section
  void _updateCurrentSectionAfterScroll(int currentItemIndex) {
    // 确保索引有效
    if (!mounted || currentItemIndex < 0 || currentItemIndex >= _sections.length) {
      return;
    }

    // 获取对应的section
    int targetSectionIndex = currentItemIndex;
    final section = _sections[targetSectionIndex];

    // 只有当目标section与当前不同时才更新
    if (targetSectionIndex != _currentSectionIndex) {
      logD("更新选中section: ${section.name}, index: $targetSectionIndex, type: ${section.type}");

      // 更新左侧菜单选中状态
      _updateLeftMenuSelectionOnly(targetSectionIndex);
    }
  }

  // 只更新左侧菜单的选中状态，不影响右侧滚动
  void _updateLeftMenuSelectionOnly(int index) {
    if (_isUpdatingSection || !mounted || index < 0 || index >= _sections.length) {
      return;
    }

    _isUpdatingSection = true;

    // 更新选中状态
    setState(() {
      _currentSectionIndex = index;
      final section = _sections[index];

      if (section.type == SectionType.genres) {
        _showGenres = true;
        _selectedTagGroupId = -1;
      } else {
        _showGenres = false;
        _selectedTagGroupId = section.id;
      }

      // 滚动左侧菜单到选中项，但使用更短的动画时间
      _scrollLeftToItemSmoothly(index);
    });

    // 较短时间后允许再次更新
    _updateSectionTimer = Timer(const Duration(milliseconds: 50), () {
      _isUpdatingSection = false;
    });
  }

  // 平滑滚动左侧菜单到指定项（右侧滑动时），使用更短的动画时间
  void _scrollLeftToItemSmoothly(int index) {
    if (!_leftScrollController.hasClients) return;

    // 计算目标位置
    final double itemHeight = 60.0; // 菜单项高度
    final targetPosition = index * itemHeight;

    // 获取当前可视区域信息
    final double viewportHeight = _leftScrollController.position.viewportDimension;
    final double currentOffset = _leftScrollController.offset;

    // 计算最佳滚动位置，确保选中项在视图中央
    double newOffset = targetPosition - (viewportHeight / 2) + (itemHeight / 2);

    // 限制范围以避免过度滚动
    newOffset = newOffset.clamp(0.0, _leftScrollController.position.maxScrollExtent);

    // 只有当需要滚动的距离超过阈值时才滚动，且使用更短的动画时间
    if ((newOffset - currentOffset).abs() > itemHeight / 2) {
      _leftScrollController.animateTo(
        newOffset,
        duration: const Duration(milliseconds: 200), // 减少动画时间
        curve: Curves.easeOutCubic,
      );
    }
  }

  @override
  bool get wantKeepAlive => true;

  _buildScrollList() {
    // 计算item总数：Genres占一个位置，每个标签组各占一个位置
    int itemCount = 1 + (_tagGroups?.length ?? 0);

    return ScrollablePositionedList.builder(
      itemCount: itemCount,
      itemScrollController: _itemScrollController,
      itemPositionsListener: _itemPositionsListener,
      physics: const BouncingScrollPhysics(),
      // 正常时使用弹性滚动
      minCacheExtent: 1000,
      // 增加缓存范围，提高滚动性能
      addSemanticIndexes: false,
      // 禁用语义索引，减少不必要的渲染
      addAutomaticKeepAlives: true,
      // 保持项目状态，避免重新构建
      addRepaintBoundaries: true,
      // 添加重绘边界，优化渲染性能
      itemBuilder: (context, index) {
        // 构建Genres部分
        if (index == 0) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSectionHeader(_genresModel?.genres ?? '', 0),
              _buildGenresSection(),
            ],
          );
        }

        // 构建标签组部分
        if (_tagGroups != null && index - 1 < _tagGroups!.length) {
          final group = _tagGroups![index - 1];
          final tagName = group.groupName ?? "";
          final sectionIndex = _sections
              .indexWhere((section) => section.name == tagName && section.type == SectionType.tag);

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSectionHeader(tagName, sectionIndex),
              _buildTagsSection(group.groupId),
            ],
          );
        }

        // 默认返回空容器
        return SizedBox.shrink();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Scaffold(
      backgroundColor: HexColor('#FFFFFF'),
      body: Column(
        children: [
          // 添加搜索栏
          ReadSearchBar(
            defaultItem: _searchBookDefaultItem,
            onSearchTap: () async {
              //搜索点击事件
              await Get.toNamed('/searchPage', arguments: {"defaultItem": _searchBookDefaultItem});
              EventReportManager.eventReportOfFirebase(clickSearch);
            },
            onLanguageTap: () {},
            isShowLanguage: false,
            isShowSignIn: isSignInSwitchOn,
          ),
          SizedBox(
            height: 10,
          ),
          Expanded(
            child: Row(
              children: [
                // 左侧选项卡
                Container(
                  width: 100,
                  decoration: BoxDecoration(
                    color: HexColor('#F8F8F8'),
                  ),
                  child: ListView(
                    controller: _leftScrollController,
                    padding: EdgeInsets.zero,
                    children: [
                      // Genres选项
                      InkWell(
                        onTap: () {
                          // 直接调用滚动方法，避免嵌套setState
                          _scrollToSection(0);
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 3),
                          decoration: BoxDecoration(
                            color: _showGenres ? HexColor('#FFFFFF') : HexColor('#F8F8F8'),
                            border: Border(
                              left: BorderSide(
                                color: _showGenres ? HexColor('#1B86FF') : Colors.transparent,
                                width: 3.0,
                              ),
                            ),
                          ),
                          child: Text(
                            _genresModel?.genres ?? '',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: _showGenres ? FontWeight.bold : FontWeight.normal,
                              color: _showGenres ? HexColor('#1B86FF') : HexColor('#787C87'),
                            ),
                          ),
                        ),
                      ),
                      // Tags分组列表
                      if (_tagGroups != null)
                        ..._tagGroups!.asMap().entries.map((entry) {
                          final int tagIndex = entry.key;
                          final group = entry.value;
                          // 计算section索引（Genres占据索引0，所以标签组从1开始）
                          final sectionIndex = tagIndex + 1;
                          final bool isSelected =
                              !_showGenres && _selectedTagGroupId == group.groupId;

                          return InkWell(
                            onTap: () {
                              // 直接调用滚动方法，避免嵌套setState
                              _scrollToSection(sectionIndex);
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 1),
                              decoration: BoxDecoration(
                                color: isSelected ? HexColor('#FFFFFF') : HexColor('#F8F8F8'),
                                border: Border(
                                  left: BorderSide(
                                    color: isSelected ? HexColor('#1B86FF') : Colors.transparent,
                                    width: 3.0,
                                  ),
                                ),
                              ),
                              child: Text(
                                (group.groupName ?? "").tr,
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                  color: isSelected ? HexColor('#1B86FF') : HexColor('#787C87'),
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          );
                        }),
                    ],
                  ),
                ),
                // 右侧内容区域 - 移除下拉刷新，直接使用ScrollablePositionedList
                Expanded(
                  child: Stack(
                    children: [
                      _buildScrollList(),

                      // 全局刷新指示器
                      if (_isLoading) const LottieAnimationView(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建部分标题
  Widget _buildSectionHeader(String title, int index) {
    // 使用GlobalKey标记标题位置
    final GlobalKey? key = _sectionKeys[title];

    return Container(
      key: key != null ? ValueKey("${title}_$index") : null,
      padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 16),
      decoration: BoxDecoration(
        color: HexColor('#FFFFFF'),
      ),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: HexColor('#333333'),
        ),
      ),
    );
  }

  // 构建Genres区域
  Widget _buildGenresSection() {
    // 动态适应内容高度
    return SizedBox(
      width: double.infinity,
      // 确保与标题的间距与其他部分一致
      child: isAvailable(_genresModel?.categoryResDtoList)
          ? TagsList(
              cachedGenresData: _genresModel?.categoryResDtoList,
              isInScrollView: true,
              isGenres: true,
            )
          : Column(
              children: [
                // 使用NoLoadView
                NoLoadView(),
                // 添加刷新按钮
                _buildRefreshButton(),
              ],
            ),
    );
  }

  // 构建Tags区域
  Widget _buildTagsSection(int? groupId) {
    if (groupId == null) return Container();

    // 获取该组的标签数据
    final tagsData = _cachedTagData[groupId];

    // 动态适应内容高度
    return SizedBox(
      width: double.infinity,
      // 确保与标题的间距一致，移除特殊内边距
      child: tagsData != null
          ? TagsList(
              groupId: groupId,
              cachedData: tagsData,
              isInScrollView: true,
              isGenres: false,
              onCacheUpdated: (data) {
                _cachedTagData[groupId] = data;
              },
            )
          : Column(
              children: [
                // 使用NoLoadView
                NoLoadView(),
                // 添加刷新按钮
                _buildRefreshButton(),
              ],
            ),
    );
  }

  // 构建刷新按钮
  Widget _buildRefreshButton() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 20),
      child: Center(
        child: IconButton(
          onPressed: _refreshData,
          icon: Icon(
            Icons.refresh_rounded,
            color: const Color.fromARGB(255, 204, 204, 204),
            size: 32,
          ),
        ),
      ),
    );
  }
}

// 部分类型枚举
enum SectionType { genres, tag }

// 部分信息类
class SectionInfo {
  final int index; // 索引
  final String name; // 名称
  final SectionType type; // 类型
  final int id; // ID (对于Tag是groupId，对于Genres是-1)
  final double? position; // 位置

  SectionInfo({
    required this.index,
    required this.name,
    required this.type,
    required this.id,
    this.position,
  });

  // 添加一个copyWith方法，方便更新position
  SectionInfo copyWith({
    int? index,
    String? name,
    SectionType? type,
    int? id,
    double? position,
  }) {
    return SectionInfo(
      index: index ?? this.index,
      name: name ?? this.name,
      type: type ?? this.type,
      id: id ?? this.id,
      position: position ?? this.position,
    );
  }
}
