import 'package:UrNovel/Util/NetWorkManager/net_work_manager.dart';
import 'package:UrNovel/Util/api_config.dart';
import 'package:UrNovel/Util/tools.dart';

import '../Model/genres_model.dart';
import '../Model/tag_groups_model.dart';
import '../Model/tags_model.dart';

class GenresViewModel {
  // TODO: 获取分类
  static Future<GenresListResultModel?> getGenresList() async {
    var response =
        await NetWorkManager.instance.post(apiGetCategoryListV2, parameters: {});
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      GenresListModel model = GenresListModel.fromJson(response.data);

      return model.result;
    }

    return null;
  }

  // TODO: 获取标签组
  static Future<List<TagGroupItem>?> getTagGroupList() async {
    var response =
        await NetWorkManager.instance.post(apiGetTagGroupList, parameters: {});
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      TagGroupsModel? model = TagGroupsModel.fromJson(response.data);

      return model.result;
    }

    return null;
  }

  // TODO: 获取标签列表
  static Future<TagsGroupModel?> getTagList(int? tagGroupId) async {
    if (isAvailable(tagGroupId)) {
      var response = await NetWorkManager.instance
          .get(apiGetTagList, params: {'tagGroupId': tagGroupId});
      if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
        TagsListModel? model = TagsListModel.fromJson(response.data);

        return model.result;
      }
    }

    return null;
  }
}
