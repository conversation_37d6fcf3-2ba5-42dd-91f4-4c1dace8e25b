import 'package:json_annotation/json_annotation.dart';
part 'tag_groups_model.g.dart';

@JsonSerializable()
class TagGroupsModel {
  int? code;
  List<TagGroupItem>? result;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  TagGroupsModel(
      {this.code, this.result, this.msg, this.sysAt, this.cost, this.traceId});

  factory TagGroupsModel.fromJson(Map<String, dynamic> json) =>
      _$TagGroupsModelFromJson(json);

  Map<String, dynamic> toJson() => _$TagGroupsModelToJson(this);
}

@JsonSerializable()
class TagGroupItem {
  String? groupName; //分组名称
  int? groupId; //主键

  TagGroupItem({this.groupName, this.groupId});

  factory TagGroupItem.fromJson(Map<String, dynamic> json) =>
      _$TagGroupItemFromJson(json);

  Map<String, dynamic> toJson() => _$TagGroupItemToJson(this);
}
