import 'package:json_annotation/json_annotation.dart';

part 'genres_model.g.dart';

@JsonSerializable()
class GenresListModel {
  int? code;
  GenresListResultModel? result;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  GenresListModel(
      {this.code, this.result, this.msg, this.sysAt, this.cost, this.traceId});

  factory GenresListModel.fromJson(Map<String, dynamic> json) =>
      _$GenresListModelFromJson(json);

  Map<String, dynamic> toJson() => _$GenresListModelToJson(this);
}

@JsonSerializable()
class GenresListResultModel {
  String? genres;
  List<GenresListItem>? categoryResDtoList;

  GenresListResultModel({this.genres, this.categoryResDtoList});

  factory GenresListResultModel.fromJson(Map<String, dynamic> json) =>
      _$GenresListResultModelFromJson(json);

  Map<String, dynamic> toJson() => _$GenresListResultModelToJson(this);
}

@JsonSerializable()
class GenresListItem {
  int? categoryId;
  String? name;
  String? imgUrl;

  GenresListItem({this.categoryId, this.name, this.imgUrl});

  factory GenresListItem.fromJson(Map<String, dynamic> json) =>
      _$GenresListItemFromJson(json);

  Map<String, dynamic> toJson() => _$GenresListItemToJson(this);
}
