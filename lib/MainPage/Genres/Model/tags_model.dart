import 'package:json_annotation/json_annotation.dart';

part 'tags_model.g.dart';

@JsonSerializable()
class TagsListModel {
  int? code;
  TagsGroupModel? result;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  TagsListModel(
      {this.code, this.result, this.msg, this.sysAt, this.cost, this.traceId});

  factory TagsListModel.fromJson(Map<String, dynamic> json) =>
      _$TagsListModelFromJson(json);

  Map<String, dynamic> toJson() => _$TagsListModelToJson(this);
}

@JsonSerializable()
class TagsGroupModel {
  String? groupName;
  int? groupId;
  List<TagInfoItem>? tagInfoList;

  TagsGroupModel({this.groupName, this.groupId, this.tagInfoList});

  factory TagsGroupModel.fromJson(Map<String, dynamic> json) =>
      _$TagsGroupModelFromJson(json);

  Map<String, dynamic> toJson() => _$TagsGroupModelToJson(this);
}

@JsonSerializable()
class TagInfoItem {
  String? name;
  int? id;

  TagInfoItem({this.name, this.id});

  factory TagInfoItem.fromJson(Map<String, dynamic> json) =>
      _$TagInfoItemFromJson(json);

  Map<String, dynamic> toJson() => _$TagInfoItemToJson(this);
}
