import 'package:UrNovel/Util/Common/Animation/lottie_animation_view.dart';
import 'package:UrNovel/Util/Common/no_load_view.dart';
import 'package:UrNovel/Util/DataReportManager/event_name_config.dart';
import 'package:UrNovel/Util/DataReportManager/event_report_manager.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../Launch&Login/Model/language_model.dart';
import '../../../../Util/Extensions/colorUtil.dart';
import '../../../../Util/RefreshLoad/refresh_load.dart';
import '../../../../Util/enum.dart';
import '../../Model/genres_model.dart';
import '../../Model/tags_model.dart';
import '../../ViewModel/ViewModel.dart';

class TagsList extends StatefulWidget {
  final int? groupId;
  final TagsGroupModel? cachedData;
  final List<GenresListItem>? cachedGenresData;
  final Function(TagsGroupModel?)? onCacheUpdated;
  final bool isInScrollView;
  final bool isGenres;
  final RefreshController? refreshController;

  const TagsList({
    super.key,
    this.groupId,
    this.cachedData,
    this.cachedGenresData,
    this.onCacheUpdated,
    this.isInScrollView = false,
    this.isGenres = false,
    this.refreshController,
  });

  @override
  State<TagsList> createState() => _TagsListState();
}

class _TagsListState extends State<TagsList>
    with AutomaticKeepAliveClientMixin {
  late TagsGroupModel? _tagsGroupModel;
  late List<GenresListItem>? _genresDataList;
  final RefreshController _refreshController = RefreshController();
  late bool _isLoading;

  @override
  void initState() {
    super.initState();

    // 使用单独的变量判断是否有缓存数据
    bool hasCachedData = widget.isGenres
        ? widget.cachedGenresData != null
        : widget.cachedData != null;

    if (hasCachedData) {
      // 有缓存数据
      _genresDataList = widget.isGenres ? widget.cachedGenresData : null;
      _tagsGroupModel = widget.isGenres ? null : widget.cachedData;
      _isLoading = false;

      // 处理刷新状态 (仅Genres模式)
      if (widget.isGenres &&
          !widget.isInScrollView &&
          widget.refreshController != null) {
        _dealRefreshState(1, 1, _genresDataList);
      }
    } else {
      // 无缓存数据
      _genresDataList = null;
      _tagsGroupModel = null;
      _isLoading = true;
      if (widget.isGenres) {
        fetchGenresData();
      } else {
        fetchTagsData();
      }
    }

    // 添加语言变化监听
    eventBusOn<LanguageItem>((item) {
      // 语言变化时刷新数据
      if (widget.isGenres) {
        fetchGenresData();
      } else {
        fetchTagsData();
      }
    });
  }

  // 处理刷新状态（用于处理 Genres 类型的刷新）
  void _dealRefreshState(int pageIndex, int total, dynamic dataList) {
    if (widget.refreshController == null) return;

    if (pageIndex == 1) {
      widget.refreshController!.refreshCompleted();
      if (isAvailable(dataList)) {
        widget.refreshController!.resetNoData();
      } else {
        widget.refreshController!.loadNoData();
      }
    } else if (!isAvailable(dataList) || pageIndex >= total) {
      widget.refreshController!.loadNoData();
    } else {
      widget.refreshController!.loadComplete();
    }
  }

  @override
  void dispose() {
    // 只在内部创建的控制器需要释放
    if (widget.refreshController == null) {
      _refreshController.dispose();
    }
    super.dispose();
  }

  @override
  void didUpdateWidget(TagsList oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 合并处理widget更新逻辑
    bool shouldUpdate = widget.isGenres
        ? widget.cachedGenresData != oldWidget.cachedGenresData
        : oldWidget.groupId != widget.groupId ||
            oldWidget.cachedData != widget.cachedData;

    if (shouldUpdate) {
      bool hasCachedData = widget.isGenres
          ? widget.cachedGenresData != null
          : widget.cachedData != null;

      if (hasCachedData) {
        setState(() {
          _genresDataList = widget.isGenres ? widget.cachedGenresData : null;
          _tagsGroupModel = widget.isGenres ? null : widget.cachedData;
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = true;
          _genresDataList = null;
          _tagsGroupModel = null;
        });
        if (widget.isGenres) {
          fetchGenresData();
        } else {
          fetchTagsData();
        }
      }
    }
  }

  // 获取 Genres 数据
  Future fetchGenresData() async {
    GenresListResultModel? model = await GenresViewModel.getGenresList();
    _genresDataList = model?.categoryResDtoList;

    // 处理刷新
    if (!widget.isInScrollView && widget.refreshController != null) {
      _dealRefreshState(1, 1, _genresDataList);
    } else if (widget.refreshController == null) {
      _refreshController.refreshCompleted();
    }

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // 获取 Tags 数据
  Future fetchTagsData() async {
    if (widget.groupId == null) return;

    _tagsGroupModel = await GenresViewModel.getTagList(widget.groupId);

    // 外部刷新控制器
    if (widget.refreshController != null) {
      widget.refreshController!.refreshCompleted();
    } else {
      _refreshController.refreshCompleted();
    }

    // 更新缓存
    if (widget.onCacheUpdated != null) {
      widget.onCacheUpdated!(_tagsGroupModel);
    }

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // 点击标签时的事件上报
  void onTagTap() {
    if (isAvailable(_tagsGroupModel?.groupName)) {
      EventReportManager.eventReportOfFirebase(
          clickTagPrefix + _tagsGroupModel!.groupName!);
    }
  }

  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);

    // 获取数据长度
    int count;
    if (widget.isGenres) {
      count = _genresDataList?.length ?? 0;
    } else {
      count = _tagsGroupModel?.tagInfoList?.length ?? 0;
    }

    // 构建内容
    Widget content = GridView.builder(
        scrollDirection: Axis.vertical,
        physics:
            widget.isInScrollView ? const NeverScrollableScrollPhysics() : null,
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 2.22,
          mainAxisSpacing: 0,
          crossAxisSpacing: 0,
        ),
        shrinkWrap: widget.isInScrollView,
        padding: const EdgeInsets.only(top: 4, left: 4, right: 6),
        itemCount: count,
        itemBuilder: (context, index) {
          if (index < count) {
            double marginLeft = 4;
            double marginRight = 4;

            // 根据不同类型获取不同的数据项
            dynamic item;
            if (widget.isGenres) {
              item = _genresDataList?[index];
            } else {
              item = _tagsGroupModel?.tagInfoList?[index];
            }

            return GestureDetector(
              onTap: () async {
                String title = item?.name ?? "";
                String code;
                SecondaryPageType pageType;

                if (widget.isGenres) {
                  code = item?.categoryId.toString() ?? "";
                  pageType = SecondaryPageType.fromGenres;
                } else {
                  code = item?.id.toString() ?? "";
                  pageType = SecondaryPageType.fromTags;
                }

                await Get.toNamed('/secondaryListPage', arguments: {
                  "title": title,
                  'code': code,
                  'pageType': pageType,
                });

                // 仅Tags模式需要上报事件
                if (!widget.isGenres) {
                  onTagTap();
                }
              },
              child: Container(
                margin: EdgeInsets.only(
                    top: 0, bottom: 10, left: marginLeft, right: marginRight),
                padding: const EdgeInsets.all(5),
                decoration: BoxDecoration(
                  color: HexColor('#F4F5F7'),
                  borderRadius: const BorderRadius.all(Radius.circular(7.5)),
                ),
                child: Center(
                  child: Text(item?.name ?? "",
                      // "Testssssssssss Testssssssssss Testssssssssss",
                      style: TextStyle(
                        fontSize: widget.isGenres ? 13 : 13,
                        height: 1.2,
                        color: widget.isGenres
                            ? HexColor('#000000')
                            : HexColor('#535962'),
                        fontWeight: widget.isGenres
                            ? FontWeight.bold
                            : FontWeight.normal,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis),
                ),
              ),
            );
          } else {
            return Container();
          }
        });

    // 判断数据是否为空
    bool isEmpty;
    if (widget.isGenres) {
      isEmpty = !isAvailable(_genresDataList);
    } else {
      isEmpty = _tagsGroupModel?.tagInfoList?.isEmpty ?? true;
    }

    // 如果在滚动视图中，直接返回网格视图
    if (widget.isInScrollView) {
      Color? bgColor = widget.isGenres ? HexColor('#FFFFFF') : null;
      return Container(
        width: double.infinity,
        color: bgColor,
        child: Stack(
          children: [
            content,
            if (_isLoading)
              const LottieAnimationView()
            else if (isEmpty && !_isLoading)
              NoLoadView()
          ],
        ),
      );
    }

    Color? bgColor = widget.isGenres ? HexColor('#FFFFFF') : null;
    EdgeInsetsGeometry? padding =
        widget.isGenres ? const EdgeInsets.only(bottom: 10) : null;

    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: Stack(
        children: [
          Container(
            color: bgColor,
            padding: padding,
            child: RefreshLoadUtil(
                controller: widget.refreshController ?? _refreshController,
                enablePullUp: false,
                onRefresh: () {
                  if (widget.isGenres) {
                    fetchGenresData();
                  } else {
                    fetchTagsData();
                  }
                },
                onLoading: () {},
                child: content),
          ),
          if (_isLoading)
            const LottieAnimationView()
          else if (isEmpty && !_isLoading)
            NoLoadView()
        ],
      ),
    );
  }
}
