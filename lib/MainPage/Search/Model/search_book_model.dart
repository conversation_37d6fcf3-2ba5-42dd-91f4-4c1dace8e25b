import 'package:json_annotation/json_annotation.dart';

part 'search_book_model.g.dart';

@JsonSerializable()
class SearchBookModel {
  int? code; //状态码
  BookSearchDtoList? result; //符合条件的书籍集合
  String? msg; //返回信息
  int? sysAt; //时间
  int? cost; //耗时
  String? traceId; //日志跟踪id

  SearchBookModel(
      {this.code, this.result, this.msg, this.sysAt, this.cost, this.traceId});

  factory SearchBookModel.fromJson(Map<String, dynamic> json) =>
      _$SearchBookModelFromJson(json);

  Map<String, dynamic> toJson() => _$SearchBookModelToJson(this);
}

@JsonSerializable()
class BookSearchDtoList {
  List<BookSearchDtoItem>? bookSearchDtoList;
  int? page;
  int? size;
  int? totalCount;

  BookSearchDtoList(
      {this.bookSearchDtoList, this.page, this.size, this.totalCount});

  factory BookSearchDtoList.fromJson(Map<String, dynamic> json) =>
      _$BookSearchDtoListFromJson(json);

  Map<String, dynamic> toJson() => _$BookSearchDtoListToJson(this);
}

@JsonSerializable()
class BookSearchDtoItem {
  int? bookId; //书籍id
  int? bookLangId; //书籍语言id
  int? authorId; //作者id
  String? authorName; //作者名称
  String? title; //标题
  String? cover; //封面
  int? viewCount; //阅读量
  String? description;//描述

  BookSearchDtoItem(
      {this.bookId,
      this.bookLangId,
      this.authorId,
      this.authorName,
      this.title,
      this.cover,
      this.viewCount,
      this.description});

  factory BookSearchDtoItem.fromJson(Map<String, dynamic> json) =>
      _$BookSearchDtoItemFromJson(json);

  Map<String, dynamic> toJson() => _$BookSearchDtoItemToJson(this);
}
