import 'package:json_annotation/json_annotation.dart';
part 'search_book_default_model.g.dart';

@JsonSerializable()
class SearchBookDefaultModel {
  int? code;
  SearchBookDefaultItem? result;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;


  SearchBookDefaultModel(
      {this.code,
        this.result,
        this.msg,
        this.sysAt,
        this.cost,
        this.traceId});

  factory SearchBookDefaultModel.fromJson(Map<String, dynamic> json) =>
      _$SearchBookDefaultModelFromJson(json);

  Map<String, dynamic> toJson() => _$SearchBookDefaultModelToJson(this);
}


@JsonSerializable()
class SearchBookDefaultItem {
  int? bookId; //书籍id
  int? bookLangId; //书籍语言id
  int? authorId; //作者id
  String? authorName; //作者名称
  String? title; //标题
  String? cover; //封面
  int? viewCount; //阅读量1
  bool? library; //是否加入书架
  bool? liked; //是否加入收藏
  int? bookGoldCount; //书籍金币数
  String? description; //描述

  SearchBookDefaultItem(
      {this.bookId,
        this.bookLangId,
        this.authorId,
        this.authorName,
        this.title,
        this.cover,
        this.viewCount,
        this.library,
        this.liked,
        this.bookGoldCount,
        this.description});

  factory SearchBookDefaultItem.fromJson(Map<String, dynamic> json) =>
      _$SearchBookDefaultItemFromJson(json);

  Map<String, dynamic> toJson() => _$SearchBookDefaultItemToJson(this);
}