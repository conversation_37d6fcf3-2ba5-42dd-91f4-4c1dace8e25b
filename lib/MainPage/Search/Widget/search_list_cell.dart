import 'package:flutter/material.dart';
import 'package:UrNovel/Util/Common/network_image_util.dart';
import 'package:UrNovel/Util/Extensions/colorUtil.dart';

import '../Model/search_book_model.dart';


class SearchListCell extends StatefulWidget {
  final BookSearchDtoItem? dtoItem;
  final int index;

  const SearchListCell({super.key, required this.dtoItem, required this.index});

  @override
  State<SearchListCell> createState() => _SearchListCellState();
}

class _SearchListCellState extends State<SearchListCell> {
  @override
  Widget build(BuildContext context) {
    var numColor = HexColor('#000000');
    if (widget.index < 3) {
      numColor = HexColor('#FF4930');
    }
    return Container(
      padding: const EdgeInsets.only(top: 9, bottom: 9),
      height: 90,
      width: double.infinity,
      color: Colors.transparent,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 37,
            alignment: Alignment.center,
            child: Text((widget.index + 1).toString(),
                style: TextStyle(
                    color: numColor,
                    fontSize: 15,
                    fontWeight: FontWeight.bold)),
          ),
          NetworkImageUtil(
              imageUrl: widget.dtoItem?.cover, width: 49, height: 65),
          const SizedBox(width: 7),
          Expanded(
              child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.dtoItem?.title ?? '',
                style: TextStyle(
                  fontSize: 15,
                  color: HexColor('#000000'),
                  fontWeight: FontWeight.bold,
                ),
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                widget.dtoItem?.authorName ?? '',
                style: TextStyle(
                  fontSize: 13,
                  color: HexColor('#888C94'),
                ),
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Container(
                height: 20,
                decoration: BoxDecoration(
                  color: HexColor("#F4F5F6"),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const SizedBox(width: 7),
                    Image.asset("assets/images/bottomNavBar/icon_profile_n.png",
                        width: 9, height: 8),
                    const SizedBox(width: 4),
                    Text(
                      '${widget.dtoItem?.viewCount ?? 0}',
                      style: TextStyle(
                        fontSize: 11,
                        color: HexColor('#888C94'),
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(width: 8),
                  ],
                ),
              )
            ],
          )),
        ],
      ),
    );
  }
}
