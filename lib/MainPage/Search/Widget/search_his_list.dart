import 'package:flutter/material.dart';
import 'package:UrNovel/Util/tools.dart';

import '../../../../Util/Extensions/colorUtil.dart';

class SearchHisList extends StatefulWidget {
  final Function(String? keyword) onItemPressed;
  final VoidCallback? onTrashPressed;
  final List<String>? hisList;

  const SearchHisList(
      {super.key,
      required this.onItemPressed,
      required this.onTrashPressed,
      required this.hisList});

  @override
  State<SearchHisList> createState() => _SearchHisListState();
}

class _SearchHisListState extends State<SearchHisList> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 30,
      width: double.infinity,
      alignment: Alignment.centerLeft,
      child: Row(
        children: [
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal, // 设置为横向滚动
              itemCount:
                  isAvailable(widget.hisList) ? widget.hisList!.length : 0,
              itemBuilder: (context, index) {
                if (isAvailable(widget.hisList) &&
                    index < widget.hisList!.length) {
                  double leftMargin;
                  double rightMargin;
                  var keyword = widget.hisList![index] ?? "";

                  if (index == 0) {
                    leftMargin = 15;
                    rightMargin = 3.5;
                  } else if (index ==
                      (isAvailable(widget.hisList)
                          ? widget.hisList!.length - 1
                          : 0)) {
                    leftMargin = 3.5;
                    rightMargin = 15;
                  } else {
                    leftMargin = 3.5;
                    rightMargin = 3.5;
                  }
                  return GestureDetector(
                    onTap: () {
                      widget.onItemPressed(keyword);
                    },
                    child: Container(
                      margin:
                          EdgeInsets.only(left: leftMargin, right: rightMargin),
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                        color: HexColor('#ECEDF1'),
                      ),
                      child: Center(
                        child: Text(
                          keyword,
                          style: TextStyle(
                              fontSize: 14, color: HexColor('#555A65')),
                        ),
                      ),
                    ),
                  );
                } else {
                  return Container();
                }
              },
            ),
          ),
          SizedBox(
            width: 40,
            height: 30,
            child: Center(
              child: IconButton(
                  onPressed: widget.onTrashPressed,
                  icon: Image.asset('assets/images/read/icon_trash.png')),
            ),
          ),
        ],
      ),
    );
  }
}
