import 'package:UrNovel/Util/Common/CommonManager/common_manager.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../Util/Extensions/colorUtil.dart';
import '../Model/search_book_default_model.dart';
import 'dart:math' as math;


class TopSearchBar extends StatefulWidget {
  final SearchBookDefaultItem? defaultItem;
  final Function(String) textFieldOnChanged;

  const TopSearchBar(
      {super.key, required this.defaultItem, required this.textFieldOnChanged});

  @override
  State<TopSearchBar> createState() => TopSearchBarState();
}

class TopSearchBarState extends State<TopSearchBar> {
  final TextEditingController _searchController = TextEditingController();

  void setText(String? keyword) {
    _searchController.text = keyword ?? "";
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context);
    return Container(
      margin: EdgeInsets.only(top: media.padding.top + 20, left: 10, right: 10),
      child: Row(
        children: [
          SizedBox(
            width: 30,
            height: 40,
            child: IconButton(
                onPressed: () {
                  Get.back();
                },
                icon: Transform.rotate(
                  angle: CommonManager.instance.isReverse() ? math.pi : 0,
                  child: Image.asset("assets/images/common/icon_back_black.png",
                      width: 12, height: 19),
                )),
          ),
          Container(
            height: 40,
            width: media.size.width - 65,
            margin: const EdgeInsets.only(left: 15),
            decoration: BoxDecoration(
              color: HexColor('#ECEDF1'),
              borderRadius: BorderRadius.circular(18),
            ),
            child: Row(
              children: [
                Expanded(
                    child: CupertinoSearchTextField(
                  controller: _searchController,
                  placeholder: widget.defaultItem?.title ?? 'search_id'.tr,
                  prefixInsets:
                      const EdgeInsetsDirectional.fromSTEB(13, 0, 8, 0),
                  // 调整图标内边距
                  prefixIcon: Image.asset(
                    'assets/images/read/icon_search.png',
                    width: 13.5,
                    height: 13,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(
                      width: 0, // 设置边框宽度为0
                      color: Colors.transparent, // 设置边框颜色为透明
                    ),
                  ),
                  keyboardType: TextInputType.text,
                  // 确保键盘类型正确
                  suffixIcon: const Icon(Icons.clear, size: 14),
                  suffixInsets: const EdgeInsetsDirectional.only(end: 10),
                  onSubmitted: widget.textFieldOnChanged,
                )),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
