import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:UrNovel/BaseWidget/base_less_widget.dart';
import 'package:UrNovel/MainPage/Search/Model/search_book_model.dart';
import 'package:UrNovel/MainPage/Search/Widget/search_list_cell.dart';
import 'package:UrNovel/Util/Extensions/colorUtil.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../../../../Util/RefreshLoad/refresh_load.dart';
import '../../../../Util/enum.dart';
import '../../../Util/LocalNotificationManager/local_notification_manager.dart';

class SearchList extends BaseLessWidget {
  final VoidCallback onRefresh;
  final VoidCallback onLoading;
  final RefreshController controller;
  final List<BookSearchDtoItem>? bookSearchDtoList;
  final VoidCallback? onMoreDetailsTap;

  SearchList(
      {super.key,
      required this.controller,
      required this.onRefresh,
      required this.onLoading,
      required this.onMoreDetailsTap,
      this.bookSearchDtoList});

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var count = bookSearchDtoList?.length ?? 0;

    return Container(
      margin: EdgeInsets.fromLTRB(15, 0, 15, mediaQuery.padding.bottom + 15),
      decoration: BoxDecoration(
        color: HexColor('#FFFFFF'),
        borderRadius: const BorderRadius.all(Radius.circular(12)),
      ),
      child: Column(
        children: [
          Container(
            margin: const EdgeInsets.only(left: 16, right: 12),
            height: 50,
            width: double.infinity,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('rankings_searches'.tr,
                    style: TextStyle(fontSize: 15, color: HexColor('#000000'))),
              ],
            ),
          ),
          Expanded(
              flex: 1,
              child: MediaQuery.removePadding(
                context: context,
                removeTop: true,
                removeBottom: true,
                removeLeft: true,
                removeRight: true,
                child: NotificationListener(
                    onNotification: (ScrollNotification notification) {
                      if (notification is ScrollStartNotification) {
                        exposureList.removeRange(0, exposureList.length);
                      } else if (notification is ScrollEndNotification) {
                        exposureReport();
                      }
                      return true;
                    },
                    child: RefreshLoadUtil(
                        onRefresh: onRefresh,
                        onLoading: onLoading,
                        controller: controller,
                        child: ListView.builder(
                            itemCount: count,
                            itemBuilder: (context, index) {
                              if (index < count) {
                                var item = bookSearchDtoList?[index];
                                return VisibilityDetector(
                                    key: Key('myList_$index'),
                                    onVisibilityChanged: (visibilityInfo) {
                                      if (0.8 <=
                                          visibilityInfo.visibleFraction) {
                                        if (item?.bookId is int) {
                                          exposureList.add(item!.bookId!);
                                        }
                                      }
                                    },
                                    child: GestureDetector(
                                        onTap: () async {
                                          //todo:去往阅读页
                                          await LocalNotificationManager
                                              .toNovelReadPage({
                                            'bookId': item?.bookId,
                                            'jumpType':
                                                BookDetailJumpType.search
                                          });
                                        },
                                        child: SearchListCell(
                                            dtoItem: item, index: index)));
                              }
                              return Container();
                            }))),
              )),
        ],
      ),
    );
  }
}
