import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/MainPage/Search/ViewModel/ViewModel.dart';
import 'package:UrNovel/Util/Common/Animation/lottie_animation_view.dart';
import 'package:UrNovel/Util/Extensions/colorUtil.dart';
import 'package:UrNovel/Util/SheetAndAlter/alter.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../Util/SharedPreferences/shared_preferences.dart';
import '../../../Util/logUtil.dart';
import '../Model/search_book_default_model.dart';
import '../Model/search_book_model.dart';
import '../Widget/search_his_list.dart';
import '../Widget/search_list.dart';
import '../Widget/top_search_bar.dart';

class SearchPage extends BaseFulWidget {
  SearchPage({super.key, super.arguments});

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  late bool isLoading;
  late int pageIndex;
  late int pageSize;
  late List<String>? hisList;
  late List<BookSearchDtoItem>? bookSearchDtoList;
  late String searchKey; //记录当前key
  late SearchBookDefaultItem? _defaultItem;
  final searchBarKey = GlobalKey<TopSearchBarState>();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    isLoading = true;
    pageIndex = 1; //page默认从1开始
    pageSize = 10;
    hisList = null;
    bookSearchDtoList = null;
    _defaultItem = widget.arguments['defaultItem'];
    searchKey = "";

    getHisList();
    onRefresh();
  }

  @override
  void dispose() {
    super.dispose();
  }

  //TODO:获取历史记录
  Future<void> getHisList() async {
    hisList = await SpUtil.spGetSearchHistory();
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> onRefresh() async {
    pageIndex = 1;
    searchHisList(true);
  }

  Future<void> onLoading() async {
    pageIndex++;
    searchHisList(false);
  }

  //TODO:搜索数据
  Future searchHisList(bool isRefresh) async {
    var parameters = {
      "searchParam": searchKey,
      "page": pageIndex,
      "size": pageSize,
    };
    bookSearchDtoList = await SearchViewModel.searchBook(parameters, isRefresh);
    // 处理刷新状态
    widget.dealRefreshState(pageIndex, pageSize, bookSearchDtoList);
    if (mounted) {
      setState(() {
        isLoading = false;
      });
    }

    widget.dealExposureList(bookSearchDtoList, 6);
  }

  //清空历史记录
  Future<void> clearHisList() async {
    await SpUtil.spDelete(SPKey.searchHistory);
    getHisList();
  }

  //搜索回调
  void searchAction(String value) {
    unFocus();
    searchKey = value;
    if (mounted) {
      setState(() {
        isLoading = true;
      });
    }
    onRefresh();
    SpUtil.spSaveSearchHistory(value);
  }

  void describeTheNovel(Size mediaSize) {
    //TODO: 书籍描述反馈
    showAlter(NoSearchResultAlter(onConfirm: (content) {}));
  }

  //TODO: 关闭键盘
  unFocus() {
    FocusScope.of(context).unfocus();
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context);
    return PopScope(
        onPopInvokedWithResult: (result, didPop) {
          unFocus();
        },
        child: Scaffold(
            body: Stack(
          children: [
            SizedBox(
              width: double.infinity,
              height: double.infinity,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TopSearchBar(
                    key: searchBarKey,
                    defaultItem: _defaultItem,
                    textFieldOnChanged: (value) {
                      if (isAvailable(value)) {
                        searchAction(value);
                      } else {
                        searchAction(_defaultItem?.title ?? "");
                      }
                    },
                  ),
                  if (isAvailable(hisList)) const SizedBox(height: 12),
                  if (isAvailable(hisList))
                    SearchHisList(
                        onItemPressed: (keyword) {
                          //历史记录点击事件
                          searchBarKey.currentState?.setText(keyword);
                          searchAction(keyword ?? "");
                        },
                        onTrashPressed: () {
                          //删除历史搜索记录
                          clearHisList();
                        },
                        hisList: hisList),
                  if (isAvailable(bookSearchDtoList))
                    const SizedBox(height: 11),
                  if (isAvailable(bookSearchDtoList))
                    Expanded(
                        child: SearchList(
                            onRefresh: onRefresh,
                            onLoading: onLoading,
                            controller: widget.refreshController,
                            onMoreDetailsTap: () {
                              logP('onMoreDetailsTap');
                            },
                            bookSearchDtoList: bookSearchDtoList)),
                  if (!isLoading && !isAvailable(bookSearchDtoList))
                    Expanded(
                      child: Center(child: Text("no_matching".tr)),
                    ),
                  if (!isLoading && !isAvailable(bookSearchDtoList))
                    Container(
                      margin:
                          EdgeInsets.only(bottom: media.padding.bottom + 20),
                      alignment: Alignment.center,
                      child: RichText(
                        text: TextSpan(
                          text: "looking_for".tr,
                          style: TextStyle(
                              fontSize: 12, color: HexColor('#1B86FF')),
                          recognizer: TapGestureRecognizer()
                            ..onTap = () {
                              // 处理点击事件
                              describeTheNovel(media.size);
                            },
                        ),
                      ),
                    ),
                ],
              ),
            ),
            if (isLoading) const LottieAnimationView()
          ],
        )));
  }
}
