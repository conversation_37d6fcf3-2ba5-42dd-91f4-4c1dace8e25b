

import '../../../Util/NetWorkManager/net_work_manager.dart';
import '../../../Util/api_config.dart';
import '../../../Util/tools.dart';
import '../Model/search_book_model.dart';
import '../Model/search_book_default_model.dart';

class SearchViewModel {

  static SearchBookModel _searchBookModel = SearchBookModel();
  SearchBookModel? get searchBookModel => _searchBookModel;
  static List<BookSearchDtoItem>? bookSearchDtoList = [];

  //todo: 获取搜索默认书籍
  static Future<SearchBookDefaultItem?> getSearchDefaultBook() async {
    var response = await NetWorkManager.instance
        .post(apiDefaultSearchBook, parameters: {});
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      SearchBookDefaultModel searchBookDefaultModel =
      SearchBookDefaultModel.fromJson(response.data);

      return searchBookDefaultModel.result;
    }

    return null;
  }

  /// 获取搜索结果
  static Future<List<BookSearchDtoItem>?> searchBook(Map<String, dynamic> parameters, bool isRefresh) async {
    var response = await NetWorkManager.instance
        .post(apiSearchBook, parameters: parameters);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      _searchBookModel = SearchBookModel.fromJson(response.data);
      if (isRefresh) {
        bookSearchDtoList = _searchBookModel.result?.bookSearchDtoList;
      } else {
        if (isAvailable(_searchBookModel.result?.bookSearchDtoList)) {
          bookSearchDtoList ??= [];
          if (_searchBookModel.result?.bookSearchDtoList
          is Iterable<BookSearchDtoItem>) {
            bookSearchDtoList!.addAll(_searchBookModel
                .result?.bookSearchDtoList! as Iterable<BookSearchDtoItem>);
          }
        }
      }
    }

    return bookSearchDtoList;
  }
}