import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../Util/Common/CommonManager/common_manager.dart';
import '../../../Util/Extensions/colorUtil.dart';
import '../../../Util/enum.dart';

class RankingsTypeList extends StatefulWidget {
  final double itemHeight;
  final RankingType type;
  final Function(RankingType) onItemTap;

  const RankingsTypeList(
      {super.key,
      required this.itemHeight,
      required this.type,
      required this.onItemTap});

  @override
  State<RankingsTypeList> createState() => _RankingsTypeListState();
}

class _RankingsTypeListState extends State<RankingsTypeList> {
  //选中状态
  late List topTypeList;

  //未选中状态
  late String imgSrc_l;
  late String imgNormalSrc_l;
  late String topColor;
  late String imgSrc_r;
  late String imgNormalSrc_r;
  late int currentIndex;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    String fixL = CommonManager.instance.isReverse() ? 'r' : 'l';
    String fixR = CommonManager.instance.isReverse() ? 'l' : 'r';

    topTypeList = [
      {
        'imgSrc_l': 'assets/images/rankings/icon_tasselPink_$fixL.png',
        'topColor': '#FD2C9A',
        'imgBottom': 'assets/images/rankings/icon_top_pink.png',
        'gifBottom': 'assets/gif/rankings/icon_top_pink.gif',
        'imgSrc_r': 'assets/images/rankings/icon_tasselPink_$fixR.png'
      },
      {
        'imgSrc_l': 'assets/images/rankings/icon_tasselBlue_$fixL.png',
        'topColor': '#37AEFE',
        'imgBottom': 'assets/images/rankings/icon_top_blue.png',
        'gifBottom': 'assets/gif/rankings/icon_top_blue.gif',
        'imgSrc_r': 'assets/images/rankings/icon_tasselBlue_$fixR.png'
      },
      {
        'imgSrc_l': 'assets/images/rankings/icon_tasselOrange_$fixL.png',
        'topColor': '#FF5F41',
        'imgBottom': 'assets/images/rankings/icon_top_orange.png',
        'gifBottom': 'assets/gif/rankings/icon_top_orange.gif',
        'imgSrc_r': 'assets/images/rankings/icon_tasselOrange_$fixR.png'
      },
      {
        'imgSrc_l': 'assets/images/rankings/icon_tasselYellow_$fixL.png',
        'topColor': '#F0920D',
        'imgBottom': 'assets/images/rankings/icon_top_yellow.png',
        'gifBottom': 'assets/gif/rankings/icon_top_yellow.gif',
        'imgSrc_r': 'assets/images/rankings/icon_tasselYellow_$fixR.png'
      },
    ];
    topColor = '#FD2C9A';
    imgSrc_l = imgNormalSrc_l = 'assets/images/rankings/icon_tasselBlack_$fixL.png';
    imgSrc_r = imgNormalSrc_r ='assets/images/rankings/icon_tasselBlack_$fixR.png';

    if (widget.type == RankingType.trending) {
      currentIndex = 0;
    } else if (widget.type == RankingType.newRelease) {
      currentIndex = 1;
    } else if (widget.type == RankingType.hotSearches) {
      currentIndex = 2;
    } else if (widget.type == RankingType.favorites) {
      currentIndex = 3;
    } else {
      currentIndex = 0;
    }
  }

  void _onTap(int index) {
    if (currentIndex != index) {
      setState(() {
        currentIndex = index;
      });
      widget.onItemTap(RankingType.values[index]);
    }
  }

  @override
  Widget build(BuildContext context) {
    return MediaQuery.removePadding(
      context: context,
      removeTop: true,
      removeLeft: true,
      removeRight: true,
      removeBottom: true,
      child: ListView.builder(
          itemCount: topTypeList.length,
          itemBuilder: (context, index) {
            String imgBottomStr;
            if (index == currentIndex) {
              topColor = topTypeList[index]['topColor'] as String;
              imgBottomStr = topTypeList[index]['gifBottom'] as String;
              imgSrc_l = topTypeList[index]['imgSrc_l'] as String;
              imgSrc_r = topTypeList[index]['imgSrc_r'] as String;
            } else {
              topColor = '#59626D';
              imgBottomStr = topTypeList[index]['imgBottom'] as String;
              imgSrc_l = imgNormalSrc_l;
              imgSrc_r = imgNormalSrc_r;
            }

            return InkWell(
              onTap: () {
                _onTap(index);
              },
              child: SizedBox(
                height: widget.itemHeight,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const SizedBox(width: 3),
                    Image.asset(
                      imgSrc_l,
                      width: 12,
                      height: 22,
                    ),
                    const SizedBox(width: 9),
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Top50'.tr,
                            style: TextStyle(
                                fontSize: 14,
                                color: HexColor(topColor),
                                fontWeight: FontWeight.bold),
                            maxLines: 2,
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 5),
                          Image.asset(imgBottomStr, width: 15, height: 15),
                        ],
                      ),
                    ),
                    const SizedBox(width: 9),
                    Image.asset(
                      imgSrc_r,
                      width: 12,
                      height: 22,
                    ),
                    const SizedBox(width: 3),
                  ],
                ),
              ),
            );
          }),
    );
  }
}
