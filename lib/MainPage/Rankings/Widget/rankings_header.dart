import 'dart:math' as math;

import 'package:UrNovel/Util/Extensions/colorUtil.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../Util/Common/CommonManager/common_manager.dart';
import '../../../Util/enum.dart';

class RankingHeader extends StatefulWidget {
  final RankingType rankingType;

  const RankingHeader({super.key, required this.rankingType});

  @override
  State<RankingHeader> createState() => RankingHeaderState();
}

class RankingHeaderState extends State<RankingHeader> {
  late RankingType rankingsType;
  late Color beginColor;
  late Color endColor;
  late String imgSrc_l;
  late String imgLine;
  late String imgSrc_r;
  late String title;
  late String subTitle;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    rankingsType = widget.rankingType;
    imgSrc_l = 'assets/images/rankings/icon_tasselWhite_${CommonManager.instance.isReverse() ? 'r' : 'l'}.png';
    imgLine = 'assets/images/rankings/icon_lineWhite.png';
    imgSrc_r = 'assets/images/rankings/icon_tasselWhite_${CommonManager.instance.isReverse() ? 'l' : 'r'}.png';
  }

  void setHeaderType(RankingType type) {
    setState(() {
      rankingsType = type;
    });
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    switch (rankingsType) {
      case RankingType.trending:
        beginColor = ColorsUtil.hexColor(0xFD04AF);
        endColor = ColorsUtil.hexColor(0xFF5150);
        title = 'rankings_trending'.tr;
        subTitle = "rankings_trending_desc".tr;
        break;
      case RankingType.newRelease:
        beginColor = ColorsUtil.hexColor(0x36A7FE);
        endColor = ColorsUtil.hexColor(0x3CCEFF);
        title = 'rankings_new'.tr;
        subTitle = "rankings_new_desc".tr;
        break;
      case RankingType.hotSearches:
        beginColor = ColorsUtil.hexColor(0xFF5942);
        endColor = ColorsUtil.hexColor(0xFF813C);
        title = 'rankings_searches'.tr;
        subTitle = "rankings_searches_desc".tr;
        break;
      case RankingType.favorites:
        beginColor = ColorsUtil.hexColor(0xF08D00);
        endColor = ColorsUtil.hexColor(0xFFB72D);
        title = 'rankings_favorites'.tr;
        subTitle = "rankings_favorites_desc".tr;
        break;
    }

    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(top: mediaQuery.padding.top + 15),
      decoration: BoxDecoration(
        gradient: LinearGradient(colors: [beginColor, endColor]),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              GestureDetector(
                onTap: () {
                  Get.back();
                },
                child: Container(
                  width: 42,
                  height: 22,
                  color: Colors.transparent,
                  alignment: Alignment.centerLeft,
                  padding: const EdgeInsets.only(left: 15),
                  child: Transform.rotate(
                    angle: CommonManager.instance.isReverse() ? math.pi : 0,
                    child: Image.asset(
                        "assets/images/common/icon_back_white.png",
                        width: 11,
                        height: 19),
                  ),
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Image.asset(
                    imgSrc_l,
                    width: 13,
                    height: 27,
                  ),
                  Text(
                    'Top50'.tr,
                    style: TextStyle(
                        color: HexColor("#FFFFFF"),
                        fontSize: 25,
                        fontWeight: FontWeight.bold),
                  ),
                  Image.asset(imgLine, width: 8, height: 12),
                  Text(title,
                      style: TextStyle(
                          fontSize: 17,
                          color: ColorsUtil.hexColor(0xFFFFFF),
                          fontWeight: FontWeight.bold)),
                  const SizedBox(width: 3),
                  Image.asset(
                    imgSrc_r,
                    width: 13,
                    height: 27,
                  ),
                ],
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.only(left: 42, top: 5, right: 42),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(subTitle,
                    style: TextStyle(
                        fontSize: 13, color: ColorsUtil.hexColor(0xFFFFFF)),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis),
              ],
            ),
          ),
          const SizedBox(height: 17),
        ],
      ),
    );
  }
}
