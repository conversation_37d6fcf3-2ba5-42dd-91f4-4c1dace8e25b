import 'package:flutter/cupertino.dart';
import 'package:UrNovel/Util/Common/no_load_view.dart';
import 'package:UrNovel/Util/Common/Animation/lottie_animation_view.dart';
import 'package:UrNovel/Util/Common/network_image_util.dart';
import 'package:UrNovel/Util/DataReportManager/event_report_manager.dart';
import 'package:UrNovel/Util/Extensions/colorUtil.dart';

import '../../../Util/DataReportManager/event_name_config.dart';
import '../../../Util/LocalNotificationManager/local_notification_manager.dart';
import '../../../Util/enum.dart';
import '../Model/ranking_model.dart';

class RankingsList extends StatefulWidget {
  final List<RankingListItem>? dataList;

  const RankingsList(
      {super.key, required this.dataList});

  @override
  State<RankingsList> createState() => RankingsListState();
}

class RankingsListState extends State<RankingsList> {

   late bool isLoading;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    isLoading = true;
  }

  void setDataList(List<Map<String, dynamic>> list) {
    setState(() {});
  }


  updateLoadingStatus(bool status) {
    setState(() {
      isLoading = status;
    });
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var count = widget.dataList?.length ?? 0;

    return Stack(
      children: [
        if (0 < count)
          Container(
            color: ColorsUtil.hexColor(0xFFFFFF),
            child: MediaQuery.removePadding(
              context: context,
              removeTop: true,
              removeBottom: true,
              removeLeft: true,
              removeRight: true,
              child: ListView.builder(
                itemCount: widget.dataList?.length,
                itemBuilder: (context, index) {
                  var item = widget.dataList![index];
                  return GestureDetector(
                    onTap: () async {
                      //todo:去往阅读页
                      await LocalNotificationManager.toNovelReadPage({
                        'bookId': item.id,
                        'jumpType': BookDetailJumpType.other
                      });
                      EventReportManager.eventReportOfFirebase(clickRanking);
                    },
                    child: Padding(
                      padding: EdgeInsets.fromLTRB(
                          16,
                          index == 0 ? 18 : 9,
                          16,
                          index == count - 1
                              ? mediaQuery.padding.bottom + 20
                              : 9),
                      child: Row(
                        children: [
                          NetworkImageUtil(
                              imageUrl: item.cover,
                              width: 80,
                              height: 100,
                              fit: BoxFit.contain),
                          Expanded(
                              child: Padding(
                                  padding: const EdgeInsets.only(left: 9),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        item.title ?? "",
                                        style: TextStyle(
                                            fontSize: 15,
                                            color: HexColor('#000000'),
                                            fontWeight: FontWeight.bold, fontFamily: 'Montserrat'),
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                      ),

                                      Text(item.authorName ?? "",
                                          style: TextStyle(
                                              fontSize: 13,
                                              color: HexColor('#555A65'))),
                                      Text(item.hotSearchesDesc ?? "",
                                          style: TextStyle(
                                              fontSize: 11,
                                              color: HexColor('#888C94'))),
                                    ],
                                  )))
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          )
        else if (!isLoading)
          NoLoadView(),
        if (isLoading)
          const LottieAnimationView()
      ],
    );
  }
}
