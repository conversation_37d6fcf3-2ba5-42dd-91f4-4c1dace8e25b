import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/MainPage/Rankings/Model/ranking_model.dart';
import 'package:UrNovel/MainPage/Rankings/ViewModel/ranking_viewModel.dart';
import 'package:UrNovel/Util/Common/no_load_view.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../../../Util/enum.dart';
import '../../../Util/DataReportManager/event_name_config.dart';
import '../../../Util/DataReportManager/event_report_manager.dart';
import '../../../Util/tools.dart';
import '../Widget/rankings_header.dart';
import '../Widget/rankings_list.dart';
import '../Widget/rankings_topType_list.dart';

class RankingsPage extends BaseFulWidget {
  RankingsPage({super.key, super.arguments});

  @override
  State<RankingsPage> createState() => _RankingsPageState();
}

class _RankingsPageState extends State<RankingsPage> {
  final rankingHeaderKey = GlobalKey<RankingHeaderState>();
  final rankingListKey = GlobalKey<RankingsListState>();

  late RankingType _currentListType;
  late bool isLoading;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    if (isAvailable(widget.arguments) && widget.arguments.containsKey('type')) {
      _currentListType = widget.arguments['type'];
    } else {
      _currentListType = RankingType.trending;
    }

    //加载数据,默认加载热门榜当
    getRankingData(_currentListType);

    setSystemUiLight();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }

  Future<void> getRankingData(RankingType type) async {
    rankingListKey.currentState?.updateLoadingStatus(true);
    _currentListType = type;
    var code = getRankingCode(type);
    // 确保在 build 方法中调用 getRankingData
    Provider.of<RankingViewModel>(context, listen: false).getRankingData(code);

    var eventName = top50Trending;
    if (type == RankingType.trending) {
      eventName = top50Trending;
    } else if (type == RankingType.newRelease) {
      eventName = top50New;
    } else if (type == RankingType.hotSearches) {
      eventName = top50Hot;
    } else if (type == RankingType.favorites) {
      eventName = top50Favorites;
    }
    await EventReportManager.eventReportOfFirebase(eventName);
  }

  List<RankingListItem>? getRankingDataList(RankingModel? model) {
    if (isAvailable(model)) {
      if (_currentListType == RankingType.trending) {
        return model?.result?.trendingList;
      } else if (_currentListType == RankingType.newRelease) {
        return model?.result?.newReleasesList;
      } else if (_currentListType == RankingType.hotSearches) {
        return model?.result?.hotSearchesList;
      } else if (_currentListType == RankingType.favorites) {
        return model?.result?.favoritesList;
      }
    }

    return null;
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var leftWidth = mediaQuery.size.width * 0.25;
    return PopScope(
        onPopInvokedWithResult: (didPop, result) {
          setSystemUiDark();
        },
        child: Consumer<RankingViewModel>(
          builder: (context, model, child) {
            rankingListKey.currentState?.updateLoadingStatus(false);
            return Scaffold(
              body: SizedBox(
                height: double.infinity,
                child: Column(
                  children: [
                    RankingHeader(key: rankingHeaderKey, rankingType: _currentListType),
                    Expanded(
                      child: Row(
                        children: [
                          SizedBox(
                            width: leftWidth,
                            height: double.infinity,
                            child: RankingsTypeList(
                                itemHeight: leftWidth / 1.06,
                                type: _currentListType,
                                onItemTap: (listType) {
                                  _currentListType = listType;
                                  rankingHeaderKey.currentState?.setHeaderType(listType);
                                  getRankingData(listType);
                                }),
                          ),
                          Expanded(
                              child: isAvailable(model.rankingModel)
                                  ? RankingsList(
                                      key: rankingListKey,
                                      dataList: getRankingDataList(model.rankingModel))
                                  : NoLoadView())
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ));
  }
}
