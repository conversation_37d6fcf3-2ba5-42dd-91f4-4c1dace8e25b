import 'package:json_annotation/json_annotation.dart';

part 'ranking_model.g.dart'; // 确保这部分正确

@JsonSerializable()
class RankingModel {
  int? code;
  RankingModelList? result;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  RankingModel({this.code, this.result, this.msg, this.sysAt, this.cost, this.traceId});

  factory RankingModel.fromJson(Map<String, dynamic> json) =>
      _$RankingModelFromJson(json);

  Map<String, dynamic> toJson() => _$RankingModelToJson(this);
}

//排行榜数据
@JsonSerializable()
class RankingModelList {
  List<RankingListItem>? hotSearchesList;
  List<RankingListItem>? trendingList;
  List<RankingListItem>? favoritesList;
  List<RankingListItem>? newReleasesList;

  RankingModelList({this.hotSearchesList, this.trendingList, this.favoritesList, this.newReleasesList});

  factory RankingModelList.fromJson(Map<String, dynamic> json) =>
      _$RankingModelListFromJson(json);

  Map<String, dynamic> toJson() => _$RankingModelListToJson(this);
}

//热搜榜
@JsonSerializable()
class RankingListItem{
     int? id;
     String? authorName;
     String? title;
     String? cover;
     String? hotSearchesDesc;

     RankingListItem({this.id, this.authorName, this.title, this.cover, this.hotSearchesDesc});

     factory RankingListItem.fromJson(Map<String, dynamic> json) =>
         _$RankingListItemFromJson(json);

     Map<String, dynamic> toJson() => _$RankingListItemToJson(this);
}