import 'package:flutter/cupertino.dart';
import 'package:UrNovel/Util/NetWorkManager/net_work_manager.dart';
import 'package:UrNovel/Util/api_config.dart';
import '../Model/ranking_model.dart';

class RankingViewModel extends ChangeNotifier {
  RankingModel _rankingModel = RankingModel();

  RankingModel get rankingModel => _rankingModel;

  Future getRankingData(String rankingCode) async {
    Map<String, dynamic> parameters = {'rankingCode': rankingCode};
    var response = await NetWorkManager.instance
        .post(apiRankingDataV2, parameters: parameters);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      _rankingModel = RankingModel.fromJson(response.data);
      // 通知 UI 更新
      notifyListeners();
    }
  }
}
