import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';

class SignInPopupAwardWidget extends BaseFulWidget {
  SignInPopupAwardWidget({super.key, required super.arguments});

  @override
  State<SignInPopupAwardWidget> createState() => SignInPopupAwardWidgetState();
}

class SignInPopupAwardWidgetState extends State<SignInPopupAwardWidget> {
  late int coins;
  late int days;

  @override
  void initState() {
    // 从传入的参数获取数据
    coins = widget.arguments['coins'] ?? 0;
    days = widget.arguments['days'] ?? 1;
    super.initState();
  }

  void claimReward() async {
    Get.back();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            decoration: BoxDecoration(
              color: const Color.fromARGB(0, 0, 0, 0),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 10),
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    child: Text(
                      // 'Check in for $days days!',
                      'check_in_for'.trParams({'param': days.toString()}),
                      style: TextStyle(
                        fontSize: 23,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                Text(
                  '+$coins ${'coins'.tr}',
                  style: const TextStyle(
                    fontSize: 35,
                    color: Color(0xFFFFE741),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 10),
                Stack(
                  alignment: Alignment.center,
                  children: [
                    Image.asset(
                      'assets/images/activity/signin/checkin_award_bg.png',
                      width: 193,
                      height: 193,
                    ),
                    Image.asset(
                      'assets/images/activity/signin/checkin_award_coins.png',
                      width: 194,
                      height: 194,
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                ElevatedButton(
                  onPressed: claimReward,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF0087FB),
                    minimumSize: const Size(250, 40),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    'claim'.tr,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
