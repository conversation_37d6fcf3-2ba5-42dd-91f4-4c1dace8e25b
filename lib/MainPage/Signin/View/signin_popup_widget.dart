import 'dart:async';

import 'package:UrNovel/MainPage/SignIn/View/signin_popup_award_widget.dart';
import 'package:UrNovel/MainPage/SignIn/View/signin_popup_widget_item.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Util/SheetAndAlter/toast.dart';
import 'package:UrNovel/Util/logUtil.dart';

import '../../../Launch&Login/Model/user_model.dart';
import '../../../Launch&Login/ViewModel/ViewModel.dart';
import '../../../Util/Common/Animation/lottie_animation_view.dart';
import '../../../Util/Common/no_load_view.dart';
import '../../../Util/LocalNotificationManager/local_notification_manager.dart';
import '../../../Util/tools.dart';
import '../Model/sign_in_model.dart';
import '../ViewModel/sign_in_view_model.dart';

class SignInPopUpWidget extends BaseFulWidget {
  SignInPopUpWidget({super.key});

  @override
  State<SignInPopUpWidget> createState() => SignInPopUpWidgetState();
}

class SignInPopUpWidgetState extends State<SignInPopUpWidget> {
  bool isLoading = true;
  bool loadFailed = false;
  int currentDay = 0;
  bool canSignIn = true;
  Timer? _timer;
  String countdownText = '';
  int countdownSeconds = 0; // 倒计时秒数
  List<int>? coinsRecord;
  SignInInfoResult? signInInfoResult;

  @override
  void initState() {
    super.initState();
    fetchSignInInfo();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  Future<SignInInfoResult?> fetchSignInInfo({bool isClickSign = false}) async {
    setState(() {
      isLoading = true;
      loadFailed = false;
    });
    SignInInfoResult? ret;
    try {
      SignInInfoResult? result = await SignInViewModel.getUserSignInInfo();
      ret = result;
      signInInfoResult = result;
      if (result != null) {
        setState(() {
          final count = result.count ?? 1;
          canSignIn = result.signInFlag ?? false;
          currentDay = count == 7
              ? count
              : canSignIn
                  ? count + 1
                  : count;

          coinsRecord = result.coinsRecord;

          if (!canSignIn && result.nextSignInSecond != null) {
            countdownSeconds = result.nextSignInSecond!;
            startCountdown();
          }

          isLoading = false;
        });

        if (isClickSign) {
          SignInInfoResult? signInInfoResult =
              await SignInViewModel.getUserSignInInfo();
          if (signInInfoResult != null &&
              signInInfoResult.nextSignInSecond != null) {
            logD("show notification  Check in");
            LocalNotificationManager.showNotification(
                title: "check_in".tr,
                body: "time_to_checkin".tr,
                payload: "signin",
                delayTime: signInInfoResult.nextSignInSecond);
          }
        }
      } else {
        setState(() {
          isLoading = false;
          loadFailed = true;
        });
      }
    } catch (e) {
      setState(() {
        isLoading = false;
        loadFailed = true;
      });
    }

    return ret;
  }

  bool isFinish = false;

  void startCountdown() {
    // countdownSeconds = 10;
    _timer = timerStartDateTime(1, countdownSeconds + 1, (dataStr, isEnd) {
      if (mounted) {
        setState(() {
          countdownText = dataStr;
          if (isEnd) {
            _timer?.cancel();
            fetchSignInInfo();
          }
        });
      }
    }, refreshRightNow: true);
  }

  Future<void> onSignIn() async {
    setState(() {
      isLoading = true;
    });

    try {
      bool success = await SignInViewModel.userSignIn();
      if (success) {
        List<Object?>? list;
        try {
          final results = await Future.wait([
            fetchSignInInfo(isClickSign: true),
            LoginViewModel.getUserInfo(),
          ]);
          list = results;
          final userInfo = results[1] as UserInfoModel;
          logD('User Info: $userInfo');
        } catch (e) {
          logD(e);
        }
        Get.back();

        if (list == null) {
          showToast("failed_checkin".tr);
        } else {
          // 显示奖励弹窗
          int rewardCoins =
              coinsRecord != null ? coinsRecord![coinsRecord!.length - 1] : 0;
          Get.dialog(
            SignInPopupAwardWidget(arguments: {
              'coins': rewardCoins,
              'days': currentDay,
            }),
            barrierColor: const Color.fromRGBO(0, 0, 0, 0.8), // 设置全屏背景的透明度
            barrierDismissible: true,
          );
        }
      } else {
        // 签到失败处理
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        isLoading = false;
        loadFailed = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const LottieAnimationView();
    }

    if (loadFailed) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          NoLoadView(),
          const SizedBox(height: 16),
          const SizedBox(height: 16),
          IconButton(
            icon: Image.asset(
              'assets/images/activity/signin/checkin_close_btn.png',
              width: 27,
              height: 27,
            ),
            onPressed: () {
              Get.back();
            },
          ),
        ],
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 305,
          height: 406,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            image: const DecorationImage(
              image: AssetImage(
                'assets/images/activity/signin/checkin_bg.png',
              ),
              fit: BoxFit.cover,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(height: 45),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 10),
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    'daily_checkin'.tr,
                    style: TextStyle(
                      fontSize: 20,
                      color: Color(0XFFAC5A20),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),

              SizedBox(height: 50),

              // 第一行签到项
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(4, (index) {
                    return SignInPopupWidgetItem(
                      day: index + 1,
                      currSigninDay: currentDay,
                      coins: coinsRecord != null && index < coinsRecord!.length
                          ? coinsRecord![index]
                          : 0,
                      canSignin: canSignIn,
                    );
                  }),
                ),
              ),

              SizedBox(height: 10),

              // 第二行签到项
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(3, (index) {
                    int day = index + 5;
                    return SignInPopupWidgetItem(
                      day: day,
                      currSigninDay: currentDay,
                      coins: coinsRecord != null &&
                              (index + 4) < coinsRecord!.length
                          ? coinsRecord![index + 4]
                          : 0,
                      canSignin: canSignIn,
                    );
                  }),
                ),
              ),

              SizedBox(height: 20),

              Text(
                // 'You have check in for ${signInInfoResult?.count ?? 0} days',
                'checked_in'.trParams(
                    {'param': (signInInfoResult?.count ?? 0).toString()}),
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0XFF7E839D),
                ),
              ),

              SizedBox(height: 10),

              // 签到按钮
              ElevatedButton(
                onPressed: canSignIn ? onSignIn : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: canSignIn
                      ? const Color(0xFF0087FB)
                      : const Color(0xFFEBECEE),
                  minimumSize: const Size(250, 40),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  canSignIn ? 'check_in'.tr : countdownText,
                  style: TextStyle(
                    color: canSignIn ? Colors.white : Color(0XFF1F1F2F),
                    fontSize: 16,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        IconButton(
          icon: Image.asset(
            'assets/images/activity/signin/checkin_close_btn.png',
            width: 27,
            height: 27,
          ),
          onPressed: () {
            Get.back();
          },
        ),
      ],
    );
  }
}
