import 'package:flutter/material.dart';
import 'package:get/utils.dart';

class SignInPopupWidgetItem extends StatelessWidget {
  final int day;
  final int currSigninDay;
  final int coins;
  final bool canSignin;

  const SignInPopupWidgetItem({
    super.key,
    required this.day,
    required this.currSigninDay,
    required this.coins,
    required this.canSignin,
  });

  @override
  Widget build(BuildContext context) {
    bool isSigninDay = day == currSigninDay;
    bool isThisDaySignined = !canSignin && isSigninDay;
    bool isSignined = (day < currSigninDay) || isThisDaySignined;
    bool isDay3 = day == 3;
    bool isDay7 = day == 7;

    String coinsStr = !isSignined ? '???' : coins.toString();

    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          width: isDay7 ? 139 : 66,
          height: 82,
          margin: const EdgeInsets.only(left: 3, right: 3),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: (isSigninDay && (!isDay3 && !isDay7))
                ? Colors.white
                : isDay3
                    ? null
                    : isDay7
                        ? null
                        : Color(0XFFF8F8F8),
            gradient: isDay3
                ? LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color(0XFFFDB969),
                      Color(0xFFFF6B64),
                    ],
                  )
                : isDay7
                    ? LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Color(0XFFFFD14D),
                          Color(0XFFFFA736),
                        ],
                      )
                    : null,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment:
                isDay7 ? CrossAxisAlignment.start : CrossAxisAlignment.center,
            children: [
              SizedBox(height: 2),
              Padding(
                padding: EdgeInsets.only(left: isDay7 ? 15 : 0),
                child: Text(
                  // 'Day $day',
                  'day'.trParams({'param': day.toString()}),
                  style: TextStyle(
                    fontSize: 12,
                    color: Color(0XFF1F1F2F),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(height: 5),
              isDay3
                  ? Image.asset(
                      'assets/images/activity/signin/checkin_day3_icon.png',
                      width: 60,
                      height: 36,
                      fit: BoxFit.contain,
                    )
                  : isDay7
                      ? SizedBox(height: 0)
                      : Image.asset(
                          'assets/images/activity/signin/checkin_day_icon.png',
                          width: 43,
                          height: 30,
                          fit: BoxFit.contain,
                        ),
              SizedBox(height: (isDay3 || isDay7) ? 2 : 8),
              Padding(
                padding: EdgeInsets.only(left: isDay7 ? 15 : 0),
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    // '$coinsStr ${'coins'.tr}',
                    coinsStr,
                    style: TextStyle(
                      fontSize: 11,
                      color: (isDay3 || isDay7)
                          ? Colors.white
                          : isSignined
                              ? Color(0xFFC79648)
                              : Color(0XFF7E839D),
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ),
              SizedBox(height: 2),
            ],
          ),
        ),

        //拉九宫格    1
        if (isSigninDay)
          Positioned(
            left: -7,
            top: -10,
            child: Container(
              width: isDay7 ? 160 : 87,
              height: 103,
              decoration: const BoxDecoration(
                // color: Colors.red,
                image: DecorationImage(
                  image: AssetImage(
                      'assets/images/activity/signin/checkin_current_day.png'),
                  centerSlice: Rect.fromLTWH(20, 40, 10, 10),
                  fit: BoxFit.fill,
                ),
              ),
            ),
          ),

        //第7天的金币图片
        if (isDay7)
          Positioned(
            bottom: 4,
            right: -4,
            child: Image.asset(
              'assets/images/activity/signin/checkin_day7_icon.png',
              width: 88,
              height: 66,
              fit: BoxFit.contain,
            ),
          ),

        //已签到了的遮罩
        if (isSignined)
          Container(
            width: isDay7 ? 139 : 66,
            height: 82,
            margin: const EdgeInsets.only(left: 3, right: 3),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Color.fromRGBO(255, 241, 210, 0.8),
            ),
          ),
        if (isSignined)
          Positioned(
            top: -1,
            right: -1,
            child: SizedBox(
              width: 18,
              height: 18,
              child: Image.asset(
                  'assets/images/activity/signin/checkin_checked.png'),
            ),
          ),
      ],
    );
  }
}
