import 'package:UrNovel/Util/logUtil.dart';

import '../../../Util/NetWorkManager/net_work_manager.dart';
import '../../../Util/api_config.dart';
import '../Model/sign_in_model.dart';

class SignInViewModel {
  static SignInModel? _signInModel ;
  static UserSignInModel? _userSignInModel;

  static SignInModel? get signInModel => _signInModel;
  static UserSignInModel? get userSignInModel => _userSignInModel;

  /// 获取用户签到信息
  static Future<SignInInfoResult?> getUserSignInInfo() async {
    logD("===>>> getUserSignInInfo");
    var response =
        await NetWorkManager.instance.get(apiGetUserSignInInfo, params: {});
    logD("<<<=== getUserSignInInfo  $response");

    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      _signInModel = SignInModel.fromJson(response.data);
      return _signInModel?.result;
    }

    return null;
  }

  /// 用户签到
  static Future<bool> userSignIn() async {
    logD("===>>> userSignIn");
    var response = await NetWorkManager.instance.get(apiUserSignIn, params: {});
    logD("<<<=== userSignIn $response");

    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      _userSignInModel = UserSignInModel.fromJson(response.data);
      return _userSignInModel?.result ?? false;
    }

    return false;
  }
}
