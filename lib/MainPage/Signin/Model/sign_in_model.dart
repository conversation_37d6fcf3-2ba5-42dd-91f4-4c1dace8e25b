import 'package:json_annotation/json_annotation.dart';

part 'sign_in_model.g.dart';

@JsonSerializable()
class SignInModel {
  int? code; //状态码
  SignInInfoResult? result; //签到信息结果
  String? msg; //返回信息
  int? sysAt; //时间
  int? cost; //耗时
  String? traceId; //日志跟踪id

  SignInModel(
      {this.code, this.result, this.msg, this.sysAt, this.cost, this.traceId});

  factory SignInModel.fromJson(Map<String, dynamic> json) =>
      _$SignInModelFromJson(json);

  Map<String, dynamic> toJson() => _$SignInModelToJson(this);
}

@JsonSerializable()
class SignInInfoResult {
  int? count; //签到次数
  List<int>? coinsRecord; //签到金币记录
  bool? signInFlag; //是否已签到
  int? nextSignInSecond; //下次签到时间（秒）

  SignInInfoResult(
      {this.count, this.coinsRecord, this.signInFlag, this.nextSignInSecond});

  factory SignInInfoResult.fromJson(Map<String, dynamic> json) =>
      _$SignInInfoResultFromJson(json);

  Map<String, dynamic> toJson() => _$SignInInfoResultToJson(this);
}

@JsonSerializable()
class UserSignInModel {
  int? code; //状态码
  bool? result; //签到结果
  String? msg; //返回信息
  int? sysAt; //时间
  int? cost; //耗时
  String? traceId; //日志跟踪id

  UserSignInModel(
      {this.code, this.result, this.msg, this.sysAt, this.cost, this.traceId});

  factory UserSignInModel.fromJson(Map<String, dynamic> json) =>
      _$UserSignInModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserSignInModelToJson(this);
} 