import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';

import '../../../../../Util/Extensions/colorUtil.dart';

class BookDetailsItem extends BaseFulWidget {
  BookDetailsItem({super.key, super.arguments});

  @override
  State<BookDetailsItem> createState() => _BookDetailsItemState();
}

class _BookDetailsItemState extends State<BookDetailsItem> {
  @override
  Widget build(BuildContext context) {
    String? title = widget.arguments['title'];
    int? value = int.tryParse(widget.arguments['value'].toString());

    var hour = 0, minute = 0;
    if (title == 'time'.tr && value != null) {
      hour = value ~/ 3600;
      minute = (value - hour * 3600) ~/ 60;
    }

    if (title != null && value != null) {
      if (title == 'time'.tr) {
        if (0 < hour && 0 < minute) {
          return Column(
            children: [
              Text(title,
                  style: TextStyle(fontSize: 13, color: HexColor('#888C94'))),
              const SizedBox(height: 15),
              Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text('$hour',
                      style: TextStyle(
                          fontSize: 23,
                          color: HexColor('#555A65'),
                          fontWeight: FontWeight.bold,
                          height: 0.8)),
                  Text('h',
                      style: TextStyle(
                          fontSize: 12,
                          color: HexColor('#555A65'),
                          fontWeight: FontWeight.bold,
                          height: 0.8)),
                  Text('$minute',
                      style: TextStyle(
                          fontSize: 23,
                          color: HexColor('#555A65'),
                          fontWeight: FontWeight.bold,
                          height: 0.8)),
                  Text('m',
                      style: TextStyle(
                          fontSize: 12,
                          color: HexColor('#555A65'),
                          fontWeight: FontWeight.bold,
                          height: 0.8)),
                ],
              )
            ],
          );
        } else if (0 < hour) {
          return Column(
            children: [
              Text(title,
                  style: TextStyle(fontSize: 13, color: HexColor('#888C94'))),
              const SizedBox(height: 15),
              Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text('$hour',
                      style: TextStyle(
                          fontSize: 23,
                          color: HexColor('#555A65'),
                          fontWeight: FontWeight.bold,
                          height: 0.8)),
                  Text('h',
                      style: TextStyle(
                          fontSize: 12,
                          color: HexColor('#555A65'),
                          fontWeight: FontWeight.bold,
                          height: 0.8)),
                ],
              )
            ],
          );
        } else if (0 < minute) {
          return Column(
            children: [
              Text(title,
                  style: TextStyle(fontSize: 13, color: HexColor('#888C94'))),
              const SizedBox(height: 15),
              Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text('$minute',
                      style: TextStyle(
                          fontSize: 23,
                          color: HexColor('#555A65'),
                          fontWeight: FontWeight.bold,
                          height: 0.8)),
                  Text('m',
                      style: TextStyle(
                          fontSize: 12,
                          color: HexColor('#555A65'),
                          fontWeight: FontWeight.bold,
                          height: 0.8)),
                ],
              )
            ],
          );
        } else {
          return Column(
            children: [
              Text(title,
                  style: TextStyle(fontSize: 13, color: HexColor('#888C94'))),
              const SizedBox(height: 15),
              Text('0',
                  style: TextStyle(
                      fontSize: 23,
                      color: HexColor('#555A65'),
                      fontWeight: FontWeight.bold,
                      height: 0.8)),
            ],
          );
        }
      } else {
        return Column(
          children: [
            Text(title,
                style: TextStyle(fontSize: 13, color: HexColor('#888C94'))),
            const SizedBox(height: 15),
            if (value < 1000)
              Text('$value',
                  style: TextStyle(
                      fontSize: 23,
                      color: HexColor('#555A65'),
                      fontWeight: FontWeight.bold,
                      height: 0.8))
            else
              Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text((value / 1000).toStringAsFixed(1),
                      style: TextStyle(
                          fontSize: 23,
                          color: HexColor('#555A65'),
                          fontWeight: FontWeight.bold,
                          height: 0.8)),
                  Text('k',
                      style: TextStyle(
                          fontSize: 12,
                          color: HexColor('#555A65'),
                          fontWeight: FontWeight.bold,
                          height: 0.8))
                ],
              )
          ],
        );
      }
    } else {
      return Column(
        children: [
          Text('未知',
              style: TextStyle(fontSize: 13, color: HexColor('#888C94'))),
          const SizedBox(height: 5),
          Text('0',
              style: TextStyle(
                  fontSize: 23,
                  color: HexColor('#555A65'),
                  fontWeight: FontWeight.bold,
                  height: 0.8)),
        ],
      );
    }
  }
}
