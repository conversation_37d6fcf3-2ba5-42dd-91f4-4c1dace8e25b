import 'package:flutter/cupertino.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';

import 'book_details_item.dart';

class BookDetailsData extends BaseFulWidget {
  BookDetailsData({super.key, super.arguments});

  @override
  State<BookDetailsData> createState() => _BookDetailsDataState();
}

class _BookDetailsDataState extends State<BookDetailsData> {

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(34, 17, 34, 0),
      child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            BookDetailsItem(arguments: widget.arguments[0]),
            BookDetailsItem(arguments: widget.arguments[1]),
            BookDetailsItem(arguments: widget.arguments[2]),
          ],
    ),
    );
  }
}
