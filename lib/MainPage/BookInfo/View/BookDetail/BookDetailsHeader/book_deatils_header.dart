import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/MainPage/NovelRead/Model/book_detailInfo_model.dart';
import 'package:UrNovel/Util/Common/network_image_util.dart';
import 'package:UrNovel/Util/DataReportManager/event_report_manager.dart';

import '../../../../../Util/DataReportManager/event_name_config.dart';
import '../../../../../Util/Extensions/colorUtil.dart';

class BookDetailsHeader extends BaseFulWidget {
  final BookDetailInfoResultModel? bookDetailInfo;

  BookDetailsHeader({super.key, required this.bookDetailInfo});

  @override
  State<BookDetailsHeader> createState() => _BookDetailsHeaderState();
}

class _BookDetailsHeaderState extends State<BookDetailsHeader> {
  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var imgWidth = mediaQuery.size.width * 0.3;
    var imgHeight = imgWidth / 0.75;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      child: SizedBox(
        width: double.infinity,
        height: imgHeight,
        child: Row(
          children: [
            NetworkImageUtil(
              imageUrl: widget.bookDetailInfo?.cover ?? "",
              width: imgWidth,
              height: imgHeight,
            ),
            Expanded(
                child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                            child: Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            widget.bookDetailInfo?.title ?? "--",
                            maxLines: 4,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                                fontSize: 19,
                                color: HexColor('#000000'),
                                fontWeight: FontWeight.bold),
                          ),
                        )),
                        const SizedBox(height: 10),
                        GestureDetector(
                          onTap: () async {
                            EventReportManager.eventReportOfFirebase(
                                openAuthor);
                            await Get.toNamed('/bookAuthorPage', arguments: {
                              "authorId": widget.bookDetailInfo?.authorId
                            });
                          },
                          child: Text(widget.bookDetailInfo?.authorName ?? "--",
                              style: TextStyle(
                                  fontSize: 14, color: HexColor('#176DE4'))),
                        ),
                        const SizedBox(height: 26),
                      ],
                    ))),
          ],
        ),
      ),
    );
  }
}
