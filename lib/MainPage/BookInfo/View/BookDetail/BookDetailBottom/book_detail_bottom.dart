import 'package:UrNovel/Util/Common/CommonManager/common_manager.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:UrNovel/MainPage/NovelRead/Model/book_detailInfo_model.dart';
import '../../../../../Util/Common/Model/normal_model.dart';
import '../../../../../Util/DataReportManager/event_name_config.dart';
import '../../../../../Util/DataReportManager/event_report_manager.dart';
import '../../../../../Util/Extensions/colorUtil.dart';
import '../../../../Library/ViewModel/ViewModel.dart';

class BookDetailBottom extends StatefulWidget {
  final BookDetailInfoResultModel? detailInfoModel;
  final VoidCallback? turnToReadPage;

  const BookDetailBottom(
      {super.key, required this.detailInfoModel, required this.turnToReadPage});

  @override
  State<BookDetailBottom> createState() => _BookDetailBottomState();
}

class _BookDetailBottomState extends State<BookDetailBottom> {

  @override
  void initState() {
    super.initState();
  }

  ///添加到书架
  addBookToLibrary() async {
    await CommonManager.instance.addBookToLibrary(widget.detailInfoModel?.bookId, 1, (isSuccess){
      if (isSuccess) {
        setState(() {
          widget.detailInfoModel?.library = true;
        });
      }
    });

    EventReportManager.eventReportOfFirebase(clickReadLibrary);
  }

  ///从书架移除
  cancelBookFromLibrary() async {
    await CommonManager.instance.cancelBookFromLibrary(widget.detailInfoModel?.bookId, 1, (isSuccess){
      if (isSuccess) {
        setState(() {
          widget.detailInfoModel?.library = false;
        });
      }
    });

    EventReportManager.eventReportOfFirebase(clickReadRemove);
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var width = (mediaQuery.size.width - 35) / 2.0;
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 40,
          width: width,
          child: TextButton(
            style: ButtonStyle(
              backgroundColor: WidgetStateProperty.all(HexColor('#F6F6F6')),
              shape: WidgetStateProperty.all(RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10))),
            ),
            onPressed: () {
              if (widget.detailInfoModel?.library == true) {
                cancelBookFromLibrary();
              }else{
                addBookToLibrary();
              }
            },
            child: Text(
                widget.detailInfoModel?.library == true ? 'library_added'.tr : 'library_add'.tr,
                style: TextStyle(
                    fontSize: 15,
                    color: HexColor('#4D4F56'),
                    fontWeight: FontWeight.bold)),
          ),
        ),
        SizedBox(
          height: 40,
          width: width,
          child: TextButton(
            style: ButtonStyle(
              backgroundColor: WidgetStateProperty.all(HexColor('#008CFB')),
              shape: WidgetStateProperty.all(RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10))),
            ),
            onPressed: widget.turnToReadPage,
            child: Text('read_now'.tr,
                style: TextStyle(
                    fontSize: 15,
                    color: HexColor('#FFFFFF'),
                    fontWeight: FontWeight.bold)),
          ),
        ),
      ],
    );
  }
}
