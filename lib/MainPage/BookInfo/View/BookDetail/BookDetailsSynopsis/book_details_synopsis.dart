import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Util/DataReportManager/event_report_manager.dart';
import 'package:UrNovel/Util/tools.dart';

import '../../../../../Util/Common/CommonManager/common_manager.dart';
import '../../../../../Util/DataReportManager/event_name_config.dart';
import '../../../../../Util/Extensions/colorUtil.dart';
import 'dart:math' as math;

class BookDetailsSynopsis extends BaseFulWidget {
  final String? description;

  BookDetailsSynopsis({super.key, required this.description});

  @override
  State<BookDetailsSynopsis> createState() => _BookDetailsSynopsisState();
}

class _BookDetailsSynopsisState extends State<BookDetailsSynopsis> {
  double angle = 0.0;
  bool isExpanded = false;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    if (isAvailable(widget.description)){
      var width = MediaQuery.of(context).size.width;
      var btHeight = width * 0.83 / 7.5;
      return Container(
        margin: const EdgeInsets.only(top: 20, left: 16, right: 16),
        decoration: BoxDecoration(
          color: HexColor('#FFFFFF'),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 14, left: 14, right: 14),
              child: Text('synopsis'.tr,
                  style: TextStyle(
                      fontSize: 17,
                      color: HexColor('#000000'),
                      fontWeight: FontWeight.bold)),
            ),
            Padding(
              padding: const EdgeInsets.only(top: 14, left: 14, right: 14),
              child: RichText(
                  text: TextSpan(
                    text: widget.description ?? "",
                    style: TextStyle(
                      fontSize: 16.0,
                      color: HexColor("#000000"),
                      fontWeight: FontWeight.bold,
                      height: 1.5,
                    ),
                  ),
                  textAlign: TextAlign.left,
                  maxLines: !isExpanded ? 3 : null,
                  overflow:
                  !isExpanded ? TextOverflow.ellipsis : TextOverflow.visible),
            ),
            GestureDetector(
              onTap: () {
                if (!isExpanded) {
                  EventReportManager.eventReportOfFirebase(clickReadMore);
                }

                setState(() {
                  angle = angle == 0.0 ? 3.141592653589793 : 0.0;
                  isExpanded = !isExpanded;
                });
              },
              child: Container(
                height: btHeight,
                margin: const EdgeInsets.only(
                    top: 20, left: 14, right: 14, bottom: 14),
                decoration: BoxDecoration(
                  color: HexColor('#F4F5F6'),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(isExpanded ? 'show_less'.tr : 'read_more'.tr,
                        style: TextStyle(
                            fontSize: 16,
                            color: HexColor('#4D4F56'),
                            fontWeight: FontWeight.bold)),
                    const SizedBox(width: 5),
                    Transform.rotate(
                      angle: angle,
                      child: Transform.rotate(
                        angle: CommonManager.instance.isReverse() ? math.pi : 0,
                        child: Image.asset(
                          'assets/images/profile/icon_arrow.png',
                          width: 10,
                          height: 5,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    }else{
      return Container();
    }
  }
}
