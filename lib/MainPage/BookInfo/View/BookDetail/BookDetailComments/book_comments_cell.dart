import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Launch&Login/Model/user_model.dart';
import 'package:UrNovel/Util/Common/network_image_util.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../Util/Extensions/colorUtil.dart';
import '../../../../../Util/SharedPreferences/shared_preferences.dart';
import '../../../../../Util/tools.dart';
import '../../../../Secondary/View/comment_reply_list.dart';
import '../../../Model/book_comments_model.dart';
import '../RatingsComments/ratings_stars.dart';

class BookCommentsCell extends BaseFulWidget {
  final BookCommentsItem? item;
  final bool isSecondaryCommentList;
  final Function(int, int?) onItemTap;
  final Function(dynamic item)? onReplyTap;

  BookCommentsCell(
      {super.key,
      required this.item,
      this.isSecondaryCommentList = false,
      required this.onItemTap,
      this.onReplyTap});

  @override
  State<BookCommentsCell> createState() => _BookCommentsCellState();
}

class _BookCommentsCellState extends State<BookCommentsCell> {
  UserInfoModel? _userInfoModel;
  bool _isMyComment = false;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    getUserInfo();
  }

  Future<void> getUserInfo() async {
    _userInfoModel = await SpUtil.spGetUserInfo();
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    _isMyComment = _userInfoModel?.uid == widget.item?.uid;
    return Column(
      children: [
        //todo:跟人信息相关
        Padding(
          padding: const EdgeInsets.only(top: 13, left: 15, right: 15),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(
                      child: SizedBox(
                        width: mediaQuery.size.width - 32 - 30 - 100,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            //头像
                            NetworkImageUtil(
                              imageUrl: widget.item?.avatar,
                              width: 25,
                              height: 25,
                              w: 32,
                              h: 32,
                              placeholder: getSexAvatarStr(_userInfoModel?.sex),
                              errorHolder: getSexAvatarStr(_userInfoModel?.sex),
                              isCircle: true,
                            ),
                            const SizedBox(width: 6),
                            Flexible(
                              child: Text(
                                widget.item?.name ?? "",
                                style:
                                    TextStyle(fontSize: 14, color: HexColor('#555A65'), height: 1),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const SizedBox(width: 8),
                            RatingStars(
                                rating: widget.item?.score?.toDouble() ?? 0,
                                size: const Size(10, 10),
                                padding: 5),
                          ],
                        ),
                      ),
                    ),
                    if (_isMyComment)
                      Container(
                        height: 22,
                        padding: const EdgeInsets.symmetric(horizontal: 9, vertical: 4),
                        decoration: BoxDecoration(
                          color: HexColor('#F5F5F5'),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        alignment: Alignment.center,
                        child: Text(
                          "my_Comments".tr,
                          style: TextStyle(
                              fontSize: 10,
                              color: HexColor('#555A65'),
                              fontWeight: FontWeight.bold,
                              height: 1),
                        ),
                      ),
                  ],
                ),
              ),
              const SizedBox(width: 40),
              GestureDetector(
                  onTap: () {
                    widget.onItemTap(_isMyComment ? 1 : 2, widget.item?.id);
                  },
                  behavior: HitTestBehavior.opaque,
                  child: Image.asset(
                    _isMyComment
                        ? 'assets/images/bookDetails/ratingCommend/icon_delete.png'
                        : 'assets/images/bookDetails/ratingCommend/icon_report.png',
                    width: 14,
                    height: 13,
                  ))
            ],
          ),
        ),
        //todo:评论
        Container(
          padding: const EdgeInsets.only(left: 15, right: 15, top: 10),
          alignment: Alignment.centerLeft,
          child: Text(widget.item?.content ?? "",
              style: TextStyle(
                  fontSize: 15,
                  color: HexColor('#000000'),
                  fontWeight: FontWeight.bold,
                  height: 1.5)),
        ),
        //todo:分割线
        Padding(
          padding: const EdgeInsets.only(left: 15, right: 15, top: 13),
          child: Divider(height: 1, color: HexColor('#EDEDED')),
        ),
        //todo:评论&编辑操作
        Padding(
            padding: const EdgeInsets.only(top: 15, left: 22, bottom: 15, right: 16),
            child: Row(
              children: [
                Text(getTimeDifference(widget.item!.addTime ?? 0),
                    style: TextStyle(
                        fontSize: 12, color: HexColor('#555A65'), fontWeight: FontWeight.bold)),
                if (_isMyComment) const SizedBox(width: 40),
                if (_isMyComment)
                  GestureDetector(
                      onTap: () {
                        widget.onItemTap(0, widget.item?.id);
                      },
                      behavior: HitTestBehavior.opaque,
                      child: Text('edit'.tr,
                          style: TextStyle(
                              fontSize: 12,
                              color: HexColor('#1B86FF'),
                              fontWeight: FontWeight.bold))),
                const Spacer(),
                GestureDetector(
                    onTap: () {
                      widget.onReplyTap?.call(widget.item);
                    },
                    behavior: HitTestBehavior.opaque,
                    child: Row(
                      children: [
                        Image.asset(
                          'assets/images/bookDetails/ratingCommend/icon_reply.png',
                          width: 11,
                          height: 10,
                        ),
                        const SizedBox(width: 9),
                        Text('reply'.tr,
                            style: TextStyle(
                                fontSize: 12,
                                color: HexColor('#555A65'),
                                fontWeight: FontWeight.bold)),
                        if (isAvailable(widget.item?.replyCount))
                          Padding(
                            padding: const EdgeInsets.only(left: 9),
                            child: Text(widget.item?.replyCount.toString() ?? "0",
                                style: TextStyle(
                                    fontSize: 12,
                                    color: HexColor('#555A65'),
                                    fontWeight: FontWeight.bold)),
                          ),
                      ],
                    ))
              ],
            )),
        if (widget.isSecondaryCommentList)
          //todo:二级互评列表
          CommentReplyList(
            item: widget.item,
            onItemTap: widget.onItemTap,
            onReplyTap: (item) {
              widget.onReplyTap?.call(item);
            },
          )
      ],
    );
  }
}
