import 'dart:math' as math;

import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../Util/Common/CommonManager/common_manager.dart';
import '../../../../../Util/Extensions/colorUtil.dart';
import '../../../Model/book_comments_model.dart';

class BookCommentsHeader extends BaseFulWidget {
  final BookCommentsRatingModel? commentsModel;
  final EdgeInsets edgeInsets;
  final VoidCallback? onCommentListTap;

  BookCommentsHeader(
      {super.key,
      required this.commentsModel,
      required this.edgeInsets,
      required this.onCommentListTap});

  @override
  State<BookCommentsHeader> createState() => _BookCommentsHeaderState();
}

class _BookCommentsHeaderState extends State<BookCommentsHeader> {
  @override
  Widget build(BuildContext context) {
    if (isAvailable(widget.commentsModel?.total) &&
        0 < widget.commentsModel!.total!) {
      return GestureDetector(
          onTap: widget.onCommentListTap,
          child: Container(
            margin: widget.edgeInsets,
            alignment: Alignment.centerLeft,
            color: Colors.transparent,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'comments'.tr,
                  style: TextStyle(
                      fontSize: 17,
                      color: HexColor('#000000'),
                      fontWeight: FontWeight.bold,
                      height: 1),
                ),
                const SizedBox(
                  width: 10,
                ),
                Text(
                  '-',
                  style: TextStyle(fontSize: 13, color: HexColor('#888C94')),
                ),
                const SizedBox(
                  width: 10,
                ),
                Text(
                  widget.commentsModel?.total.toString() ?? '0',
                  style: TextStyle(
                      fontSize: 13,
                      color: HexColor('#888C94'),
                      fontWeight: FontWeight.bold),
                ),
                const SizedBox(width: 8),
                Transform.rotate(
                  angle: CommonManager.instance.isReverse() ? math.pi : 0,
                  child: Image.asset(
                    'assets/images/common/icon_arrow_gray.png',
                    width: 5,
                    height: 8,
                  ),
                )
              ],
            ),
          ));
    }
    return Container();
  }
}
