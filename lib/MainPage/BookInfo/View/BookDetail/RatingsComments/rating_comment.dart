import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../Util/Extensions/colorUtil.dart';

class RatingComment extends BaseFulWidget {
  final VoidCallback? onRatingCommentTap;
  RatingComment({super.key, required this.onRatingCommentTap});

  @override
  State<RatingComment> createState() => _RatingCommentState();
}

class _RatingCommentState extends State<RatingComment> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 14, left: 15, right: 15),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
              child: Text('ratings_comments'.tr,
                  style: TextStyle(
                      fontSize: 17, color: HexColor('#000000'), fontWeight: FontWeight.bold))),
          Sized<PERSON>ox(
            height: 28,
            child: OutlinedButton(
              style: ButtonStyle(
                shape: WidgetStateProperty.all(RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                )),
                side: WidgetStateProperty.all(BorderSide(
                  color: HexColor('#D7D7D7'), // 设置边框颜色
                  width: 0.5, // 设置边框宽度
                )),
                padding: WidgetStateProperty.all(
                    const EdgeInsets.symmetric(horizontal: 10)), // 设置左右空白区域大小
              ),
              onPressed: widget.onRatingCommentTap,
              child: Text('comment'.tr,
                  style: TextStyle(
                      fontSize: 12, color: HexColor('#555A65'), fontWeight: FontWeight.bold)),
            ),
          )
        ],
      ),
    );
  }
}
