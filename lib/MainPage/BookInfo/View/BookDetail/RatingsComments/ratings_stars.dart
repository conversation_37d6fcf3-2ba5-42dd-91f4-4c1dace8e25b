import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class RatingStars extends BaseFulWidget {
  final double rating;
  final int maxStars;
  final Size size;
  final double padding;

  RatingStars(
      {super.key,
      required this.rating,
      this.maxStars = 5,
      this.size = const Size(20, 20),
      this.padding = 10});

  @override
  State<RatingStars> createState() => _RatingStarsState();
}

class _RatingStarsState extends State<RatingStars> {
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(widget.maxStars, (index) {
        if (index < widget.rating.floor()) {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset('assets/images/bookDetails/icon_star.png',
                  width: widget.size.width, height: widget.size.height),
              SizedBox(width: widget.padding),
            ],
          );
        } else if (index == widget.rating.floor() && widget.rating % 1 != 0) {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset('assets/images/bookDetails/icon_star_half.png',
                  width: widget.size.width, height: widget.size.height),
              SizedBox(width: widget.padding),
            ],
          );
        } else {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(
                  0 < widget.rating
                      ? 'assets/images/bookDetails/icon_star_empty.png'
                      : 'assets/images/bookDetails/ratingCommend/icon_star_normal.png',
                  width: widget.size.width,
                  height: widget.size.height),
              SizedBox(width: widget.padding),
            ],
          );
        }
      }),
    );
  }
}
