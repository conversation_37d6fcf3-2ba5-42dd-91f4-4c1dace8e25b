import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/MainPage/BookInfo/View/BookDetail/RatingsComments/rating_comment.dart';
import 'package:UrNovel/MainPage/BookInfo/View/BookDetail/RatingsComments/rating_leftPart.dart';
import 'package:UrNovel/MainPage/BookInfo/View/BookDetail/RatingsComments/rating_rightPart.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:flutter/material.dart';

import '../../../../../Util/Extensions/colorUtil.dart';
import '../../../Model/book_comments_model.dart';

class RatingsComments extends BaseFulWidget {
  final BookCommentsRatingModel? commentsRatingModel;
  final VoidCallback? onRatingCommentTap;
  final VoidCallback? onCommentListTap;

  RatingsComments(
      {super.key,
      required this.commentsRatingModel,
      required this.onRatingCommentTap,
      required this.onCommentListTap});

  @override
  State<RatingsComments> createState() => RatingsCommentsState();
}

class RatingsCommentsState extends State<RatingsComments> {
  @override
  Widget build(BuildContext context) {
    if (isAvailable(widget.commentsRatingModel)) {
      var mediaQuery = MediaQuery.of(context);
      var ratingHeight = mediaQuery.size.width / 2.2;
      return GestureDetector(
          onTap: widget.onRatingCommentTap,
          child: Container(
            margin: const EdgeInsets.only(top: 20, left: 16, right: 16),
            width: double.infinity,
            height: ratingHeight,
            decoration: BoxDecoration(
              color: HexColor('#FFFFFF'),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                //todo:标题部分
                RatingComment(onRatingCommentTap: widget.onRatingCommentTap),

                //todo:评分部分
                Expanded(
                    child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    //todo:评分部分左侧
                    Expanded(
                      flex: 4,
                      child: RatingLeftPart(
                          commentScoreCount: widget.commentsRatingModel?.commentScoreCount,
                          avgScore: widget.commentsRatingModel!.avgScore,
                          onCommentListTap: widget.onCommentListTap),
                    ),

                    //todo:评分部分右侧
                    Expanded(
                      flex: 6,
                      child: RatingRightPart(commentsRatingModel: widget.commentsRatingModel),
                    ),
                  ],
                )),
              ],
            ),
          ));
    } else {
      return Container();
    }
  }
}
