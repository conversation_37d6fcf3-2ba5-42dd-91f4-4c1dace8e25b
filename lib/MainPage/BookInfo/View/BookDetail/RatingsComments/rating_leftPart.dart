import 'dart:math' as math;

import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Util/Common/CommonManager/common_manager.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../Util/Extensions/colorUtil.dart';
import '../../../../../Util/tools.dart';

class RatingLeftPart extends BaseFulWidget {
  final int? commentScoreCount;
  final int? avgScore;
  final VoidCallback? onCommentListTap;

  RatingLeftPart(
      {super.key,
      required this.commentScoreCount,
      required this.avgScore,
      required this.onCommentListTap});

  @override
  State<RatingLeftPart> createState() => _RatingLeftPartState();
}

class _RatingLeftPartState extends State<RatingLeftPart> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 22, left: 24, right: 24, bottom: 22),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (isAvailable(widget.avgScore) && widget.avgScore != 0)
            Padding(
                padding: const EdgeInsets.only(left: 9),
                child: Text(widget.avgScore!.toStringAsFixed(1),
                    style: TextStyle(
                        fontSize: 35,
                        color: HexColor('#1B86FF'),
                        fontWeight: FontWeight.bold,
                        height: 0.8))),
          if (isAvailable(widget.avgScore) && widget.avgScore != 0)
            GestureDetector(
              onTap: widget.onCommentListTap,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    'ratings'.trParams({"param": widget.commentScoreCount.toString()}),
                    style: TextStyle(
                        fontSize: 13, color: HexColor('#666A71'), fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(width: 5),
                  Transform.rotate(
                    angle: CommonManager.instance.isReverse() ? math.pi : 0,
                    child: Image.asset(
                      'assets/images/common/icon_arrow_gray.png',
                      width: 5,
                      height: 8,
                    ),
                  )
                ],
              ),
            ),
          if (!isAvailable(widget.avgScore) || widget.avgScore == 0)
            Text('no_enough_ratings'.tr,
                style: TextStyle(
                    fontSize: 16, color: HexColor('#666A71'), fontWeight: FontWeight.bold)),
          SizedBox(
            height: 20,
            child: Text('tap_to_rate'.tr,
                style: TextStyle(
                    fontSize: 12, color: HexColor('#666A71'), fontWeight: FontWeight.bold)),
          )
        ],
      ),
    );
  }
}
