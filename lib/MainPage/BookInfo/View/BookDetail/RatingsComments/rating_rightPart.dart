import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/MainPage/BookInfo/View/BookDetail/RatingsComments/ratings_stars.dart';
import 'package:flutter/material.dart';

import '../../../../../Util/Extensions/colorUtil.dart';
import '../../../Model/book_comments_model.dart';

class RatingRightPart extends BaseFulWidget {
  final BookCommentsRatingModel? commentsRatingModel;

  RatingRightPart({super.key, required this.commentsRatingModel});

  @override
  State<RatingRightPart> createState() => _RatingRightPartState();
}

class _RatingRightPartState extends State<RatingRightPart> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 22, left: 9, right: 9, bottom: 22),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          //todo:评分详细上部分
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              //todo:评分5星
              Expanded(
                  flex: 2, child: Image.asset('assets/images/bookDetails/icon_evaluations5.png')),
              const SizedBox(width: 6),
              Expanded(
                  flex: 7,
                  child: LinearProgressIndicator(
                    borderRadius: BorderRadius.circular(2),
                    value: widget.commentsRatingModel?.score5Rate ?? 0.0,
                    // 设置进度值，范围是0.0到1.0
                    backgroundColor: HexColor('#EEEEEE'),
                    // 设置背景颜色
                    valueColor: AlwaysStoppedAnimation<Color>(HexColor('#1B86FF')), // 设置进度颜色
                  )),
            ],
          ),
          // const SizedBox(height: 7),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              //todo:评分4星
              Expanded(
                  flex: 2, child: Image.asset('assets/images/bookDetails/icon_evaluations4.png')),
              const SizedBox(width: 6),
              Expanded(
                  flex: 7,
                  child: LinearProgressIndicator(
                    borderRadius: BorderRadius.circular(2),
                    value: widget.commentsRatingModel?.score4Rate ?? 0.0,
                    // 设置进度值，范围是0.0到1.0
                    backgroundColor: HexColor('#EEEEEE'),
                    // 设置背景颜色
                    valueColor: AlwaysStoppedAnimation<Color>(HexColor('#1B86FF')), // 设置进度颜色
                  )),
            ],
          ),
          // const SizedBox(height: 7),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              //todo:评分3星
              Expanded(
                  flex: 2, child: Image.asset('assets/images/bookDetails/icon_evaluations3.png')),
              const SizedBox(width: 6),
              Expanded(
                  flex: 7,
                  child: LinearProgressIndicator(
                    borderRadius: BorderRadius.circular(2),
                    value: widget.commentsRatingModel?.score3Rate ?? 0.0,
                    // 设置进度值，范围是0.0到1.0
                    backgroundColor: HexColor('#EEEEEE'),
                    // 设置背景颜色
                    valueColor: AlwaysStoppedAnimation<Color>(HexColor('#1B86FF')), // 设置进度颜色
                  )),
            ],
          ),
          // const SizedBox(height: 7),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              //todo:评分2星
              Expanded(
                  flex: 2, child: Image.asset('assets/images/bookDetails/icon_evaluations2.png')),
              const SizedBox(width: 6),
              Expanded(
                  flex: 7,
                  child: LinearProgressIndicator(
                    borderRadius: BorderRadius.circular(2),
                    value: widget.commentsRatingModel?.score2Rate ?? 0.0,
                    // 设置进度值，范围是0.0到1.0
                    backgroundColor: HexColor('#EEEEEE'),
                    // 设置背景颜色
                    valueColor: AlwaysStoppedAnimation<Color>(HexColor('#1B86FF')), // 设置进度颜色
                  )),
            ],
          ),
          // const SizedBox(height: 7),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              //todo:评分1星
              Expanded(
                  flex: 2, child: Image.asset('assets/images/bookDetails/icon_evaluations1.png')),
              const SizedBox(width: 6),
              Expanded(
                  flex: 7,
                  child: LinearProgressIndicator(
                    borderRadius: BorderRadius.circular(2),
                    value: widget.commentsRatingModel?.score1Rate ?? 0.0,
                    // 设置进度值，范围是0.0到1.0
                    backgroundColor: HexColor('#EEEEEE'),
                    // 设置背景颜色
                    valueColor: AlwaysStoppedAnimation<Color>(HexColor('#1B86FF')), // 设置进度颜色
                  )),
            ],
          ),

          //todo:评分星星下部分
          const SizedBox(height: 16),
          RatingStars(
            rating: (widget.commentsRatingModel?.avgScore ?? 0.00).toDouble(), // 设置评分
            maxStars: 5, // 设置最大星星数
          ),
        ],
      ),
    );
  }
}
