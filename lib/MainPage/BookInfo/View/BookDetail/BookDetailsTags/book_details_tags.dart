import 'package:flutter/cupertino.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Util/DataReportManager/event_report_manager.dart';
import '../../../../../Util/DataReportManager/event_name_config.dart';
import '../../../../../Util/Extensions/colorUtil.dart';

class BookDetailsTags extends BaseFulWidget {
  final List<String>? tagList;
  BookDetailsTags({super.key, required this.tagList});

  @override
  State<BookDetailsTags> createState() => _BookDetailsTagsState();
}

class _BookDetailsTagsState extends State<BookDetailsTags> {

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var count = widget.tagList?.length?? 0;
    return SizedBox(
      height: 0 < count ? 27 : 0,
      width: double.infinity,
      child: ListView.builder(
        scrollDirection: Axis.horizontal, // 设置为横向滚动
        itemCount: count,
        itemBuilder: (context, index) {
          if (index < count){
            double leftMargin;
            double rightMargin;
            if (index == 0) {
              leftMargin = 16;
              rightMargin = 3.5;
            } else if (index == count - 1) {
              leftMargin = 3.5;
              rightMargin = 16;
            }else {
              leftMargin = 3.5;
              rightMargin = 3.5;
            }
            return GestureDetector(
              onTap: () {
                EventReportManager.eventReportOfFirebase(clickDetailTag);
              },
              child: Container(
                margin: EdgeInsets.only(left: leftMargin, right: rightMargin),
                padding: const EdgeInsets.symmetric(horizontal: 10),
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15),
                  color: HexColor('#ECEDF1'),
                ),
                child: Text(
                  widget.tagList?[index] ?? '',
                  style: TextStyle(fontSize: 14, color: HexColor('#555A65')),
                ),
              )
            );
          }else{
            return Container();
          }
        },
      ),
    );
  }
}
