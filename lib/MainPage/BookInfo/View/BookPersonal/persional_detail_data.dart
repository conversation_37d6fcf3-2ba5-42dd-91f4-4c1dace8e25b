import 'package:flutter/cupertino.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/MainPage/BookInfo/View/BookPersonal/personal_detail_data_Item.dart';
import 'package:UrNovel/Util/Extensions/colorUtil.dart';

class PersonalDetailData extends BaseFulWidget {
  PersonalDetailData({super.key, required super.arguments});

  @override
  State<PersonalDetailData> createState() => _PersonalDetailDataState();
}

class _PersonalDetailDataState extends State<PersonalDetailData> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(47, 38, 47, 24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          PersonalDetailDataItem(arguments: widget.arguments[0]),
          Container(
            width: 1,
            height: 40,
            color: ColorsUtil.hexColor(0x444750, alpha: 0.06),
          ),
          PersonalDetailDataItem(arguments: widget.arguments[1]),
          Container(
            width: 1,
            height: 40,
            color: ColorsUtil.hexColor(0x444750, alpha: 0.06),
          ),
          PersonalDetailDataItem(arguments: widget.arguments[2]),
        ],
      ),
    );
  }
}
