import 'package:UrNovel/Util/Common/network_image_util.dart';
import 'package:UrNovel/Util/Extensions/colorUtil.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../Util/Common/CommonManager/common_manager.dart';
import '../../../../Util/Common/Model/normal_model.dart';
import '../../../../Util/DataReportManager/event_name_config.dart';
import '../../../../Util/DataReportManager/event_report_manager.dart';
import '../../../Library/ViewModel/ViewModel.dart';
import '../../../Read/Model/ForYou/home_read_model.dart';
import '../../../Secondary/Model/secondary_list_model.dart';
import '../../Model/book_author_info.dart';

class PersonalNovelCell extends StatefulWidget {
  final dynamic bookItem;

  const PersonalNovelCell({super.key, this.bookItem});

  @override
  State<PersonalNovelCell> createState() => _PersonalNovelCellState();
}

class _PersonalNovelCellState extends State<PersonalNovelCell> {
  int? bookId = 0;
  String? cover = "";
  String? title = "";
  String? authorName = "";
  int? viewCount = 0;
  bool? library = false;
  bool isShowLibrary = false;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    if (widget.bookItem is HomeReadBookItem) {
      HomeReadBookItem item = widget.bookItem as HomeReadBookItem;
      bookId = item.id;
      cover = item.cover;
      title = item.title;
      authorName = item.authorName;
      viewCount = item.viewCount;
      isShowLibrary = false;
    } else if (widget.bookItem is SecondaryListItem) {
      SecondaryListItem item = widget.bookItem as SecondaryListItem;
      bookId = item.bookId;
      cover = item.cover;
      title = item.title;
      authorName = item.authorName;
      viewCount = item.viewCount;
      library = item.library;
      isShowLibrary = true;
    } else if (widget.bookItem is BookVoListItem) {
      BookVoListItem item = widget.bookItem as BookVoListItem;
      bookId = item.bookId;
      cover = item.cover;
      title = item.title;
      authorName = item.authorName;
      viewCount = item.viewCount;
      library = item.library;
      isShowLibrary = true;
    }
  }

  ///添加到书架
  addBookToLibrary() async {
    await CommonManager.instance.addBookToLibrary(bookId, 1, (isSuccess){
      if (isSuccess) {
        if (mounted) {
          setState(() {
            library = true;
            if (widget.bookItem is SecondaryListItem) {
              SecondaryListItem item = widget.bookItem as SecondaryListItem;
              item.library = true;
            } else if (widget.bookItem is BookVoListItem) {
              BookVoListItem item = widget.bookItem as BookVoListItem;
              item.library = true;
            }
          });
        }
      }
    });
    EventReportManager.eventReportOfFirebase(clickSecondlyLibrary);
  }

  ///从书架移除
  cancelBookFromLibrary() async {
    await CommonManager.instance.cancelBookFromLibrary(bookId, 1, (isSuccess){
      if (isSuccess) {
        if (mounted) {
          setState(() {
            library = false;
            if (widget.bookItem is SecondaryListItem) {
              SecondaryListItem item = widget.bookItem as SecondaryListItem;
              item.library = library;
            } else if (widget.bookItem is BookVoListItem) {
              BookVoListItem item = widget.bookItem as BookVoListItem;
              item.library = library;
            }
          });
        }
      }
    });
    EventReportManager.eventReportOfFirebase(clickSecondlyRemove);
  }


  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        NetworkImageUtil(imageUrl: cover, width: 68, height: 88),
        const SizedBox(width: 5),
        Expanded(
            flex: 8,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title ?? "",
                  style: TextStyle(
                    fontSize: 15,
                    color: HexColor("#000000"),
                    fontWeight: FontWeight.bold,
                    height: 1,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                if (isAvailable(authorName)) const SizedBox(height: 11),
                if (isAvailable(authorName))
                  Text(
                    authorName!,
                    style: TextStyle(
                      fontSize: 13,
                      color: HexColor("#555A65"),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                if (isShowLibrary) const SizedBox(height: 10),
                if (isShowLibrary)
                  SizedBox(
                    height: 18,
                    child: TextButton(
                      onPressed: () {
                        if (library == true) {
                          cancelBookFromLibrary();
                        } else {
                          addBookToLibrary();
                        }
                      },
                      style: TextButton.styleFrom(
                        padding: EdgeInsets.zero, // 取消所有边距
                      ),
                      child: Text(
                        library == true ? "library_added".tr : "library_add".tr,
                        style: TextStyle(
                            color: library == true ? HexColor("#888C94") : HexColor("#1B86FF"),
                            fontSize: 11),
                      ),
                    ),
                  )
              ],
            )),
        if (isAvailable(viewCount) && 0 < viewCount!)
          Container(
            height: 20,
            margin: const EdgeInsets.only(left: 15),
            decoration: BoxDecoration(
              color: HexColor("#F4F5F6"),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                const SizedBox(width: 8),
                Image.asset("assets/images/bottomNavBar/icon_profile_n.png", width: 9, height: 8),
                const SizedBox(width: 4),
                Text(viewCount?.toString() ?? "",
                    style: TextStyle(fontSize: 10, color: HexColor("#888C94"))),
                const SizedBox(width: 8),
              ],
            ),
          )
      ],
    );
  }
}
