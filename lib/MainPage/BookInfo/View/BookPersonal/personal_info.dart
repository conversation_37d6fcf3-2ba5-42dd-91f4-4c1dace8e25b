import 'package:flutter/cupertino.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Util/Common/network_image_util.dart';

import '../../../../Util/Extensions/colorUtil.dart';
import '../../Model/book_author_info.dart';

class PersonalInfo extends BaseFulWidget {
  final BookAuthorResultModel? authorModel;

  PersonalInfo({super.key, required this.authorModel});

  @override
  State<PersonalInfo> createState() => _PersonalInfoState();
}

class _PersonalInfoState extends State<PersonalInfo> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        NetworkImageUtil(
            imageUrl: widget.authorModel?.cover,
            w: 148,
            h: 148,
            width: 72,
            height: 72,
            fit: BoxFit.cover,
            isCircle: true),
        const SizedBox(height: 20),
        Text(widget.authorModel?.name ?? "",
            style: TextStyle(
                fontSize: 20,
                color: HexColor('#000000'),
                fontWeight: FontWeight.bold),
            textAlign: TextAlign.center),
        const SizedBox(height: 24),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 22),
          child: Text(
            widget.authorModel?.description ?? "",
            style: TextStyle(fontSize: 13, color: HexColor('#888C94')),
            maxLines: null,
            strutStyle: const StrutStyle(height: 1.5),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }
}
