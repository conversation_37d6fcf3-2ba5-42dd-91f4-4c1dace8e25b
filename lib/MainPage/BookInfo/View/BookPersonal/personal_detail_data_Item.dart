import 'package:flutter/cupertino.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';

import '../../../../Util/Extensions/colorUtil.dart';

class PersonalDetailDataItem extends BaseFulWidget {
  @override
  PersonalDetailDataItem({super.key, required super.arguments});

  @override
  State<PersonalDetailDataItem> createState() => _PersonalDetailDataItemState();
}

class _PersonalDetailDataItemState extends State<PersonalDetailDataItem> {
  @override
  Widget build(BuildContext context) {
    String? title = widget.arguments['title'];
    int? value = int.tryParse(widget.arguments['value']);

    if (title != null && value != null) {
      if (title == 'Novels') {
        return Column(
          children: [
            Text('$value',
                style: TextStyle(
                    fontSize: 21,
                    color: HexColor('#555A65'),
                    fontWeight: FontWeight.bold,
                    height: 0.8)),
            const SizedBox(height: 9),
            Text(title,
                style: TextStyle(fontSize: 13, color: HexColor('#888C94'))),
          ],
        );
      } else {
        return Column(
          children: [
            if (value < 1000)
              Text('$value',
                  style: TextStyle(
                      fontSize: 21,
                      color: HexColor('#555A65'),
                      fontWeight: FontWeight.bold,
                      height: 0.8))
            else
              Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text((value / 1000).toStringAsFixed(1),
                      style: TextStyle(
                          fontSize: 21,
                          color: HexColor('#555A65'),
                          fontWeight: FontWeight.bold,
                          height: 0.8)),
                  Text('k',
                      style: TextStyle(
                          fontSize: 12,
                          color: HexColor('#555A65'),
                          fontWeight: FontWeight.bold,
                          height: 0.8))
                ],
              ),
            const SizedBox(height: 9),
            Text(title,
                style: TextStyle(fontSize: 13, color: HexColor('#888C94'))),
          ],
        );
      }
    } else {
      return Column(
        children: [
          Text('0',
              style: TextStyle(
                  fontSize: 21,
                  color: HexColor('#555A65'),
                  fontWeight: FontWeight.bold,
                  height: 0.8)),
          const SizedBox(height: 9),
          Text('未知',
              style: TextStyle(fontSize: 13, color: HexColor('#888C94'))),
        ],
      );
    }
  }
}
