import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../../../Util/Extensions/colorUtil.dart';
import '../../../../Util/logUtil.dart';

class Comment extends StatefulWidget {
  final TextEditingController controller;
  final FocusNode focusNode;
  final String? content;
  final Function(String) onCommentChanged;

  const Comment(
      {super.key, required this.controller, required this.focusNode, this.content = '', required this.onCommentChanged});

  @override
  State<Comment> createState() => _CommentState();
}

class _CommentState extends State<Comment> {

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    widget.controller.text = widget.content?? '';
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          constraints: const BoxConstraints(
            maxHeight: 200,
          ),
          child: SingleChildScrollView(
            child: TextField(
              controller: widget.controller,
              focusNode: widget.focusNode,
              style: TextStyle(color: HexColor('#000000'), fontSize: 15),
              //光标颜色
              cursorColor: HexColor('#1B86FF'),
              // 设置为 null 表示不限制行数
              maxLines: null,
              // 最小行数
              minLines: 1,
              keyboardType: TextInputType.multiline,
              // 设置键盘类型为多行文本
              decoration: InputDecoration(
                hintText: 'add_a_comment'.tr,
                // 设置占位符
                hintStyle: TextStyle(
                  color: HexColor('#A0A1A8'),
                  fontSize: 15,
                ),
                border: InputBorder.none,
                // 取消边框及下划线
                focusedBorder: InputBorder.none,
                // 取消聚焦时的边框及下划线
                enabledBorder: InputBorder.none,
                // 取消启用时的边框及下划线
                errorBorder: InputBorder.none,
                // 取消错误时的边框及下划线
                disabledBorder: InputBorder.none, // 取消禁用时的边框及下划线
              ),
              inputFormatters: [
                // 使用 TextInputFormatter 来设置键盘类型
                TextInputFormatter.withFunction((oldValue, newValue) {
                  // 这里可以添加自定义逻辑来处理输入
                  if (1000 < newValue.text.length) {
                    return oldValue;
                  }
                  return newValue;
                }),
              ],
              onChanged: (value) {
                widget.onCommentChanged(value);
              },
              onSubmitted: (value) {
                 logP(value);
              },
            ),
          ),
        ),
      ],
    );
  }
}
