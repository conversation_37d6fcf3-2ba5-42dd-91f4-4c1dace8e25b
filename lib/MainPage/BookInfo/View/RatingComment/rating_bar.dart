import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:get/get.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';

import '../../../../Util/Extensions/colorUtil.dart';

class Rating extends BaseFulWidget {
  final int ratingCount;
  final double score;
  final Function(double) onRatingUpdate;

  Rating({super.key, this.ratingCount = 5, this.score = 0.0, required this.onRatingUpdate});

  @override
  State<Rating> createState() => _RatingState();
}

class _RatingState extends State<Rating> {
  double rating = 0.0;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    rating = widget.score;
  }

  @override
  Widget build(BuildContext context) {
    var text = "click_star".tr;
    if (rating <= 0.0) {
      text = "click_star".tr;
    } else if (rating <= 1.0) {
      text = "terrible".tr;
    } else if (rating <= 2.0) {
      text = "not_good".tr;
    } else if (rating <= 3.0) {
      text = "just_so_so".tr;
    } else if (rating <= 4.0) {
      text = "excellent".tr;
    } else {
      text = "azmazing".tr;
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        RatingBar(
            initialRating: rating,
            minRating: 0.0,
            direction: Axis.horizontal,
            allowHalfRating: true,
            itemCount: widget.ratingCount,
            glow: false,
            itemSize: 40,
            itemPadding: const EdgeInsets.symmetric(horizontal: 10.0),
            updateOnDrag: true,
            ratingWidget: RatingWidget(
              full: Image.asset(
                "assets/images/bookDetails/ratingCommend/icon_star_full.png",
                width: 30,
                height: 30,
              ),
              half: Image.asset(
                "assets/images/bookDetails/ratingCommend/icon_star_half.png",
                width: 30,
                height: 30,
              ),
              empty: Image.asset(
                "assets/images/bookDetails/ratingCommend/icon_star_normal.png",
                width: 30,
                height: 30,
              ),
            ),
            onRatingUpdate: (double value) {
              widget.onRatingUpdate(value);
              setState(() {
                rating = value;
              });
            }),
        const SizedBox(height: 25),
        Center(
          child: Text(
            text,
            style: TextStyle(color: HexColor('#A0A1A8'), fontSize: 15),
          ),
        )
      ],
    );
  }
}
