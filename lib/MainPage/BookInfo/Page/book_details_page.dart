import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/MainPage/BookInfo/Model/book_comments_model.dart';
import 'package:UrNovel/MainPage/NovelRead/Model/book_detailInfo_model.dart';
import 'package:UrNovel/Util/Common/CommonManager/common_manager.dart';
import 'package:UrNovel/Util/Common/novel_download.dart';
import 'package:UrNovel/Util/DataReportManager/event_report_manager.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../Launch&Login/Model/user_model.dart';
import '../../../Launch&Login/ViewModel/ViewModel.dart';
import '../../../Util/Common/Animation/lottie_animation_view.dart';
import '../../../Util/Common/Model/share_channel_model.dart';
import '../../../Util/Common/no_load_view.dart';
import '../../../Util/DataReportManager/event_name_config.dart';
import '../../../Util/Extensions/colorUtil.dart';
import '../../../Util/LocalNotificationManager/local_notification_manager.dart';
import '../../../Util/RefreshLoad/refresh_load.dart';
import '../../../Util/ShareManager/share_manager.dart';
import '../../../Util/SharedPreferences/shared_preferences.dart';
import '../../../Util/SheetAndAlter/alter.dart';
import '../../../Util/SheetAndAlter/bottom_sheet.dart';
import '../../../Util/enum.dart';
import '../../Profile/Widget/profile_card_premium.dart';
import '../View/BookDetail/BookDetailBottom/book_detail_bottom.dart';
import '../View/BookDetail/BookDetailComments/book_comments_cell.dart';
import '../View/BookDetail/BookDetailComments/book_comments_header.dart';
import '../View/BookDetail/BookDetailDatas/book_details_datas.dart';
import '../View/BookDetail/BookDetailsHeader/book_deatils_header.dart';
import '../View/BookDetail/BookDetailsSynopsis/book_details_synopsis.dart';
import '../View/BookDetail/BookDetailsTags/book_details_tags.dart';
import '../View/BookDetail/RatingsComments/ratings_comments.dart';
import '../ViewModel/ViewModel.dart';

class BookDetailsPage extends BaseFulWidget {
  BookDetailsPage({super.key, super.arguments});

  @override
  State<BookDetailsPage> createState() => _BookDetailsPageState();
}

class _BookDetailsPageState extends State<BookDetailsPage> {
  late BookDetailInfoResultModel? _bookDetailInfoModel;
  late BookDetailJumpType? _jumpType;
  late BookCommentsRatingModel? _commentsRatingModel;
  late List<Map<String, dynamic>> _bookDetailData;
  late List<BookCommentsItem>? _commentsList;
  int pageIndex = 1; //page默认从1开始
  int pageSize = 10;
  bool _isLoading = true;
  late UserInfoModel? _userInfoModel;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _bookDetailInfoModel = widget.arguments['bookDetailInfo'];
    _jumpType = widget.arguments['jumpType'] ?? BookDetailJumpType.other;
    _commentsRatingModel = null;
    _commentsList = [];
    _userInfoModel = null;
    eventBusOn((obj) async {
      await refreshUserInfo();
    });

    fetchData(true);
    eventReport();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_jumpType == BookDetailJumpType.voiceListening) {
        audioBookController.isShowFloating.value = true;
      }
    });
  }

  @override
  void dispose() {
    if (_jumpType == BookDetailJumpType.voiceListening) {
      Future.delayed(Duration(milliseconds: 100), () {
        audioBookController.isShowFloating.value = false;
      });
    }
    super.dispose();
  }

  //获取详情数据
  fetchData(bool isRefresh) async {
    _bookDetailData = [
      {'title': 'reads'.tr, 'value': _bookDetailInfoModel?.reads ?? "0"},
      {'title': 'likes'.tr, 'value': _bookDetailInfoModel?.likes ?? "0"},
      {'title': 'time'.tr, 'value': _bookDetailInfoModel?.time ?? "0"}
    ];

    _isLoading = true;

    _userInfoModel = await SpUtil.spGetUserInfo();
    if (isRefresh) {
      _commentsRatingModel = await getCommentList();
      _commentsList = _commentsRatingModel?.list;

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } else {
      BookCommentsRatingModel? model = await getCommentList();
      _commentsList?.addAll(model?.list as Iterable<BookCommentsItem>);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }

    // 处理刷新状态
    widget.dealRefreshState(pageIndex, pageSize, _commentsList);
  }

  // 获取用户信息
  Future<void> refreshUserInfo() async {
    _userInfoModel = await LoginViewModel.getUserInfo();
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> eventReport() async {
    await EventReportManager.eventReportOfCommon(unlockAchievement);
    await EventReportManager.eventReportOfFacebook(fbUnlockAchievement,
        parameters: {"fb_currency": "USD", "_valueToSum": 0});
  }

  //todo:获取评论、评分信息
  Future<BookCommentsRatingModel?> getCommentList() async {
    var parameters = {"bookId": _bookDetailInfoModel?.bookId, "page": pageIndex, "size": pageSize};
    return await BookInfoViewModel.getCommentListV2(parameters);
  }

  // TODO: 刷新数据
  Future<void> onRefresh() async {
    pageIndex = 1;
    fetchData(true);
  }

  // TODO: 加载更多数据
  Future<void> onLoading() async {
    pageIndex++;
    fetchData(false);
  }

  // TODO: 下载书籍
  Future<void> onNovelDownload() async {
    await NovelDownload.instance.onNovelDownload(context, _bookDetailInfoModel,
        onLoadingCallBack: (isLoading) {
      if (mounted) {
        setState(() {
          _isLoading = isLoading;
        });
      }
    }, onRecharged: () {
      refreshUserInfo();
    }, onFinished: (detailInfoModel) {
      _bookDetailInfoModel = detailInfoModel;
      fetchData(true);
    });

    EventReportManager.eventReportOfFirebase(clickDetailDownload);
  }

  // TODO: 评分评论页面
  Future<void> toRatingCommentPage(double? score, String? content, int? id, bool isEdit) async {
    await Get.toNamed('/ratingCommentPage', arguments: {
      "bookId": _bookDetailInfoModel?.bookId,
      "score": score,
      "content": content,
      "id": id,
      'isEdit': isEdit,
      "onBackPressed": ratingCommentCallBack
    });

    EventReportManager.eventReportOfFirebase(clickDetailComment);
  }

  void ratingCommentCallBack() {
    /// 刷新评论数据
    fetchData(true);
  }

  // TODO: 评分列表
  Future<void> toCommentListPage(dynamic item) async {
    await Get.toNamed('/commentListPage', arguments: {
      "bookId": _bookDetailInfoModel?.bookId,
      "pageIndex": pageIndex,
      "commendList": _commentsList,
      "curCommentItem": item,
      "onBackPressed": ratingCommentCallBack
    });
  }

  ///删除评论
  Future<void> commentDelete(int? id) async {
    // TODO: implement
    setState(() {
      _isLoading = true;
    });

    Map<String, dynamic> parameters = {
      "commentId": id,
    };
    bool isEdited = await BookInfoViewModel.commentDelete(parameters);
    if (isEdited) {
      setState(() {
        _isLoading = false;
      });

      fetchData(true);
    } else {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _shareNovel(ShareType type) async {
    // TODO: 获取分享链接
    setState(() {
      _isLoading = true;
    });
    ShareBookUrlModel? model = await CommonManager.getShareBookUrl(_bookDetailInfoModel?.bookId);
    setState(() {
      _isLoading = false;
    });

    ShareManager.instance.shareNovel(
        _bookDetailInfoModel?.title,
        _bookDetailInfoModel?.description,
        _bookDetailInfoModel?.cover, // 传递封面图URL
        model,
        type,
        context: context); // 传递context
  }

  Future<void> _toPremiumPage() async {
    // TODO: implement premium purchase
    await Get.toNamed("/premiumPage", arguments: {
      "userInfo": _userInfoModel,
      'bookId': _bookDetailInfoModel?.bookId,
      'chapterId': null,
      'orderType': OrderType.novelDetail
    });
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var cardWidth = mediaQuery.size.width * 0.9;
    var cardHeight = cardWidth / 4.9;
    var marginH = (mediaQuery.size.width - cardWidth) / 2.0;
    var count = _commentsList?.length ?? 0;
    return Padding(
        padding: EdgeInsets.only(bottom: mediaQuery.padding.bottom),
        child: Scaffold(
            appBar: AppBar(
              actions: isAvailable(_bookDetailInfoModel)
                  ? [
                      IconButton(
                        icon: Image.asset(
                          'assets/images/bookDetails/icon_download.png',
                          width: 24,
                          height: 20,
                          fit: BoxFit.contain,
                        ),
                        onPressed: onNovelDownload,
                      ),
                      IconButton(
                        icon: Image.asset(
                          'assets/images/bookDetails/icon_share.png',
                          width: 20,
                          height: 20,
                          fit: BoxFit.contain,
                        ),
                        onPressed: () {
                          // TODO: Share book
                          showBookBottomSheet(context, BookShareSheet(onShareTap: (type) {
                            _shareNovel(type);
                          }), HexColor('#F4F5F6'));

                          EventReportManager.eventReportOfFirebase(clickDetailShare);
                        },
                      ),
                      IconButton(
                        icon: Image.asset(
                          'assets/images/bookDetails/icon_error.png',
                          width: 22,
                          height: 22,
                          fit: BoxFit.contain,
                        ),
                        onPressed: () {
                          showAlter(ReportErrorContent(
                            reportType: ReportType.comment,
                            onConfirm: (content) async {
                              //todo: 错误反馈
                              await errorReport(
                                ReportType.bookDetail,
                                content,
                                bookId: _bookDetailInfoModel?.bookId,
                              );
                            },
                          ));

                          EventReportManager.eventReportOfFirebase(clickDetailReport);
                        },
                      ),
                    ]
                  : null,
            ),
            body: Stack(children: [
              Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: !isAvailable(_bookDetailInfoModel) && !isAvailable(_commentsRatingModel)
                      ? _isLoading
                          ? Container(color: Colors.white)
                          : NoLoadView()
                      : Column(
                          children: [
                            Expanded(
                              flex: 9,
                              child: RefreshLoadUtil(
                                onRefresh: onRefresh,
                                onLoading: onLoading,
                                controller: widget.refreshController,
                                child: ListView.builder(
                                  itemCount: 7 + count,
                                  itemBuilder: (context, index) {
                                    if (index == 0) {
                                      //todo:头部信息
                                      return BookDetailsHeader(
                                          bookDetailInfo: _bookDetailInfoModel);
                                    } else if (index == 1) {
                                      //todo:标签信息
                                      return BookDetailsTags(
                                          tagList: _bookDetailInfoModel?.tagList);
                                    } else if (index == 2) {
                                      //todo:数据信息
                                      return BookDetailsData(arguments: _bookDetailData);
                                    } else if (index == 3) {
                                      if (_userInfoModel?.vipStatus == 1) {
                                        return Container();
                                      } else {
                                        return GestureDetector(
                                          onTap: _toPremiumPage,
                                          behavior: HitTestBehavior.opaque,
                                          child: Container(
                                              width: cardWidth,
                                              height: cardHeight,
                                              margin: EdgeInsets.only(
                                                  top: 20, left: marginH, right: marginH),
                                              decoration: BoxDecoration(
                                                borderRadius: BorderRadius.circular(12),
                                                gradient: LinearGradient(
                                                    colors: [
                                                      HexColor('#20243d'),
                                                      HexColor('#2b304d'),
                                                    ],
                                                    begin: Alignment.centerLeft,
                                                    end: Alignment.centerRight),
                                              ),
                                              child: ProfileCardPremium(
                                                cardWidth: cardWidth,
                                                cardHeight: cardHeight,
                                                userInfoModel: _userInfoModel,
                                              )),
                                        );
                                      }
                                    } else if (index == 4) {
                                      //todo:简介信息
                                      return BookDetailsSynopsis(
                                          description: _bookDetailInfoModel?.description);
                                    } else if (index == 5) {
                                      //todo:评分信息
                                      return RatingsComments(
                                          commentsRatingModel: _commentsRatingModel,
                                          onRatingCommentTap: () {
                                            //todo:评分评论页面
                                            toRatingCommentPage(null, null, null, false);
                                          },
                                          onCommentListTap: () {
                                            toCommentListPage(null);
                                          });
                                    } else if (index == 6) {
                                      //todo:评论信息
                                      return BookCommentsHeader(
                                          commentsModel: _commentsRatingModel,
                                          edgeInsets:
                                              const EdgeInsets.only(top: 32, left: 18, right: 18),
                                          onCommentListTap: () {
                                            toCommentListPage(null);
                                          });
                                    } else {
                                      //todo:评论cell
                                      var item = _commentsList?[index - 7];
                                      return Container(
                                        margin:
                                            EdgeInsets.fromLTRB(16, index == 6 ? 14 : 20, 16, 0),
                                        decoration: BoxDecoration(
                                          color: HexColor('#FFFFFF'),
                                          borderRadius: BorderRadius.circular(12),
                                        ),
                                        child: BookCommentsCell(
                                          item: item,
                                          onItemTap: (index, commendId) {
                                            if (index == 0) {
                                              //todo:编辑评论
                                              toRatingCommentPage(
                                                  item?.score, item?.content, item?.id, true);
                                            } else if (index == 1) {
                                              //todo:删除评论
                                              commentDelete(commendId);
                                            } else if (index == 2) {
                                              //todo:举报评论
                                              showAlter(
                                                ReportErrorContent(
                                                  reportType: ReportType.comment,
                                                  onConfirm: (content) async {
                                                    //todo: 错误反馈
                                                    await errorReport(ReportType.comment, content,
                                                        commentId: item?.id, bookId: item?.bookId);
                                                  },
                                                ),
                                              );
                                            }
                                          },
                                          onReplyTap: (item) async {
                                            await toCommentListPage(item);
                                          },
                                        ),
                                      );
                                    }
                                  },
                                ),
                              ),
                            ),
                            Expanded(
                                flex: 1,
                                child: Container(
                                  width: double.infinity,
                                  height: 60,
                                  color: HexColor('#FFFFFF'),
                                  child: Padding(
                                      padding: const EdgeInsets.fromLTRB(10, 10, 10, 10),
                                      child: BookDetailBottom(
                                          detailInfoModel: _bookDetailInfoModel,
                                          turnToReadPage: () async {
                                            if (curRoutePage == RoutePage.novelReadPage) {
                                              Get.back();
                                            } else {
                                              //去往阅读页
                                              await LocalNotificationManager.toNovelReadPage({
                                                'bookId': _bookDetailInfoModel?.bookId,
                                                'jumpType': _jumpType
                                              });
                                            }
                                          })),
                                ))
                          ],
                        )),
              if (_isLoading)
                const Positioned(top: 0, left: 0, right: 0, bottom: 0, child: LottieAnimationView())
            ])));
  }
}
