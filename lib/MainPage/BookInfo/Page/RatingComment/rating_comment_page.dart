import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/MainPage/BookInfo/View/RatingComment/rating_bar.dart';
import 'package:UrNovel/Util/AlterManager/alter_manager.dart';
import 'package:UrNovel/Util/Common/Animation/lottie_animation_view.dart';
import 'package:UrNovel/Util/enum.dart';
import 'package:UrNovel/Util/tools.dart';
import '../../../../Util/Extensions/colorUtil.dart';
import '../../../../Util/SheetAndAlter/toast.dart';
import '../../View/RatingComment/comment.dart';
import '../../ViewModel/ViewModel.dart';

class RatingCommentPage extends BaseFulWidget {
  RatingCommentPage({super.key, required super.arguments});

  @override
  State<RatingCommentPage> createState() => _RatingCommentPageState();
}

class _RatingCommentPageState extends State<RatingCommentPage> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final GlobalKey<CommentNumState> _commentNumKey =
      GlobalKey<CommentNumState>();

  late double _rating;
  late String _content;
  late int _id;
  late VoidCallback? _onBackPressed;
  late bool _isLoading;
  late bool _isEdit;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    _rating = widget.arguments['score'] ?? 0.0;
    _content = widget.arguments['content'] ?? "";
    _id = widget.arguments['id'] ?? 0;
    _isEdit =  widget.arguments['isEdit'] ?? false;
    _onBackPressed = widget.arguments['onBackPressed'];
    _isLoading = false;

    // 在页面加载时请求焦点
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FocusScope.of(context).requestFocus(_focusNode);
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    _commentNumKey.currentState?.dispose();
    super.dispose();
  }

  Future<void> commentAdd() async {
    // TODO: implement
    if (_rating <= 0.0) {
      showToast("post_toast".tr);
      return;
    }

    if (!isAvailable(_content)) {
      showToast("no_comment".tr);
      return;
    }

    removeKeyBoard();

    setState(() {
      _isLoading = true;
    });

    Map<String, dynamic> parameters = {
      "bookId": widget.arguments['bookId'],
      "score": _rating,
      "content": _content
    };
    bool isAdd =
        await BookInfoViewModel.commentAdd(parameters);
    if (isAdd) {
      setState(() {
        _isLoading = false;
      });

      Get.back();

      showToast('thanks_rating'.tr);
      await AlterManager.instance.showNotificationAlter(NotificationAlterSource.firstComment);
    } else {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> commentEdit() async {
    // TODO: implement
    if (!isAvailable(_rating)) {
      showToast("post_toast".tr);
      return;
    }

    setState(() {
      _isLoading = true;
    });

    Map<String, dynamic> parameters = {
      "commentId": _id,
      "bookId": widget.arguments['bookId'],
      "score": _rating,
      "content": _content
    };
    bool isEdited =
    await BookInfoViewModel.commentEdit(parameters);
    if (isEdited) {
      setState(() {
        _isLoading = false;
      });

      Future.delayed(const Duration(seconds: 2)).then((value) {
        showToast('thanks_rating'.tr);
      });
    } else {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void removeKeyBoard() {
    FocusScope.of(context).unfocus();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
        onPopInvokedWithResult: (didPop, result) {
          removeKeyBoard();
          _onBackPressed?.call();
        },
        child: Scaffold(
          appBar: AppBar(
            backgroundColor: HexColor('#FFFFFF'),
            title: Text('comment'.tr,
                style: TextStyle(
                    color: HexColor('#000000'),
                    fontSize: 19,
                    fontWeight: FontWeight.bold)),
          ),
          body: Stack(
            children: [
              Container(
                  width: double.infinity,
                  height: double.infinity,
                  color: HexColor('#FFFFFF'),
                  child: Column(
                    children: [
                      Column(
                        children: [
                          Rating(score: _rating, onRatingUpdate: (rating) {
                            _rating = rating;
                          }),
                          Padding(
                            padding: const EdgeInsets.only(
                                top: 18, left: 10, right: 10),
                            child: Divider(
                                height: 1,
                                color:
                                    ColorsUtil.hexColor(0x444750, alpha: 0.06)),
                          ),
                          Padding(
                              padding: const EdgeInsets.only(
                                  top: 10, left: 18, right: 18),
                              child: Comment(
                                  controller: _controller,
                                  focusNode: _focusNode,
                                  content: _content,
                                  onCommentChanged: (comment) {
                                    _content = comment;
                                    _commentNumKey.currentState
                                        ?.updateComment(comment);
                                  })),
                        ],
                      ),
                      const Spacer(),
                      SizedBox(
                        width: double.infinity,
                        height: 24,
                        child: Padding(
                            padding: const EdgeInsets.only(left: 20, right: 20),
                            child: CommentNum(key: _commentNumKey)),
                      ),
                      Container(
                        width: double.infinity,
                        height: 48,
                        color: HexColor("#F4F5F6"),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                const SizedBox(width: 20),
                                TextButton(
                                  onPressed: () {
                                    removeKeyBoard();
                                  },
                                  style: ButtonStyle(
                                    padding: WidgetStateProperty.all(
                                        EdgeInsets.zero),
                                  ),
                                  child: Text("cancel".tr,
                                      style: TextStyle(
                                          color: HexColor("#555A65"),
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold)),
                                ),
                                const Spacer(),
                                TextButton(
                                  onPressed: () {
                                    if (_isEdit) {
                                      commentEdit();
                                    }else{
                                      commentAdd();
                                    }
                                  },
                                  style: ButtonStyle(
                                    padding: WidgetStateProperty.all(
                                        EdgeInsets.zero),
                                  ),
                                  child: Text("post".tr,
                                      style: TextStyle(
                                          color: HexColor("#555A65"),
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold)),
                                ),
                                const SizedBox(width: 20),
                              ],
                            )
                          ],
                        ),
                      )
                    ],
                  )),
              if (_isLoading) const LottieAnimationView()
            ],
          ),
        ));
  }
}

class CommentNum extends StatefulWidget {
  const CommentNum({super.key});

  @override
  State<CommentNum> createState() => CommentNumState();
}

class CommentNumState extends State<CommentNum> {
  late String? content;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    content = '';
  }

  void updateComment(String comment) {
    setState(() {
      content = comment;
    });
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 40,
      child: Text("${content?.length}/1000",
          style: TextStyle(color: HexColor('#555A65'), fontSize: 13)),
    );
  }
}
