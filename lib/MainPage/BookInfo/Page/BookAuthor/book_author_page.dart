import 'package:UrNovel/BaseWidget/base_less_widget.dart';
import 'package:UrNovel/MainPage/BookInfo/ViewModel/ViewModel.dart';
import 'package:UrNovel/Util/Common/Animation/lottie_animation_view.dart';
import 'package:UrNovel/Util/Common/no_load_view.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../Util/Extensions/colorUtil.dart';
import '../../../../Util/LocalNotificationManager/local_notification_manager.dart';
import '../../../../Util/enum.dart';
import '../../Model/book_author_info.dart';
import '../../View/BookPersonal/persional_detail_data.dart';
import '../../View/BookPersonal/personal_info.dart';
import '../../View/BookPersonal/personal_novel_cell.dart';

class BookAuthorPage extends BaseLessWidget {
  BookAuthorPage({super.key, required super.arguments});

  Future<BookAuthorResultModel?>? fetchData() async {
    // TODO: fetch data from server
    return await BookInfoViewModel.getBookAuthorInfo(arguments);
  }

  List<Map<String, Object>> getDetailData(BookAuthorResultModel? authorModel) {
    var bookDetailData = [
      {'title': 'reads'.tr, 'value': authorModel?.reads ?? 0},
      {'title': 'likes'.tr, 'value': authorModel?.likes ?? 0},
      {'title': 'novels'.tr, 'value': authorModel?.novels ?? 0}
    ];

    return bookDetailData;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: FutureBuilder(
          future: fetchData(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.done && snapshot.hasData) {
              BookAuthorResultModel? authorModel = snapshot.data;
              var bookDetailData = getDetailData(authorModel);
              var count = authorModel?.bookVoList?.length ?? 0;
              return Container(
                color: const Color(0x00ffffff),
                width: double.infinity,
                height: double.infinity,
                child: ListView.builder(
                    itemCount: 2 + count,
                    itemBuilder: (context, index) {
                      if (index == 0) {
                        return PersonalInfo(authorModel: authorModel);
                      } else if (index == 1) {
                        return PersonalDetailData(arguments: bookDetailData);
                      } else {
                        var item = authorModel?.bookVoList?[index - 2];
                        return GestureDetector(
                          onTap: () async {
                            //todo:去往阅读页
                            await LocalNotificationManager.toNovelReadPage(
                                {'bookId': item?.bookId, 'jumpType': BookDetailJumpType.other});
                          },
                          behavior: HitTestBehavior.opaque,
                          child: Container(
                            margin: const EdgeInsets.only(left: 15, right: 15),
                            padding: EdgeInsets.only(
                                top: index == 2 ? 15 : 18,
                                left: 15,
                                right: 15,
                                bottom: index == count + 1 ? 15 : 0),
                            decoration: BoxDecoration(
                              color: HexColor('#FFFFFF'),
                              borderRadius: count == 1
                                  ? const BorderRadius.all(Radius.circular(12))
                                  : index == 2
                                      ? const BorderRadius.only(
                                          topLeft: Radius.circular(12),
                                          topRight: Radius.circular(12))
                                      : index == count + 1
                                          ? const BorderRadius.only(
                                              bottomLeft: Radius.circular(12),
                                              bottomRight: Radius.circular(12))
                                          : null,
                            ),
                            child: PersonalNovelCell(bookItem: item),
                          ),
                        );
                      }
                    }),
              );
            } else if (snapshot.connectionState == ConnectionState.waiting) {
              return const LottieAnimationView();
            } else {
              return NoLoadView();
            }
          }),
    );
  }
}
