import 'package:UrNovel/Util/Common/Model/normal_model.dart';
import 'package:UrNovel/Util/NetWorkManager/net_work_manager.dart';
import 'package:UrNovel/Util/api_config.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:get/get.dart';

import '../../../Util/SheetAndAlter/toast.dart';
import '../../NovelRead/Model/book_detailInfo_model.dart';
import '../Model/book_author_info.dart';
import '../Model/book_comments_model.dart';

class BookInfoViewModel {
  /// 获取书籍作者信息
  static Future<BookAuthorResultModel?> getBookAuthorInfo(Map<String, dynamic> parameters) async {
    var response = await NetWorkManager.instance.get(apiGetAuthorInfo, params: parameters);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      BookAuthorInfoModel authorInfoModel = BookAuthorInfoModel.fromJson(response.data);

      return authorInfoModel.result;
    }

    return null;
  }

  /// 获取书籍评论列表V2
  static Future<BookCommentsRatingModel?> getCommentListV2(Map<String, dynamic> parameters) async {
    var response = await NetWorkManager.instance.post(apiCommentsListV2, parameters: parameters);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      BookCommentsModel commentsModel = BookCommentsModel.fromJson(response.data);

      return commentsModel.result;
    }

    return null;
  }

  /// 添加评论
  static Future<bool> commentAdd(Map<String, dynamic> parameters) async {
    var response = await NetWorkManager.instance.post(apiCommentAdd, parameters: parameters);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      NormalModel model = NormalModel.fromJson(response.data);

      if (model.code == 200) {
        return true;
      } else {
        if (isAvailable(model.msg)) {
          showToast(model.msg!);
        }
      }

      return false;
    } else {
      if (isAvailable(response.data["msg"])) {
        showToast(response.data["msg"]!);
      }
    }

    return false;
  }

  /// 编辑评论
  static Future<bool> commentEdit(Map<String, dynamic> parameters) async {
    var response = await NetWorkManager.instance.post(apiCommentEdit, parameters: parameters);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      NormalModel model = NormalModel.fromJson(response.data);

      if (model.code == 200) {
        return true;
      } else {
        if (isAvailable(model.msg)) {
          showToast(model.msg!);
        }
      }

      return false;
    } else {
      if (isAvailable(response.data["msg"])) {
        showToast(response.data["msg"]!);
      }
    }

    return false;
  }

  /// 删除评论
  static Future<bool> commentDelete(Map<String, dynamic> parameters) async {
    var response = await NetWorkManager.instance.get(apiCommentDelete, params: parameters);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      NormalModel model = NormalModel.fromJson(response.data);

      if (model.code == 200) {
        return true;
      } else {
        if (isAvailable(model.msg)) {
          showToast(model.msg!);
        }
      }

      return false;
    } else {
      if (isAvailable(response.data["msg"])) {
        showToast(response.data["msg"]!);
      }
    }

    return false;
  }

  ///下载离线书籍所有章节内容
  static Future<BookDetailInfoResultModel?> getNovelInfo(Map<String, dynamic> parameters,
      {required ProgressCallback onReceiveProgress}) async {
    var response = await NetWorkManager.instance
        .get(apiDownloadBookV2, onReceiveProgress: onReceiveProgress, params: parameters);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      BookDetailInfoModel? model = BookDetailInfoModel.fromJson(response.data);

      return model.result;
    }

    return null;
  }

  ///用户提交错误报告
  // BOOK_DETAIL("book_detail", "书籍详情错误报告"),
  // BOOK_READ("book_read", "书籍阅读错误报告"),
  // COMMENT("comment", "评论错误报告"),
  // COMMENT_SECOND("comment_second", "评论二级错误报告");
  //
  static Future<void> errorReport(Map<String, dynamic> parameters, String apiUrl) async {
    var response = await NetWorkManager.instance.post(apiUrl, parameters: parameters);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      NormalModel normalModel = NormalModel.fromJson(response.data);
      if (normalModel.code == 200) {
        showToast("thank_feedback".tr);
      }
    }
  }
}
