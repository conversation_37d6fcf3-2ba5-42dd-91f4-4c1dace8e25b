import 'package:json_annotation/json_annotation.dart';

part 'book_author_info.g.dart';

@JsonSerializable()
class BookAuthorInfoModel {
  int? code;
  BookAuthorResultModel? result;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  BookAuthorInfoModel(
      {this.code, this.result, this.msg, this.sysAt, this.cost, this.traceId});

  factory BookAuthorInfoModel.fromJson(Map<String, dynamic> json) =>
      _$BookAuthorInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$BookAuthorInfoModelToJson(this);
}

@JsonSerializable()
class BookAuthorResultModel {
  int? id; //作者id
  String? name; //作者名
  String? cover; //作者头像
  String? description; //作者简介
  String? reads; //作者书籍阅读量
  String? likes; //作者书籍收藏量
  String? novels; //作者书籍数量
  List<BookVoListItem>? bookVoList; //作者书籍列表

  BookAuthorResultModel(
      {this.id,
      this.name,
      this.cover,
      this.description,
      this.reads,
      this.likes,
      this.novels,
      this.bookVoList});

  factory BookAuthorResultModel.fromJson(Map<String, dynamic> json) =>
      _$BookAuthorResultModelFromJson(json);

  Map<String, dynamic> toJson() => _$BookAuthorResultModelToJson(this);
}

@JsonSerializable()
class BookVoListItem {
  int? bookId; // 书籍id
  int? bookLangId; // 书籍语言id
  int? authorId; // 作者id
  String? authorName; // 作者名
  String? title; // 书名
  String? cover; // 封面
  int? viewCount; // 阅读数
  bool? library; // 是否加入书架
  BookVoListItem(
      {this.bookId,
      this.bookLangId,
      this.authorId,
      this.authorName,
      this.title,
      this.cover,
      this.viewCount,
      this.library});

  factory BookVoListItem.fromJson(Map<String, dynamic> json) =>
      _$BookVoListItemFromJson(json);

  Map<String, dynamic> toJson() => _$BookVoListItemToJson(this);
}
