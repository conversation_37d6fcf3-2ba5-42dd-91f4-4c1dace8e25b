import 'package:json_annotation/json_annotation.dart';

part 'book_comments_model.g.dart';

@JsonSerializable()
class BookCommentsModel {
  int? code; //状态码
  BookCommentsRatingModel? result;
  String? msg; //返回信息
  int? sysAt; //时间
  int? cost; //耗时
  String? traceId; //日志跟踪id

  BookCommentsModel({
    this.code,
    this.result,
    this.msg,
    this.sysAt,
    this.cost,
    this.traceId,
  });

  factory BookCommentsModel.fromJson(Map<String, dynamic> json) =>
      _$BookCommentsModelFromJson(json);

  Map<String, dynamic> toJson() => _$BookCommentsModelToJson(this);
}

@JsonSerializable()
class BookCommentsRatingModel {
  List<BookCommentsItem>? list; //评论列表
  AuthorSimpleDtoModel? authorSimpleDto; //作者信息
  int? limit; //每页条数
  int? pageNumber; //当前页码
  int? pages; //总页数
  int? total; //总条数
  int? commentScoreCount; //评论总分数
  int? low; //起始条数
  int? high; //查看条数
  int? avgScore; //平均分
  double? avgStar; //平均星级
  double? score1Rate; //评分为1的占比
  double? score2Rate; //评分为2的占比
  double? score3Rate; //评分为3的占比
  double? score4Rate; //评分为4的占比
  double? score5Rate; //评分为5的占比

  BookCommentsRatingModel({
    this.list,
    this.authorSimpleDto,
    this.limit,
    this.pageNumber,
    this.pages,
    this.total,
    this.commentScoreCount,
    this.low,
    this.high,
    this.avgScore,
    this.avgStar,
    this.score1Rate,
    this.score2Rate,
    this.score3Rate,
    this.score4Rate,
    this.score5Rate,
  });

  factory BookCommentsRatingModel.fromJson(Map<String, dynamic> json) =>
      _$BookCommentsRatingModelFromJson(json);

  Map<String, dynamic> toJson() => _$BookCommentsRatingModelToJson(this);
}

@JsonSerializable()
class AuthorSimpleDtoModel {
  int? id;
  String? authorName;
  String? cover;

  AuthorSimpleDtoModel({
    this.id,
    this.authorName,
    this.cover,
  });

  factory AuthorSimpleDtoModel.fromJson(Map<String, dynamic> json) =>
      _$AuthorSimpleDtoModelFromJson(json);

  Map<String, dynamic> toJson() => _$AuthorSimpleDtoModelToJson(this);
}

@JsonSerializable()
class BookCommentsItem {
  int? id; //评论ID
  int? bookId; //书籍ID
  int? uid; //用户ID
  String? name; //用户昵称
  String? avatar; //头像
  String? content; //评论内容
  int? positionNum; //评论位置
  int? addTime; //评论时间
  double? score; //评分
  int? replyCount; //回复数量
  List<CommentsV2DtoItem>? commentsV2DtoList; //子评论列表

  BookCommentsItem({
    this.id,
    this.bookId,
    this.name,
    this.avatar,
    this.content,
    this.positionNum,
    this.addTime,
    this.score,
    this.replyCount,
    this.commentsV2DtoList,
  });

  factory BookCommentsItem.fromJson(Map<String, dynamic> json) => _$BookCommentsItemFromJson(json);

  Map<String, dynamic> toJson() => _$BookCommentsItemToJson(this);
}

//子评论列表
@JsonSerializable()
class CommentsV2DtoItem {
  int? id;
  int? bookId;
  int? uid;
  String? name;
  String? avatar;
  String? parentCommentId;
  String? receivedReplyUserId;
  String? receivedReplyUserName;
  bool? authorReply;
  String? content;
  int? positionNum;
  int? addTime;
  double? score;

  CommentsV2DtoItem({
    this.id,
    this.bookId,
    this.uid,
    this.name,
    this.avatar,
    this.parentCommentId,
    this.receivedReplyUserId,
    this.receivedReplyUserName,
    this.authorReply,
    this.content,
    this.positionNum,
    this.addTime,
    this.score,
  });

  factory CommentsV2DtoItem.fromJson(Map<String, dynamic> json) =>
      _$CommentsV2DtoItemFromJson(json);

  Map<String, dynamic> toJson() => _$CommentsV2DtoItemToJson(this);
}
