import 'package:json_annotation/json_annotation.dart';
part 'book_comments_add_model.g.dart';

@JsonSerializable()
class BookCommentsAddModel {
  int? code;//状态码
  int? result;//返回数据
  String? msg;//返回信息
  int? sysAt;//时间
  int? cost;//耗时
  String? traceId;//日志跟踪id

  BookCommentsAddModel({this.code, this.result, this.msg, this.sysAt, this.cost, this.traceId});

  factory BookCommentsAddModel.fromJson(Map<String, dynamic> json) => _$BookCommentsAddModelFromJson(json);

  Map<String, dynamic> toJson() => _$BookCommentsAddModelToJson(this);
}