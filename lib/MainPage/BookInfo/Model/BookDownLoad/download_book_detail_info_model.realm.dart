// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'download_book_detail_info_model.dart';

// **************************************************************************
// RealmObjectGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
class DownloadBookDetailInfoModel extends $DownloadBookDetailInfoModel
    with RealmEntity, RealmObjectBase, RealmObject {
  DownloadBookDetailInfoModel(
    int? bookId, {
    int? nextBookId,
    int? bookLangId,
    String? authorName,
    int? authorId,
    String? authorCover,
    String? title,
    String? cover,
    Iterable<String> tagList = const [],
    String? reads,
    String? likes,
    String? time,
    String? description,
    bool? library,
    bool? liked,
    int? bookGoldCount,
    String? contentUrl,
    int? lock,
    Iterable<DownloadChapterVoModel> chapterVoList = const [],
    Iterable<DownloadContentModel> contentList = const [],
  }) {
    RealmObjectBase.set(this, 'bookId', bookId);
    RealmObjectBase.set(this, 'nextBookId', nextBookId);
    RealmObjectBase.set(this, 'bookLangId', bookLangId);
    RealmObjectBase.set(this, 'authorName', authorName);
    RealmObjectBase.set(this, 'authorId', authorId);
    RealmObjectBase.set(this, 'authorCover', authorCover);
    RealmObjectBase.set(this, 'title', title);
    RealmObjectBase.set(this, 'cover', cover);
    RealmObjectBase.set<RealmList<String>>(
        this, 'tagList', RealmList<String>(tagList));
    RealmObjectBase.set(this, 'reads', reads);
    RealmObjectBase.set(this, 'likes', likes);
    RealmObjectBase.set(this, 'time', time);
    RealmObjectBase.set(this, 'description', description);
    RealmObjectBase.set(this, 'library', library);
    RealmObjectBase.set(this, 'liked', liked);
    RealmObjectBase.set(this, 'bookGoldCount', bookGoldCount);
    RealmObjectBase.set(this, 'contentUrl', contentUrl);
    RealmObjectBase.set(this, 'lock', lock);
    RealmObjectBase.set<RealmList<DownloadChapterVoModel>>(this,
        'chapterVoList', RealmList<DownloadChapterVoModel>(chapterVoList));
    RealmObjectBase.set<RealmList<DownloadContentModel>>(
        this, 'contentList', RealmList<DownloadContentModel>(contentList));
  }

  DownloadBookDetailInfoModel._();

  @override
  int? get bookId => RealmObjectBase.get<int>(this, 'bookId') as int?;
  @override
  set bookId(int? value) => RealmObjectBase.set(this, 'bookId', value);

  @override
  int? get nextBookId => RealmObjectBase.get<int>(this, 'nextBookId') as int?;
  @override
  set nextBookId(int? value) => RealmObjectBase.set(this, 'nextBookId', value);

  @override
  int? get bookLangId => RealmObjectBase.get<int>(this, 'bookLangId') as int?;
  @override
  set bookLangId(int? value) => RealmObjectBase.set(this, 'bookLangId', value);

  @override
  String? get authorName =>
      RealmObjectBase.get<String>(this, 'authorName') as String?;
  @override
  set authorName(String? value) =>
      RealmObjectBase.set(this, 'authorName', value);

  @override
  int? get authorId => RealmObjectBase.get<int>(this, 'authorId') as int?;
  @override
  set authorId(int? value) => RealmObjectBase.set(this, 'authorId', value);

  @override
  String? get authorCover =>
      RealmObjectBase.get<String>(this, 'authorCover') as String?;
  @override
  set authorCover(String? value) =>
      RealmObjectBase.set(this, 'authorCover', value);

  @override
  String? get title => RealmObjectBase.get<String>(this, 'title') as String?;
  @override
  set title(String? value) => RealmObjectBase.set(this, 'title', value);

  @override
  String? get cover => RealmObjectBase.get<String>(this, 'cover') as String?;
  @override
  set cover(String? value) => RealmObjectBase.set(this, 'cover', value);

  @override
  RealmList<String> get tagList =>
      RealmObjectBase.get<String>(this, 'tagList') as RealmList<String>;
  @override
  set tagList(covariant RealmList<String> value) =>
      throw RealmUnsupportedSetError();

  @override
  String? get reads => RealmObjectBase.get<String>(this, 'reads') as String?;
  @override
  set reads(String? value) => RealmObjectBase.set(this, 'reads', value);

  @override
  String? get likes => RealmObjectBase.get<String>(this, 'likes') as String?;
  @override
  set likes(String? value) => RealmObjectBase.set(this, 'likes', value);

  @override
  String? get time => RealmObjectBase.get<String>(this, 'time') as String?;
  @override
  set time(String? value) => RealmObjectBase.set(this, 'time', value);

  @override
  String? get description =>
      RealmObjectBase.get<String>(this, 'description') as String?;
  @override
  set description(String? value) =>
      RealmObjectBase.set(this, 'description', value);

  @override
  bool? get library => RealmObjectBase.get<bool>(this, 'library') as bool?;
  @override
  set library(bool? value) => RealmObjectBase.set(this, 'library', value);

  @override
  bool? get liked => RealmObjectBase.get<bool>(this, 'liked') as bool?;
  @override
  set liked(bool? value) => RealmObjectBase.set(this, 'liked', value);

  @override
  int? get bookGoldCount =>
      RealmObjectBase.get<int>(this, 'bookGoldCount') as int?;
  @override
  set bookGoldCount(int? value) =>
      RealmObjectBase.set(this, 'bookGoldCount', value);

  @override
  String? get contentUrl =>
      RealmObjectBase.get<String>(this, 'contentUrl') as String?;
  @override
  set contentUrl(String? value) =>
      RealmObjectBase.set(this, 'contentUrl', value);

  @override
  int? get lock => RealmObjectBase.get<int>(this, 'lock') as int?;
  @override
  set lock(int? value) => RealmObjectBase.set(this, 'lock', value);

  @override
  RealmList<DownloadChapterVoModel> get chapterVoList =>
      RealmObjectBase.get<DownloadChapterVoModel>(this, 'chapterVoList')
          as RealmList<DownloadChapterVoModel>;
  @override
  set chapterVoList(covariant RealmList<DownloadChapterVoModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  RealmList<DownloadContentModel> get contentList =>
      RealmObjectBase.get<DownloadContentModel>(this, 'contentList')
          as RealmList<DownloadContentModel>;
  @override
  set contentList(covariant RealmList<DownloadContentModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  Stream<RealmObjectChanges<DownloadBookDetailInfoModel>> get changes =>
      RealmObjectBase.getChanges<DownloadBookDetailInfoModel>(this);

  @override
  Stream<RealmObjectChanges<DownloadBookDetailInfoModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<DownloadBookDetailInfoModel>(
          this, keyPaths);

  @override
  DownloadBookDetailInfoModel freeze() =>
      RealmObjectBase.freezeObject<DownloadBookDetailInfoModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'bookId': bookId.toEJson(),
      'nextBookId': nextBookId.toEJson(),
      'bookLangId': bookLangId.toEJson(),
      'authorName': authorName.toEJson(),
      'authorId': authorId.toEJson(),
      'authorCover': authorCover.toEJson(),
      'title': title.toEJson(),
      'cover': cover.toEJson(),
      'tagList': tagList.toEJson(),
      'reads': reads.toEJson(),
      'likes': likes.toEJson(),
      'time': time.toEJson(),
      'description': description.toEJson(),
      'library': library.toEJson(),
      'liked': liked.toEJson(),
      'bookGoldCount': bookGoldCount.toEJson(),
      'contentUrl': contentUrl.toEJson(),
      'lock': lock.toEJson(),
      'chapterVoList': chapterVoList.toEJson(),
      'contentList': contentList.toEJson(),
    };
  }

  static EJsonValue _toEJson(DownloadBookDetailInfoModel value) =>
      value.toEJson();
  static DownloadBookDetailInfoModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'bookId': EJsonValue bookId,
      } =>
        DownloadBookDetailInfoModel(
          fromEJson(ejson['bookId']),
          nextBookId: fromEJson(ejson['nextBookId']),
          bookLangId: fromEJson(ejson['bookLangId']),
          authorName: fromEJson(ejson['authorName']),
          authorId: fromEJson(ejson['authorId']),
          authorCover: fromEJson(ejson['authorCover']),
          title: fromEJson(ejson['title']),
          cover: fromEJson(ejson['cover']),
          tagList: fromEJson(ejson['tagList']),
          reads: fromEJson(ejson['reads']),
          likes: fromEJson(ejson['likes']),
          time: fromEJson(ejson['time']),
          description: fromEJson(ejson['description']),
          library: fromEJson(ejson['library']),
          liked: fromEJson(ejson['liked']),
          bookGoldCount: fromEJson(ejson['bookGoldCount']),
          contentUrl: fromEJson(ejson['contentUrl']),
          lock: fromEJson(ejson['lock']),
          chapterVoList: fromEJson(ejson['chapterVoList']),
          contentList: fromEJson(ejson['contentList']),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(DownloadBookDetailInfoModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(ObjectType.realmObject,
        DownloadBookDetailInfoModel, 'DownloadBookDetailInfoModel', [
      SchemaProperty('bookId', RealmPropertyType.int,
          optional: true, primaryKey: true),
      SchemaProperty('nextBookId', RealmPropertyType.int, optional: true),
      SchemaProperty('bookLangId', RealmPropertyType.int, optional: true),
      SchemaProperty('authorName', RealmPropertyType.string, optional: true),
      SchemaProperty('authorId', RealmPropertyType.int, optional: true),
      SchemaProperty('authorCover', RealmPropertyType.string, optional: true),
      SchemaProperty('title', RealmPropertyType.string, optional: true),
      SchemaProperty('cover', RealmPropertyType.string, optional: true),
      SchemaProperty('tagList', RealmPropertyType.string,
          collectionType: RealmCollectionType.list),
      SchemaProperty('reads', RealmPropertyType.string, optional: true),
      SchemaProperty('likes', RealmPropertyType.string, optional: true),
      SchemaProperty('time', RealmPropertyType.string, optional: true),
      SchemaProperty('description', RealmPropertyType.string, optional: true),
      SchemaProperty('library', RealmPropertyType.bool, optional: true),
      SchemaProperty('liked', RealmPropertyType.bool, optional: true),
      SchemaProperty('bookGoldCount', RealmPropertyType.int, optional: true),
      SchemaProperty('contentUrl', RealmPropertyType.string, optional: true),
      SchemaProperty('lock', RealmPropertyType.int, optional: true),
      SchemaProperty('chapterVoList', RealmPropertyType.object,
          linkTarget: 'DownloadChapterVoModel',
          collectionType: RealmCollectionType.list),
      SchemaProperty('contentList', RealmPropertyType.object,
          linkTarget: 'DownloadContentModel',
          collectionType: RealmCollectionType.list),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class DownloadChapterVoModel extends $DownloadChapterVoModel
    with RealmEntity, RealmObjectBase, RealmObject {
  DownloadChapterVoModel(
    int? id, {
    int? bookId,
    String? content,
    int? chapterIndex,
    String? title,
    int? lock,
    int? cost,
    int? prevId,
    int? nextId,
    String? lang,
    Iterable<DownloadChapterVoiceModel> chapterVoice = const [],
  }) {
    RealmObjectBase.set(this, 'id', id);
    RealmObjectBase.set(this, 'bookId', bookId);
    RealmObjectBase.set(this, 'content', content);
    RealmObjectBase.set(this, 'chapterIndex', chapterIndex);
    RealmObjectBase.set(this, 'title', title);
    RealmObjectBase.set(this, 'lock', lock);
    RealmObjectBase.set(this, 'cost', cost);
    RealmObjectBase.set(this, 'prevId', prevId);
    RealmObjectBase.set(this, 'nextId', nextId);
    RealmObjectBase.set(this, 'lang', lang);
    RealmObjectBase.set<RealmList<DownloadChapterVoiceModel>>(this,
        'chapterVoice', RealmList<DownloadChapterVoiceModel>(chapterVoice));
  }

  DownloadChapterVoModel._();

  @override
  int? get id => RealmObjectBase.get<int>(this, 'id') as int?;
  @override
  set id(int? value) => RealmObjectBase.set(this, 'id', value);

  @override
  int? get bookId => RealmObjectBase.get<int>(this, 'bookId') as int?;
  @override
  set bookId(int? value) => RealmObjectBase.set(this, 'bookId', value);

  @override
  String? get content =>
      RealmObjectBase.get<String>(this, 'content') as String?;
  @override
  set content(String? value) => RealmObjectBase.set(this, 'content', value);

  @override
  int? get chapterIndex =>
      RealmObjectBase.get<int>(this, 'chapterIndex') as int?;
  @override
  set chapterIndex(int? value) =>
      RealmObjectBase.set(this, 'chapterIndex', value);

  @override
  String? get title => RealmObjectBase.get<String>(this, 'title') as String?;
  @override
  set title(String? value) => RealmObjectBase.set(this, 'title', value);

  @override
  int? get lock => RealmObjectBase.get<int>(this, 'lock') as int?;
  @override
  set lock(int? value) => RealmObjectBase.set(this, 'lock', value);

  @override
  int? get cost => RealmObjectBase.get<int>(this, 'cost') as int?;
  @override
  set cost(int? value) => RealmObjectBase.set(this, 'cost', value);

  @override
  int? get prevId => RealmObjectBase.get<int>(this, 'prevId') as int?;
  @override
  set prevId(int? value) => RealmObjectBase.set(this, 'prevId', value);

  @override
  int? get nextId => RealmObjectBase.get<int>(this, 'nextId') as int?;
  @override
  set nextId(int? value) => RealmObjectBase.set(this, 'nextId', value);

  @override
  String? get lang => RealmObjectBase.get<String>(this, 'lang') as String?;
  @override
  set lang(String? value) => RealmObjectBase.set(this, 'lang', value);

  @override
  RealmList<DownloadChapterVoiceModel> get chapterVoice =>
      RealmObjectBase.get<DownloadChapterVoiceModel>(this, 'chapterVoice')
          as RealmList<DownloadChapterVoiceModel>;
  @override
  set chapterVoice(covariant RealmList<DownloadChapterVoiceModel> value) =>
      throw RealmUnsupportedSetError();

  @override
  Stream<RealmObjectChanges<DownloadChapterVoModel>> get changes =>
      RealmObjectBase.getChanges<DownloadChapterVoModel>(this);

  @override
  Stream<RealmObjectChanges<DownloadChapterVoModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<DownloadChapterVoModel>(this, keyPaths);

  @override
  DownloadChapterVoModel freeze() =>
      RealmObjectBase.freezeObject<DownloadChapterVoModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'id': id.toEJson(),
      'bookId': bookId.toEJson(),
      'content': content.toEJson(),
      'chapterIndex': chapterIndex.toEJson(),
      'title': title.toEJson(),
      'lock': lock.toEJson(),
      'cost': cost.toEJson(),
      'prevId': prevId.toEJson(),
      'nextId': nextId.toEJson(),
      'lang': lang.toEJson(),
      'chapterVoice': chapterVoice.toEJson(),
    };
  }

  static EJsonValue _toEJson(DownloadChapterVoModel value) => value.toEJson();
  static DownloadChapterVoModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'id': EJsonValue id,
      } =>
        DownloadChapterVoModel(
          fromEJson(ejson['id']),
          bookId: fromEJson(ejson['bookId']),
          content: fromEJson(ejson['content']),
          chapterIndex: fromEJson(ejson['chapterIndex']),
          title: fromEJson(ejson['title']),
          lock: fromEJson(ejson['lock']),
          cost: fromEJson(ejson['cost']),
          prevId: fromEJson(ejson['prevId']),
          nextId: fromEJson(ejson['nextId']),
          lang: fromEJson(ejson['lang']),
          chapterVoice: fromEJson(ejson['chapterVoice']),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(DownloadChapterVoModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(ObjectType.realmObject, DownloadChapterVoModel,
        'DownloadChapterVoModel', [
      SchemaProperty('id', RealmPropertyType.int,
          optional: true, primaryKey: true),
      SchemaProperty('bookId', RealmPropertyType.int, optional: true),
      SchemaProperty('content', RealmPropertyType.string, optional: true),
      SchemaProperty('chapterIndex', RealmPropertyType.int, optional: true),
      SchemaProperty('title', RealmPropertyType.string, optional: true),
      SchemaProperty('lock', RealmPropertyType.int, optional: true),
      SchemaProperty('cost', RealmPropertyType.int, optional: true),
      SchemaProperty('prevId', RealmPropertyType.int, optional: true),
      SchemaProperty('nextId', RealmPropertyType.int, optional: true),
      SchemaProperty('lang', RealmPropertyType.string, optional: true),
      SchemaProperty('chapterVoice', RealmPropertyType.object,
          linkTarget: 'DownloadChapterVoiceModel',
          collectionType: RealmCollectionType.list),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class DownloadContentModel extends $DownloadContentModel
    with RealmEntity, RealmObjectBase, RealmObject {
  DownloadContentModel(
    int? id, {
    int? chapterIndex,
    String? content,
  }) {
    RealmObjectBase.set(this, 'id', id);
    RealmObjectBase.set(this, 'chapterIndex', chapterIndex);
    RealmObjectBase.set(this, 'content', content);
  }

  DownloadContentModel._();

  @override
  int? get id => RealmObjectBase.get<int>(this, 'id') as int?;
  @override
  set id(int? value) => RealmObjectBase.set(this, 'id', value);

  @override
  int? get chapterIndex =>
      RealmObjectBase.get<int>(this, 'chapterIndex') as int?;
  @override
  set chapterIndex(int? value) =>
      RealmObjectBase.set(this, 'chapterIndex', value);

  @override
  String? get content =>
      RealmObjectBase.get<String>(this, 'content') as String?;
  @override
  set content(String? value) => RealmObjectBase.set(this, 'content', value);

  @override
  Stream<RealmObjectChanges<DownloadContentModel>> get changes =>
      RealmObjectBase.getChanges<DownloadContentModel>(this);

  @override
  Stream<RealmObjectChanges<DownloadContentModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<DownloadContentModel>(this, keyPaths);

  @override
  DownloadContentModel freeze() =>
      RealmObjectBase.freezeObject<DownloadContentModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'id': id.toEJson(),
      'chapterIndex': chapterIndex.toEJson(),
      'content': content.toEJson(),
    };
  }

  static EJsonValue _toEJson(DownloadContentModel value) => value.toEJson();
  static DownloadContentModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'id': EJsonValue id,
      } =>
        DownloadContentModel(
          fromEJson(ejson['id']),
          chapterIndex: fromEJson(ejson['chapterIndex']),
          content: fromEJson(ejson['content']),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(DownloadContentModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, DownloadContentModel, 'DownloadContentModel', [
      SchemaProperty('id', RealmPropertyType.int,
          optional: true, primaryKey: true),
      SchemaProperty('chapterIndex', RealmPropertyType.int, optional: true),
      SchemaProperty('content', RealmPropertyType.string, optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class DownloadChapterVoiceModel extends $DownloadChapterVoiceModel
    with RealmEntity, RealmObjectBase, RealmObject {
  DownloadChapterVoiceModel(
    int? id, {
    String? voiceName,
    String? headImg,
    int? chapterId,
    String? name,
    String? url,
  }) {
    RealmObjectBase.set(this, 'id', id);
    RealmObjectBase.set(this, 'voiceName', voiceName);
    RealmObjectBase.set(this, 'headImg', headImg);
    RealmObjectBase.set(this, 'chapterId', chapterId);
    RealmObjectBase.set(this, 'name', name);
    RealmObjectBase.set(this, 'url', url);
  }

  DownloadChapterVoiceModel._();

  @override
  int? get id => RealmObjectBase.get<int>(this, 'id') as int?;
  @override
  set id(int? value) => RealmObjectBase.set(this, 'id', value);

  @override
  String? get voiceName =>
      RealmObjectBase.get<String>(this, 'voiceName') as String?;
  @override
  set voiceName(String? value) => RealmObjectBase.set(this, 'voiceName', value);

  @override
  String? get headImg =>
      RealmObjectBase.get<String>(this, 'headImg') as String?;
  @override
  set headImg(String? value) => RealmObjectBase.set(this, 'headImg', value);

  @override
  int? get chapterId => RealmObjectBase.get<int>(this, 'chapterId') as int?;
  @override
  set chapterId(int? value) => RealmObjectBase.set(this, 'chapterId', value);

  @override
  String? get name => RealmObjectBase.get<String>(this, 'name') as String?;
  @override
  set name(String? value) => RealmObjectBase.set(this, 'name', value);

  @override
  String? get url => RealmObjectBase.get<String>(this, 'url') as String?;
  @override
  set url(String? value) => RealmObjectBase.set(this, 'url', value);

  @override
  Stream<RealmObjectChanges<DownloadChapterVoiceModel>> get changes =>
      RealmObjectBase.getChanges<DownloadChapterVoiceModel>(this);

  @override
  Stream<RealmObjectChanges<DownloadChapterVoiceModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<DownloadChapterVoiceModel>(this, keyPaths);

  @override
  DownloadChapterVoiceModel freeze() =>
      RealmObjectBase.freezeObject<DownloadChapterVoiceModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'id': id.toEJson(),
      'voiceName': voiceName.toEJson(),
      'headImg': headImg.toEJson(),
      'chapterId': chapterId.toEJson(),
      'name': name.toEJson(),
      'url': url.toEJson(),
    };
  }

  static EJsonValue _toEJson(DownloadChapterVoiceModel value) =>
      value.toEJson();
  static DownloadChapterVoiceModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'id': EJsonValue id,
      } =>
        DownloadChapterVoiceModel(
          fromEJson(ejson['id']),
          voiceName: fromEJson(ejson['voiceName']),
          headImg: fromEJson(ejson['headImg']),
          chapterId: fromEJson(ejson['chapterId']),
          name: fromEJson(ejson['name']),
          url: fromEJson(ejson['url']),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(DownloadChapterVoiceModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(ObjectType.realmObject, DownloadChapterVoiceModel,
        'DownloadChapterVoiceModel', [
      SchemaProperty('id', RealmPropertyType.int,
          optional: true, primaryKey: true),
      SchemaProperty('voiceName', RealmPropertyType.string, optional: true),
      SchemaProperty('headImg', RealmPropertyType.string, optional: true),
      SchemaProperty('chapterId', RealmPropertyType.int, optional: true),
      SchemaProperty('name', RealmPropertyType.string, optional: true),
      SchemaProperty('url', RealmPropertyType.string, optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}
