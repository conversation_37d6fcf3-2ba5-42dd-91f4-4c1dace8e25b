import 'package:realm/realm.dart';

part 'download_book_detail_info_model.realm.dart';

@RealmModel()
class $DownloadBookDetailInfoModel {
  @PrimaryKey()
  int? bookId; //书籍id
  int? nextBookId; //下一本书籍id
  int? bookLangId; //书籍语言id
  String? authorName; //作者名称
  int? authorId; //作者id
  String? authorCover; //作者头像
  String? title; //标题
  String? cover; //封面
  late List<String> tagList; //标签列表
  String? reads; //阅读数
  String? likes; //收藏数
  String? time; //阅读时长
  String? description; //描述
  bool? library; //是否加入书架
  bool? liked; //是否加入收藏
  int? bookGoldCount; //书籍金币数
  String? contentUrl; //书籍内容地址
  int? lock; //书籍锁的状态（0-解锁，1-试看，需要购买，2-已购买） 只有书籍下载才有这个字段
  late List<$DownloadChapterVoModel> chapterVoList; //章节数据
  late List<$DownloadContentModel> contentList; //章节内容数据
}

///章节信息
@RealmModel()
class $DownloadChapterVoModel {
  @PrimaryKey()
  int? id; //章节ID
  int? bookId; //书籍ID
  String? content; //章节内容 (只有下一本书才有第一章的内容)
  int? chapterIndex; //章节索引
  String? title; //章节标题
  int? lock; //章节解锁状态，0免费，1-试看，需要购买，需要购买，2-已购买
  int? cost; //章节价格
  int? prevId; //上一章节ID (废弃)
  int? nextId; //下一章节ID (废弃)
  String? lang; //章节语言项
  late List<$DownloadChapterVoiceModel> chapterVoice; //章节语音文件
}


///章节内容信息
@RealmModel()
class $DownloadContentModel {
  @PrimaryKey()
  int? id;
  int? chapterIndex;
  String? content;
}


///章节语音信息
@RealmModel()
class $DownloadChapterVoiceModel {
  @PrimaryKey()
  int? id; //id
  String? voiceName; //发生人
  String? headImg; //人物头像名称
  int? chapterId; //章节id
  String? name; //语音名称
  String? url; //语音文件链接
}