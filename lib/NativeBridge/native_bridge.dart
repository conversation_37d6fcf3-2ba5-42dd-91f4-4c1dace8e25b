import 'dart:io';

import 'package:UrNovel/Util/ThirdSdkManger/third_manger.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:flutter/services.dart';

import '../Util/enum.dart';
import '../Util/logUtil.dart';
import 'native_mgr.dart';

class NativeBridge {
  // NativeBridge._();
  // static final instance = NativeBridge._();

  static NativeBridge? _instance;

  NativeBridge._internal() {}

  static NativeBridge get instance {
    _instance ??= NativeBridge._internal();
    return _instance!;
  }

  // 定义 channel
  static const platform = MethodChannel('native_bridge_channel');

  // 调用原生方法
  Future<void> callNativeMethod() async {
    try {
      logD("Flutter: callNativeMethod");
      final result = await platform.invokeMethod('methodName', {
        'param1': 'value1',
        'param2': 'value2',
      });
      logD('Flutter callNativeMethod Result: $result');
    } catch (e) {
      logW('Error: $e');
    }
  }

  Future<void> callTiktokLogin(
      Function(String)? tiktokLoginSucCallback, Function(String)? tiktokLoginFailedCallback) async {
    NativeMgr.instance.tiktokLoginSucCallback = tiktokLoginSucCallback;
    NativeMgr.instance.tiktokLoginFailedCallback = tiktokLoginFailedCallback;
    try {
      logD("Flutter: callTiktokLogin");
      await platform.invokeMethod('tiktokLogin');
      // logD('Flutter callNativeMethod Result: $result');
    } catch (e) {
      logW('Error: $e');
    }
  }

  Future<void> callTiktokShare(String path, Function(String) tiktokShareSucCallback,
      Function(String) tiktokShareFailedCallback) async {
    NativeMgr.instance.tiktokShareSucCallback = tiktokShareSucCallback;
    NativeMgr.instance.tiktokShareFailedCallback = tiktokShareFailedCallback;
    try {
      logD("Flutter: callTiktokShare");
      if (Platform.isIOS) {
        await platform.invokeMethod('tiktokShare', {
          'path': path,
        });
        // logD('Flutter callNativeMethod Result: $result');
      } else {
        await platform.invokeMethod('tiktokShare', {
          'path': path,
        });
        // logD('Flutter callNativeMethod Result: $result');
      }
    } catch (e) {
      logW('Error: $e');
    }
  }

  void init() {
    logD("Flutter: init");

    // 接收原生端的调用
    platform.setMethodCallHandler((call) async {
      logD("Native Call Flutter: ${call.method} ${call.arguments}");

      switch (call.method) {
        case 'fromNative':
          return handleFromNative(call.arguments);
        case 'tiktokLoginSuc':
          return handleTiktokLoginSuc(call.arguments);
        case 'tiktokLoginFailed':
          return handleTiktokLoginFailed(call.arguments);
        case 'tiktokShareSuc':
          return handleTiktokShareSuc(call.arguments);
        case 'tiktokShareFailed':
          return handleTiktokShareFailed(call.arguments);
        case 'liveActivityNovel':
          copyToClipboard(call.arguments);
        default:
          throw PlatformException(
            code: 'NotImplemented',
            message: 'Method not implemented',
          );
      }
    });
  }

  void handleFromNative(String arguments) {
    logD("handleFromNative: $arguments");
  }

  void handleTiktokLoginSuc(arguments) {
    logD("handleTiktokLoginSuc: $arguments");
    if (NativeMgr.instance.tiktokLoginSucCallback != null) {
      NativeMgr.instance.tiktokLoginSucCallback!(arguments);
    }
  }

  void handleTiktokLoginFailed(arguments) {
    logD("handleTiktokLoginFailed: $arguments");
    if (NativeMgr.instance.tiktokLoginFailedCallback != null) {
      NativeMgr.instance.tiktokLoginFailedCallback!(arguments);
    }
  }

  void handleTiktokShareSuc(arguments) {
    logD("handleTiktokShareSuc: $arguments");
    if (NativeMgr.instance.tiktokShareSucCallback != null) {
      NativeMgr.instance.tiktokShareSucCallback!(arguments);
    }
  }

  void handleTiktokShareFailed(arguments) {
    logD("handleTiktokShareFailed: $arguments");
    logD(
        "handleTiktokShareFailed: tiktokShareFailedCallback == null  ${NativeMgr.instance.tiktokShareFailedCallback == null}");
    if (NativeMgr.instance.tiktokShareFailedCallback != null) {
      NativeMgr.instance.tiktokShareFailedCallback!(arguments);
    }
  }
}
