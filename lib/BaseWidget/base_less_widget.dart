import 'package:flutter/cupertino.dart';

import '../Util/DataReportManager/ServiceReport/ViewModel/data_report_viewModel.dart';
import '../Util/tools.dart';


class BaseLessWidget extends StatelessWidget {
  final dynamic arguments;
  BaseLessWidget({super.key, this.arguments});

  //todo:列表书籍曝光
  final List<int> exposureList = [];

  Future<void> dealExposureList(List<dynamic>? list, int rangeIndex) async {
    await DataReportViewModel.dealExposureList(list, rangeIndex);
  }

  //todo:书籍曝光上报
  Future<void> exposureReport() async {
    if (exposureList.isNotEmpty) {
      await DataReportViewModel.reportBookExposure(exposureList);
      exposureList.removeRange(0, exposureList.length);
    }
  }

  ///发送事件
  void eventBusFire(dynamic event) {
    eventBusUtil.fire(event);
  }

  ///监听事件
  ///T 事件传递对象类型
  ///listener 监听回调
  void eventBusOn<T>(Function(dynamic) listener) {
    eventBusUtil.on<T>().listen((obj) {
      listener(obj);
    });
  }

  @override
  Widget build(BuildContext context) {
    return const Placeholder();
  }
}
