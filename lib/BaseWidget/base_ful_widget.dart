import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:screen_protector/screen_protector.dart';

import '../MainPage/BookInfo/ViewModel/ViewModel.dart';
import '../Util/DataReportManager/ServiceReport/ViewModel/data_report_viewModel.dart';
import '../Util/api_config.dart';
import '../Util/enum.dart';
import '../Util/tools.dart';

class BaseFulWidget extends StatefulWidget {
  final dynamic arguments;

  BaseFulWidget({super.key, this.arguments});

  //todo:刷新refreshController状态
  final RefreshController refreshController = RefreshController();

  //todo:列表书籍曝光
  final List<int> exposureList = [];

  void dealRefreshState(int pageIndex, int pageSize, List? list) {
    if (pageIndex == 1) {
      refreshController.refreshCompleted();
      if (isAvailable(list)) {
        if (list!.length < pageSize) {
          refreshController.loadNoData();
        } else {
          refreshController.footerMode?.value = LoadStatus.idle;
        }
      } else {
        refreshController.loadNoData();
      }
    } else {
      if (isAvailable(list)) {
        if (list!.length < pageIndex * pageSize) {
          refreshController.loadNoData();
        } else {
          refreshController.loadComplete();
        }
      } else {
        refreshController.loadNoData();
      }
    }
  }

  Future<void> dealExposureList(List<dynamic>? list, int rangeIndex) async {
    await DataReportViewModel.dealExposureList(list, rangeIndex);
  }

  //todo:书籍曝光上报
  Future<void> exposureReport() async {
    if (exposureList.isNotEmpty) {
      await DataReportViewModel.reportBookExposure(exposureList);
    }
  }

  @override
  State<BaseFulWidget> createState() => _BaseFulWidgetState();
}

showSystemUiMode() {
  SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
}

hideSystemUiMode() {
  SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
}

setSystemUiLight() {
  SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.light);
}

setSystemUiDark() {
  SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);
}

void preventScreenshotOn() async => await ScreenProtector.preventScreenshotOn();

void preventScreenshotOff() async => await ScreenProtector.preventScreenshotOff();

void unFocusScope(BuildContext context) {
  FocusScopeNode currentFocus = FocusScope.of(context);
  if (!currentFocus.hasPrimaryFocus) {
    currentFocus.unfocus();
  }
}

///判定商品是否是活动商品
bool isGoodsActivity(bool? activityPricePower, String? activityType) {
  //活动类型 ("First_Charge", "首充活动"), ("Discount", "额外赠送&折扣活动"), ("Best_Deal", "最佳交易"),
  if (activityPricePower == true &&
      (activityType == 'First_Charge' ||
          activityType == 'Discount' ||
          activityType == 'Best_Deal')) {
    return true;
  }

  return false;
}

//todo:举报
Future<void> errorReport(ReportType reportType, List<dynamic> content,
    {int? commentId, int? bookId, String? chatId, int? messageId}) async {
  //todo: 错误反馈
  String apiUrl = apiUserErrorReport;
  var params = {
    "commentId": commentId,
    "bookId": bookId,
    "errorReportType": reportType,
    "content": content,
  };
  if (reportType == ReportType.chat) {
    apiUrl = chatMessageReport;
    params = {
      "chatId": chatId,
      "messageId": messageId,
      "content": content,
    };
  }
  await BookInfoViewModel.errorReport(params, apiUrl);
}

class _BaseFulWidgetState extends State<BaseFulWidget> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return const Placeholder();
  }
}
