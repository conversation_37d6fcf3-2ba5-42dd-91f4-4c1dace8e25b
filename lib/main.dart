import 'dart:io';

import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Launch&Login/Page/ad_launch_page.dart';
import 'package:UrNovel/Launch&Login/Page/login_page.dart';
import 'package:UrNovel/Util/Common/CommonManager/common_manager.dart';
import 'package:UrNovel/Util/DataReportManager/event_name_config.dart';
import 'package:UrNovel/Util/SharedPreferences/shared_preferences.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

import './Router/router.dart';
import 'Launch&Login/ViewModel/ViewModel.dart';
import 'MainPage/Rankings/ViewModel/ranking_viewModel.dart';
import 'NativeBridge/native_bridge.dart';
import 'Util/Common/RouteObserver/my_route_observer.dart';
import 'Util/DataReportManager/ServiceReport/data_report_manager.dart';
import 'Util/DataReportManager/event_report_manager.dart';
import 'Util/Extensions/colorUtil.dart';
import 'Util/LanguageManager/MyTranslations.dart';
import 'Util/NetWorkManager/net_work_manager.dart';
import 'Util/PaymentManager/payment_manager.dart';
import 'Util/PermissionManager/permission_manager.dart';
import 'Util/ThirdSdkManger/third_manger.dart';
import 'Util/enum.dart';
import 'Util/logUtil.dart';
import 'Util/tools.dart';

Future<void> main() async {
  // 禁用调试标志
  debugPrintRebuildDirtyWidgets = false;
  debugPrintLayouts = false;

  /// 在调用sdk初始化方法之前准备好 Flutter 框架的运行环境。
  /// 这对于那些需要在应用展示之前完成的初始化操作是至关重要的
  WidgetsFlutterBinding.ensureInitialized();
  //TODO: 初始化dio
  await NetWorkManager.instance.initDio();
  //TODO: 初始化第三方SDK
  await ThirdManger.initSDK();
  // TODO: 初始化语言等配置数据
  await CommonManager.instance.initConfigData();
  //TODO: 任务开关查询(查询所有任务开关)
  await CommonManager.instance.getSwitchByCode();
  //TODO: 初始化有声播放器
  await audioBookController.initAudioHandler();
  NativeBridge.instance.init();
  await EventReportManager.eventReportOfCommon(appLaunch);
  await EventReportManager.eventReportOfFacebook(fbActivatedApp);

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => RankingViewModel()),
        ChangeNotifierProvider(create: (_) => LoginViewModel()),
        // 其他 Provider 可以在这里添加
      ],
      child: MyApp(),
    ),
  );
}

class MyApp extends BaseFulWidget {
  MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    setSystemUiDark();
    SpUtil.spSetIsColdStart(true);
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this); // 移除观察者
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    logP("AppLifecycleState change: $state");
    currentLifecycleState = state;
    switch (state) {
      case AppLifecycleState.resumed:
        logP('App is in the foreground');
        setState(() {});
        //TODO: 追踪权限申请
        PermissionManager.instance.requestTrackingTransparencyPermission();
        //TODO: 检查剪切板的内容
        await ThirdManger.handleClipboardContent();
        //TODO: 通知授权状态上报
        ThirdManger.reportNotificationStatus();
        eventBusFire(state);
        break;
      case AppLifecycleState.inactive:
        logP('App is inactive');
        eventBusFire(state);
        break;
      case AppLifecycleState.paused:
        logP('App is in the background');
        eventBusFire(state);
        break;
      case AppLifecycleState.detached:
        logP('App is detached');
        eventBusUtil.dispose();
        await SpUtil.spSetLastReadType(audioBookFloating?.mounted == true
            ? ReadReportReadingType.listen
            : ReadReportReadingType.reading);
        await removeAudioBookFloatingOverlay();
        await PaymentManager.instance.disposeStore();
        await PaymentManager.instance.onClose();
        break;
      case AppLifecycleState.hidden:
        // TODO: Handle this case.
        break;
    }
  }

  // 静态变量，用于跟踪是否已初始化
  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Widget>(
        future: _buildFuture(context),
        builder: (context, snapshot) {
          if (snapshot.hasData) {
            return snapshot.data!;
          }
          return _buildSplashScreen();
        });
  }

  Widget _buildSplashScreen() {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: Scaffold(
          body: SafeArea(
              child: SizedBox(
                  width: double.infinity,
                  height: double.infinity,
                  child: Image.asset('assets/images/launch/splash_screen.png',
                      fit: BoxFit.cover) // 启动图,
                  ))),
    );
  }

  Future<Widget> _buildFuture(BuildContext context) async {
    final PageRouteObserver<PageRoute> routeObserver = PageRouteObserver<PageRoute>();
    //TODO: 启动页
    bool isLogin = await SpUtil.isLogin();
    Locale skinLanguage = CommonManager.instance.skinLanguage;
    return ScreenUtilInit(
      designSize: Platform.isIOS ? const Size(375, 812) : const Size(360, 800),
      minTextAdapt: true,
      splitScreenMode: true,
      //分屏模式，折叠屏
      // Use builder only if you need to use library outside ScreenUtilInit context
      builder: (_, child) {
        return GestureDetector(
          onTap: () {
            // 点击键盘外部区域时关闭键盘
            FocusScopeNode currentFocus = FocusScope.of(context);
            if (!currentFocus.hasPrimaryFocus) {
              currentFocus.unfocus();
            }
          },
          child: GetMaterialApp(
            debugShowCheckedModeBanner: false,
            navigatorObservers: [routeObserver],
            initialRoute: isLogin ? '/adLaunchPage' : '/loginPage',
            builder: EasyLoading.init(),
            // 使用GetX的Translation
            translations: MyTranslations(),
            // 设置当前皮肤语言环境
            locale: skinLanguage,
            //默认皮肤语言环境
            fallbackLocale: MyTranslations.fallbackSkinLanguage(),
            //支持的皮肤语言列表
            supportedLocales: MyTranslations.skinSupportList(),
            // 支持的语言环境列表
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            localeResolutionCallback: (locale, supportedLocales) {
              for (var supportedLocale in supportedLocales) {
                if (supportedLocale.languageCode == locale?.languageCode &&
                    supportedLocale.countryCode == locale?.countryCode) {
                  return supportedLocale;
                }
              }
              return supportedLocales.first; // 如果不支持，返回第一个支持的语言环境
            },
            onGenerateRoute: onGenerateRoute,
            theme: ThemeData(
              useMaterial3: true,
              // 去除TabBar底部线条
              // tabBarTheme: TabBarTheme(dividerColor: HexColor.pageThemeColor),
              appBarTheme: AppBarTheme(backgroundColor: HexColor.appBarThemeColor),
              primarySwatch: Colors.blue,
              primaryColor: HexColor.appBarThemeColor,
              // 设置主题颜色
              scaffoldBackgroundColor: HexColor.pageThemeColor, // 设置页面背景颜色
            ),
          ),
        );
      },
      child: isLogin ? AdLaunchPage() : LoginPage(),
    );
  }
}
