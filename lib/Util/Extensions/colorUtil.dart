import 'dart:ui';

//传字符串‘#--’，默认1.0透明度
class HexColor extends Color {
  //主题色
  static Color appBarThemeColor = HexColor('#F4F5F6');
  static Color tabBarThemeColor = HexColor('#F4F5F6');
  static Color pageThemeColor = HexColor('#F4F5F6');

  static int _getColorFromHex(String hexColor) {
    hexColor = hexColor.toUpperCase().replaceAll("#", "");
    if (hexColor.length == 6) {
      hexColor = "FF$hexColor";
    }
    return int.parse(hexColor, radix: 16);
  }

  static hexColor(int hex, {double alpha = 1}) {
    if (alpha < 0) {
      alpha = 0;
    } else if (alpha > 1) {
      alpha = 1;
    }
    return Color.fromRGBO((hex & 0xFF0000) >> 16, (hex & 0x00FF00) >> 8,
        (hex & 0x0000FF) >> 0, alpha);
  }

  HexColor(final String hexColor) : super(_getColorFromHex(hexColor));
}


//传int 0x--，透明度可自定义
class ColorsUtil{
  /// 十六进制颜色，
  /// hex, 十六进制值，例如：0xffffff,
  /// alpha, 透明度 [0.0,1.0]

  //主题色
  static Color tabBarThemeColor = hexColor(0xF4F5F6);
  static Color pageThemeColor = hexColor(0xF4F5F6);

  static Color hexColor(int hex, {double alpha = 1.0}) {
    if (alpha < 0) {
      alpha = 0;
    } else if (alpha > 1) {
      alpha = 1;
    }
    return Color.fromRGBO((hex & 0xFF0000) >> 16, (hex & 0x00FF00) >> 8,
        (hex & 0x0000FF) >> 0, alpha);
  }
}
