///
///todo：泛型工具类
///
import 'package:get/get.dart';

///
///----------GetX状态管理器获取,保证内存中只有一份instance----------
///
T findGetXInstance<T extends GetxController>(T object) {
  try {
    return findController<T>();
  } catch (e) {
    return putController(object);
  }
}

// 泛型方式注入控制器
T putController<T>(T controller, {String? tag, bool permanent = false}) {
  return Get.put<T>(controller, tag: tag, permanent: permanent);
}

// 泛型方式查找控制器
T findController<T>({String? tag}) {
  try {
    return Get.find<T>(tag: tag);
  } catch (e) {
    throw 'Controller of type $T not found. Did you forget to call Get.put()?';
  }
}

// 泛型方式懒加载注入
void lazyPutController<T>(InstanceBuilderCallback<T> builder, {
  String? tag,
  bool fenix = false,
}) {
  Get.lazyPut<T>(builder, tag: tag, fenix: fenix);
}