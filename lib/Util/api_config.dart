/*
api novel.urnovel.com
后台 manage.urnovel.com
官网, 隐私协议等对外 www.urnovel.com
 */

//todo:domain域名配置
//测试
// const String baseUrl = "https://localurnovel.japanovel.com:9080";
//online
// const String baseUrl = "https://ur.novelonly.com";
//线上
const String baseUrl = "https://novel.urnovel.com";

//TODO:业务接口配置
///版本更新
const String apiVersionUpdate = "/novel/app/versionUpdate";

///推送token上传
const String apiSaveFireBaseToken = "/novel/user/saveUserFireBaseToken";

///启动推广页&登录&信息采集
//登录
const String apiAuthLogin = "/auth/login";
//退出登录
const String apiAuthLoginOut = "/auth/logout";
//用户注销,删除账号
const String apiAuthDestroyed = "/auth/destroyed";
//刷新token
const String apiAuthRefreshToken = "/auth/refreshToken";
//获取用户数据
const String apiGetUserPersonalData = "/novel/user/getPersonalPageData";
//推广
const String apiPromotionContent = "/novel/home/<USER>";
//获取app内容语言
const String apiGetAppLanguage = "/novel/app/language";
//欢迎页保存用户选择数据
const String apiSaveNewUserChooseData = "/novel/user/saveNewUserChooseData";
//根据品牌动态获取登录方式
const String apiGetLoginWay = "/novel/app/getLoginWay";

///用户签到
//获取用户签到信息
const String apiGetUserSignInInfo = "/novel/user/getUserSignInInfo";
//用户签到
const String apiUserSignIn = "/novel/user/userSignIn";

///首页
//获取搜索默认书籍
const String apiDefaultSearchBook = "/novel/home/<USER>";
//获取首页Banner数据
const String apiBenefitAndBanner = "/novel/home/<USER>";
//阅读首页其他列表数据
const String apiHomeReadData = "/novel/home/<USER>";
//阅读首页栏目&栏目二级列表数据
const String apiHomeReadColumnData = "/novel/home/<USER>";
//获取排行榜数据
const String apiRankingDataV2 = "/novel/home/<USER>";

//搜索书籍
const String apiSearchBook = "/novel/books/searchBook";
//获取首页弹窗推荐内容
const String apiIndexRecommendBook = "/novel/home/<USER>";

///书架
//MyList
const String apiLibraryMyList = "/novel/library/myListPage";
//用户书架收藏列表
const String apiLibraryGetLiked = "/novel/library/getLikedPage";
//用户书架历史列表
const String apiLibraryGetHistory = "/novel/library/getHistory";
//用户书架离线列表
const String apiLibraryGetOffline = "/novel/library/getOfflinePage";
//用户书架添加书籍
const String apiLibraryAdd = "/novel/library/addLibrary";
//用户书架删除书籍
const String apiLibraryRemove = "/novel/library/removeLibrary";
//用户取消书架中书籍
const String apiLibraryCancel = "/novel/library/cancelLibrary";

///分类
//获取所有书籍分类列表
const String apiGetCategoryList = "/novel/category/categoryList";
//获取所有书籍分类列表V2
const String apiGetCategoryListV2 = "/novel/category/categoryListV2";
//获取标签分组列表
const String apiGetTagGroupList = "/novel/category/getTagGroupList";
//根据标签分组id获取分组下的标签列表
const String apiGetTagList = "/novel/category/getTagListByGroupId";

///二级页面
//获取分类书籍
const String apiGetBookByCategory = "/novel/category/getBookByCategory";
//获取标签书籍
const String apiGetBookByTag = "/novel/category/getBookByTag";

///书籍详情
//获取作者信息
const String apiGetAuthorInfo = "/novel/author/getAuthorInfo";
//评论列表V2
const String apiCommentsListV2 = "/novel/comments/listV2";
//添加评论
const String apiCommentAdd = "/novel/comments/add";
//编辑评论
const String apiCommentEdit = "/novel/comments/edit";
//删除评论
const String apiCommentDelete = "/novel/comments/remove";
//批量购买章节
const String apiBatchBuyChapter = "/novel/chapter/batchBuyChapter";
//下载书籍
const String apiDownloadBookV2 = "/novel/chapter/downloadBookV2";
//用户提交错误报告
const String apiUserErrorReport = "/novel/user/userErrorReport";
//获取阅读页推荐订阅商品
const String apiReadRecommendSubGoodList = "/shop/getReadRecommendSubGoodList";
//获取阅读页推荐金币商品
const String apiReadRecommendCoinGoodList = "/shop/getReadRecommendCoinGoodList";
//获取书籍和章节信息V2-内容改为地址
const String apiGetBookAllChapterInfoV2 = "/novel/books/getBookChapterInfoV2";
//获取书籍信息以及第一章节内容
const String apiGetBookFirstChapterInfo = "/novel/books/getBookFirstChapterInfo";
//下载s3上面的文件(书籍章节内容)
const String apiDownloadS3File = "/file/download/s3";
//打赏
const String apiAuthorReward = "/novel/chapter/reward";

///个人信息资料
//编辑个人信息
const String apiEditUserInfo = "/novel/user/editUserInfo";
//获取用户收货地址列表
//获取商品列表
const String apiGetGoodsList = "/shop/getGoodList";
//创建订单
const String apiCreateShopOrder = "/shop/order";
//服务验签
const String apiShopOrderValidateV2 = "/shop/order/validateV2";
//服务验签V3
const String apiShopOrderValidateV3 = "/shop/order/appleValidateV3";
//收集af数据
const String apiSaveAFMessage = "/novel/user/saveAFMessage";

///获取分享渠道链接列表
const String apiGetShareBookUrl = "/share/getShareBookUrl";

///进入聊天-聊天信息查询
const String chatStartChat = "/api/v1/chat/start/chat";

///聊天消息发送
const String chatMessageSend = "/api/v1/chat/message/send";

///ai继续发言
const String chatMessageContinue = "/api/v1/chat/message/continue";

///重新生成ai回复
const String chatMessageRegenerate = "/api/v1/chat/message/regenerate";

///举报
const String chatMessageReport = "/api/v1/chat/message/report";

///user回复推荐
const String chatMessageRecommend = "/api/v1/chat/message/recommend";

///ForYou
const String chatBookPage = "/api/v1/chat/book_page";

///聊天历史列表
const String chatList = "/api/v1/chat/list";

///人物场景，信息
const String chatPersonScene = "/api/v1/chat/person/scene";

///根据功能code返回对应开关状态
const String apiGetSwitchByCode = "/task/getSwitchByCode";

//TODO:打点相关接口
///app打开行为打点
const String apiAppOpenRecord = "/novel/event/app/open";

///章节阅读记录打点
const String apiChapterReadRecord = "/novel/event/chapter/read";

///书籍曝光打点
const String apiBookExposureRecord = "/novel/event/book/exposure";

///保存用户阅读记录
const String apiSaveUserReadingHistory = "/novel/event/saveUserReadingHistory";

///插图打点
const String apiEventIllustration = "/novel/event/illustration";

///查询当前用户点赞了的所有插图信息
const String apiIllustrationLikeAll = "/novel/illustration/all";

///firebase推送事件打点
const String apiEventFirebasePush = "/novel/event/firebasePush";
