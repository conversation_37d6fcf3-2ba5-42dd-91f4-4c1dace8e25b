import 'dart:convert';
import 'dart:io';

import 'package:UrNovel/Util/logUtil.dart';
import 'package:path_provider/path_provider.dart';

import '../../MainPage/NovelRead/Model/content_list_model.dart';
import '../NetWorkManager/net_work_manager.dart';
import '../api_config.dart';
import '../tools.dart';

class FileExporter {
  static Future<void> exportBookList() async {
    List<dynamic> bookIdList = [];
    if(bookIdList.isEmpty) return;

    List<Map> bookList = [];
    for (var bookId in bookIdList) {
      var parameters = {
        'bookId': bookId,
        "eventWay": getBookDetailJumpType(null)
      };
      var bookResponse = await NetWorkManager.instance
          .get(apiGetBookAllChapterInfoV2, params: parameters);
      if (bookResponse.statusCode == 200 && bookResponse.data is Map<String, dynamic>) {
        Map? result = bookResponse.data?['result'];
        result?['contentList'] = null;
        var parameters = {
          'url': result?['contentUrl'],
        };

        //内容
        var response = await NetWorkManager.instance.post(apiDownloadS3File,
            parameters: parameters);
        if (response.statusCode == 200 &&
            isAvailable(response.data) &&
            response.data is String) {
          List<dynamic> list = jsonDecode(response.data);
          Map<String, dynamic> map = {'contents': list};
          list.map((e) {
            if (e is Map<String, dynamic>) {
              if (isAvailable(e['content'])) {
                ContentModel contentModel = ContentModel();
                contentModel.id = e['id'];
                contentModel.chapterIndex = e['chapterIndex'];
                contentModel.content = e['content'];
                e['content'] = contentModel.getContent();
              }
            }
          }).toList();
          String jsonStr = list.toString();
          result?['contentList'] = jsonStr;
          if (isAvailable(result)) {
            bookList.add(result!);
          }
        }
      }
    }

    await exportArrayToFiles(bookList).then((_) {
      // 导出完成后的操作
      logP('导出完成');
    });
  }

  static Future<void> exportArrayToFiles(List<Map> contents) async {
    try {
      if (contents.isEmpty) return;
      // 获取应用文档目录
      final Directory appDocDir = await getApplicationDocumentsDirectory();
      // 创建Files文件夹
      final Directory filesDir =
          Directory('${appDocDir.path}/FileExporter');
      if (!await filesDir.exists()) {
        await filesDir.create(recursive: true);
      }

      // 遍历数组，为每个内容创建文件
      for (int i = 0; i < contents.length; i++) {
        Map map = contents[i];
        final String fileName = '${map['bookId']}.txt';
        final File file = File('${filesDir.path}/$fileName');
        await file.writeAsString(map.toString());
      }
    } catch (e) {
      logP('导出文件时发生错误: $e');
      rethrow;
    }
  }
}
