//TODO: 事件名称  平台公用配置
//app启动,激活应用
const appLaunch               = 'LaunchApp';
//注册/登录成功(总的全部加起来)
const addPaymentInfo          = 'AddPaymentInfo';
//新用户注册(创建ID)
const regsRegistration        = 'Registration';
//打开详情(进入简介页面)
const unlockAchievement       = 'UnlockAchievement';
//阅读(进入内容页面)全部的, 打开阅读器就算
const viewContent             = 'ViewContent';
//全部金币解锁内容-新用户
const addToCart               = 'AddToCart';
//全部金币解锁内容-新+老
const startTrial              = 'StartTrial';
//金币充值成功-新用户
const addToWishlist           = 'AddToWishlist'; //TODO: CURRENCY 用USD, valueToSum 传金额   以下同
//金币充值成功-新+老
const checkout                = 'Checkout';
//订阅-新用户
const spendCredit             = 'SpendCredit';
//订阅-新+老
const subscribe               = 'Subscribe';
//充值+订阅-新
const rate                    = 'Rate';
//充值+订阅-新+老
const purchase                = 'Purchase';



//TODO: Firebase上报事件名称配置
//打开登录页面
const regsOpen                = 'regs_open';
//苹果登录成功
const regsApple               = 'regs_apple';
//谷歌登录成功
const regsGoogle              = 'regs_google';
//Facebook登录成功
const regsFacebook            = 'regs_facebook';
//tiktok登录成功
const regsTiktok              = 'regs_tiktok';
//library点击
const clickLibrary            = 'click_library';
//read点击
const clickMain               = 'click_main';
//profile点击
const clickMe                 = 'click_me';
//genres点击
const clickGenres             = 'click_genres';
//试读结束金币解锁内容-新用户
const trialUnlockNew          = 'trial_unlock_new';
//试读结束金币解锁内容-老用户
const trialUnlockOld          = 'trial_unlock_old';
//试读结束金币解锁内容-新+老
const trialUnlockAll          = 'trial_unlock_all';
//下载页面金币解锁内容-新用户
const downloadUnlockNew       = 'download_unlock_new';
//下载页面金币解锁内容-老用户
const downloadUnlockOld       = 'download_unlock_old';
//下载页面金币解锁内容-新+老
const downloadUnlockAll       = 'download_unlock_all';
//首次启动完成信息手机
const startReading            = 'start_reading';
//首次启动信息信息跳过点击
const later                   = 'later';
//通知权限打开
const notificationOn          = 'notification_on';
//首页新用户试用会员领取
const firstTrialUnlock        = 'first_trial_unlock';
//右上角点击切换语言
const homeLanguage            = 'home_language';
//设置页面点击切换语言
const meLanguage              ='me_language';
//启动页点击小说推荐
const clickLaunch             = 'click_launch';
//点击首页弹窗推荐
const clickPopup              = 'click_popup';
//点击搜索
const clickSearch             = 'click_search';
//打开排行榜
const openRanking             = 'open_ranking';
//首页搜索栏下genres按钮点击
const clickHomeGenres         = 'click_home_genres';
//首页搜索栏下tags按钮点击
const clickHomeTags           = 'click_home_tags';
//首页搜索栏下rankings按钮点击
const clickHomeRankings       = 'click_home_rankings';
//首页顶部第一栏推荐小说封面点击总次数(top pikcs上面, new user benefit部分)
const clickHomeBanner         = 'click_home_banner';
//排行榜页面封面点击次数(总)
const clickRanking            = 'click_ranking';
//library点击顶部标签list
const libraryList             = 'library_list';
//library点击顶部标签liked
const libraryLiked            = 'library_liked';
//library点击顶部标签history
const libraryHistory          = 'library_history';
//library点击顶部标签offline
const libraryOffline          = 'library_offline';
//编辑书架按钮点击次数
const clickLibraryEdit        = 'click_library_edit';
//分类页面顶部标签genres点击次数
const clickGenresGenres       = 'click_genres_genres';
//分类页面顶部标签tags点击次数
const clickGenresTags         = 'click_genres_tags';
//分组下标签总点击前缀
const clickTagPrefix          = 'click_';
//展开简介点击
const clickReadMore           = 'click_readmore';
//小说简介页点下载
const clickDetailDownload     = 'click_detail_download';
//小说简介页面点击分享
const clickDetailShare        = 'click_detail_share';
//小说简介页面点击反馈
const clickDetailReport       = 'click_detail_Report';
//小说简介点击添加到书架
const clickDetailLibrary      = 'click_detail_library';
//小说简介点击移除书架
const clickDetailRemove       = 'click_detail_remove';
//小说二级页面点击添加到书架
const clickSecondlyLibrary    = 'click_secondly_library';
//小说二级页面点击移除书架
const clickSecondlyRemove     = 'click_secondly_remove';
//点击简介封面下面的标签(现在不能跳转, 先看看点击次数)
const clickDetailTag          = 'click_detail_tag';
//阅读页面点击评论,打开评分页就算
const clickDetailComment      = 'click_detail_comment';
//me页面点击编辑个人信息
const clickMeEdit             = 'click_me_edit';
//me页面点击订阅
const clickMeVip              = 'click_me_vip';
//me页面点击account
const clickMeCoin             = 'click_me_coin';
//点击purchased novels
const clickCoinNovels         = 'click_coin_novels';
//点击transaction history
const clickCoinTransaction    = 'click_coin_transaction';
//点击purchase history
const clickCoinPurchase       = 'click_coin_purchase';
//小说阅读器点击添加到书架
const clickReadLibrary        = 'click_read_library';
//小说阅读器点击移除书架
const clickReadRemove         = 'click_read_remove';
//小说阅读器点击收藏
const clickReadLike           = 'click_read_like';
//小说阅读器点击章节
const clickReadChapter        = 'click_read_chapter';
//小说阅读器修改亮度, 有修改行为
const clickReadLight          = 'click_read_light';
//小说阅读器修改颜色, 有修改行为
const clickReadColor          = 'click_read_color';
//小说阅读器修改字号
const clickReadSize           = 'click_read_size';
//小说阅读器修改边距
const clickReadMargin         = 'click_read_margin';
//小说阅读器修改间距
const clickReadSpace          = 'click_read_space';
//小说阅读器修改字体
const clickReadFont           = 'click_read_font';
//小说阅读器修改翻页
const clickReadMode           = 'click_read_mode';
//小说阅读器点下载
const clickReadDownload       = 'click_read_download';
//小说阅读器面点击分享
const clickReadShare          = 'click_read_share';
//小说阅读器面点击反馈
const clickReadReport         = 'click_read_Report';
//作者页面打开
const openAuthor              = 'open_author';
//排行榜页面展示-trending页面展示次数
const top50Trending           = 'top50_trending';
//排行榜页面展示-new页面展示次数
const top50New                = 'top50_new';
//排行榜页面展示-hot页面展示次数
const top50Hot                = 'top50_hot';
//排行榜页面展示-favorites页面展示次数
const top50Favorites          = 'top50_favorites';
//explorea排行榜-trending-点击切换/滑动次数
const top9Trending            = 'top9_trending';
//explorea排行榜-new-点击切换/滑动次数
const top9New                 = 'top9_new';
//explorea排行榜-hot-点击切换/滑动次数
const top9Hot                 = 'top9_hot';
//explorea排行榜-favorites-点击切换/滑动次数
const top9Favorites           = 'top9_favorites';
//free栏目点击(hurry free for now)
const freeList                = 'free_list';
//听书按钮点击次数(也就是页面打开次数)
const audiobook               = 'Audiobook';
//聊天页面打开次数
const chat                    = 'Chat';


//TODO: Facebook上报事件名称配置
///Facebook标准事件
//app启动,激活应用
const fbActivatedApp            = 'fb_mobile_activate_app';
//注册/登录成功(总的全部加起来)
const fbAddPaymentInfo          = 'fb_mobile_add_payment_info';
//新用户注册(创建ID)
// const fbRegsRegistration        = 'fb_mobile_complete_registration';
//打开详情(进入简介页面)
const fbUnlockAchievement       = 'fb_mobile_achievement_unlocked';
//阅读(进入内容页面)全部的, 打开阅读器就算
// const fbViewContent             = 'fb_mobile_content_view';
//全部金币解锁内容-新用户
// const fbAddToCart               = 'fb_mobile_add_to_cart';
//全部金币解锁内容-新+老
// const fbStartTrial              = 'StartTrial';
//金币充值成功-新用户
// const fbAddToWishlist           = 'fb_mobile_add_to_wishlist';
//金币充值成功-新+老
// const fbCheckout                = 'fb_mobile_initiated_checkout';
//订阅-新用户
const fbSpendCredit             = 'fb_mobile_spent_credits';
//订阅-新+老
// const fbSubscribe               = 'Subscribe';
//充值+订阅-新
// const fbRate                    = 'fb_mobile_rate';
//充值+订阅-新+老
// const fbPurchase                = 'fb_mobile_purchase';


//TODO: Appsflyer上报事件名称配置
//充值+订阅-新
const completeTutoria                = 'CompleteTutoria';