import 'package:UrNovel/MainPage/NovelRead/Model/book_detailInfo_model.dart';
import 'package:UrNovel/Util/Common/Model/normal_model.dart';
import 'package:UrNovel/Util/SharedPreferences/shared_preferences.dart';

import '../../enum.dart';
import '../../logUtil.dart';
import '../../tools.dart';
import 'ViewModel/data_report_viewModel.dart';

class DataReportManager {
  static final DataReportManager instance = DataReportManager._internal();

  DataReportManager._internal();

  //TODO：章节阅读记录上报
  final List<Map<String, dynamic>> _chapterList = [];

  //上一章
  ChapterVoModel? lastReadChapter;

  //上一次阅读时间 单位：毫秒
  int lastReadTime = 0;

  ///阅读当前章节，上报上一章,若返回上一章，且上一张已经报过，则不上报
  ///[chapter]章节内容，[pointType]上报来源, [readType]阅读方式，[finish]上一章节是否读完，读完才更新上一章的完成状态，[isReport]是否上报
  Future<void> recordReadChapter(ChapterVoModel? chapter, ReadReportActivityType pointType,
      ReadReportReadingType readType, bool finish, bool isReport) async {
    if (!isAvailable(chapter)) {
      logP("章节阅读内容为空,无法添加到阅读记录列表");
      return;
    }

    if (!isAvailable(chapter)) {
      logP("未知章节类型");
      return;
    }

    if (!isAvailable(lastReadChapter)) {
      //第一次阅读
      lastReadChapter = chapter;
      lastReadTime = getCurrentTimeMillis();

      //如果有历史数据，则先上报历史数据
      await reportOldReadChapterList(pointType, readType);
      addChapterToList(chapter, finish);
    } else {
      if (isReport) {
        //先获取已有记录，赋值上一章的结束时间
        updateLastChapter(pointType: pointType);
        //上报阅读记录
        await reportReadChapterList(pointType, readType, _chapterList);
      } else {
        //更新上次阅读章节
        lastReadChapter = chapter;
        lastReadTime = getCurrentTimeMillis();

        //添加到阅读记录列表
        addChapterToList(lastReadChapter, finish);
      }
    }
  }

  Future<void> addChapterToList(ChapterVoModel? chapter, bool finish) async {
    if (!isAvailable(chapter)) {
      logP("章节阅读内容为空,无法添加到阅读记录列表");
      return;
    }

    //校验是否已经有记录，否则不添加
    if (!checkHasReadChapter(chapter)) {
      //先获取已有记录，赋值上一章的结束时间
      updateLastChapter();

      var chapterMap = {
        "bookId": chapter?.bookId,
        "chapterId": chapter?.id,
        "chapterIndex": chapter?.chapterIndex,
        "finish": false,
        "readId": lastReadTime, //以上报的时间
        "startTime": lastReadTime,
        "endTime": 0
      };
      _chapterList.add(chapterMap);

      //本地缓存数据
      await SpUtil.spSaveNovelReadBookChapter(_chapterList);
    }
  }

  void updateLastChapter(
      {List<dynamic>? dataList, ReadReportActivityType? pointType, bool onlySetFinish = false}) {
    List<dynamic>? chapterList = _chapterList;
    if (isAvailable(dataList)) {
      chapterList = dataList;
    }
    if (isAvailable(chapterList)) {
      Map<String, dynamic>? lastChapter = chapterList!.last;
      if (isAvailable(lastChapter)) {
        if (onlySetFinish) {
          lastChapter?['finish'] = true;
        } else {
          if (pointType == ReadReportActivityType.activityTimeout) {
            lastChapter?['endTime'] = getCurrentTimeMillis() - 5 * 60 * 1000;
          } else {
            lastChapter?['endTime'] = getCurrentTimeMillis();
          }
        }
      }
    }
  }

  bool checkHasReadChapter(ChapterVoModel? chapterModel) {
    for (var chapter in _chapterList) {
      if (chapter["bookId"] == chapterModel?.bookId &&
          chapter["chapterId"] == chapterModel?.id &&
          chapter["chapterIndex"] == chapterModel?.chapterIndex) {
        return true;
      }
    }

    return false;
  }

  //TODO：app打开行为打点
  Future<void> reportAppOpen() async {
    await DataReportViewModel.reportAppOpen();
  }

  //TODO：章节阅读记录上报
  ///[pointType]上报来源
  Future<void> reportOldReadChapterList(
      ReadReportActivityType pointType, ReadReportReadingType readType) async {
    List<dynamic>? list = await SpUtil.spGetNovelReadBookChapter();
    if (!isAvailable(list)) {
      return;
    }

    //先获取已有记录，赋值上一章的结束时间
    updateLastChapter(dataList: list);

    await reportReadChapterList(pointType, readType, list!);
  }

  //TODO：阅读记录上报
  ///[pointType]上报来源，[chapterList]章节列表
  Future<void> reportReadChapterList(ReadReportActivityType pointType,
      ReadReportReadingType readType, List<dynamic> chapterList) async {
    if (!isAvailable(chapterList)) {
      logP("章节阅读上报内容为空");
      return;
    }

    //上报阅读记录
    var parameters = {
      "info": chapterList,
      "pointType": getReportPointTypeStr(pointType),
      "readId": getCurrentTimeMillis().toString(),
      'readType': getReadTypeStr(readType)
    };
    NormalModel? model = await DataReportViewModel.reportReadChapter(parameters);
    if (model?.code == 200) {
      //清理本地缓存数据
      lastReadChapter = null;
      lastReadTime = 0;
      chapterList.clear();
      bool isSuccess = await SpUtil.spDelete(SPKey.reportNovelReadBookChapter);
      if (isSuccess) {
        logP('章节阅读上报本地缓存清理成功');
      } else {
        logP('章节阅读上报本地缓存清理失败');
      }
    }
  }

  //TODO：获取上报阅读活动类型
  String getReportPointTypeStr(ReadReportActivityType pointType) {
    switch (pointType) {
      case ReadReportActivityType.exitReading:
        return "EXIT_READING";
      case ReadReportActivityType.purchase:
        return "PURCHASE";
      case ReadReportActivityType.subscribe:
        return "SUBSCRIPTION";
      case ReadReportActivityType.finishNovel:
        return "FINISH_NOVEL";
      case ReadReportActivityType.inactive:
        return "INACTIVE";
      case ReadReportActivityType.activityTimeout:
        return "OVERTIME";
      case ReadReportActivityType.enterBackground:
        return "BACKGROUND";
      default:
        return "unknown";
    }
  }

  //TODO：获取阅读方式类型
  String getReadTypeStr(ReadReportReadingType readType) {
    switch (readType) {
      case ReadReportReadingType.reading:
        return "READING";
      case ReadReportReadingType.listen:
        return "LISTEN";
    }
  }
}
