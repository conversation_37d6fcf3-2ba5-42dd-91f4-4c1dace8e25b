import 'package:UrNovel/Util/Common/Model/normal_model.dart';

import '../../../../MainPage/Library/Model/library_list_model.dart';
import '../../../../MainPage/Read/Model/ForYou/home_read_model.dart';
import '../../../../MainPage/Search/Model/search_book_model.dart';
import '../../../../MainPage/Secondary/Model/secondary_list_model.dart';
import '../../../NetWorkManager/net_work_manager.dart';
import '../../../api_config.dart';
import '../../../logUtil.dart';
import '../../../tools.dart';

class DataReportViewModel {
//TODO: app打开行为打点
  static Future<void> reportAppOpen() async {
    try {
      var response = await NetWorkManager.instance.get(apiAppOpenRecord, params: {});
      if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
        NormalModel model = NormalModel.fromJson(response.data);
        if (model.code == 200) {
          logP('app打开行为打点成功');
        } else {
          logP('app打开行为打点失败');
        }
      }
    } catch (e) {
      logP('app打开行为打点失败: $e');
    }
  }

  //TODO:Novel 阅读章节上报
  static Future<NormalModel?> reportReadChapter(Map<String, dynamic> parameters) async {
    try {
      var response =
          await NetWorkManager.instance.post(apiChapterReadRecord, parameters: parameters);
      if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
        NormalModel model = NormalModel.fromJson(response.data);

        return model;
      }
    } catch (e) {
      logP('reportReadChapter error: $e');
    }

    return null;
  }

  //todo:书籍曝光上报
  static Future<void> dealExposureList(List<dynamic>? list, int rangeIndex) async {
    List<int> exposureList = [];
    if (isAvailable(list) && 0 < rangeIndex) {
      for (var i = 0; i < list!.length; i++) {
        try {
          dynamic item = list[i];
          if (i < rangeIndex) {
            int? bookId;
            if (item is HomeReadBookItem) {
              bookId = item.id;
            } else if (item is LibraryListItem) {
              bookId = item.bookId;
            } else if (item is BookSearchDtoItem) {
              bookId = item.bookId;
            } else if (item is SecondaryListItem) {
              bookId = item.bookId;
            }

            if (bookId is int) {
              exposureList.add(bookId);
            }
          } else {
            break;
          }
        } catch (e) {
          logP('dealRefreshState error: $e');
        }
      }
    }

    if (exposureList.isNotEmpty) {
      await DataReportViewModel.reportBookExposure(exposureList);
      exposureList.removeRange(0, exposureList.length);
    }
  }

  static Future<NormalModel?> reportBookExposure(List<int> bookIds) async {
    try {
      var response = await NetWorkManager.instance
          .post(apiBookExposureRecord, parameters: {'bookIds': bookIds});
      if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
        NormalModel model = NormalModel.fromJson(response.data);

        return model;
      }
    } catch (e) {
      logP('reportReadChapter error: $e');
    }

    return null;
  }

  //TODO: 保存用户阅读记录
  static Future<NormalModel?> reportUserReadingHistory(int? chapterId) async {
    if (chapterId is int) {
      try {
        Map<String, dynamic> params = {'chapterId': chapterId};
        var response = await NetWorkManager.instance.get(apiSaveUserReadingHistory, params: params);
        if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
          NormalModel model = NormalModel.fromJson(response.data);

          return model;
        }
      } catch (e) {
        logP('reportUserReadRecord error: $e');
      }
    }

    return null;
  }

  //TODO: 推送打点
  static Future<NormalModel?> pushEventReport(Map<String, dynamic> params) async {
    try {
      var response =
          await NetWorkManager.instance.post(apiEventFirebasePush, parameters: params);
      if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
        NormalModel model = NormalModel.fromJson(response.data);

        return model;
      }
    } catch (e) {
      logP('pushEventReport error: $e');
    }

    return null;
  }
}
