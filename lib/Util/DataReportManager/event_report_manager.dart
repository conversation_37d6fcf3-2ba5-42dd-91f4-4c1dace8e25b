import 'package:facebook_app_events/facebook_app_events.dart';


import '../ThirdSdkManger/third_manger.dart';
import '../logUtil.dart';
import '../tools.dart';
import 'event_name_config.dart';

class EventReportManager {
  static final EventReportManager instance = EventReportManager._internal();

  EventReportManager._internal();

  //TODO: 自定义公共事件上报
  static Future<void> eventReportOfCommon(String eventName,
      {Map<String, Object>? parameters}) async {
    try {
      await EventReportManager.eventReportOfFirebase(eventName,
          parameters: parameters);
      await EventReportManager.eventReportOfAppsflyer(eventName,
          parameters: parameters);
      await EventReportManager.eventReportOfFacebook(eventName,
          parameters: parameters);
    } catch (e) {
      logP('自定义公共事件上报 Failed: $e');
    }
  }

  //TODO: Firebase Event Report
  static Future<void> eventReportOfFirebase(String eventName,
      {Map<String, Object>? parameters}) async {
    await ThirdManger.analytics.logEvent(
      name: eventName,
      parameters: parameters,
    );
  }

  //TODO: Appsflyer Event Report
  static Future<void> eventReportOfAppsflyer(String eventName,
      {Map<String, dynamic>? parameters}) async {
    try {
      bool? result =
          await ThirdManger.appsflyer?.logEvent(eventName, parameters);
      if (result == true) {
        logP('Appsflyer Event Report Success: $eventName, $parameters');
      } else {
        logP('Appsflyer Event Report Failed: $eventName, $parameters');
      }
    } on Exception catch (e) {
      logP('Appsflyer Event Report Failed: $e');
    }
  }

  //TODO: Facebook Event Report
  static Future<void> eventReportOfFacebook(String eventName,
      {Map<String, Object>? parameters}) async {
    try {
      Map<String, Object>? param = parameters;
      if (isAvailable(param)) {
        param!['advertiser_tracking_enabled'] = 1;
      }
      await FacebookAppEvents().logEvent(
          name: eventName, parameters: param);
    } catch (e) {
      // 记录错误或进行重试
      logP("Error logging Facebook event: $e");
      // 例如重试代码
    }
  }

  //TODO: 新用户注册(创建ID)
  static Future<void> logCompletedRegistration(
      {String? registrationMethod}) async {
    return FacebookAppEvents()
        .logCompletedRegistration(registrationMethod: registrationMethod);
  }

  //TODO: 注册/登录成功(总的全部加起来)
  static Future<void> logUnlockedAchievement(
      {String? registrationMethod}) async {
    if (isAvailable(registrationMethod)) {
      return eventReportOfFacebook(fbUnlockAchievement,
          parameters: {'fb_description': registrationMethod!});
    }

    return;
  }

  //TODO: 阅读(进入内容页面)全部的, 打开阅读器就算
  static Future<void> logViewedContent() async {
    return FacebookAppEvents().logViewContent();
  }

  static Future<void> logAddToCart(
      String id, String type, String currency, double price) {
    return FacebookAppEvents()
        .logAddToCart(id: id, type: type, currency: currency, price: price);
  }

  //TODO: 全部金币解锁内容-新+老
  static Future<void> logStartTrial(String orderId) async {
    if (orderId.isEmpty) {
      return;
    }
    return FacebookAppEvents().logStartTrial(orderId: orderId);
  }

  //TODO: 金币充值成功-新+老
  static Future<void> logInitiatedCheckout(
      double? totalPrice,
      String? currency,
      String? contentType,
      String? contentId,
      int? numItems,
      bool paymentInfoAvailable) async {
    return FacebookAppEvents().logInitiatedCheckout(
        totalPrice: totalPrice,
        currency: currency,
        contentType: contentType,
        contentId: contentId,
        numItems: numItems,
        paymentInfoAvailable: paymentInfoAvailable);
  }

  //TODO: 充值+订阅-新
  static Future<void> logRated({double? valueToSum}) async {
    return FacebookAppEvents().logRated(valueToSum: valueToSum);
  }

  //TODO: 金币充值成功-新用户
  static Future<void> logAddToWishlist(
      {required String id,
      required String type,
      required String currency,
      required double price}) async {
    return FacebookAppEvents()
        .logAddToWishlist(id: id, type: type, currency: currency, price: price);
  }

  //TODO: 订阅-新+老
  static Future<void> logSubscribe({required String orderId}) async {
    if (orderId.isEmpty) {
      return;
    }
    return FacebookAppEvents().logSubscribe(orderId: orderId);
  }

  //TODO: 充值+订阅-新+老
  static Future<void> logPurchase({
    required double amount,
    required String currency,
    Map<String, dynamic>? parameters,
  }) async {
    return FacebookAppEvents().logPurchase(
        amount: amount, currency: currency, parameters: parameters);
  }
}
