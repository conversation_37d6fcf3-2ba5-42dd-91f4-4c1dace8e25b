import 'dart:async';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';

import '../../Launch&Login/Model/promotion_content_model.dart';
import '../../Launch&Login/Model/user_model.dart';
import '../../MainPage/Read/ViewModel/ViewModel.dart';
import '../../MainPage/SignIn/Model/sign_in_model.dart';
import '../../MainPage/SignIn/View/signin_popup_widget.dart';
import '../../MainPage/SignIn/ViewModel/sign_in_view_model.dart';
import '../PermissionManager/permission_manager.dart';
import '../SharedPreferences/shared_preferences.dart';
import '../SheetAndAlter/alter.dart';
import '../SheetAndAlter/bottom_sheet.dart';
import '../ThirdSdkManger/third_manger.dart';
import '../enum.dart';
import '../tools.dart';

class AlterManager {
  static final AlterManager instance = AlterManager._internal();

  AlterManager._internal();

  Timer? _delayTimer;

  //todo: 弹窗显示控制
  Future<void> showContentAlter(BuildContext content) async {
    await getHomeRecommendContent(content, onDismiss: () async {
      Future.delayed(Duration(seconds: 3));
      await checkSignInAlert(content, onDismiss: () {
        _cancelTimer();
      });
    });
  }

  //todo: 首页推荐内容弹窗
  Future<void> getHomeRecommendContent(BuildContext content, {VoidCallback? onDismiss}) async {
    List<PromotionContentItem>? contentList = await HomeReadViewModel.getHomeRecommendContent();
    if (isAvailable(contentList) && content.mounted) {
      showAlter(ReadBookRecommendContent(isNormal: false, contentList: contentList),
          backgroundColor: Colors.transparent, onDismiss: onDismiss);
    } else {
      onDismiss?.call();
    }
  }

  //todo: 签到弹窗
  Future<void> checkSignInAlert(BuildContext content, {VoidCallback? onDismiss}) async {
    if (isSignInSwitchOn) {
      SignInInfoResult? signInInfoResult = await SignInViewModel.getUserSignInInfo();
      UserInfoModel? userModel = await SpUtil.spGetUserInfo();
      if (userModel != null && signInInfoResult != null && userModel.newUser != true) {
        if (signInInfoResult.signInFlag == true) {
          if (curRoutePage != RoutePage.novelReadPage) {
            showAlter(SignInPopUpWidget(),
                backgroundColor: const Color.fromARGB(0, 255, 255, 255),
                barrierDismissible: true,
                onDismiss: onDismiss);
          } else {
            onDismiss?.call();
          }
        } else {
          _cancelTimer();
          _delayTimer = Timer(Duration(seconds: signInInfoResult.nextSignInSecond ?? 0), () async {
            if (content.mounted && curRoutePage != RoutePage.novelReadPage) {
              showAlter(SignInPopUpWidget(),
                  backgroundColor: const Color.fromARGB(0, 255, 255, 255),
                  barrierDismissible: true,
                  onDismiss: onDismiss);
            } else {
              onDismiss?.call();
            }
          });
        }
      } else {
        onDismiss?.call();
      }
    } else {
      onDismiss?.call();
    }
  }

  //TODO:请求通知隐私权限弹窗
  Future<void> showNotificationAlter(NotificationAlterSource source) async {
    if (source != NotificationAlterSource.firstPopReadPage) {
      Map<String, dynamic>? map = await SpUtil.spGetNotificationAlterShowTimes();
      int count = map?[NotificationAlterSource.firstPopReadPage.name] ?? 0;
      if (count < 1) {
        return;
      }
    }

    switch (source) {
      case NotificationAlterSource.firstPopReadPage:
        Map<String, dynamic>? map = await SpUtil.spGetNotificationAlterShowTimes();
        int count = map?[NotificationAlterSource.firstPopReadPage.name] ?? 0;
        if (count < 1) {
          await requestNotificationPermission();
          await SpUtil.spSetNotificationAlterShowTimes(source);
        }
        break;
      case NotificationAlterSource.addToLibrary:
        await requestNotificationPermission();
        break;
      case NotificationAlterSource.firstClickCollection:
        Map<String, dynamic>? map = await SpUtil.spGetNotificationAlterShowTimes();
        int count = map?[NotificationAlterSource.firstClickCollection.name] ?? 0;
        if (count < 1) {
          await requestNotificationPermission();
          await SpUtil.spSetNotificationAlterShowTimes(source);
        }
        break;
      case NotificationAlterSource.openAppAfter24Hours:
        bool isShow = await SpUtil.spGetIsNotificationAlterShow();
        if (isShow) {
          await requestNotificationPermission();
        }
        break;
      case NotificationAlterSource.afterShare:
        await requestNotificationPermission();
        break;
      case NotificationAlterSource.firstComment:
        Map<String, dynamic>? map = await SpUtil.spGetNotificationAlterShowTimes();
        int count = map?[NotificationAlterSource.firstComment.name] ?? 0;
        if (count < 1) {
          await requestNotificationPermission();
          await SpUtil.spSetNotificationAlterShowTimes(source);
        }
        break;
      case NotificationAlterSource.deniedAfter7days:
        await requestNotificationPermission();
        break;
      case NotificationAlterSource.afterCharge:
        await requestNotificationPermission();
        break;
      default:
        break;
    }
  }

  static Future<void> requestNotificationPermission() async {
    bool isVerify = await SpUtil.spGetVerifyStatus();
    if (isVerify) return;

    AuthorizationStatus status =
        await PermissionManager.instance.getNotificationAuthorizationStatus();
    if (status == AuthorizationStatus.notDetermined) {
      await PermissionManager.requestNotificationPermission();
    } else if (status == AuthorizationStatus.denied) {
      await showNotificationPermissionSheet();
    }
  }

  static Future<void> showNotificationPermissionSheet() async {
    showAlter(NotificationTrackingPermissionSheet(
        isNotificationType: true,
        onTurnOnTap: () async {
          await ThirdManger.openAppSettings();
        }));
    await SpUtil.spSetNotificationAlterShowTime(getCurrentTimeSeconds());
  }

  void _cancelTimer() {
    // 取消定时器
    if (_delayTimer != null) {
      _delayTimer?.cancel();
      _delayTimer = null;
    }
  }
}
