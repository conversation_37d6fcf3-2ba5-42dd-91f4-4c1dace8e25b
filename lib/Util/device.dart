import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:UrNovel/Util/tools.dart';

import 'logUtil.dart';

const String deviceIdKey = "deviceId";

class Device {
  static var pixelRatio = 0.0;
  static var screenWidth = 0.0;
  static var screenHeight = 0.0;
  static var statusBarHeight = 0.0;
  static var bottomBarHeight = 0.0;
  static const FlutterSecureStorage _storage = FlutterSecureStorage();

  static init(BuildContext context) {
    MediaQueryData mq = MediaQuery.of(context);
// 屏幕密度
    pixelRatio = mq.devicePixelRatio;
// 屏幕宽(注意是dp, 转换px 需要 screenWidth * pixelRatio)
    screenWidth = mq.size.width;
// 屏幕高(注意是dp)
    screenHeight = mq.size.height;
// 顶部状态栏, 随着刘海屏会增高
    statusBarHeight = mq.padding.top;
// 底部功能栏, 类似于iPhone XR 底部安全区域
    bottomBarHeight = mq.padding.bottom;
  }

  static Future<String> getDeviceId() async {
    String deviceIdentifier = "unknown";
    try {
      String? value = await _storage.read(key: deviceIdKey);
      if (isAvailable(value)) {
        deviceIdentifier = value!;
        return deviceIdentifier;
      }

      if (Platform.isIOS) {
        // iOS-specific code
        IosDeviceInfo deviceInfo = await Device.iosDeviceInfo;
        if (isAvailable(deviceInfo.identifierForVendor)) {
          // iOS唯一设备ID
          deviceIdentifier = deviceInfo.identifierForVendor!;
        }
      } else if (Platform.isAndroid) {
        // Android-specific code
        AndroidDeviceInfo deviceInfo = await Device.androidDeviceInfo;
        deviceIdentifier = deviceInfo.device; // Android唯一设备ID
      }

      await _storage.write(key: deviceIdKey, value: deviceIdentifier);

      return deviceIdentifier;
    } catch (e) {
      logP('Error getting device id: $e');
      return deviceIdentifier;
    }
  }

  static Future<IosDeviceInfo> get iosDeviceInfo async {
    var deviceInfo = DeviceInfoPlugin();
    IosDeviceInfo iosInfo = await deviceInfo.iosInfo;

    return iosInfo;
  }

  static Future<AndroidDeviceInfo> get androidDeviceInfo async {
    var deviceInfo = DeviceInfoPlugin();
    AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;

    return androidInfo;
  }
}
