import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:UrNovel/Launch&Login/Model/login_model.dart';
import 'package:UrNovel/Util/DBManager/db_manager.dart';
import 'package:UrNovel/Util/DataReportManager/ServiceReport/ViewModel/data_report_viewModel.dart';
import 'package:UrNovel/Util/DataReportManager/event_report_manager.dart';
import 'package:UrNovel/Util/LocalNotificationManager/local_notification_manager.dart';
import 'package:UrNovel/Util/ReaderUtils/novel_read_chapter.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:appsflyer_sdk/appsflyer_sdk.dart';
import 'package:facebook_app_events/facebook_app_events.dart';
import 'package:facebook_audience_network/facebook_audience_network.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flame_spine/flame_spine.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:freshchat_sdk/freshchat_sdk.dart';
import 'package:freshchat_sdk/freshchat_user.dart';

import '../../Launch&Login/Model/user_model.dart';
import '../../MainPage/Profile/ViewModel/ViewModel.dart';
import '../../firebase_options.dart';
import '../Common/CommonManager/common_manager.dart';
import '../DataReportManager/event_name_config.dart';
import '../LiveActivitiesManger/live_activities_manager.dart';
import '../PermissionManager/permission_manager.dart';
import '../SharedPreferences/shared_preferences.dart';
import '../enum.dart';
import '../global.dart';
import '../logUtil.dart';

const appsFlyerDevKey = 'Bht5UeN6PmWTKKDeUVKG4e';
const appsFlyerAppId = '6736648910';

const freshChatAppId = '2ae37e7a-dbad-4e10-91d4-17c65794b76c';
const freshChatAppKey = 'f8aaa08d-033b-44b1-b947-c728433e1ca6';
const freshChatDomain = 'msdk.freshchat.com';

class ThirdManger {
  static ThirdManger instance = ThirdManger._internal();

  ThirdManger._internal();

  //TODO: 初始化Firebase
  static final FirebaseMessaging messaging = FirebaseMessaging.instance;
  static final FirebaseCrashlytics crashlytics = FirebaseCrashlytics.instance;

  //TODO: Firebase Events Report
  static final FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  //TODO: 初始化FacebookAuth
  final facebookAuth = FacebookAuth.instance;

  //TODO: 初始化AppsflyerSdk
  static AppsflyerSdk? appsflyer;

  //TODO: 初始化sdk&数据
  static Future<void> initSDK() async {
    //TODO: 查看网络状态
    PermissionManager.instance.requestNetworkPermission();
    //TODO: 网络状态监听
    PermissionManager.instance.startConnectivityListener();
    //TODO: 初始化本地通知
    LocalNotificationManager.initialize();
    //TODO: 初始化活动
    LiveActivitiesManager.instance.initActivity();
    //TODO: 初始化第三方SDK
    initFirebase();
    initFacebook();
    initAppsFlyer();
    initFreshChat();
    initSpine();
  }

  ///初始化Firebase
  static Future<void> initFirebase() async {
    try {
      await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
      await crashlytics.setCrashlyticsCollectionEnabled(true);
      await analytics.setAnalyticsCollectionEnabled(true);

      // //使用 FirebaseCrashlytics.instance.recordFlutterFatalError 替换 FlutterError.onError，自动捕获 Flutter 框架中抛出的所有错误：
      // FlutterError.onError = (errorDetails) {
      //   FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
      // };
      // // 如需捕获未由 Flutter 框架处理的异步错误，请使用 PlatformDispatcher.instance.onError：
      // PlatformDispatcher.instance.onError = (error, stack) {
      //   FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
      //   return true;
      // };
    } catch (e) {
      logP("FirebaseCrashlyticson错误上报异常: $e");
    }
  }

  //TODO: 初始化Facebook
  static Future<void> initFacebook() async {
    try {
      await FacebookAppEvents().setAdvertiserTracking(enabled: true);
      await FacebookAudienceNetwork.init(
        testingId: "37b1da9d-b48c-4103-a393-999", // 可选，用于测试
        iOSAdvertiserTrackingEnabled: true, // 默认false
      );
    } catch (e) {
      logP('初始化Facebook失败: $e');
    }
  }

  //TODO：请求通知权限
  static Future<void> requestNotificationPermission({AuthorizationStatus? status}) async {
    AuthorizationStatus? authorizationStatus = status;
    if (!isAvailable(status)) {
      authorizationStatus = await getNotificationAuthorizationStatus();
    }

    if (authorizationStatus == AuthorizationStatus.notDetermined) {
      //请求权限
      NotificationSettings settings = await messaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );
      AuthorizationStatus newStatus = settings.authorizationStatus;
      if (newStatus == AuthorizationStatus.authorized) {
        //获取令牌
        await registerFirebaseMessaging();
        EventReportManager.eventReportOfFirebase(notificationOn);
      } else if (newStatus == AuthorizationStatus.denied) {
        await SpUtil.spSetNotificationFirstDeniedTime(getCurrentTimeSeconds());
      }
    } else if (status != AuthorizationStatus.denied) {
      await registerFirebaseMessaging();
    }

    await reportNotificationStatus();
  }

  /// 处理权限请求结果
  static Future<AuthorizationStatus> getNotificationAuthorizationStatus() async {
    NotificationSettings settings = await messaging.getNotificationSettings();
    var status = settings.authorizationStatus;
    logP('User granted permission: $status');
    if (status == AuthorizationStatus.authorized) {
      logP('用户已授权接收通知');
    } else if (status == AuthorizationStatus.denied) {
      logP('用户拒绝了通知权限');
    } else if (status == AuthorizationStatus.notDetermined) {
      logP('用户还未决定是否接收通知');
    } else if (status == AuthorizationStatus.provisional) {
      logP('用户授予临时权限');
    } else {
      logP('未知的权限状态');
    }

    return status;
  }

  /// 通知授权状态上报
  static Future<void> reportNotificationStatus() async {
    AuthorizationStatus? authorizationStatus = await getNotificationAuthorizationStatus();
    if (pushStatus == authorizationStatus) {
      return;
    }
    pushStatus = authorizationStatus;
    await ProfileViewModel.editProfile({
      'noticeStatus': authorizationStatus == AuthorizationStatus.authorized ||
              authorizationStatus == AuthorizationStatus.provisional
          ? 1
          : 0
    });
  }

  /// 打开应用设置
  static Future<void> openAppSettings() async {
    PermissionManager.instance.gotoSettingCenter();
  }

  /// 打开系统语言设置界面
  Future<void> openLanguageSettings() async {
    PermissionManager.instance.openLanguageSettings();
  }

  /// 注册Firebase消息
  static Future<void> registerFirebaseMessaging() async {
    try {
      final apnsToken = await messaging.getAPNSToken();
      if (isAvailable(apnsToken)) {
        // APNS token is available, make FCM plugin API requests...
      }
      String? token = await messaging.getToken();
      logP('FCM Token: $token');
      await reportToken(token);
      //监听令牌刷新
      messaging.onTokenRefresh.listen((fcmToken) async {
        logP("FCM onTokenRefresh: $fcmToken");
        await reportToken(fcmToken);
      }).onError((err) {
        logP("发生错误: $err");
      });
    } catch (e) {
      logP('注册Firebase消息失败: $e');
    }

    //在运行时重新启用 FCM 自动初始化功能 此值一经设置便会持久保存，不受应用重启的影响。
    await messaging.setAutoInitEnabled(true);

    ///前台消息：App 正在打开时收到推送。
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      _receiveMessagingForegroundHandler(message);
    });

    ///后台消息：App 在后台或已关闭时收到推送。
    FirebaseMessaging.onBackgroundMessage(_receiveMessagingBackgroundHandler);

    ///后台消息：App 在后台或已关闭，用户点击通知栏消息进入 App。
    FirebaseMessaging.onMessageOpenedApp.listen(_clickMessagingBackgroundHandler);

    ///App 被杀死后，点击通知启动App，需要在启动时获取初始消息：
    RemoteMessage? message = await FirebaseMessaging.instance.getInitialMessage();
    if (isAvailable(message)) {
      _clickMessagingUnLoadHandler(message!);
    }
  }

  ///token上报
  static Future<void> reportToken(String? token) async {
    if (isAvailable(token)) {
      Freshchat.setPushRegistrationToken(token!);
      appsflyer?.updateServerUninstallToken(token);
      await ProfileViewModel.saveFireBaseToken(token);
    }
  }

  ///接收前台消息：App 正在打开时收到推送。
  static Future<void> _receiveMessagingForegroundHandler(RemoteMessage message) async {
    LocalNotificationManager.showNotification(
      title: message.notification?.title ?? '',
      body: message.notification?.body ?? '',
      payload: jsonEncode(message.data),
    );
    DataReportViewModel.pushEventReport(
        {'pushId': message.data['pushId'], 'eventType': PushType.receive.name});
  }

  ///接收后台消息：App在后台或已关闭时收到推送。
  static Future<void> _receiveMessagingBackgroundHandler(RemoteMessage message) async {
    DataReportViewModel.pushEventReport(
        {'pushId': message.data['pushId'], 'eventType': PushType.receive.name});
  }

  ///后台消息：App在后台，点击通知栏消息进入App。
  static Future<void> _clickMessagingBackgroundHandler(RemoteMessage message) async {
    LocalNotificationManager.dealNotificationResponse(message);
  }

  ///App被杀死后，点击通知启动App，需要在启动时获取初始消息：
  static Future<void> _clickMessagingUnLoadHandler(RemoteMessage message) async {
    LocalNotificationManager.dealNotificationResponse(message);
  }

  // TODO: 初始化AppsFlyer
  static Future<void> initAppsFlyer() async {
    try {
      final options = AppsFlyerOptions(
          afDevKey: appsFlyerDevKey,
          showDebug: false,
          appId: appsFlyerAppId,
          disableAdvertisingIdentifier: false,
          timeToWaitForATTUserAuthorization: 30,
          // Optional field
          disableCollectASA: false,
          // Optional field
          manualStart: true //Optional field
          );

      appsflyer = AppsflyerSdk(options);
      appsflyer?.initSdk(
        registerConversionDataCallback: true,
        registerOnAppOpenAttributionCallback: true,
        registerOnDeepLinkingCallback: true,
      );
      appsflyer?.onInstallConversionData((data) async {
        logP("onInstallConversionData: $data");
        await ProfileViewModel.saveAFMessage(jsonEncode(data));
      });
    } catch (e) {
      logP('初始化AppsFlyer失败: $e');
    }

    // Starting the SDK with optional success and error callbacks
    appsflyer?.startSDK(
      onSuccess: () {
        logP("AppsFlyer SDK initialized successfully.");
      },
      onError: (int errorCode, String errorMessage) {
        logP("Error initializing AppsFlyer SDK: Code $errorCode - $errorMessage");
      },
    );
  }

  // TODO: 处理剪切板内容跳转
  static Future<void> handleClipboardContent() async {
    String? content = await getClipboardContent();
    bool isLogin = await SpUtil.isLogin();
    if (isAvailable(content) && isLogin) {
      //todo:重置剪切板内容
      clearClipboard();

      /// UrNovel://read?bookId=7912&chapterIndex=1
      if (content!.startsWith('UrNovel://')) {
        Map<String, dynamic>? params = Uri.parse(content).queryParameters;

        ///是否已经点击了广告
        bool isClickNovelAd = await SpUtil.isNovelAdClick();
        if (isClickNovelAd) {
          SpUtil.spSetNovelAdClick(false);
          return;
        } else {
          parseParamsAndGoToReadPage(params, BookDetailJumpType.localAdPage);
        }
      }
    }
  }

  // TODO: 解析参数，调往阅读页
  static Future<void> parseParamsAndGoToReadPage(
      Map<String, dynamic>? params, BookDetailJumpType? jumpType) async {
    try {
      if (isAvailable(params)) {
        String? bookId = params!['bookId'];
        String? chapterIndex = params['chapterIndex'];
        String? jumpTypeStr = params['jumpType'];
        BookDetailJumpType type = jumpType ?? BookDetailJumpType.other;
        if (jumpTypeStr == 'liveActivity') {
          type = BookDetailJumpType.liveActivity;
        }

        if (isAvailable(bookId)) {
          //去往阅读页
          await LocalNotificationManager.toNovelReadPage({
            'bookId': int.parse(bookId!),
            'chapterIndex': isAvailable(chapterIndex) ? int.parse(chapterIndex!) : null,
            'jumpType': type,
          });
        }
      }
    } catch (e) {
      logP("解析参数异常: $e");
    }
  }

  // TODO: 初始化FreshChat
  static void initFreshChat() {
    // 初始化 FreshChat
    Freshchat.init(freshChatAppId, freshChatAppKey, freshChatDomain, stringsBundle: 'UrNovel');
  }

  static Future<void> identifyUser(UserInfoModel? userInfoModel) async {
    if (isAvailable(userInfoModel)) {
      Freshchat.identifyUser(externalId: userInfoModel?.uid.toString() ?? "");
      FreshchatUser user = await Freshchat.getUser;
      NovelReadRecentlyModel? readRecentlyModel = DBManager.instance.getRecentlyReadBook()?.first;
      var externalId = user.getExternalId();
      var restoreId = user.getRestoreId();
      String? phoneCode, phoneNum;
      try {
        if (isAvailable(userInfoModel?.phone)) {
          List list = userInfoModel!.phone!.split(" ");
          phoneCode = list[0];
          phoneNum = list.last;
        }
      } catch (e) {
        phoneCode = "";
        phoneNum = "";
      }

      FreshchatUser freshChatUser = FreshchatUser(externalId, restoreId);
      freshChatUser.setFirstName(userInfoModel?.nickName ?? userInfoModel?.name ?? "");
      freshChatUser.setLastName("");
      freshChatUser.setEmail(userInfoModel?.email ?? "");
      freshChatUser.setPhone(phoneCode ?? "", phoneNum ?? "");
      Freshchat.setUser(freshChatUser);
      Map<String, dynamic> properties = await _setPropertyMap(userInfoModel!,
          novelId: readRecentlyModel?.bookId, chapterId: readRecentlyModel?.chapterId);
      Freshchat.setUserProperties(properties);
    }
  }

  //获取客服基本信息
  static Future<Map<String, dynamic>> _setPropertyMap(UserInfoModel userInfoModel,
      {int? novelId, int? chapterId}) async {
    LoginInfoModel? loginInfoModel = await SpUtil.spGetLoginInfo();
    var loginChannel = '';
    if (loginInfoModel?.loginChannel == 10) {
      loginChannel = 'facebookApp';
    } else if (loginInfoModel?.loginChannel == 11) {
      loginChannel = 'googleApp';
    } else if (loginInfoModel?.loginChannel == 12) {
      loginChannel = 'appleApp';
    } else if (loginInfoModel?.loginChannel == 15) {
      loginChannel = 'tiktok';
    }

    return {
      // 邮箱
      "cf_e_mail": userInfoModel.email ?? "",
      // 用户ID
      "cf_uid": userInfoModel.uid?.toString() ?? "",
      // 是否是VIP
      "cf_isvip": userInfoModel.vipStatus?.toString() ?? "",
      // 是否购买
      "cf_ispurchase": userInfoModel.purchase == true ? '1' : '0',
      // 操作系统
      "cf_os": Platform.operatingSystem,
      // 应用名称
      "cf_app": "UrNovel",
      //10:facebookApp; 11:googleApp; 12:appleApp; 15:tiktok
      "cf_logintype": loginChannel,
      // 小说ID
      "cf_novelid": "$novelId",
      // 章节ID
      "cf_chapterid": "$chapterId",
      // 页面
      "cf_page": "$currentRouteName",
      // 皮肤语言
      "cf_applanguage": CommonManager.instance.skinLanguage.languageCode,
      // 内容语言
      "cf_contentlanguage": CommonManager.instance.contentLanguage.code ?? "en",
    };
  }

  static void freshChatResetUser() {
    Freshchat.resetUser();
    initFreshChat();
  }

// TODO: 初始化Spine
  static Future<void> initSpine() async {
    await initSpineFlutter(enableMemoryDebugging: false);
  }
}
