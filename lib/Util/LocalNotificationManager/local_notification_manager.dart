// 导入包
import 'dart:convert';

import 'package:UrNovel/Util/tools.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:get/get.dart';
import 'package:timezone/data/latest_all.dart' as tz_data;
import 'package:timezone/timezone.dart' as tz;

import '../DataReportManager/ServiceReport/ViewModel/data_report_viewModel.dart';
import '../SharedPreferences/shared_preferences.dart';
import '../enum.dart';
import '../global.dart';
import '../logUtil.dart';

class LocalNotificationManager {
  // FlutterLocalNotificationsPlugin是一个用于处理本地通知的插件，它提供了在Flutter应用程序中发送和接收本地通知的功能。
  static final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  // 初始化函数
  static Future<void> initialize() async {
    // 初始化时区
    tz_data.initializeTimeZones();
    final String timeZoneName = await FlutterTimezone.getLocalTimezone();
    tz.setLocalLocation(tz.getLocation(timeZoneName));

    // AndroidInitializationSettings是一个用于设置Android上的本地通知初始化的类
    // 使用了app_icon作为参数，这意味着在Android上，应用程序的图标将被用作本地通知的图标。
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    // 15.1是DarwinInitializationSettings，旧版本好像是IOSInitializationSettings（有些例子中就是这个）
    const DarwinInitializationSettings initializationSettingsIOS = DarwinInitializationSettings();
    // 初始化
    InitializationSettings initializationSettings = const InitializationSettings(
        android: initializationSettingsAndroid, iOS: initializationSettingsIOS);
    await _notificationsPlugin.initialize(initializationSettings,
        onDidReceiveNotificationResponse: onDidReceiveNotificationResponse,
        onDidReceiveBackgroundNotificationResponse: onDidReceiveNotificationResponse);
  }

  // 本地通知处理通知点击响应
  static Future<void> onDidReceiveNotificationResponse(NotificationResponse response) async {
    dealNotificationResponse(response);
  }

//  显示通知
  static Future<void> showNotification({
    required String title,
    required String body,
    required String? payload,
    int? delayTime,
  }) async {
    // 安卓的通知
    // 'your channel id'：用于指定通知通道的ID。
    // 'your channel name'：用于指定通知通道的名称。
    // 'your channel description'：用于指定通知通道的描述。
    // Importance.max：用于指定通知的重要性，设置为最高级别。
    // Priority.high：用于指定通知的优先级，设置为高优先级。
    // 'ticker'：用于指定通知的提示文本，即通知出现在通知中心的文本内容。
    AndroidNotificationDetails androidNotificationDetails =
        AndroidNotificationDetails('urnovel.story.books.wattpad', 'urnovel_android',
            channelDescription: 'messages from urnovel',
            importance: Importance.max,
            priority: Priority.high,
            ticker: 'ticker',
            styleInformation: BigPictureStyleInformation(
              const DrawableResourceAndroidBitmap('@mipmap/ic_launcher'), // 在 drawable 中放置图片
              contentTitle: title,
              summaryText: body,
            ));

    // ios的通知
    const String darwinNotificationCategoryPlain = 'plainCategory';
    const DarwinNotificationDetails iosNotificationDetails = DarwinNotificationDetails(
      categoryIdentifier: darwinNotificationCategoryPlain, // 通知分类
    );
    // 创建跨平台通知
    NotificationDetails platformChannelSpecifics =
        NotificationDetails(android: androidNotificationDetails, iOS: iosNotificationDetails);

    if (delayTime == null) {
      // 发起一个通知
      await _notificationsPlugin.show(
        1,
        title,
        body,
        platformChannelSpecifics,
        payload: payload, //便于标识或处理通知
      );
    } else {
      await _notificationsPlugin.zonedSchedule(
        2,
        title,
        body,
        tz.TZDateTime.now(tz.local).add(Duration(seconds: delayTime)),
        platformChannelSpecifics,
        payload: payload,
        // androidAllowWhileIdle: true,
        // uiLocalNotificationDateInterpretation:
        //     UILocalNotificationDateInterpretation.absoluteTime,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      );
    }
  }

  static Future<void> dealNotificationResponse(dynamic response) async {
    try {
      ///是否已经点击了广告
      bool isClickNovelAd = await SpUtil.isNovelAdClick();
      if (isClickNovelAd) {
        SpUtil.spSetNovelAdClick(false);
        return;
      }

      int? bookId, chapterIndex;
      if (response is RemoteMessage) {
        RemoteMessage remoteMessage = response;
        bookId = int.parse(remoteMessage.data['bookId'] ?? '0');
        chapterIndex = int.parse(remoteMessage.data['chapterIndex'] ?? '0');
        pushId = int.parse(remoteMessage.data['pushId'] ?? '0');
      } else if (response is NotificationResponse) {
        if (isAvailable(response.payload)) {
          if (response.payload == "signin") {
            await Get.offAllNamed("/bottomNavBarPage");
            return;
          }

          Map? payload = jsonDecode(response.payload!);
          if (isAvailable(payload)) {
            bookId = int.parse(payload!['bookId'] ?? '0');
            chapterIndex = int.parse(payload['chapterIndex'] ?? '0');
            pushId = int.parse(payload['pushId'] ?? '0');
          }
        }
      }

      DataReportViewModel.pushEventReport({'pushId': pushId, 'eventType': PushType.click.name});

      //todo: 跳转到小说阅读页面
      await LocalNotificationManager.toNovelReadPage({
        'bookId': bookId == 0 ? null : bookId,
        'chapterIndex': chapterIndex == 0 ? null : chapterIndex,
        'jumpType': BookDetailJumpType.push
      });
    } catch (e) {
      logP(e.toString());
    }
  }

  //todo:跳转到小说阅读页面
  static Future<void> toNovelReadPage(Map arguments) async {
    try {
      if (curRoutePage == RoutePage.novelReadPage) {
        Get.back();
      } else if (curRoutePage == RoutePage.bookDetailPage &&
          lastRoutePage == RoutePage.novelReadPage) {
        Get.back();
        return;
      }
      await Get.toNamed('/novelReadPage', arguments: arguments);
    } catch (e) {
      logP(e.toString());
    }
  }
}
