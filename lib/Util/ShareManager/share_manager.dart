import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lecle_social_share/lecle_social_share.dart';
import 'package:path_provider/path_provider.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:UrNovel/Util/AlterManager/alter_manager.dart';
import 'package:UrNovel/Util/Common/Model/share_channel_model.dart';
import 'package:UrNovel/Util/Common/Animation/lottie_animation_view.dart';
import 'package:UrNovel/Util/NetWorkManager/net_work_manager.dart';
import 'package:UrNovel/Util/SheetAndAlter/toast.dart';
import 'package:UrNovel/Util/stringUtil.dart';
import '../../NativeBridge/native_bridge.dart';
import '../enum.dart';
import '../logUtil.dart';
import '../tools.dart';

class ShareManager {
  static final ShareManager instance = ShareManager._internal();

  // 最大重试次数
  static const int _maxRetryCount = 3;

  // 对话框状态
  OverlayEntry? _overlayEntry;

  ShareManager._internal();

  // TODO: 分享
  //《书名》+简介+链接
  shareNovel(String? bookName, String? description, String? bookCoverUrl,
      ShareBookUrlModel? shareBookUrlModel, ShareType type,
      {BuildContext? context}) {
    var msg = '《$bookName》\n\n${description ?? ""}\n\n';

    // 如果shareBookUrlModel为空，创建一个新的实例
    shareBookUrlModel ??= ShareBookUrlModel();

    // 设置书籍封面URL
    shareBookUrlModel.bookCoverUrl = bookCoverUrl;

    share(msg, shareBookUrlModel, type, context: context);
  }

  // 显示加载视图
  void _showLoadingOverlay(BuildContext context) {
    // 如果已经有一个加载视图在显示，先移除它
    _removeLoadingOverlay();

    _overlayEntry = OverlayEntry(
      builder: (context) => Material(
        color: Colors.black.withValues(alpha: 0.5),
        child: Center(
          child: Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: const Color.fromARGB(0, 255, 255, 255),
              borderRadius: BorderRadius.circular(8.0),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const LottieAnimationView(),
              ],
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  // 隐藏加载视图
  void _removeLoadingOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  // 从Flutter资产获取真实文件路径并保存到相册
  Future<String> _getImageFilePath(String assetPath) async {
    try {
      // 请求相册权限
      final permissionResult = await PhotoManager.requestPermissionExtend();
      if (!permissionResult.isAuth) {
        logD("无相册权限");
      }

      // 1. 加载资产
      final byteData = await rootBundle.load(assetPath);
      final bytes = byteData.buffer.asUint8List();

      // 2. 创建临时文件以便保存到相册
      final tempDir = await getTemporaryDirectory();
      final fileName =
          "share_image_${DateTime.now().millisecondsSinceEpoch}.png";
      final file = File('${tempDir.path}/$fileName');
      await file.writeAsBytes(bytes);

      // 3. 使用photo_manager保存图片到相册并获取asset
      final saveResult = await PhotoManager.editor.saveImageWithPath(
        file.path,
        title: "UrNovel_${DateTime.now().millisecondsSinceEpoch}",
      );

      if (isAvailable(saveResult)) {
        // 对于iOS，我们需要localIdentifier
        if (Platform.isIOS) {
          // 获取资源的原始数据，包含localIdentifier
          final assetEntity = saveResult;
          final localIdentifier = assetEntity.id;
          logD("保存图片到相册成功，localIdentifier: $localIdentifier");
          return localIdentifier;
        }
        // 对于Android，我们仍然使用文件路径
        else {
          logD("保存图片到相册成功，使用文件路径: ${file.path}");
          return file.path;
        }
      } else {
        throw Exception('保存图片到相册失败');
      }
    } catch (e) {
      logD("Error in _getImageFilePath: $e");
      rethrow;
    }
  }

  // 删除相册中的图片
  Future<bool> _deleteImageFromGallery(String identifier) async {
    try {
      if (identifier.isEmpty) {
        return false;
      }

      // 请求相册权限
      final permissionResult = await PhotoManager.requestPermissionExtend();
      if (!permissionResult.isAuth) {
        logD("无相册权限，无法删除图片");
        return false;
      }

      if (Platform.isIOS) {
        // iOS 使用 localIdentifier 删除资产
        final List<String> result =
            await PhotoManager.editor.deleteWithIds([identifier]);
        if (result.isNotEmpty) {
          logD("成功从相册删除图片: $identifier");
          return true;
        } else {
          logD("无法从相册删除图片: $identifier");
          return false;
        }
      } else if (Platform.isAndroid) {
        // 如果是 Android 11+ 可以尝试移动到回收站
        try {
          final AssetEntity? asset = await AssetEntity.fromId(identifier);
          if (asset != null) {
            final result =
                await PhotoManager.editor.android.moveToTrash([asset]);
            if (result.isNotEmpty) {
              logD("成功将图片移动到回收站: $identifier");
              return true;
            }
          }
        } catch (e) {
          logD("移动到回收站失败，尝试直接删除: $e");
        }

        // 如果移动到回收站失败或者是低版本Android，尝试直接删除
        final List<String> result =
            await PhotoManager.editor.deleteWithIds([identifier]);
        if (result.isNotEmpty) {
          logD("成功从相册删除图片: $identifier");
          return true;
        } else {
          logD("无法从相册删除图片: $identifier");

          // 最后尝试直接删除文件
          try {
            final file = File(identifier);
            if (await file.exists()) {
              await file.delete();
              logD("成功删除图片文件: $identifier");
              return true;
            }
          } catch (e) {
            logD("删除文件失败: $e");
          }

          return false;
        }
      }

      return false;
    } catch (e) {
      logD("删除相册图片时出错: $e");
      return false;
    }
  }

  // 处理封面图缓存，支持显示加载界面
  Future<String> getCoverImage(String? imageUrl,
      {BuildContext? context}) async {
    // 如果提供了context，显示加载界面
    if (context != null) {
      _showLoadingOverlay(context);
    }

    try {
      // 下载图片
      final result = await _downloadAndSaveBookCover(imageUrl);
      return result;
    } finally {
      // 无论成功与否，都移除加载界面
      if (context != null) {
        _removeLoadingOverlay();
      }
    }
  }

  // 从网络下载小说封面图并保存到相册，带有重试和缓存机制
  Future<String> _downloadAndSaveBookCover(String? imageUrl,
      {int retryCount = 0}) async {
    try {
      // 检查URL有效性
      if (imageUrl == null || imageUrl.isEmpty) {
        logD("封面图URL为空，使用默认图片");
        return await _getImageFilePath('assets/images/sign/icon_app_logo.png');
      }

      // 请求相册权限
      final permissionResult = await PhotoManager.requestPermissionExtend();
      if (!permissionResult.isAuth) {
        logD("无相册权限");
        // 如果没有权限，返回默认图片
        return await _getDefaultImage();
      }

      // 1. 下载网络图片
      final tempDir = await getTemporaryDirectory();
      final fileName =
          "novel_cover_${DateTime.now().millisecondsSinceEpoch}.jpg";
      final filePath = '${tempDir.path}/$fileName';

      // 获取处理过的URL (可能需要重定向等处理)
      final processedUrl = getRedirectImageUrl(imageUrl);

      // 使用http包下载图片
      // final response = await http
      //     .get(Uri.parse(processedUrl))
      //     .timeout(const Duration(seconds: 15)); // 设置超时时间
      final response = await NetWorkManager.instance.downloadFile(
          processedUrl, DownLoadResponseType.bytes,
          onReceiveProgress: (int count, int total) {});

      if (response.statusCode != 200) {
        throw Exception("下载封面图失败: ${response.statusCode}");
      }

      // 2. 保存到临时文件
      final file = File(filePath);
      await file.writeAsBytes(response.data);

      // 3. 使用photo_manager保存图片到相册并获取asset
      final saveResult = await PhotoManager.editor.saveImageWithPath(
        file.path,
        title: "UrNovel_${DateTime.now().millisecondsSinceEpoch}",
      );

      if (isAvailable(saveResult)) {
        String identifier;
        // 对于iOS，我们需要localIdentifier
        if (Platform.isIOS) {
          final assetEntity = saveResult;
          identifier = assetEntity.id;
          logD("保存封面图到相册成功，localIdentifier: $identifier");
        }
        // 对于Android，我们仍然使用文件路径
        else {
          identifier = file.path;
          logD("保存封面图到相册成功，使用文件路径: $identifier");
        }
        return identifier;
      } else {
        throw Exception('保存封面图到相册失败');
      }
    } catch (e) {
      logD("下载保存封面图出错: $e");

      // 实现重试机制
      if (retryCount < _maxRetryCount) {
        logD("重试下载封面图，当前重试次数: ${retryCount + 1}");
        // 延迟一段时间后重试
        await Future.delayed(Duration(milliseconds: 500 * (retryCount + 1)));
        return _downloadAndSaveBookCover(imageUrl, retryCount: retryCount + 1);
      }

      // 出错且重试达到最大次数时使用默认图片
      return await _getDefaultImage();
    }
  }

  // 获取默认图片
  Future<String> _getDefaultImage() async {
    return await _getImageFilePath('assets/images/sign/icon_app_logo.png');
  }

  share(String msg, ShareBookUrlModel? shareBookUrlModel, ShareType type,
      {BuildContext? context}) async {
    String content = msg;
    if (type == ShareType.facebook) {
      content = '$content${shareBookUrlModel?.facebook ?? ''}';
      String imagePath =
          await _getImageFilePath('assets/images/sign/icon_app_logo.png');

      LecleSocialShare.F.shareFeedContentToFacebook(
        link: shareBookUrlModel?.facebook ?? '',
        quote: msg,
        //iOS
        linkName: "Novel",
        //Android
        hashtag: "UrNovel",
        //Android
        picture: imagePath, //Android
      );
      // if (Platform.isIOS) {
      //   appinioSocialShare.iOS.shareToFacebook(imagePath, imageFiles);
      // } else {
      //   appinioSocialShare.android.shareToFacebook(imagePath, imageFiles);
      // }
    } else if (type == ShareType.instagram) {
      content = '$content${shareBookUrlModel?.instagram ?? ''}';
      LecleSocialShare.I.sendMessageToInstagram(
        message: content,
      );
      // if (Platform.isIOS) {
      //   appinioSocialShare.iOS.shareToInstagramDirect(content);
      // } else {
      //   appinioSocialShare.android.shareToInstagramDirect(content);
      // }
    } else if (type == ShareType.whatsApp) {
      content = '$content${shareBookUrlModel?.whatsApp ?? ''}';
      LecleSocialShare.W.sendMessageToWhatsApp(
        message: content,
      );
      // if (Platform.isIOS) {
      //   appinioSocialShare.iOS.shareToWhatsapp(content);
      // } else {
      //   appinioSocialShare.android.shareToWhatsapp(content, null);
      // }
    } else if (type == ShareType.tiktok) {
      try {
        // 从shareBookUrlModel获取封面图URL并进行下载保存
        String bookCoverUrl = shareBookUrlModel?.bookCoverUrl ?? '';

        // 下载并保存小说封面图到相册，使用封装后的方法
        String identifier = await getCoverImage(bookCoverUrl, context: context);
        logD("获取到的identifier: $identifier");

        // 调用原生方法分享到TikTok
        WidgetsBinding.instance.addPostFrameCallback((_) {
          NativeBridge.instance.callTiktokShare(
            identifier,
            (String result) {
              logD("Flutter: callTiktokShare result: $result");
              // // 分享成功后删除图片
              // _deleteImageFromGallery(identifier).then((success) {
              //   if (success) {
              //     logD("分享成功后删除图片成功");
              //   } else {
              //     logD("分享成功后删除图片失败");
              //   }
              // });
            },
            (String error) {
              logD("Flutter: callTiktokShare error: $error");
            },
          );
        });
      } catch (e) {
        logD("分享到TikTok时出错: $e");
      }
    } else if (type == ShareType.copyLink) {
      content = shareBookUrlModel?.copyLink ?? '';
      copyToClipboard(content);
    }

    await AlterManager.instance.showNotificationAlter(NotificationAlterSource.afterShare);
  }

  // 打开链接
  Future<void> launchLinkUrl(dynamic url) async {
    // 检查URL是否可以被打开
    Uri? uri;
    if (url is String) {
      uri = Uri.parse(url);
    } else if (url is Uri) {
      uri = url;
    }

    if (uri != null) {
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri); // 打开链接
      } else {
        launchUrl(Uri.parse(appStoreAppUrl)); // 打开链接字符串
      }
    } else {
      showToast('cannot open link');
    }
  }
}
