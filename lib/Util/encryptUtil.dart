import 'package:encrypt/encrypt.dart' as Encrypt;

///章节解密iv
const String _contentIV = "34wiY291bnRyeSI6";
///章节解密key
const String _contentKey =
    "9hdmF0YXIucG5nIiwiZGV2aWNlX2lkIjoiNzEzMjJmODEwOTQ5N2Q1MyIsImVt";

class EncryptUtil {
  static EncryptUtil instance = EncryptUtil();
  /// aes 解密
  /// [content] 加密内容
  /// [key] 密钥
  /// [iv] 偏移量
  static String aesDecrypt(String enc, String uniqueKey, String iv) {
    final key = Encrypt.Key.fromUtf8(uniqueKey);
    final ivSpec = Encrypt.IV.fromUtf8(iv);
    final encrypter = Encrypt.Encrypter(
        Encrypt.AES(key, mode: Encrypt.AESMode.cbc, padding: 'PKCS7'));
    final decrypted = encrypter.decrypt(
      Encrypt.Encrypted.fromBase16(enc),
      iv: ivSpec,
    );

    return decrypted;
  }

  /// 章节内容解密
  /// [content] 加密内容
  /// [chapterId] 章节id
  static String chapterContentDecrypt(String content, int chapterId) {
    return aesDecrypt(content,
        _contentKey.substring(chapterId % 10, chapterId % 10 + 32), _contentIV);
  }
}

