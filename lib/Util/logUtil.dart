import 'dart:developer' as developer;

import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';

/// 看淡打印 调试日志
void logP(dynamic message) {
  if(kDebugMode) {
    if (message is String) {
      debugPrint(message);
    }else{
      debugPrint('$message');
    }
  }
}

///详细打印 堆栈信息 调试日志
void logD(dynamic message) {
  if (kDebugMode) {
    developer.log(_getStackTraceString(message), name: 'DEBUG');
  }
}

///打印警告日志
void logW(dynamic message) {
  if (kDebugMode) {
    developer.log(_getStackTraceString(message), name: 'WARN');
  }
}

// 获取堆栈信息字符串
String _getStackTraceString(dynamic message) {
  final sb = StringBuffer();

  // 添加传入的消息
  if (message != null) {
    sb.write('[Message]: ${message.toString()}  ');
  }

  // 添加时间戳
  final currentTime =
      DateFormat('yyyy-MM-dd HH:mm:ss.SSS').format(DateTime.now());
  sb.write('Time: $currentTime  ');

  // 添加堆栈信息
  sb.write('Stack Trace:');
  final stackTrace = StackTrace.current.toString().split('\n');
  if (stackTrace.length > 2) {
    final element = stackTrace[2].trim(); // 获取调用点的堆栈信息
    sb.write('  at $element');
  }

  //     // 添加完整的堆栈信息
  // sb.writeln('Stack Trace:');
  // final stackTrace = StackTrace.current.toString().split('\n');
  // for (var i = 0; i < stackTrace.length; i++) {
  //   if (stackTrace[i].trim().isNotEmpty) {
  //     sb.writeln('    at ${stackTrace[i].trim()}');
  //   }
  // }

  return sb.toString();
}
