import 'package:UrNovel/Launch&Login/Model/language_model.dart';
import 'package:UrNovel/Util/Common/CommonManager/common_manager.dart';
import 'package:UrNovel/Util/ReaderUtils/readerUtil.dart';
import 'package:UrNovel/Util/SharedPreferences/shared_preferences.dart';
import 'package:audio_service/audio_service.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:screen_brightness/screen_brightness.dart';

import '../../MainPage/NovelRead/ListeningBook/AudioHandler/audio_handler.dart';
import '../../MainPage/NovelRead/Model/book_detailInfo_model.dart';
import '../../MainPage/NovelRead/Model/speed_option.dart';
import '../DataReportManager/ServiceReport/data_report_manager.dart';
import '../ReaderUtils/novel_preferences_settings.dart';
import '../SheetAndAlter/toast.dart';
import '../enum.dart';
import '../logUtil.dart';
import '../tools.dart';

///Getx状态管理

//todo:BottomNavBar状态控制
class BottomBarController extends GetxController {
  //RxBool 来表示底部导航栏的显示状态
  var isBottomBarVisible = true.obs;

  //RxInt 来表示当前选中的 BottomBar 项的索引
  var currentIndex = 0.obs;
  var isTurnToForYou = false.obs;

  // 方法：切换底部导航栏的显示状态
  void toggleBottomBar(bool isVisible) {
    isBottomBarVisible.value = isVisible;
  }

  // 方法：切换 BottomBar 项
  void changeIndex(int index, bool isForYou) {
    currentIndex.value = index;
    if (index == 0) {
      isTurnToForYou.value = isForYou;
    }
  }

  int getIndex() {
    return currentIndex.value;
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }
}

class ChatBarController extends GetxController {
  //RxBool 来表示底部导航栏的显示状态
  var isBottomBarVisible = true.obs;

  //RxInt 来表示当前选中的 BottomBar 项的索引
  var currentIndex = 1.obs;

  // 方法：切换底部导航栏的显示状态
  void toggleBottomBar(bool isVisible) {
    isBottomBarVisible.value = isVisible;
  }

  // 方法：切换 BottomBar 项
  void changeIndex(int index) {
    currentIndex.value = index;
  }

  int getIndex() {
    return currentIndex.value;
  }
}

//todo: 多选，混合选择 （书架状态）
class BookSelectionController extends GetxController {
  var selectedBookList = <Map<String, dynamic>>[].obs;

  //初始化
  // void initialize(int length) {
  //   selectedBookList.value = List.generate(length, (_) => false);
  // }

  //选择
  void bookSelection(int sectionIndex, int itemIndex) {
    Map<String, dynamic> map = {sectionIndex.toString(): itemIndex.toString()};
    if (judgeIsContains(map, isClear: true)) {
      selectedBookList.remove(map);
    } else {
      selectedBookList.add(map);
    }
  }

  //是否被选中
  bool isSelected(int sectionIndex, int itemIndex) {
    Map<String, dynamic> map = {sectionIndex.toString(): itemIndex.toString()};
    return judgeIsContains(map);
  }

  bool judgeIsContains(Map<String, dynamic> map, {bool isClear = false}) {
    if (selectedBookList.isNotEmpty) {
      for (var item in selectedBookList) {
        if (item.keys.first == map.keys.first && item.values.first == map.values.first) {
          if (isClear) {
            selectedBookList.remove(item);
          }
          return true;
        }
      }
    }

    return false;
  }

  //清理状态
  void clearSelection() {
    selectedBookList.clear();
  }
}

//todo:多选 （bookdetail report）
class BookDetailReportController extends GetxController {
  var reportList = <dynamic>[].obs;

  void addReport(dynamic report) {
    reportList.add(report);
  }

  //是否被选中
  void selected(dynamic report) {
    if (isSelected(report)) {
      reportList.remove(report);
    } else {
      reportList.add(report);
    }
  }

  //是否被选中
  bool isSelected(dynamic report) {
    return reportList.contains(report);
  }

  void clearReport() {
    reportList.clear();
  }
}

//todo: novel阅读书架收藏-亮度管理
class NovelReadStatusController extends GetxController {
  //亮度
  var brightness = 0.0.obs;

  //是否加入书架
  var isAddLibrary = false.obs;

  //是否收藏
  var isLiked = false.obs;

  //是否收藏
  var likes = 0.obs;

  //聊天，听书入口透明度
  var floatMenuOpacity = 0.6.obs;

  //当前用户点赞了的所有插图信息
  var illustrationList = <dynamic>[].obs;

  @override
  void onInit() {
    super.onInit();
    _initBrightness();
  }

  Future<void> _initBrightness() async {
    try {
      brightness.value = await ScreenBrightness().application;
    } catch (e) {
      logP("Failed to get current brightness: $e");
      brightness.value = 0.0;
    }
  }

  Future<void> setBrightness(double value) async {
    try {
      await ScreenBrightness().setApplicationScreenBrightness(value);
      brightness.value = value;
    } catch (e) {
      logP("Failed to set brightness: $e");
    }
  }

  void setAddLibrary(bool value) {
    isAddLibrary.value = value;
  }

  void setLiked(bool value) {
    isLiked.value = value;
  }

  //todo: 收藏数
  void setLikes(String value) {
    likes.value = int.parse(value);
  }

  void updateLikes(bool isAdd) {
    if (isAdd) {
      likes.value += 1;
    } else {
      likes.value -= 1;
    }

    if (likes.value < 0) {
      likes.value = 0;
    }
  }

  void setFloatMenuOpacity(double alpha) {
    floatMenuOpacity.value = alpha;
  }

  void setIllustrationList(List<dynamic> list) {
    illustrationList.value = list;
  }
}

//todo: novel阅读偏好设置管理
class NovelPreferencesSettingController extends GetxController {
  var themeColor = ReaderUtil.instance.themeColor.obs;
  var titleColor = ReaderUtil.instance.titleColor.obs;
  var fontSize = ReaderUtil.instance.fontSize.obs;
  var lineSpace = ReaderUtil.instance.lineSpace.obs;
  var horizontalPadding = ReaderUtil.instance.horizontalPadding.obs;
  var fontType = ReaderUtil.instance.fontType.obs;
  var fontFamily = ReaderUtil.instance.fontFamily.obs;
  var readingMode = ReaderUtil.instance.readingMode.obs;

  @override
  void onInit() {
    super.onInit();
    initPreferencesSettings();
  }

  Future<void> initPreferencesSettings() async {
    await getNovelPreferencesSettings();
  }

  //todo: 设置主题颜色
  void setThemeColor(String? value, int index) {
    themeColor.value = value ?? ReaderUtil.instance.themeColor;
    if (index == 3) {
      titleColor.value = "#b8b8b8";
      SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.light);
    } else {
      titleColor.value = ReaderUtil.instance.titleColor;
      SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);
    }
  }

  void setTitleColor(String? value) {
    titleColor.value = value ?? ReaderUtil.instance.titleColor;
  }

  void setFontSize(double? value) {
    logP(value);
    fontSize.value = value ?? ReaderUtil.instance.fontSize;
  }

  void setLineSpace(double? value) {
    lineSpace.value = value ?? ReaderUtil.instance.lineSpace;
  }

  void setHorizontalPadding(double? value) {
    horizontalPadding.value = value ?? ReaderUtil.instance.horizontalPadding;
  }

  void setFontType(FontType? value) {
    fontType.value = value ?? ReaderUtil.instance.fontType;
  }

  Future setFontFamily() async {
    var family = await getFontFamily();
    fontFamily.value = family;
  }

  void setReadingMode(ReadingMode? model) {
    readingMode.value = model ?? ReaderUtil.instance.readingMode;
  }

  //TODO: 获取偏好设置
  Future<void> getNovelPreferencesSettings() async {
    NovelPreferencesSettings preferencesSettings = await SpUtil.spGetNovelPreferencesSettings();
    if (isAvailable(preferencesSettings)) {
      themeColor.value = preferencesSettings.themeColor ?? ReaderUtil.instance.themeColor;
      titleColor.value = preferencesSettings.titleColor ?? ReaderUtil.instance.titleColor;
      fontSize.value = preferencesSettings.fontSize ?? ReaderUtil.instance.fontSize;
      lineSpace.value = preferencesSettings.lineSpace ?? ReaderUtil.instance.lineSpace;
      horizontalPadding.value =
          preferencesSettings.horizontalPadding ?? ReaderUtil.instance.horizontalPadding;
      fontType.value = preferencesSettings.fontType ?? ReaderUtil.instance.fontType;
      fontFamily.value = preferencesSettings.fontFamily ?? ReaderUtil.instance.fontFamily;
      readingMode.value = preferencesSettings.readingMode ?? ReaderUtil.instance.readingMode;
    }
  }

  //TODO: 更新偏好设置
  Future<void> setPreferencesSettings() async {
    NovelPreferencesSettings preferencesSettings = NovelPreferencesSettings(
        themeColor: themeColor.value,
        titleColor: titleColor.value,
        fontSize: fontSize.value,
        lineSpace: lineSpace.value,
        horizontalPadding: horizontalPadding.value,
        fontType: fontType.value,
        fontFamily: fontFamily.value,
        readingMode: readingMode.value);
    await SpUtil.spSetNovelPreferences(preferencesSettings);
  }

  //TODO: 获取文本计算高度偏移
  double getOffset() {
    double offset = 0.0;
    if (lineSpace.value < 1.8) {
      offset = 10;
    } else if (lineSpace.value < 2.0) {
      offset = 15;
    } else {
      offset = 20;
    }
    return offset;
  }

  //TODO: 获取字体名称
  ///三种特殊语言字体，一种普通字体
  ///泰语--NotoSerifThai  NotoSansThai
  ///高棉语--NotoSerifKhmer  NotoSansKhmer
  ///阿拉伯语--NotoNaskhArabic
  ///通用字体--NotoSerif  NotoSans
  Future<String> getFontFamily() async {
    FontType style = fontType.value;
    LanguageItem? languageItem = await SpUtil.spGetContentLanguageItem();
    if (style == FontType.serifed) {
      if (isAvailable(languageItem) && languageItem?.code == "th") {
        //泰语
        return "NotoSerifThai";
      } else if (isAvailable(languageItem) && languageItem?.code == "km") {
        //高棉语
        return "NotoSerifKhmer";
      } else if (isAvailable(languageItem) && languageItem?.code == "ar") {
        //阿拉伯语
        return "NotoNaskhArabic";
      }

      return "NotoSerif";
    } else if (style == FontType.sansSerifed) {
      if (isAvailable(languageItem) && languageItem?.code == "th") {
        //泰语
        return "NotoSansThai";
      } else if (isAvailable(languageItem) && languageItem?.code == "km") {
        //高棉语
        return "NotoSansKhmer";
      } else if (isAvailable(languageItem) && languageItem?.code == "ar") {
        //阿拉伯语
        return "NotoNaskhArabic";
      }

      return "NotoSans";
    }

    return "NotoSans";
  }
}

//todo: 有声书管理
class AudioBookController extends GetxController {
  bool _isDisposed = false;

  // final speeds = [0.5, 0.75, 0.85, 1.0, 1.25, 1.5];
  final List<SpeedOption> speedOptions = [
    SpeedOption('slowest'.tr, 0.5),
    SpeedOption('slower'.tr, 0.75),
    SpeedOption('slow'.tr, 0.85),
    SpeedOption('normal'.tr, 1.0),
    SpeedOption('fast'.tr, 1.25),
    SpeedOption('faster'.tr, 1.5),
  ];

  late Function(bool isNeedUnlock)? onChapterChangeCallBack;

  var isShowFloating = false.obs;
  var isPlaying = false.obs;
  var isLoading = false.obs;
  var isResetRotation = false.obs;
  var duration = Duration.zero.obs;
  var position = Duration.zero.obs;
  var isSingleCompleted = false.obs;
  var isAllCompleted = false.obs;
  var isLoadingAudioError = false.obs;
  var currentChapter = ChapterVoModel().obs;
  var currentVoiceModel = ChapterVoiceModel().obs;
  var isCheckingChapter = false;
  var currSpeedOption = SpeedOption('normal'.tr, 1.0).obs;

  static late final ListeningBookAudioHandler _audioHandler;
  late BookDetailInfoResultModel bookDetailInfoModel;

  get bookId => bookDetailInfoModel.bookId ?? '';

  get coverUrl => bookDetailInfoModel.cover ?? '';

  get bookName => bookDetailInfoModel.title ?? '';

  get bookGoldCount => bookDetailInfoModel.bookGoldCount;

  get authorName => bookDetailInfoModel.authorName ?? '';

  get chapterId => currentChapter.value.id;

  get chapterName => currentChapter.value.title ?? '';

  get cost => currentChapter.value.cost;

  get isAddToLibrary => bookDetailInfoModel.library ?? false;

  get chapterVoList => bookDetailInfoModel.chapterVoList ?? [];

  get chapterVoiceList => currentChapter.value.chapterVoice ?? [];

  double get progress =>
      position.value.inMicroseconds /
      (duration.value.inMicroseconds == 0 ? 1 : duration.value.inMicroseconds);

  //todo: 初始化数据
  init(BookDetailInfoResultModel infoModel, ChapterVoModel chapter) async {
    bookDetailInfoModel = infoModel;
    currentChapter.value = chapter;
    await getCurrVoiceTone();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await setVoiceTone();
    });
  }

  Future<void> setCurrChapter(ChapterVoModel chapter) async {
    await playerStop();
    currentChapter.value = chapter;
    checkCurrChapterVoice(null);
    await setVoiceTone();
    await reportReadRecord(ReadReportActivityType.reading, ReadReportReadingType.listen, false);
  }

  void updateCurrentChapterLockStatus() {
    try {
      for (var chapter in chapterVoList) {
        if (chapter.id == currentChapter.value.id) {
          chapter.lock = 2;
          currentChapter.value = chapter;
          break;
        }
      }

      setVoiceTone();
    } catch (e) {
      logP("Error updating current chapter lock status: $e");
    }
  }

  Future<void> getCurrVoiceTone() async {
    // final SharedPreferences prefs = await SharedPreferences.getInstance();
    int id = await SpUtil.spGetVoiceSpeakerId();
    checkCurrChapterVoice(id);
  }

  Future<void> setCurrVoiceTone(ChapterVoiceModel? model) async {
    if (isAvailable(model?.id)) {
      await SpUtil.spSetVoiceSpeakerId(model!.id!);
    }
  }

  void checkCurrChapterVoice(int? id) {
    if (isAvailable(currentChapter.value.chapterVoice)) {
      if (isAvailable(id)) {
        currentVoiceModel.value = currentChapter.value.chapterVoice!.firstWhere(
            (voice) => voice?.id == id,
            orElse: () => currentChapter.value.chapterVoice!.first)!;
      } else {
        currentVoiceModel.value = currentChapter.value.chapterVoice!.first!;
      }
    }
  }

  bool judgeChapterIsAvailable() {
    return currentChapter.value.lock != 1;
  }

  Future<void> onChapterChange(bool isNeedUnlock) async {
    onChapterChangeCallBack?.call(isNeedUnlock);
    if (isNeedUnlock) {
      await reportReadRecord(ReadReportActivityType.purchase, ReadReportReadingType.listen, false);
    } else {
      await reportReadRecord(ReadReportActivityType.reading, ReadReportReadingType.listen, false);
    }
  }

  //todo: 初始化音频播放器以及回调
  Future<void> initAudioHandler() async {
    try {
      _audioHandler = await AudioService.init(
        builder: () => ListeningBookAudioHandler(),
        config: const AudioServiceConfig(
          androidNotificationChannelId: 'com.urNovel.channel.audio',
          androidNotificationChannelName: 'Listening Book',
          androidNotificationOngoing: true,
          fastForwardInterval: Duration(seconds: 15),
          rewindInterval: Duration(seconds: 15),
        ),
      );

      // 监听播放状态变化
      _audioHandler.playbackState.listen((state) async {
        isPlaying.value = state.playing;
        isLoading.value = state.processingState == AudioProcessingState.loading;
        isSingleCompleted.value = state.processingState == AudioProcessingState.completed;
        // 播放完成时的特殊处理
        if (isSingleCompleted.value) {
          if (!isCheckingChapter) {
            await playNext();
          }
        } else {
          isCheckingChapter = false;
          // 只在非完成状态下更新播放位置
          if (state.updatePosition != position.value) {
            position.value = state.updatePosition;
          }
        }
      });

      // 监听音频时长变化
      _audioHandler.mediaItem.listen((mediaItem) {
        if (mediaItem?.duration != null) {
          duration.value = mediaItem!.duration!;
        }
      });
      await setPlaybackSpeed();
    } catch (e) {
      logW("Error initializing audio handler: $e");
      isLoading.value = false;
      isLoadingAudioError.value = true;
    }
  }

  Future<void> setChapterVoiceModel(ChapterVoiceModel model) async {
    if (currentVoiceModel.value.id == model.id) {
      return;
    }
    currentVoiceModel.value = model;
    setVoiceTone();
  }

  Future<void> playNext() async {
    try {
      if (isAvailable(chapterVoList)) {
        isCheckingChapter = true;
        for (var chapter in chapterVoList) {
          if (chapter?.id == currentChapter.value.id) {
            int index = chapterVoList.indexOf(chapter);
            if (index < chapterVoList.length - 1) {
              if (currentLifecycleState == AppLifecycleState.resumed) {
                await playerStop();
              }
              currentChapter.value = chapterVoList[index + 1];
              checkCurrChapterVoice(null);
              await setVoiceTone();
              isCheckingChapter = false;
              await onChapterChange(false);
              break;
            } else {
              await playerStop();

              /// 已经是最后一章
              isAllCompleted.value = true;
              showToast("already_last_chapter".tr);

              /// 阅读数据上报
              await DataReportManager.instance.reportOldReadChapterList(
                  ReadReportActivityType.finishNovel, ReadReportReadingType.listen);
              break;
            }
          }
        }
      }
    } catch (e) {
      logP("Error playing next chapter: $e");
    }
  }

  Future<void> playPrevious() async {
    try {
      if (isAvailable(chapterVoList)) {
        isCheckingChapter = true;
        for (var chapter in chapterVoList) {
          if (chapter?.id == currentChapter.value.id) {
            int index = chapterVoList.indexOf(chapter);
            if (index > 0) {
              if (currentLifecycleState == AppLifecycleState.resumed) {
                await playerStop();
              }
              currentChapter.value = chapterVoList[index - 1];
              checkCurrChapterVoice(null);
              await setVoiceTone();
              isCheckingChapter = false;
              await onChapterChange(false);
              break;
            } else {
              await playerStop();

              /// 已经是第一章
              showToast('already_first_chapter'.tr);
              break;
            }
          }
        }
      }
    } catch (e) {
      logP("Error playing previous chapter: $e");
    }
  }

  Future<void> playNow() async {
    setSource(currentVoiceModel.value.url ?? "", true);
  }

  Future<void> playByUrl(String url) async {
    isSingleCompleted.value = false;
    isAllCompleted.value = false;
    isPlaying.value = false;
    isResetRotation.value = true;
    position.value = Duration.zero;
    duration.value = Duration.zero;
    await setSource(url, true);
    await seek(position.value);
    isResetRotation.value = false;
  }

  Future<void> setSource(String url, bool autoPlay) async {
    try {
      isLoadingAudioError.value = false;
      isLoading.value = true;
      await playerPause();
      await _audioHandler.setAudioSource(
        url,
        currentChapter.value.title ?? '', //chapter
        bookName, //title
        coverUrl,
      );
      // 等待音频准备就绪
      await setPlaybackSpeed();
      if (autoPlay) {
        await playerPlay();
      }
    } catch (e) {
      logW("Loading audio source error: $e");
      isLoadingAudioError.value = true;
      isLoading.value = false;
      showToast("load_audio_error".tr);
    }
  }

  Future<void> playerPlay() async {
    try {
      if (judgeChapterIsAvailable()) {
        if (!isPlaying.value) {
          await _audioHandler.play();
          isPlaying.value = true;
        }
      } else {
        await onChapterChange(true);
      }
    } catch (e) {
      logW("Error playing/pausing audio: $e");
    }
  }

  Future<void> playerPause() async {
    try {
      if (judgeChapterIsAvailable()) {
        if (isPlaying.value) {
          await _audioHandler.pause();
          isPlaying.value = false;
        }
      }
    } catch (e) {
      logW("Error playing/pausing audio: $e");
    }
  }

  Future<void> playerStop() async {
    try {
      await _audioHandler.stop();
      isPlaying.value = false;
    } catch (e) {
      logW("Error playing/pausing audio: $e");
    }
  }

  void setShowFloating(bool isShow) {
    isShowFloating.value = isShow;
  }

  Future<void> setCurrSpeedOption(SpeedOption option) async {
    currSpeedOption.value = option;
    setPlaybackSpeed();
  }

  Future<void> setPlaybackSpeed() async {
    try {
      await _audioHandler.player.setSpeed(currSpeedOption.value.speed.abs());
    } catch (e) {
      logW("Error changing playback speed: $e");
    }
  }

  Future<void> setVoiceTone() async {
    await setCurrVoiceTone(currentVoiceModel.value);
    if (judgeChapterIsAvailable()) {
      await playByUrl(currentVoiceModel.value.url ?? "");
    } else {
      playerStop();
      position.value = Duration.zero;
      duration.value = Duration.zero;
      isPlaying.value = false;
      await onChapterChange(true);
    }
  }

  Future<void> seek(Duration duration) async {
    try {
      await _audioHandler.seek(duration.abs());
    } catch (e) {
      logW("Error seeking audio: $e");
    }
  }

  // 修改开始拖拽方法
  void startDragging() {
    if (!judgeChapterIsAvailable()) {
      position.value = Duration.zero;
      return;
    }

    _audioHandler.startDragging();
  }

  // 修改结束拖拽方法
  Future<void> endDragging(Duration dragPosition) async {
    if (!judgeChapterIsAvailable()) {
      position.value = Duration.zero;
      return;
    }

    Duration drag = dragPosition;
    if (dragPosition.inSeconds == duration.value.inSeconds) {
      drag = Duration.zero;
      updateDragPosition(Duration.zero);
      playNext();
    }

    await _audioHandler.endDragging(drag.abs());
  }

  // 修改拖拽过程中更新位置的方法
  void updateDragPosition(Duration dragPosition) {
    if (!judgeChapterIsAvailable()) {
      position.value = Duration.zero;
      return;
    }
    _audioHandler.updateDragPosition(dragPosition.abs());
    // 临时更新UI显示，但不影响实际播放位置
    position.value = dragPosition.abs();
  }

  String formatTime(Duration duration) {
    String twoDigits(dynamic n) => n.toString().padLeft(2, '0');
    final hours = twoDigits(duration.inHours);
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return [if (duration.inHours > 0) hours, minutes, seconds].join(':');
  }

  //TODO: 存储阅读记录上报
  Future<void> reportReadRecord(
      ReadReportActivityType pointType, ReadReportReadingType readType, bool isReport) async {
    DataReportManager.instance.recordReadChapter(
        audioBookController.currentChapter.value, pointType, readType, false, isReport);
  }

  @override
  void dispose() {
    playerStop();
    setShowFloating(false);
    removeAudioBookFloatingOverlay();

    if (_isDisposed) {
      _isDisposed = false;
      return;
    }
    _isDisposed = true;

    super.dispose();
  }
}

//todo: 网络状态管理
class NetWorkStatusController extends GetxController {
  var isConnectivity = true.obs;

  Future<void> updateConnectivityStatus(ConnectivityResult status) async {
    if (status == ConnectivityResult.mobile || status == ConnectivityResult.wifi) {
      isConnectivity = true.obs;
    } else {
      isConnectivity = false.obs;
    }

    if (isConnectivity.value == true) {
      // 网络连接成功
      if (!isAvailable(CommonManager.instance.languageList)) {
        await CommonManager.instance.getContentLanguage();
      }
    }
  }
}

//todo: 通用状态管理
class CommonStatusController extends GetxController {
  ///评论相关
  var replyName = ''.obs; //回复对象的名称
  var isReply = false.obs; //是否是回复

  void setReplyName(String name) {
    if (replyName.value == name) return;
    replyName.value = name;
  }

  void setReply(bool isReply) {
    if (this.isReply.value == isReply) return;
    this.isReply = isReply.obs;
  }
}
