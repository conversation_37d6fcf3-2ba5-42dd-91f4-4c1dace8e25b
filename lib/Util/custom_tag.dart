// Custom Tag Widget
import 'package:flutter/material.dart';

class CustomTag extends StatelessWidget {
  final String label;

  const CustomTag({
    super.key,
    required this.label,
  });

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: Color(0xFFEAEDEE).withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
        child: Text(
          label,
          style: const TextStyle(
            color: Color(0XFFB2B3B3),
            // color: Color(0xFFB2B3B3),
            fontSize: 12,
          ),
        ),
      ),
    );
  }
}
