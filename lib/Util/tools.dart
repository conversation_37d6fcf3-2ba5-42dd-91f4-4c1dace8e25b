import 'dart:async';
import 'dart:io';

import 'package:UrNovel/Util/ShareManager/share_manager.dart';
import 'package:UrNovel/Util/SheetAndAlter/alter.dart';
import 'package:UrNovel/Util/enum.dart';
import 'package:UrNovel/Util/stringUtil.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../MainPage/NovelRead/ListeningBook/Widget/listening_book_floating_widget.dart';
import 'DataReportManager/ServiceReport/data_report_manager.dart';
import 'DocumentFileManager/document_file_manager.dart';
import 'EventBusUtil/event_bus_util.dart';
import 'ReaderUtils/readerUtil.dart';
import 'StatusManagement/status_management.dart';
import 'genericUtil.dart';
import 'logUtil.dart';

///当前生命周期状态
AppLifecycleState currentLifecycleState = AppLifecycleState.resumed;

///路由观察者
RoutePage lastRoutePage = RoutePage.unKnown;
RoutePage curRoutePage = RoutePage.unKnown;

///全局事件总线,时间监听通知
final eventBusUtil = EventBusUtil();

///发送事件
void eventBusFire(dynamic event) {
  eventBusUtil.fire(event);
}

///监听事件
///T 事件传递对象类型
///listener 监听回调
StreamSubscription<T> eventBusOn<T>(Function(dynamic) listener) {
  return eventBusUtil.on<T>().listen((obj) {
    listener(obj);
  });
}

//todo：支付提醒弹窗是否展示
bool isPaymentReminderNeedShow = false;
bool isPaymentReminderCurrentShow = false;
//todo：弹窗是否已展示
bool isAlterShow = false;

//todo：签到是否打开
bool isSignInSwitchOn = false;

///有声书控制器及浮窗
final AudioBookController audioBookController = findGetXInstance(AudioBookController());

OverlayEntry? audioBookFloating;

void showAudioBookFloating(BuildContext context) {
  if (!isAvailable(audioBookFloating)) {
    audioBookFloating = OverlayEntry(
      builder: (context) {
        return Obx(() {
          return audioBookController.isShowFloating.value
              ? const ListeningBookFloatingWidget()
              : SizedBox.shrink(); // 使用 SizedBox.shrink() 代替 SizedBox() 来消除空白区域
        });
      },
    );
    Overlay.of(context).insert(audioBookFloating!);
  } else {
    audioBookFloating?.markNeedsBuild();
  }
}

Future<void> removeAudioBookFloatingOverlay() async {
  if (isAvailable(audioBookFloating)) {
    audioBookController.setShowFloating(false);
    if (audioBookFloating!.mounted) {
      audioBookFloating!.remove();
    }
    audioBookFloating!.dispose();
    audioBookFloating = null;

    /// 阅读数据上报
    await DataReportManager.instance
        .reportOldReadChapterList(ReadReportActivityType.exitReading, ReadReportReadingType.listen);
  }
}

bool isAvailable(Object? obj) {
  if (obj == null) {
    return false;
  } else {
    if (obj is String && (obj.isEmpty || obj == "null" || obj == " ")) {
      return false;
    } else if (obj is List && obj.isEmpty) {
      return false;
    } else if (obj is Map && obj.isEmpty) {
      return false;
    }
  }

  return true;
}

Future<void> handleBack() async {
  if (Get.key.currentState?.canPop() ?? false) {
    Get.back();
  } else {
    await Get.offAllNamed('/bottomNavBarPage');
  }
}

//todo：版本升级
bool isNewVersionAvailable(String currentVersion, String latestVersion) {
  List<String> currentParts = currentVersion.split('.');
  List<String> latestParts = latestVersion.split('.');
  var length = currentParts.length <= latestParts.length ? currentParts.length : latestParts.length;
  for (int i = 0; i < length; i++) {
    int currentPart = int.parse(currentParts[i]);
    int latestPart = int.parse(latestParts[i]);

    if (latestPart > currentPart) {
      return true;
    } else if (latestPart < currentPart) {
      return false;
    }
  }

  return false; // 版本相同
}

String getRankingCode(RankingType type) {
  if (type == RankingType.trending) {
    return rankingTRENDING;
  } else if (type == RankingType.newRelease) {
    return rankingNEWRELEASES;
  } else if (type == RankingType.hotSearches) {
    return rankingHOTSEARCHES;
  } else if (type == RankingType.favorites) {
    return rankingFAVORITES;
  }

  return rankingTRENDING;
}

//todo：Document文件读写
Future<File> saveStringToFile(String fileName, String str) {
  return DocumentFileManager.instance.writeFile(fileName, str);
}

Future<String?> readStringFormFile(String fileName) {
  return DocumentFileManager.instance.readFile(fileName);
}

//获取首页板块类型
HomeSectionType getHomeSectionType(String? type) {
  if (type == "STYLE1") {
    return HomeSectionType.sixCardsCard;
  } else if (type == "STYLE2") {
    return HomeSectionType.singleLineVerticalSliding;
  } else if (type == "STYLE3") {
    return HomeSectionType.threeLinesCard;
  } else if (type == "STYLE4") {
    return HomeSectionType.singleLineHorizontalSliding;
  } else if (type == "STYLE5") {
    return HomeSectionType.singleLineVerticalSlidingCard;
  }

  return HomeSectionType.unknown;
}

String getBookDetailJumpType(BookDetailJumpType? type) {
  if (type == BookDetailJumpType.search) {
    return bookDetailJumpSearch;
  }

  return bookDetailJumpOther;
}

//todo：languageCode
Locale getLanguageLocale(Locale? locale) {
  return locale?.languageCode == 'zh' ? const Locale('cn' 'CH') : const Locale('en' 'US');
}

/// 获取当前时间戳（秒级）
int getCurrentTimeSeconds() {
  return (DateTime.now().millisecondsSinceEpoch ~/ 1000);
}

/// 获取当前时间戳（毫秒级）
int getCurrentTimeMillis() {
  return DateTime.now().millisecondsSinceEpoch;
}

/// 检测时间戳是秒还是毫秒
DateTime convertTimestamp(int timestamp) {
  // 检查时间戳的长度
  if (timestamp.toString().length == 10) {
    // 秒级时间戳
    return DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
  } else if (timestamp.toString().length == 13) {
    // 毫秒级时间戳
    return DateTime.fromMillisecondsSinceEpoch(timestamp);
  } else {
    // 默认处理，假设是秒级时间戳
    return DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
  }
}

String getDateWithFormat(int timestamp, String? format) {
  // 将时间戳转换为 DateTime 对象
  DateTime dateTime = convertTimestamp(timestamp);
  if (isAvailable(format)) {
    return DateFormat(format).format(dateTime);
  }

  return DateFormat.yMd().add_jm().format(dateTime);
}

//todo：根据滑条获取字体大小
double getFontSize(double value) {
  var offset = 100 / 11.0;
  var fontSize = ReaderUtil.instance.fontSize;
  if (value <= offset) {
    fontSize = 16;
  } else if (value <= offset * 2) {
    fontSize = 17;
  } else if (value <= offset * 3) {
    fontSize = 18;
  } else if (value <= offset * 4) {
    fontSize = 19;
  } else if (value <= offset * 5) {
    fontSize = 21;
  } else if (value <= offset * 6) {
    fontSize = 23;
  } else if (value <= offset * 7) {
    fontSize = 25;
  } else if (value <= offset * 8) {
    fontSize = 28;
  } else if (value <= offset * 9) {
    fontSize = 30;
  } else if (value <= offset * 10) {
    fontSize = 33;
  } else {
    fontSize = 36;
  }

  return fontSize;
}

//todo：根据字体大小获取滑条值
double getSliderValue(double fontSize) {
  var value = 0.0;
  var offset = 100 / 11.0;
  if (fontSize == 16) {
    value = offset;
  } else if (fontSize == 17) {
    value = offset * 2;
  } else if (fontSize == 18) {
    value = offset * 3;
  } else if (fontSize == 19) {
    value = offset * 4;
  } else if (fontSize == 21) {
    value = offset * 5;
  } else if (fontSize == 23) {
    value = offset * 6;
  } else if (fontSize == 25) {
    value = offset * 7;
  } else if (fontSize == 28) {
    value = offset * 8;
  } else if (fontSize == 30) {
    value = offset * 9;
  } else if (fontSize == 33) {
    value = offset * 10;
  } else {
    value = offset * 11;
  }

  return value;
}

//todo：设置左右边距
double getHorizontalPadding(double value) {
  var horizontalPadding = ReaderUtil.instance.horizontalPadding;
  var offset = 100 / 7.0;
  if (value <= offset) {
    horizontalPadding = 14;
  } else if (value <= offset * 2) {
    horizontalPadding = 17;
  } else if (value <= offset * 3) {
    horizontalPadding = 20;
  } else if (value <= offset * 4) {
    horizontalPadding = 25;
  } else if (value <= offset * 5) {
    horizontalPadding = 30;
  } else if (value <= offset * 6) {
    horizontalPadding = 33;
  } else {
    horizontalPadding = 36;
  }

  return horizontalPadding;
}

//todo：根据左右边距获取滑条值
double getHorizontalSliderValue(double horizontalPadding) {
  var value = 0.0;
  var offset = 100 / 7.0;
  if (horizontalPadding == 14) {
    value = 0.0;
  } else if (horizontalPadding == 17) {
    value = offset;
  } else if (horizontalPadding == 20) {
    value = offset * 2;
  } else if (horizontalPadding == 25) {
    value = offset * 3;
  } else if (horizontalPadding == 30) {
    value = offset * 4;
  } else if (horizontalPadding == 33) {
    value = offset * 5;
  } else if (horizontalPadding == 36) {
    value = offset * 6;
  }

  return value;
}

//todo：设置行距
double getLineSpacing(double value) {
  var lineSpacing = ReaderUtil.instance.lineSpace;
  var offset = 100 / 7.0;
  if (value <= offset) {
    lineSpacing = 1.2;
  } else if (value <= offset * 2) {
    lineSpacing = 1.4;
  } else if (value <= offset * 3) {
    lineSpacing = 1.6;
  } else if (value <= offset * 4) {
    lineSpacing = 1.7;
  } else if (value <= offset * 5) {
    lineSpacing = 1.8;
  } else if (value <= offset * 6) {
    lineSpacing = 2.0;
  } else {
    lineSpacing = 2.2;
  }

  return lineSpacing;
}

//todo：根据行距获取滑条值
double getLineSpacingSliderValue(double lineSpacing) {
  var value = 0.0;
  var offset = 100 / 7.0;
  if (lineSpacing == 1.2) {
    value = 0.0;
  } else if (lineSpacing == 1.4) {
    value = offset;
  } else if (lineSpacing == 1.6) {
    value = offset * 2;
  } else if (lineSpacing == 1.7) {
    value = offset * 3;
  } else if (lineSpacing == 1.8) {
    value = offset * 4;
  } else if (lineSpacing == 2.0) {
    value = offset * 5;
  } else if (lineSpacing == 2.2) {
    value = offset * 6;
  }

  return value;
}

//Rate Us
Future<void> toAppStore(String str) async {
  String urlStr;
  // 根据平台选择不同的商店链接
  if (Platform.isIOS) {
    // iOS商店链接（确保使用你应用的实际ID）
    urlStr = str;
  } else {
    // Android商店链接（确保使用你应用的实际ID）
    urlStr = 'https://play.google.com/store/apps/details?id=YOUR_PACKAGE_NAME';
  }

  await ShareManager.instance.launchLinkUrl(urlStr);
}

//todo：获取时间差  timestamp 秒级时间戳
String getTimeDifference(int timestamp) {
  if (!isAvailable(timestamp) || timestamp == 0) {
    return 'just_now'.tr;
  }

  int inDays = getDaysDifference(timestamp).abs();

  if (inDays < 1) {
    return 'time1'.tr;
  } else if (inDays < 2) {
    return 'time2'.tr;
  } else if (inDays < 7) {
    return 'time3'.trParams({'param': '$inDays'});
  } else if (inDays < 14) {
    return 'time4'.tr;
  } else if (inDays < 30) {
    var week = (inDays / 7).floor();
    return 'time5'.trParams({'param': '$week'});
  } else if (inDays < 60) {
    return 'time6'.tr;
  } else if (inDays < 365) {
    var month = (inDays / 30).floor();
    return 'time7'.trParams({'param': '$month'});
  } else if (inDays < 730) {
    return 'time8'.tr;
  } else {
    var year = (inDays / 365).floor();
    return 'time9'.trParams({'param': '$year'});
  }
}

//todo：获取时间相差天数
int getDaysDifference(int timestamp) {
  DateTime dateTime = convertTimestamp(timestamp);
  DateTime now = DateTime.now();
  Duration difference = dateTime.difference(now);

  //difference.inDays 只会获取相差天数，不包含当天和最后一天时、分、秒，所以需要加1
  return difference.inDays;
}

//todo：获取时间相差天数
int getHoursDifference(int timestamp) {
  DateTime dateTime = convertTimestamp(timestamp);
  DateTime now = DateTime.now();
  Duration difference = dateTime.difference(now);

  //difference.inDays 只会获取相差天数，不包含当天和最后一天时、分、秒，所以需要加1
  return difference.inHours;
}

//todo：根据错误报告类型获取内容
String getReportTypeStr(ReportType type) {
  if (type == ReportType.bookDetail) {
    return 'book_detail';
  } else if (type == ReportType.bookRead) {
    return 'book_read';
  } else if (type == ReportType.comment) {
    return 'comment';
  } else if (type == ReportType.commentSecond) {
    return 'comment_second';
  }

  return 'other';
}

//TODO: 赋值内容到剪切板
void copyToClipboard(String? text) {
  if (isAvailable(text)) {
    Clipboard.setData(ClipboardData(text: text!));
  }
}

//TODO: 清除剪切板内容
void clearClipboard() {
  Clipboard.setData(const ClipboardData(text: ''));
}

//TODO: 获取剪切板内容
Future<String?> getClipboardContent() async {
  final ClipboardData? data = await Clipboard.getData(Clipboard.kTextPlain);
  return data?.text; //返回剪切板中的文本
}

//todo：比较版本号
///v1: 新版本号
///v2: 当前版本号
bool isVersionNeedUpdate(String onlineVersion, String localVersion) {
  try {
    int onlineVersionValue = int.parse(onlineVersion.replaceAll('.', ''));
    int localVersionValue = int.parse(localVersion.replaceAll('.', ''));

    return localVersionValue < onlineVersionValue;
  } catch (e) {
    logP('比较版本号异常：$e');
    return false;
  }
}

//todo: 根据当前页面做相应逻辑操作
Future<void> handleCurrentPage(BuildContext context, VoidCallback? callback) async {
  if (isPaymentReminderNeedShow) {
    if (!isPaymentReminderCurrentShow) {
      if (curRoutePage == RoutePage.accountPage || curRoutePage == RoutePage.premiumPage) {
        showAlter(PaymentReminderContent(onConfirm: (bool isContinue) async {
          isPaymentReminderCurrentShow = false;
          if (!isContinue) {
            isPaymentReminderNeedShow = false;
            Get.back();
          }
        }), onDismiss: () {
          isPaymentReminderCurrentShow = false;
        });

        isPaymentReminderCurrentShow = true;
      }
    }
  } else {
    isPaymentReminderCurrentShow = false;
    callback?.call();
  }
}

//todo: 根据商品编码获取订阅商品类型对应多语言标题
///1_week：1_week，1_month：1_month， 3_months：3_months， 6_months：6_months， 1_year：1_year
///1_week_renewing：billed_weekly， billed_monthly：billed_monthly， 3_months_renewing：billed_quarterly，6_months_renewing：billed_semi， 1_year_renewing：billed_yearly
///monthly_renewing_offer：monthly_renewing_offer
String getPremiumTitle(String? productCode) {
  if (isAvailable(productCode)) {
    if (productCode == '1_week' ||
        productCode == 'billed_weekly' ||
        productCode == 'weekly_renewing_offer') {
      return '1week'.tr;
    } else if (productCode == '1_month' ||
        productCode == 'billed_monthly' ||
        productCode == 'monthly_renewing_offer') {
      return '1 ${'month'.tr}';
    } else if (productCode == '3_months' || productCode == 'billed_quarterly') {
      return '3 ${'month'.tr}';
    } else if (productCode == '6_months' || productCode == 'billed_semi') {
      return '6 ${'month'.tr}';
    } else if (productCode == '1_year' ||
        productCode == 'billed_yearly' ||
        productCode == 'yearly_renewing_offer') {
      return '1year'.tr;
    }
  }

  return '--';
}

//todo: 根据商品编码获取订阅商品类型对应多语言描述
///renewing_offer系列: 这里的翻译就是固定 1st month special（billed monthly）
String getPremiumSubTitle(int? numberOfPeriods, String? productCode, {bool isBilled = true}) {
  if (isAvailable(productCode)) {
    String periodStr = '';
    if (isAvailable(numberOfPeriods)) {
      periodStr = '$numberOfPeriods' 'st'.tr;
    }
    if (productCode == 'weekly_renewing_offer') {
      if (isBilled) {
        return '$periodStr ${'month'.tr} ${'Special'.tr} ${'billed_weekly'.tr}';
      }
      return '$periodStr ${'month'.tr} ${'Special'.tr}';
    } else if (productCode == 'monthly_renewing_offer') {
      if (isBilled) {
        return '$periodStr ${'month'.tr} ${'Special'.tr} ${'billed_monthly'.tr}';
      }
      return '$periodStr ${'month'.tr} ${'Special'.tr}';
    } else if (productCode == 'yearly_renewing_offer') {
      if (isBilled) {
        return '$periodStr ${'month'.tr} ${'Special'.tr} ${'billed_yearly'.tr}';
      }
      return '$periodStr ${'month'.tr} ${'Special'.tr}';
    }
  }

  return productCode!.tr;
}

//todo: 根据商品价格计算平均每天价格
String getPremiumPricePerDay(String? price, String? productCode) {
  if (isAvailable(price) && isAvailable(productCode)) {
    double priceValue = double.parse(price!);
    if (productCode == '1_week' ||
        productCode == 'billed_weekly' ||
        productCode == 'weekly_renewing_offer') {
      return (priceValue / 7).toStringAsFixed(2);
    } else if (productCode == '1_month' ||
        productCode == 'billed_monthly' ||
        productCode == 'monthly_renewing_offer') {
      return (priceValue / 30).toStringAsFixed(2);
    } else if (productCode == '3_months' || productCode == 'billed_quarterly') {
      return (priceValue / 90).toStringAsFixed(2);
    } else if (productCode == '6_months' || productCode == 'billed_semi') {
      return (priceValue / 180).toStringAsFixed(2);
    } else if (productCode == '1_year' ||
        productCode == 'billed_yearly' ||
        productCode == 'yearly_renewing_offer') {
      return (priceValue / 365).toStringAsFixed(2);
    }
  }

  return '0.0';
}

//todo: 获取卡片类型
String getPremiumType(String? productCode) {
  if (isAvailable(productCode)) {
    if (productCode == '1_week' ||
        productCode == 'billed_weekly' ||
        productCode == 'weekly_renewing_offer') {
      return 'billed_weekly'.tr;
    } else if (productCode == '1_month' ||
        productCode == 'billed_monthly' ||
        productCode == 'monthly_renewing_offer') {
      return 'billed_monthly'.tr;
    } else if (productCode == '3_months' || productCode == 'billed_quarterly') {
      return 'billed_quarterly'.tr;
    } else if (productCode == '6_months' || productCode == 'billed_semi') {
      return 'billed_semi'.tr;
    } else if (productCode == '1_year' ||
        productCode == 'billed_yearly' ||
        productCode == 'yearly_renewing_offer') {
      return 'billed_yearly'.tr;
    }
  }

  return '';
}

//todo: 根据会员时间，获取月份多语言
String getPremiumExpireTimeStr(int? subExpireTime) {
  if (isAvailable(subExpireTime)) {
    var monthStr = getDateWithFormat(subExpireTime!, 'MM');
    switch (int.parse(monthStr)) {
      case 1:
        monthStr = 'January'.tr;
        break;
      case 2:
        monthStr = 'February'.tr;
        break;
      case 3:
        monthStr = 'March'.tr;
        break;
      case 4:
        monthStr = 'April'.tr;
        break;
      case 5:
        monthStr = 'May'.tr;
        break;
      case 6:
        monthStr = 'June'.tr;
        break;
      case 7:
        monthStr = 'July'.tr;
        break;
      case 8:
        monthStr = 'August'.tr;
        break;
      case 9:
        monthStr = 'September'.tr;
        break;
      case 10:
        monthStr = 'October'.tr;
        break;
      case 11:
        monthStr = 'November'.tr;
        break;
      case 12:
        monthStr = 'December'.tr;
        break;
    }

    var subExpireTimeStr = getDateWithFormat(subExpireTime, 'dd, yyyy');

    return '$monthStr $subExpireTimeStr';
  }

  return '--';
}

//todo: 图片地址重定向压缩
String getRedirectImageUrl(String imageUrl, {int w = 540, int h = 720}) {
  List originDomains = [
    'https://hm-novel.s3.amazonaws.com',
    'https://hm-novel.s3.us-east-1.amazonaws.com'
  ];
  String newDomain = 'https://media.easynovelhub.com';
  for (var domain in originDomains) {
    if (imageUrl.startsWith(domain)) {
      String newImageUrl = imageUrl.replaceFirst(domain, newDomain);
      newImageUrl = '$newImageUrl?w=$w&h=$h';

      return newImageUrl;
    }
  }

  return '$imageUrl?w=$w&h=$h';
}

Timer timerStart(int duration, int seconds, void Function(String dataStr, bool isEnd) callback,
    {bool refreshRightNow = false}) {
  if (refreshRightNow) {
    callback(getActivityDataStr(seconds), seconds == 0);
  }
  return Timer.periodic(Duration(seconds: duration), (timer) {
    if (seconds > 0) {
      seconds--;
    } else {
      timer.cancel();
    }
    callback(getActivityDataStr(seconds), seconds == 0);
  });
}

Timer timerStartDateTime(
    int duration, int seconds, void Function(String dataStr, bool isEnd) callback,
    {bool refreshRightNow = false}) {
  // 计算结束时间
  final DateTime endDateTime = DateTime.now().add(Duration(seconds: seconds));

  if (refreshRightNow) {
    int remainingSeconds = _getRemainingSeconds(endDateTime);
    callback(getActivityDataStr(remainingSeconds), remainingSeconds <= 0);
  }

  return Timer.periodic(Duration(seconds: duration), (timer) {
    int remainingSeconds = _getRemainingSeconds(endDateTime);

    if (remainingSeconds > 0) {
      callback(getActivityDataStr(remainingSeconds), false);
    } else {
      timer.cancel();
      callback(getActivityDataStr(0), true);
    }
  });
}

int _getRemainingSeconds(DateTime endDateTime) {
  final now = DateTime.now();
  final difference = endDateTime.difference(now);
  return difference.inSeconds > 0 ? difference.inSeconds : 0;
}

String getActivityDataStr(int seconds) {
  int day = seconds ~/ 86400;
  int hour = (seconds % 86400) ~/ 3600;
  int minute = (seconds % 3600) ~/ 60;
  int second = seconds % 60;

  if (day <= 0) {
    return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}:${second.toString().padLeft(2, '0')}';
  } else if (day == 1) {
    return '${'day_'.trParams({
          'param': '$day'
        })} ${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}:${second.toString().padLeft(2, '0')}';
  } else {
    return '${'days'.trParams({
          'param': '$day'
        })} ${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}:${second.toString().padLeft(2, '0')}';
  }
}

///获取用户头像
String getUserAvatar(String? avatarStr, String? sex) {
  if (isAvailable(avatarStr) &&
      (avatarStr!.startsWith('http://') || avatarStr.startsWith('https://'))) {
    return avatarStr;
  } else {
    return getSexAvatarStr(sex);
  }
}

String getSexAvatarStr(String? sex) {
  String avatar = 'assets/images/profile/icon_profile_other.png';
  if (sex == '1') {
    //性别,1：男；2：女；0：未设置
    avatar = 'assets/images/profile/icon_profile_male.png';
  } else if (sex == '2') {
    avatar = 'assets/images/profile/icon_profile_female.png';
  }

  return avatar;
}
