import 'package:flutter/material.dart';
import 'package:UrNovel/Util/tools.dart';

class DeviceScreenUtil {
  static DeviceScreenUtil instance = DeviceScreenUtil();

  static double? _topSafeHeight;
  static double? _bottomSafeHeight;
  static double? _screenWidth;
  static double? _screenHeight;

  void setContext(BuildContext context) {
    if (!isAvailable(_topSafeHeight) || _topSafeHeight! <= 0){
      final mediaQuery = MediaQuery.of(context);
      _topSafeHeight = mediaQuery.padding.top;
      _bottomSafeHeight = mediaQuery.padding.bottom;
      _screenWidth = mediaQuery.size.width;
      _screenHeight = mediaQuery.size.height;
    }
  }

  double get topSafeHeight => _topSafeHeight?? 0.0;
  double get bottomSafeHeight => _bottomSafeHeight?? 0.0;
  double get width => _screenWidth?? 0.0;
  double get height => _screenHeight?? 0.0;
}