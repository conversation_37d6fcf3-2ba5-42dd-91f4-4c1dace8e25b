//ToDo：Read首页tabList列表类型
enum ReadHomeListType { forYou, discover, female, male }

//ToDo：Library书架tabList列表类型
enum LibraryListType {
  myList,
  liked,
  history,
  offline,
}

//todo:排行榜类型
enum RankingType {
  trending, //热门榜
  newRelease, //最新发布
  hotSearches, //热搜榜
  favorites //收藏榜
}

//ToDo：设备系统类型
enum OSType { android, iOS, linux, macOS, windows, other }

//ToDo：Profile cell类型
enum ProfileCellType { account, premium, none }

//ToDo：Premium 类型
enum PremiumType { expiringSoon, hasExpired, buyNow }

//ToDo：阅读底部菜单类型
enum NovelBottomBarType { chapters, add, like, luminance, character, none }

//todo:首页ForYou栏目类型
enum HomeSectionType {
  sixCardsCard, //6张卡片
  singleLineVerticalSliding, //单行竖版滑动
  singleLineVerticalSlidingCard, //单行竖版滑动小卡片
  threeLinesCard, //三行
  singleLineHorizontalSliding, //单行横版滑动
  unknown, //未知类型
}

//todo:二级页面类型
enum SecondaryPageType {
  formHomeReadSection, //首页阅读
  fromGenres, //分类
  fromTags, //标签
  fromOther, //其他
}

//todo:阅读页面跳转类型
enum PageJumpType { stay, firstPage, lastPage }

//todo:跳转阅读页详情页类型
enum BookDetailJumpType {
  search,
  offline,
  voiceListening,
  chat,
  push,
  localAdPage,
  liveActivity,
  other
}

//todo:阅读模式
enum ReadingMode {
  pageFlip, //单页翻页
  verticalScroll, //竖向滚动
  horizontalScroll //横向滚动
}

//todo:字体类型
enum FontType { serifed, sansSerifed }

//todo:支付网关(1:GOOGLE PAY; 2:APPLE PAY)
enum PaymentGateway { googlePay, applePay }

//todo:商品类型
enum GoodsType {
  visitorLogin, //游客登录 1101
  googleLogin, //谷歌登录 1102
  appleLogin, //苹果登录 1103
  subscriptionMembership, //订阅会员 1211
  purchaseMembership, //购买会员 1212
  purchaseCoins, //金币购买 1251
  taskReward, //任务奖励 1252
  signInReward, //签到奖励 1253
  unlockChapter, //解锁章节 1254
  purchaseDiamonds, //钻石购买 1249
}

//todo:订单类型
enum OrderType {
  novelReadDownLoad, //1:小说阅读下载
  personalCenter, //2:个人中心、首页创建订单
  novelDetail, //3:小说详情页创建订单
  novelReadTrialUnlock, //4:小说阅读试读结束解锁
  novelChat //5:小说聊天
}

//todo:订阅验签状态
enum SubscribeVerifyStatus {
  purchased, //已购买
  canceled, //已取消
  pending, //待定
  expired, //过期
  exception, //异常
  purchasedAndNoTransaction, //已购买且无交易
  repeatVerify, //重复验签
  mismatch //订单商品和验签商品不匹配
}

//todo:举报类型
enum ReportType {
  bookDetail, //书籍详情错误报告
  bookRead, //书籍阅读错误报告
  comment, //评论错误报告
  commentSecond, //评论二级错误报告
  chat, //聊天错误报告
}

//todo:分享类型
enum ShareType { facebook, instagram, whatsApp, tiktok, copyLink }

//todo:阅读记录上报活动类型
enum ReadReportActivityType {
  exitReading, //退出阅读
  purchase, //充值
  subscribe, //订阅
  finishNovel, //小说阅读完结
  inactive, //应用隐藏，被覆盖等
  reading, //阅读
  home, //首页
  activityTimeout, //阅读活跃超时
  enterBackground //进入后台
}

//todo:阅读记录上报阅读方式类型
enum ReadReportReadingType {
  reading, //阅读
  listen //听书
}

//todo:设置目录类型
enum SettingSectionType {
  notifications, //通知
  autoUnlock, //自动解锁
  other, //其他
}

//todo: 阅读章节查询类型
enum ChapterQueryType {
  previous, //上一章
  current, //当前章节
  next, //下一章
  none, //不做分页计算
}

//todo:二级页面类型
enum ChatPageType {
  chatHistory, //列表
  chatForYou, //查找
  chatCharge, //其他
}

enum ChatItemCharacterType { SYSTEM, USER, ASSISTANT, TIPS, LOADING }

enum ChatItemTextType { ITALIC, NORMAL }

//todo: 下载返回类型
enum DownLoadResponseType { json, stream, plain, bytes }

//todo: 推送通知弹窗来源
enum NotificationAlterSource {
  firstPopReadPage, //首次打开阅读页面, 然后关闭
  addToLibrary, //添加到书架
  firstClickCollection, //首次点击收藏
  openAppAfter24Hours, //24小时后打开app
  afterShare, //分享小说后(不用管是不是首次)
  firstComment, //首次评论
  deniedAfter7days, //每7天请求一次：如果用户在首次打开应用时拒绝了权限请求，那么在用户首次打开应用的每隔7天，再次弹出权限请求对话框
  afterCharge, //充值后
  notFirstOpenAndNoReading, //非第一打开，并切未进行过阅读
  none, //其他情况
}

//todo: 路由类型
enum MyRouteType { push, pop, replace, remove, startUserGesture }

//todo:路由页面类型（根据业务场景添加）
enum RoutePage {
  accountPage, //金币购买页
  premiumPage, //会员购买页
  novelReadPage, //小说阅读页
  bookDetailPage, //书籍详情页
  unKnown, //未知页面
}

enum SpineAnimationType { like, love, bouquet, ring, balloon, rocket, none }

//todo: 阅读插图占用页面空间类型
enum ReadPictureFitType { onlyFit, hasSpace, noSpace, none }

//todo: 阅读插图打点类型
enum IllustrationEventType {
  VIEW, //浏览
  LIKE, //点赞
  UNLIKE, //取消点赞
}

//todo: push类型
enum PushType {
  receive, //收到推送
  click, //点击推送
  read, //阅读推送
  pay, //推送付费
}
