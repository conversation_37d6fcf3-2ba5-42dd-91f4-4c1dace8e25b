import 'dart:convert';

import 'package:UrNovel/Util/tools.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../Launch&Login/Model/language_model.dart';
import '../../Launch&Login/Model/login_model.dart';
import '../../Launch&Login/Model/promotion_content_model.dart';
import '../../Launch&Login/Model/user_model.dart';
import '../Common/Extension/extension.dart';
import '../ReaderUtils/novel_preferences_settings.dart';
import '../ReaderUtils/readerUtil.dart';
import '../enum.dart';
import '../logUtil.dart';

/// 键值对 key
class SPKey {
  static const String isFirstOpen = 'isFirstOpen';
  static const String isColdStart = 'isColdStart';
  static const String launchNovelAd = 'launchNovelAd';
  static const String isNovelAdClick = 'isNovelAdClick';
  static const String isEnableNotifications = 'isEnableNotifications';
  static const String isPrivacyAgree = 'isPrivacyAgree';
  static const String isLogin = 'isLogin';
  static const String loginInfo = 'loginInfo';
  static const String userInfo = 'userInfo';
  static const String contentLanguage = 'contentLanguage';
  static const String isFirstGuide = 'isFirstGuide';
  static const String searchHistory = 'searchHistory';
  static const String novelSettings = 'novelSettings';
  static const String novelReadBookChapter = 'novelReadBookChapter';
  static const String reportNovelReadBookChapter = 'ReportNovelReadBookChapter';
  static const String orderSignData = 'orderSignData';
  static const String isChapterAutoUnlock = 'isChapterAutoUnlock';
  static const String notificationAlterShowTime = 'notificationAlterShowTime';
  static const String notificationAlterShowTimes = 'notificationAlterShowTimes';
  static const String notificationFirstDeniedTime =
      'notificationFirstDeniedTime';
  static const String trackingAlterShowTime = 'trackingAlterShowTime';
  static const String trackingAlterShowTimes = 'trackingAlterShowTimes';
  static const String readingReminderAlterShowTime =
      'readingReminderAlterShowTime';
  static const String unlockReminderAlterGroupShowTime =
      'unlockReminderAlterGroupShowTime';

  //阅读章节解锁挽留组合弹窗次数，默认两次常规收费小说弹窗，一次免费小说弹窗
  static const String unlockReminderAlterGroupShowTimes =
      'unlockReminderAlterGroupShowTimes';
  static const String unlockGoodsAlterShowTime = 'unlockGoodsAlterShowTime';
  static const String purchaseAlterShowTime = 'purchaseAlterShowTime';
  static const String premiumAlterShowTime = 'premiumAlterShowTime';

  //审核状态
  static const String isVerifyBuildCode = 'isVerifyBuildCode';
  static const String chapterVoiceSpeakerId = 'chapterVoiceSpeakerId';
  static const String hasShownAlter = 'hasShownSignInFirstAlert';
  static const String loginChannel = 'loginChannel';
  static const String lastReadType = 'lastReadType';

  //购买商品存储
  static const String purchaseGoods = 'purchaseGoods';
}

//todo:是否第一次打开应用
/// 键值对存储
class SpUtil {
  //todo:是否第一次打开
  static Future<bool> isFirstOpen() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getBool(SPKey.isFirstOpen) ?? true;
  }

  static Future<void> spSetAppOpenState() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setBool(SPKey.isFirstOpen, false);
  }

  //todo:获取是否是冷启动
  static Future<bool> isColdStart() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getBool(SPKey.isColdStart) ?? true;
  }

  static Future<void> spSetIsColdStart(bool value) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setBool(SPKey.isColdStart, value);
  }

  //todo:获取是否启动小说推广
  static Future<PromotionContentModel?> spGetLaunchNovelAd() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var jsonStr = prefs.getString(SPKey.launchNovelAd);
    if (isAvailable(jsonStr)) {
      return PromotionContentModel.fromJson(jsonDecode(jsonStr!));
    }

    return null;
  }

  static Future<void> spSetLaunchNovelAd(Map<String, dynamic> value) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setString(SPKey.launchNovelAd, jsonEncode(value));
  }

  //todo:获取是否点击了广告小说
  static Future<bool> isNovelAdClick() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getBool(SPKey.isNovelAdClick) ?? false;
  }

  static Future<void> spSetNovelAdClick(bool value) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setBool(SPKey.isNovelAdClick, value);
  }

  //todo:登录装态
  static Future<void> spSetLoginState(bool value) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setBool(SPKey.isLogin, value);
  }

  static Future<bool> isLogin() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getBool(SPKey.isLogin) ?? false;
  }

  //todo:登录信息
  static Future<void> spSetLoginInfo(Map<String, dynamic> value) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    if (await prefs.setString(SPKey.loginInfo, jsonEncode(value))) {
      logP("登录信息存储成功");
    } else {
      logP("登录信息存储失败");
    }

    if (isAvailable(value)) {
      spSetLoginState(true);
    }
  }

  static Future<LoginInfoModel?> spGetLoginInfo() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? str = prefs.getString(SPKey.loginInfo);
    if (isAvailable(str)) {
      LoginModel loginModel = LoginModel.fromJson(jsonDecode(str!));
      return loginModel.loginInfo;
    }

    return null;
  }

  //todo:用户信息
  static Future<void> spSetUserInfo(UserModel model) async {
    Map<String, dynamic> value = model.toJson();
    SharedPreferences prefs = await SharedPreferences.getInstance();

    if (await prefs.setString(SPKey.userInfo, jsonEncode(value))) {
      logP("用户信息存储成功");
    } else {
      logP("用户信息存储失败");
    }
  }

  static Future<UserInfoModel?> spGetUserInfo() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? str = prefs.getString(SPKey.userInfo);
    if (isAvailable(str)) {
      UserModel userModel = UserModel.fromJson(jsonDecode(str!));

      return userModel.userInfo;
    }

    return null;
  }

  static Future clearLoginInfo() async {
    await spDelete(SPKey.isLogin);
    await spDelete(SPKey.loginInfo);
    await spDelete(SPKey.userInfo);
  }

  //todo:语言设置
  static Future<void> spSetContentLanguageItem(LanguageItem item) async {
    var jsonStr = jsonEncode(item.toJson());
    SharedPreferences prefs = await SharedPreferences.getInstance();
    bool result = await prefs.setString(SPKey.contentLanguage, jsonStr);
    if (result) {
      logP("语言设置存储成功：${item.code} - ${item.desc}");
    } else {
      logP("语言设置存储失败");
    }
  }

  static Future<LanguageItem?> spGetContentLanguageItem() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? jsonStr = prefs.getString(SPKey.contentLanguage);
    if (isAvailable(jsonStr)) {
      LanguageItem item = LanguageItem.fromJson(jsonDecode(jsonStr!));
      if (item.code == 'zh') {
        return null;
      }

      return item;
    }

    return null;
  }

  //todo:小说阅读页面 第一次引导状态存储
  static Future<bool> isFirstGuide() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getBool(SPKey.isFirstGuide) ?? true;
  }

  static Future<void> spSetFirstGuideState() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setBool(SPKey.isFirstGuide, !await isFirstGuide());
  }

  //todo:搜索历史记录存储
  static Future<void> spSaveSearchHistory(String value) async {
    if (isAvailable(value)) {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      List<String> list = await spGetSearchHistory() ?? [];
      if (list.contains(value)) {
        list.remove(value);
      }
      list.insert(0, value);
      if (list.length > 10) {
        list.removeLast();
      }
      prefs.setStringList(SPKey.searchHistory, list);
    }
  }

  static Future<List<String>?> spGetSearchHistory() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getStringList(SPKey.searchHistory);
  }

  //todo:小说阅读页面偏好设置
  ///存储偏好设置
  static Future<void> spSetNovelPreferences(
      NovelPreferencesSettings? model) async {
    if (!isAvailable(model)) {
      return;
    }
    SharedPreferences prefs = await SharedPreferences.getInstance();
    // 转换为 JSON
    Map<String, dynamic> json = model!.toJson();
    String jsonString = jsonEncode(json);
    prefs.setString(SPKey.novelSettings, jsonString);
  }

  ///获取偏好设置
  static Future<NovelPreferencesSettings>
      spGetNovelPreferencesSettings() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    Object? obj = prefs.get(SPKey.novelSettings);
    if (obj is String) {
      return NovelPreferencesSettings.fromJson(jsonDecode(obj));
    } else {
      return spGetNormalPreferencesSettings();
    }
  }

  ///获取默认偏好设置
  static NovelPreferencesSettings spGetNormalPreferencesSettings() {
    return NovelPreferencesSettings(
      themeColor: ReaderUtil.instance.themeColor,
      titleColor: ReaderUtil.instance.titleColor,
      fontSize: ReaderUtil.instance.fontSize,
      lineSpace: ReaderUtil.instance.lineSpace,
      horizontalPadding: ReaderUtil.instance.horizontalPadding,
      fontType: ReaderUtil.instance.fontType,
      readingMode: ReaderUtil.instance.readingMode,
    );
  }

  //todo:数据上报数据存储
  ///章节阅读记录存储
  static Future<bool> spSaveNovelReadBookChapter(List<dynamic> list) async {
    if (isAvailable(list)) {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String jsonString = jsonEncode(list);
      return prefs.setString(SPKey.reportNovelReadBookChapter, jsonString);
    }

    return false;
  }

  ///章节阅读记录获取
  static Future<List<dynamic>?> spGetNovelReadBookChapter() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    Object? obj = prefs.get(SPKey.reportNovelReadBookChapter);
    if (obj is String) {
      return jsonDecode(obj);
    }

    return null;
  }

  //todo:设置章节自动解锁 1：开启 0：关闭 -1：未设置
  static Future<void> spSetChapterAutoUnlock(int value) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setInt(SPKey.isChapterAutoUnlock, value);
  }

  static Future<int> spGetChapterAutoUnlock() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getInt(SPKey.isChapterAutoUnlock) ?? -1;
  }

  //todo:设置通知授权弹窗显示时间
  static Future<void> spSetNotificationAlterShowTime(int value) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setInt(SPKey.notificationAlterShowTime, value);
  }

  static Future<bool> spGetIsNotificationAlterShow() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    int value = prefs.getInt(SPKey.notificationAlterShowTime) ?? 0;
    int hours = getHoursDifference(value);
    if (0 < value && hours <= 24) {
      return false;
    }

    return true;
  }

  //todo:设置通知授权弹窗显示次数
  static Future<void> spSetNotificationAlterShowTimes(
      NotificationAlterSource source) async {
    Map<String, dynamic> map = await spGetNotificationAlterShowTimes() ?? {};
    int count = map[source.name] ?? 0;
    map[source.name] = count += 1;
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString(SPKey.notificationAlterShowTimes, jsonEncode(map));
  }

  static Future<Map<String, dynamic>?> spGetNotificationAlterShowTimes() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? timesStr = prefs.getString(SPKey.notificationAlterShowTimes);

      if (isAvailable(timesStr)) {
        return jsonDecode(timesStr!);
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  //todo:设置通知授权第一次拒绝时间
  static Future<void> spSetNotificationFirstDeniedTime(int value) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setInt(SPKey.notificationFirstDeniedTime, value);
  }

  static Future<int> spGetNotificationFirstDeniedTime() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    int value = prefs.getInt(SPKey.notificationFirstDeniedTime) ?? 0;
    int days = getDaysDifference(value);

    return days;
  }

  //todo:设置追踪授权弹窗显示时间&次数
  static Future<void> spSetTrackingAlterShowTime(int value) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setInt(SPKey.trackingAlterShowTime, value);

    /// 记录弹窗显示次数
    int times = prefs.getInt(SPKey.trackingAlterShowTimes) ?? 0;
    await prefs.setInt(SPKey.trackingAlterShowTimes, times + 1);
  }

  static Future<bool> spGetIsTrackingAlterShow() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    int times = prefs.getInt(SPKey.trackingAlterShowTimes) ?? 0;
    int value = prefs.getInt(SPKey.trackingAlterShowTime) ?? 0;
    int hours = getHoursDifference(value);
    if (0 < value && hours <= 24 || 3 <= times) {
      return false;
    }

    return true;
  }

  //todo:设置阅读提醒弹窗显示时间
  static Future<void> spSetReadingReminderAlterShowTime(int value) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setInt(SPKey.readingReminderAlterShowTime, value);
  }

  static Future<bool> spGetIsReadingReminderAlterShow() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    int value = prefs.getInt(SPKey.readingReminderAlterShowTime) ?? 0;
    int hours = getHoursDifference(value);
    if (0 < value && hours <= 24) {
      return false;
    }

    return true;
  }

  //todo:设置阅读章节解锁挽留组合弹窗时间
  static Future<void> spSetUnlockReminderAlterGroupShowTime(int value) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setInt(SPKey.unlockReminderAlterGroupShowTime, value);
  }

  //todo:设置阅读章节解锁挽留组合弹窗次数
  static Future<void> spSetUnlockReminderAlterGroupShowTimes() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    int times = prefs.getInt(SPKey.unlockReminderAlterGroupShowTimes) ?? 0;
    if (times < 3) {
      times += 1;
    }

    if (times == 3) {
      times = 0;
    }
    await prefs.setInt(SPKey.unlockReminderAlterGroupShowTimes, times);
  }

  ///是否展示解锁免费弹窗（第三次弹，从0开始就是展示次数为2的时候弹），否则弹三个收费书籍弹窗
  ///返回1：展示收费弹窗，0：展示免费弹窗，-1：不展示弹窗
  static Future<int> spGetIsUnlockReminderAlterGroupShow() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    int times = prefs.getInt(SPKey.unlockReminderAlterGroupShowTimes) ?? 0;
    int value = prefs.getInt(SPKey.unlockReminderAlterGroupShowTime) ?? 0;
    int hours = getHoursDifference(value);
    if (times == 2) {
      if (0 < value && hours <= 24) {
        return -1;
      }

      return 0;
    } else {
      if (0 < value && hours <= 24) {
        return -1;
      }

      return 1;
    }
  }

  //todo:设置阅读章节解锁商品弹窗时间
  static Future<void> spSetUnlockGoodsAlterShowTime(int value) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setInt(SPKey.unlockGoodsAlterShowTime, value);
  }

  static Future<bool> spGetIsUnlockGoodsAlterShow() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    int value = prefs.getInt(SPKey.unlockGoodsAlterShowTime) ?? 0;
    int hours = getHoursDifference(value);
    if (0 < value && hours <= 24) {
      return false;
    }

    return true;
  }

  //todo:设置充值挽留弹窗时间
  static Future<void> spSetPurchaseReminderAlterShowTime(int value) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setInt(SPKey.purchaseAlterShowTime, value);
  }

  static Future<bool> spGetIsPurchaseReminderAlterShow() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    int value = prefs.getInt(SPKey.purchaseAlterShowTime) ?? 0;
    int hours = getHoursDifference(value);
    if (0 < value && hours <= 24) {
      return false;
    }

    return true;
  }

  //todo:设置会员购买挽留弹窗时间
  static Future<void> spSetPremiumReminderAlterShowTime(int value) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setInt(SPKey.premiumAlterShowTime, value);
  }

  static Future<bool> spGetIsPremiumReminderAlterShow() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    int value = prefs.getInt(SPKey.premiumAlterShowTime) ?? 0;
    int hours = getHoursDifference(value);
    if (0 < value && hours <= 24) {
      return false;
    }

    return true;
  }

  //todo:设置审核状态
  static Future<void> spSetVerifyStatus(bool value) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setBool(SPKey.isVerifyBuildCode, value);
  }

  static Future<bool> spGetVerifyStatus() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getBool(SPKey.isVerifyBuildCode) ?? true;
  }

  //todo:听书语音播者id
  static Future<void> spSetVoiceSpeakerId(int value) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setInt(SPKey.chapterVoiceSpeakerId, value);
  }

  static Future<int> spGetVoiceSpeakerId() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getInt(SPKey.chapterVoiceSpeakerId) ?? 0;
  }

  //todo:设置是否已显示过 Alter
  static Future<void> spSetHasShownAlter() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setBool(SPKey.hasShownAlter, true);
  }

  static Future<bool> spGetHasShownAlter() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getBool(SPKey.hasShownAlter) ?? false;
  }

  //todo:设置当前登录方式
  static Future<void> spSetLoginChannel(int channel) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setInt(SPKey.loginChannel, channel);
  }

  static Future<int?> spGetLoginChannel() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getInt(SPKey.loginChannel);
  }

  //todo:设置生命周期最后一刻当前阅读类型
  static Future<void> spSetLastReadType(ReadReportReadingType type) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString(SPKey.lastReadType, type.name);
  }

  static Future<ReadReportReadingType> spGetLastReadType() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? type = prefs.getString(SPKey.lastReadType);

    return ReadReportReadingType.values
        .firstWhere((e) => e.name == (type ?? 'reading'));
  }

  //todo:购买商品记录存储
  static Future<void> spSavePurchaseGoods(List? list) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    if (isAvailable(list)) {
      String jsonString = jsonEncode(list);
      await prefs.setString(SPKey.purchaseGoods, jsonString);
    }
  }

  static Future<List?> spGetPurchaseRecord() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? jsonString = prefs.getString(SPKey.purchaseGoods);
    if (isAvailable(jsonString)) {
      return jsonDecode(jsonString!);
    }

    return null;
  }

  //todo:删除数据
  static Future<bool> spDelete(String key) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return await prefs.remove(key);
  }
}
