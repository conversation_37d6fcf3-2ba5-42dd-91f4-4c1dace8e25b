import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:freshchat_sdk/freshchat_sdk.dart';
import 'package:get/get.dart';
import 'package:get/get_utils/get_utils.dart';
import 'package:get/state_manager.dart';

import '../../MainPage/Signin/View/signin_popup_award_widget.dart';
import '../LocalNotificationManager/local_notification_manager.dart';
import '../SheetAndAlter/alter.dart';
import '../logUtil.dart';

class DebugMgr {
  static final DebugMgr instance = DebugMgr._internal();
  DebugMgr._internal();

  OverlayEntry? _overlayEntry;

  void showTestBtn(BuildContext context) {
    if (!kDebugMode) return;
    // 在页面初始化时添加悬浮按钮
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _overlayEntry = OverlayEntry(builder: (context) {
        return Positioned(
          top: MediaQuery.of(context).padding.top, // 与顶部对齐
          left: 38, // 左边距
          child: TextButton(
            onPressed: () {
              logD("Test按钮被点击了！");
              // Get.toNamed('/chatNavBarPage', arguments: {}); //, arguments: {}
              // NativeBridge.instance.callNativeMethod();
              // Get.to(() => ChatNavBarPage(arguments: {}));
              // Get.to(() => ListeningBookPage(arguments: {"autoPlay": true, "novelId": 1}));

              // LocalNotificationManager.showNotification(
              //     title: "check_in".tr,
              //     body: "time_to_checkin".tr,
              //     payload: "signin",
              //     delayTime: 5);

              // Test.instance.test111();

              // showAlter(
              //     SigninPopupAwardWidget(arguments: {
              //       'coins': 10,
              //       'days': 7,
              //     }),
              //     backgroundColor: Color.fromRGBO(0, 0, 0, 0),
              //     barrierDismissible: true);

              // Get.dialog(
              //   SigninPopupAwardWidget(arguments: {
              //     'coins': 10,
              //     'days': 7,
              //   }),
              //   barrierColor: const Color.fromRGBO(0, 0, 0, 0.8), // 设置全屏背景的透明度
              //   barrierDismissible: true,
              // );

              Freshchat.showFAQ();
            },
            child: Text(
              "Test",
              style: TextStyle(
                color: Colors.blue,
                fontSize: 16,
              ),
            ),
          ),
        );
      });
      Overlay.of(context).insert(_overlayEntry!);
    });
  }

  void removeTestBtn() {
    if (_overlayEntry != null) {
      _overlayEntry?.remove();
      _overlayEntry = null;
    }
  }
}
