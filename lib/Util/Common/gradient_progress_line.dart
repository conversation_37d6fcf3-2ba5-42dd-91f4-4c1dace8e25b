import 'package:flutter/cupertino.dart';
import '../Extensions/colorUtil.dart';

class GradientProgressLine extends CustomPainter {
  final double progress;

  GradientProgressLine(this.progress);

  @override
  void paint(Canvas canvas, Size size) {
    // 定义背景颜色
    Paint backgroundPaint = Paint()
      ..color = HexColor('#EBEBEB')
      ..style = PaintingStyle.fill;

    // 绘制背景
    Rect backgroundRect = Rect.fromLTWH(0.0, 0.0, size.width, size.height);
    canvas.drawRect(backgroundRect, backgroundPaint);

    // 定义渐变颜色
    final gradient = LinearGradient(
      colors: [HexColor('#0087FB'), HexColor('#0099F8')],
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
    );

    // 绘制渐变进度条
    Rect foregroundRect =
    Rect.fromLTWH(0.0, 0.0, size.width * progress, size.height);
    canvas.drawRect(foregroundRect,
        Paint()..shader = gradient.createShader(foregroundRect));
  }

  @override
  bool shouldRepaint(GradientProgressLine oldDelegate) {
    return oldDelegate.progress != progress;
  }
}
