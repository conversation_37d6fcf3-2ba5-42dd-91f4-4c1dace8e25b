import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:UrNovel/Util/DataReportManager/event_report_manager.dart';

import '../../Launch&Login/Model/user_model.dart';
import '../../Launch&Login/ViewModel/ViewModel.dart';
import '../../MainPage/BookInfo/Model/BookDownLoad/download_book_detail_info_model.dart';
import '../../MainPage/BookInfo/ViewModel/ViewModel.dart';
import '../../MainPage/NovelRead/Model/batchBuy_chapter_model.dart';
import '../../MainPage/NovelRead/Model/book_detailInfo_model.dart';
import '../../MainPage/NovelRead/ViewModel/ViewModel.dart';
import '../../MainPage/Profile/Model/goods_info_model.dart';
import '../../MainPage/Profile/ViewModel/ViewModel.dart';
import '../DBManager/db_manager.dart';
import '../DataReportManager/event_name_config.dart';
import '../PaymentManager/payment_manager.dart';
import '../SharedPreferences/shared_preferences.dart';
import '../SheetAndAlter/bottom_sheet.dart';
import '../SheetAndAlter/toast.dart';
import '../enum.dart';
import '../tools.dart';

class NovelDownload {
  static final NovelDownload instance = NovelDownload._internal();

  NovelDownload._internal();

  late BuildContext _context;
  late Function(bool isLoading)? _onLoadingCallBack;
  late VoidCallback? _onRecharged;
  late Function(BookDetailInfoResultModel? detailInfoModel)? _onFinished;
  late BookDetailInfoResultModel? _bookDetailInfoModel;
  late UserInfoModel? _userInfoModel;

  //todo:获取用户信息
  Future<void> getLocaleUserInfo(bool isCharged) async {
    _userInfoModel = await SpUtil.spGetUserInfo();
  }

  //todo:更新用户信息
  Future<void> updateUserInfo() async {
    _userInfoModel = await LoginViewModel.getUserInfo();
  }

  // TODO: 下载书籍
  Future<void> onNovelDownload(
      BuildContext context, BookDetailInfoResultModel? detailInfoModel,
      {Function(bool isLoading)? onLoadingCallBack,
      VoidCallback? onRecharged,
      Function(BookDetailInfoResultModel? detailInfoModel)? onFinished}) async {
    _context = context;
    _bookDetailInfoModel = detailInfoModel;
    _onLoadingCallBack = onLoadingCallBack;
    _onRecharged = onRecharged;
    _onFinished = onFinished;

    await getLocaleUserInfo(false);

    bool isNovelExist = DBManager.instance
        .checkIsNovelExist<DownloadBookDetailInfoModel>(
            _bookDetailInfoModel?.bookId);
    if (isNovelExist) {
      //如果书籍已经下载过
      showToast("downloaded".tr);
      return;
    }

    await onGetNovelInfo();
  }

  Future<void> onGetNovelInfo() async {
    if (isAvailable(_onLoadingCallBack)) {
      _onLoadingCallBack!(true);
    }

    var parameters = {"bookId": _bookDetailInfoModel?.bookId};
    BookDetailInfoResultModel? detailInfoModel =
        await BookInfoViewModel.getNovelInfo(parameters,
            onReceiveProgress: (count, total) {});
    //更新书籍信息
    _bookDetailInfoModel?.lock = detailInfoModel?.lock;
    if (isAvailable(detailInfoModel?.chapterVoList)) {
      _bookDetailInfoModel?.chapterVoList = detailInfoModel?.chapterVoList;
    }

    //todo:判断是否有余额
    ///书籍锁的状态（0-免费，1-试看，需要购买，2-已购买）
    if (_bookDetailInfoModel?.lock == 0 || _bookDetailInfoModel?.lock == 2) {
      //已购买,直接下载
      await onDownloadNovel();
    } else if (_bookDetailInfoModel?.lock == 1) {
      //试看，判断是否有余额
      await onBuyNovel();
    }
  }

  Future<void> onDownloadNovel() async {
    if (isAvailable(_bookDetailInfoModel?.contentList)) {
      _bookDetailInfoModel?.contentList = _bookDetailInfoModel?.contentList;
    } else {
      if (isAvailable(_bookDetailInfoModel?.contentUrl)) {
        _bookDetailInfoModel =
            await NovelReadViewModel.downloadS3FileOfChapterContent(
                _bookDetailInfoModel!);
      }
    }

    /// 下载成功后，转换为Realm数据库Model
    DownloadBookDetailInfoModel? bookDetailInfoRealmModel =
        DBManager.instance.novelDataTurnToRealmObject(_bookDetailInfoModel);
    DBManager.instance.addNovel(bookDetailInfoRealmModel);
    await updateUserInfo();
    if (isAvailable(_onLoadingCallBack)) {
      _onLoadingCallBack!(false);
    }

    if (isAvailable(_onFinished)) {
      _onFinished!(_bookDetailInfoModel);
    } else {
      showToast('downloaded'.tr);
    }
  }

  // TODO: 购买书籍
  Future<void> onBuyNovel() async {
    //未购买，判断余额是否充足
    if ((_bookDetailInfoModel?.bookGoldCount ?? 0) <=
        (_userInfoModel?.goldCoin ?? 0)) {
      //余额充足，先购买
      bool isBuySuccess = await onNovelBuy();
      if (isBuySuccess) {
        //购买成功，再下载
        showToast("novel_purchased".tr);
        await onGetNovelInfo();
        await reportData();
      }
    } else {
      //余额不足 充值
      onShowDownLoadSheet();
    }
  }

  // TODO: 数据上报
  Future<void> reportData() async {
    if (_userInfoModel?.newUser == true) {
      EventReportManager.eventReportOfFirebase(downloadUnlockNew);
    } else {
      EventReportManager.eventReportOfFirebase(downloadUnlockOld);
    }

    EventReportManager.eventReportOfFirebase(downloadUnlockAll);
  }

  // TODO: 下载书籍弹窗
  onShowDownLoadSheet() {
    bool isBuying = false;
    showBookBottomSheet(
        _context,
        BookDownloadPaySheet(
            bookGoldCount: _bookDetailInfoModel?.bookGoldCount,
            title: _bookDetailInfoModel?.title,
            authorName: _bookDetailInfoModel?.authorName,
            userInfoModel: _userInfoModel,
            onRecharge: () {
              isBuying = true;
              onGetGoodsList((List<GoodsListItem>? goodList) {
                showBookBottomSheet(
                    _context,
                    AccountPurchaseSheet(
                      goodList: goodList,
                      isSheetType: true,
                      onProductTap: (item) {
                        // TODO: implement onProductTap 购买商品
                        _createShopOrder(item!);
                      },
                    ),
                    Colors.transparent);
              });
            }),
        null, onDismiss: () {
      if (!isBuying) {
        if (isAvailable(_onLoadingCallBack)) {
          _onLoadingCallBack!(false);
        }
      }
    });
  }

  // TODO: 获取商品列表
  onGetGoodsList(Function(List<GoodsListItem>?)? onDataFetched) async {
    // TODO: implement fetchData
    if (isAvailable(_onLoadingCallBack)) {
      _onLoadingCallBack!(true);
    }
    List<GoodsListItem>? list =
        await ProfileViewModel.getGoodList(GoodsType.purchaseCoins);
    if (isAvailable(list)) {
      onDataFetched?.call(list);
    }

    if (isAvailable(_onLoadingCallBack)) {
      _onLoadingCallBack!(false);
    }
  }

  // TODO: 创建订单
  _createShopOrder(GoodsListItem item) async {
    if (!isAvailable(item)) {
      showToast("product is Not Available");

      return;
    }

    if (isAvailable(_onLoadingCallBack)) {
      _onLoadingCallBack!(true);
    }

    PaymentManager.instance.createShopOrder(item,
        goodsType: GoodsType.purchaseCoins,
        bookId: _bookDetailInfoModel?.bookId,
        orderType: OrderType.novelReadDownLoad, pullPurchaseCallback: () async {
      // TODO: 支付掉起回调
    }, onPurchaseCanceled: () async {
      // TODO: 支付取消回调
      if (isAvailable(_onLoadingCallBack)) {
        _onLoadingCallBack!(false);
      }
    }, onPurchaseValidateCallback: (status, info) async {
      // TODO: 验证订单回调
      if (isAvailable(_onLoadingCallBack)) {
        _onLoadingCallBack!(false);
      }
      if (status == SubscribeVerifyStatus.purchased) {
        _onRecharged?.call();
        await updateUserInfo();
        //充值完成
        await onBuyNovel();
      }
    });
  }

  // TODO: 购买书籍
  Future<bool> onNovelBuy() async {
    if (isAvailable(_onLoadingCallBack)) {
      _onLoadingCallBack!(true);
    }
    BatchBuyChapterResultModel? result =
        await NovelReadViewModel.batchBuyChapter(
            _bookDetailInfoModel?.bookId, [], true);
    if (isAvailable(_onLoadingCallBack)) {
      _onLoadingCallBack!(false);
    }

    if (result?.succ == true) {
      return true;
    } else {
      if (isAvailable(result?.msg)) {
        showToast(result!.msg!);
      }
      return false;
    }
  }

  // TODO: 创建订单
  createShopOrder(GoodsListItem item) async {
    if (!isAvailable(item)) {
      showToast("product is Not Available");

      return;
    }

    if (isAvailable(_onLoadingCallBack)) {
      _onLoadingCallBack!(true);
    }

    PaymentManager.instance.createShopOrder(item,
        goodsType: GoodsType.purchaseCoins,
        bookId: _bookDetailInfoModel?.bookId,
        orderType: OrderType.novelReadDownLoad, pullPurchaseCallback: () {
      // TODO: 支付掉起回调
      if (isAvailable(_onLoadingCallBack)) {
        _onLoadingCallBack!(false);
      }
    }, onPurchaseCanceled: () {
      // TODO: 支付取消回调
      if (isAvailable(_onLoadingCallBack)) {
        _onLoadingCallBack!(true);
      }
    }, onPurchaseValidateCallback: (status, info) async {
      // TODO: 验证订单回调
      if (status == SubscribeVerifyStatus.purchased) {
        await updateUserInfo();
        if (isAvailable(_onLoadingCallBack)) {
          _onLoadingCallBack!(false);
        }

        //充值完成，购买书籍
        await onBuyNovel();
      }
    });
  }
}
