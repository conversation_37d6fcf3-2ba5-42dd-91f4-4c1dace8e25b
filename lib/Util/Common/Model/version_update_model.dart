import 'package:json_annotation/json_annotation.dart';

part 'version_update_model.g.dart';

@JsonSerializable()
class VersionUpdateModel {
  int? code;
  @JsonKey(name: "result")
  VersionInfoModel? versionInfo;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  VersionUpdateModel(
      {this.code,
      this.versionInfo,
      this.msg,
      this.sysAt,
      this.cost,
      this.traceId});

  factory VersionUpdateModel.fromJson(Map<String, dynamic> json) =>
      _$VersionUpdateModelFromJson(json);

  Map<String, dynamic> toJson() => _$VersionUpdateModelToJson(this);
}

@JsonSerializable()
class VersionInfoModel {
  bool? forceUpdate; //是否强制更新，默认false
  String? latestVersion; //最新版本号
  int? verifyBuildCode; //版本号校验码(应用当前正在审核的版本值)

  VersionInfoModel(
      {this.forceUpdate, this.latestVersion, this.verifyBuildCode});

  factory VersionInfoModel.fromJson(Map<String, dynamic> json) =>
      _$VersionInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$VersionInfoModelToJson(this);
}
