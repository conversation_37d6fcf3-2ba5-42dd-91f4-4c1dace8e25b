import 'package:UrNovel/Util/enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'reward_item.g.dart';

@JsonSerializable()
class RewardItem {
  String title;
  int coin;
  String assets;
  SpineAnimationType type;

  RewardItem({required this.title, required this.coin, required this.assets, required this.type});

  factory RewardItem.fromJson(Map<String, dynamic> json) =>
      _$RewardItemFromJson(json);

  Map<String, dynamic> toJson() => _$RewardItemToJson(this);
}
