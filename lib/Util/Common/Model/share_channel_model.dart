import 'package:json_annotation/json_annotation.dart';
part 'share_channel_model.g.dart';

@JsonSerializable()
class ShareBookModel {
  int? code;
  ShareBookUrlModel? result;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  ShareBookModel(
      {this.code,
      this.result,
      this.msg,
      this.sysAt,
      this.cost,
      this.traceId});

  factory ShareBookModel.fromJson(Map<String, dynamic> json) =>
      _$ShareBookModelFromJson(json);

  Map<String, dynamic> toJson() => _$ShareBookModelToJson(this);
}

@JsonSerializable()
class ShareBookUrlModel {
  String? facebook;
  String? instagram;
  String? whatsApp;
  String? copyLink;
  String? bookCoverUrl;

  ShareBookUrlModel({this.instagram, this.whatsApp, this.copyLink, this.facebook, this.bookCoverUrl});

  factory ShareBookUrlModel.from<PERSON>son(Map<String, dynamic> json) =>
      _$ShareBookUrlModelFromJson(json);

  Map<String, dynamic> toJson() => _$ShareBookUrlModelToJson(this);
}