import 'package:json_annotation/json_annotation.dart';
part 'normal_model.g.dart';

@JsonSerializable()
class NormalModel {
  int? code;
  dynamic result;
  String? msg;
  int? sysAt;
  int? cost;
  String? traceId;

  NormalModel({
    this.code,
    this.result,
    this.msg,
    this.sysAt,
    this.cost,
    this.traceId,
  });

  factory NormalModel.fromJson(Map<String, dynamic> json) => _$NormalModelFromJson(json);

  Map<String, dynamic> toJson() => _$NormalModelToJson(this);
}