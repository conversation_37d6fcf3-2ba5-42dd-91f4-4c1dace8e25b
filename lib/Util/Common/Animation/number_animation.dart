import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:flutter/material.dart';

class NumberAnimated extends BaseFulWidget {
  final double? beginNumber;
  final double endNumber;
  final Duration numberDuration;
  final Duration scaleDuration;
  final Duration delay;
  final String? symbol;
  final TextStyle? style;
  final VoidCallback? animationCompleted;

  NumberAnimated(
      {super.key,
      this.beginNumber = 0.0,
      required this.endNumber,
      this.numberDuration = const Duration(milliseconds: 1500),
      this.scaleDuration = const Duration(milliseconds: 500),
      this.delay = Duration.zero,
      this.symbol,
      this.style,
      this.animationCompleted});

  @override
  State<NumberAnimated> createState() => _NumberAnimatedState();
}

class _NumberAnimatedState extends State<NumberAnimated> with TickerProviderStateMixin {
  late AnimationController _numberController;
  late AnimationController _scaleController;
  late Animation<double> _numberAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    // 数字变化动画控制器
    _numberController = AnimationController(
      duration: widget.numberDuration,
      vsync: this,
    );

    // 缩放动画控制器
    _scaleController = AnimationController(
      duration: widget.scaleDuration,
      vsync: this,
    );

    // 数字变化动画
    _numberAnimation = Tween<double>(
      begin: widget.beginNumber ?? 0.0,
      end: widget.endNumber,
    ).animate(CurvedAnimation(
      parent: _numberController,
      curve: Curves.easeOut,
    ))
      ..addListener(() {
        setState(() {});
      })
      ..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          // 数字动画完成后开始缩放动画
          _scaleController.forward();
        }
      });

    // 创建复杂的缩放动画序列
    final List<TweenSequenceItem<double>> sequenceItems = [
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 1.5).chain(CurveTween(curve: Curves.easeOut)),
        weight: 25.0,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.5, end: 1.0).chain(CurveTween(curve: Curves.easeInOut)),
        weight: 25.0,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 1.2).chain(CurveTween(curve: Curves.easeOut)),
        weight: 25.0,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.2, end: 1.0).chain(CurveTween(curve: Curves.easeInOut)),
        weight: 25.0,
      ),
    ];
    // 缩放动画序列
    _scaleAnimation = TweenSequence<double>(sequenceItems).animate(_scaleController)
      ..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          widget.animationCompleted?.call();
        }
      });
    // 开始数字变化动画
    Future.delayed(widget.delay, () {
      _numberController.forward();
    });
  }

  @override
  void dispose() {
    _numberController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ScaleTransition(
        scale: _scaleAnimation,
        child: FittedBox(
          child: Text(
            '${widget.symbol ?? ''}${_numberAnimation.value.toStringAsFixed(2)}',
            style: widget.style,
          ),
        ));
  }
}
