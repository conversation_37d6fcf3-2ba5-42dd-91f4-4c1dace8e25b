import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';

import 'package:UrNovel/Util/tools.dart';

import '../../Extensions/colorUtil.dart';
import '../../StatusManagement/status_management.dart';
import '../gradient_progress_line.dart';

class LottieAnimationView extends StatefulWidget {
  final bool isFromReadPage;
  final bool isLoadingTimeOut;
  final NetWorkStatusController? netWorkStatusController;

  const LottieAnimationView(
      {super.key,
      this.isFromReadPage = false,
      this.isLoadingTimeOut = false,
      this.netWorkStatusController});

  @override
  State<LottieAnimationView> createState() => LottieAnimationViewState();
}

class LottieAnimationViewState extends State<LottieAnimationView>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  late double _receiveProgress; // 接收到的进度

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    )..repeat(); // 循环播放

    _receiveProgress = 0.0;
  }

  @override
  void dispose() {
    _controller.dispose(); // 释放控制器
    super.dispose();
  }

  void updateProgress(double progress) {
    if (mounted) {
      setState(() {
        _receiveProgress = progress;
      });
    }
  }

  Obx _buildReadProgress() {
    return Obx(() {
      return Column(
        children: [
          Padding(
              padding: EdgeInsets.only(left: 10, top: 56, right: 10),
              child: Text.rich(TextSpan(
                  text: widget.netWorkStatusController!.isConnectivity.value
                      ? widget.isLoadingTimeOut
                          ? 'take_a_while'.tr
                          : 'still_loading'.tr
                      : 'failed_load'.tr,
                  style: TextStyle(
                      color: HexColor('#555A65'),
                      fontSize: 14,
                      fontWeight: FontWeight.bold)))),
          if (widget.netWorkStatusController!.isConnectivity.value)
            Column(
              children: [
                Padding(
                    padding: EdgeInsets.only(left: 10, top: 25, right: 10),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(4),
                      child: CustomPaint(
                        painter: GradientProgressLine(_receiveProgress),
                        child: Container(
                          height: 6.0, // 进度条的高度
                        ),
                      ),
                    )),
                Padding(
                    padding: EdgeInsets.only(left: 10, top: 15, right: 10),
                    child: Text(
                      '${(_receiveProgress * 100).toStringAsFixed(0)}%',
                      style:
                          TextStyle(fontSize: 11, color: HexColor('#555A65')),
                    ))
              ],
            )
        ],
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Align(
        alignment: Alignment.center,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ColorFiltered(
              colorFilter:
                  ColorFilter.mode(HexColor('#d4d4d8'), BlendMode.srcIn),
              child: Lottie.asset('assets/json/loading.json',
                  width: 80,
                  height: 40,
                  controller: _controller,
                  onLoaded: (composition) {}),
            ),
            if (widget.isFromReadPage &&
                isAvailable(widget.netWorkStatusController))
              _buildReadProgress()
          ],
        ));
  }
}
