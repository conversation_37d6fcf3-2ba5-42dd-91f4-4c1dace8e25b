import 'package:UrNovel/Util/Extensions/colorUtil.dart';
import 'package:flame_spine/flame_spine.dart' as spine;
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SpineThanksView extends StatefulWidget {
  final VoidCallback onAnimationComplete;

  const SpineThanksView({
    super.key,
    required this.onAnimationComplete,
  });

  @override
  State<SpineThanksView> createState() => _SpineThanksViewState();
}

class _SpineThanksViewState extends State<SpineThanksView> with TickerProviderStateMixin {
  late String _atlasFile;
  late String _skeletonFile;
  late final spine.SpineWidget _spineWidget;
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();

    setAnimationType();
    _initAnimation();
    _initOpacityAnimation();
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  void setAnimationType() {
    _atlasFile = 'assets/spine/thanks/toast.atlas';
    _skeletonFile = 'assets/spine/thanks/toast.json';

    var controller = spine.SpineWidgetController(onInitialized: (controller) {
      final entry = controller.animationState.setAnimationByName(0, 'animation', false);
      entry.setListener((type, trackEntry, event) {
        if (type == spine.EventType.complete) {}
      });
    });
    _spineWidget =
        spine.SpineWidget.fromAsset(_atlasFile, _skeletonFile, controller, sizedByBounds: true);
  }

  void _initAnimation() {
    // 缩放动画控制器
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // 创建复杂的缩放动画序列
    final List<TweenSequenceItem<double>> sequenceItems = [
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 0.95).chain(CurveTween(curve: Curves.easeInOut)),
        weight: 10,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 0.95, end: 1.05).chain(CurveTween(curve: Curves.easeInOut)),
        weight: 10,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.05, end: 1.0).chain(CurveTween(curve: Curves.easeInOut)),
        weight: 10,
      ),
    ];
    // 缩放动画序列
    _scaleAnimation = TweenSequence<double>(sequenceItems).animate(_scaleController)
      ..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          //此时是透明度控制器
          _scaleController.forward();
        }
      });

    _scaleController.forward();
  }

  void _initOpacityAnimation() {
    _scaleController = AnimationController(
      duration: const Duration(seconds: 1), // 动画持续时间
      vsync: this,
    );
    _opacityAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(_scaleController)
      ..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          widget.onAnimationComplete.call();
        }
      });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
        animation: _opacityAnimation,
        builder: (context, child) {
          return Opacity(
            opacity: _opacityAnimation.value,
            child: Align(
              alignment: Alignment.center,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 50.0),
                child: ScaleTransition(
                  scale: _scaleAnimation,
                  child: SizedBox(
                    height: 50,
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        Positioned.fill(
                          child: DecoratedBox(
                            decoration: BoxDecoration(
                              color: HexColor('#000000'), // 设置背景颜色
                              borderRadius: BorderRadius.circular(10), // 圆角
                            ),
                            child: Padding(
                              padding:
                                  const EdgeInsets.only(top: 5, left: 53, right: 10, bottom: 5),
                              child: Align(
                                alignment: Alignment.centerLeft,
                                child: FittedBox(
                                  child: Text(
                                    'thanks_support'.tr,
                                    style: TextStyle(
                                        color: HexColor('#ECC014'),
                                        fontWeight: FontWeight.bold,
                                        fontSize: 17),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                        Positioned(
                          left: 5,
                          width: 40,
                          top: 0,
                          bottom: 0,
                          child: Align(
                            alignment: Alignment.centerLeft,
                            child: ScaleTransition(
                              scale: AlwaysStoppedAnimation<double>(0.8),
                              child: _spineWidget,
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        });
  }
}
