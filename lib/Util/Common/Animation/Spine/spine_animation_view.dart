import 'package:flame_spine/flame_spine.dart';
import 'package:flutter/material.dart';

import '../../../enum.dart';

class SpineAnimationView extends StatefulWidget {
  final SpineAnimationType animationType;
  final VoidCallback onAnimationComplete;

  const SpineAnimationView(
      {super.key,
      required this.animationType,
      required this.onAnimationComplete});

  @override
  State<SpineAnimationView> createState() => _SpineAnimationViewState();
}

class _SpineAnimationViewState extends State<SpineAnimationView> {
  late String _atlasFile;
  late String _skeletonFile;
  late final SpineWidget _spineWidget;

  @override
  void initState() {
    super.initState();

    setAnimationType(widget.animationType);
  }

  void setAnimationType(SpineAnimationType type) {
    if (type == SpineAnimationType.none) {
      return;
    }

    _atlasFile = 'assets/spine/${type.name}/${type.name}.atlas';
    _skeletonFile = 'assets/spine/${type.name}/${type.name}.skel.bytes';

    var controller = SpineWidgetController(onInitialized: (controller) {
      final entry =
          controller.animationState.setAnimationByName(0, type.name, false);
      entry.setListener((type, trackEntry, event) {
        if (type == EventType.complete) {
          widget.onAnimationComplete.call();
        }
      });
    });
    _spineWidget = SpineWidget.fromAsset(
      _atlasFile,
      _skeletonFile,
      controller,
      fit: (type == SpineAnimationType.balloon ||
              type == SpineAnimationType.rocket)
          ? BoxFit.cover
          : BoxFit.contain,
    );
  }

  @override
  Widget build(BuildContext context) {
    return widget.animationType != SpineAnimationType.none
        ? SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: Stack(
              children: [
                Positioned.fill(
                  child: Opacity(
                    opacity: 1, // 设置背景透明度
                    child: DecoratedBox(
                      decoration: BoxDecoration(
                        color: Colors.black, // 设置背景颜色
                      ),
                    ),
                  ),
                ),
                Center(
                  child: _spineWidget,
                ),
              ],
            ),
          )
        : SizedBox.shrink();
  }

  @override
  void dispose() {
    super.dispose();
  }
}
