import 'dart:ui';

import 'package:UrNovel/Util/logUtil.dart';
import 'package:flame/events.dart';
import 'package:flame/game.dart';
import 'package:flame_spine/flame_spine.dart';
import 'package:flutter/material.dart';

class SpineGameFlame extends FlameGame with TapDetector {
  final String atlasFile;
  final String skeletonFile;
  final String animationName;
  final bool isLoop;
  final VoidCallback? onAnimationComplete;

  SpineGameFlame(this.atlasFile, this.skeletonFile, this.animationName, this.isLoop,
      this.onAnimationComplete);

  late final SpineComponent spineComponent;

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    try {
      // 确保在 onLoad 中初始化
      spineComponent = await SpineComponent.fromAssets(
        atlasFile: atlasFile,
        skeletonFile: skeletonFile,
        // anchor: Anchor.center,
        scale: Vector2(0.5, 0.5),
        position: Vector2(size.x / 2, size.y / 2),
      );

      final trackEntry = spineComponent.animationState
          .setAnimationByName(0, animationName, isLoop);
      trackEntry.setListener(_animationStateListener);
      // 添加到游戏
      await add(spineComponent);
    } catch (e) {
      logP('SpineFlame 加载失败 $e');
    }
  }

  void _animationStateListener(
      EventType type, TrackEntry trackEntry, Event? event) {
    if (type == EventType.complete) {
      // 动画结束的回调
      onAnimationComplete?.call();
      onRemove();
      onDetach();
    }
  }

  @override
  void onRemove() {
    // 确保在移除时清理资源
    if (spineComponent.isMounted) {
      spineComponent.removeFromParent();
    }
    super.onRemove();
  }

  @override
  void onTap() {
  }

  @override
  void onDetach() {
    // Dispose the native resources that have been loaded for spineboy.
    spineComponent.dispose();
    super.onDetach();
  }
}
