import 'dart:io';

import 'package:UrNovel/BaseWidget/base_less_widget.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:http/io_client.dart';

///w: 540, h: 720, 封面的压缩尺寸
//540*720 书籍封面尺寸
//84*112,174*232 书籍封面尺寸(live activity)
//148*148 作者页作者头像尺寸
//100*100 打赏作者头像尺寸
//44*44 阅读页简介作者头像尺寸
//32*32 主页瀑布流卡片作者头像尺寸
//68*68 用户的头像尺寸

class NetworkImageUtil extends BaseLessWidget {
  final String? imageUrl;
  final int w;
  final int h;
  final double? width;
  final double? height;
  final BoxFit? fit;
  final bool showPlaceholder;
  final String placeholder;
  final bool showError;
  final String errorHolder;
  final bool isNeedRedirect;
  final bool isCircle;
  final Color? placeholderColor;

  NetworkImageUtil({
    super.key,
    required this.imageUrl,
    this.w = 540,
    this.h = 720,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.showPlaceholder = true,
    this.placeholder = 'assets/images/common/img_placeholder.png',
    this.showError = true,
    this.errorHolder = 'assets/images/common/img_card_error.png',
    this.isNeedRedirect = true,
    this.isCircle = false,
    this.placeholderColor,
  });

  @override
  Widget build(BuildContext context) {
    Widget imageWidget = SizedBox(
        width: width ?? double.infinity,
        height: height ?? double.infinity,
        child: isAvailable(imageUrl)
            ? (imageUrl!.startsWith('http://') || imageUrl!.startsWith('https://'))
                ? CachedNetworkImage(
                    // 实际图片的URL
                    imageUrl:
                        isNeedRedirect ? getRedirectImageUrl(imageUrl!, w: w, h: h) : imageUrl!,
                    placeholder: (context, url) {
                      if (placeholderColor != null) {
                        return DecoratedBox(
                          decoration: BoxDecoration(color: placeholderColor),
                        );
                      }
                      if (showPlaceholder) {
                        return Image.asset(placeholder, fit: fit);
                      } else {
                        return SizedBox();
                      }
                    },
                    // 加载中的占位图
                    errorWidget:
                        showError ? ((context, url, error) => Image.asset(errorHolder)) : null,
                    // 加载失败时的错误图
                    cacheManager: CachedNetworkImageManager(),
                    fit: fit,
                  )
                : Image.asset(imageUrl!, width: width, height: height, fit: fit)
            : showPlaceholder
                ? Image.asset(placeholder)
                : SizedBox());

    return isCircle ? ClipOval(child: imageWidget) : imageWidget;
  }
}

// 自定义 CacheManager 修复Connection closed before full header was received问题
class CachedNetworkImageManager extends CacheManager with ImageCacheManager {
// 为了不影响之前的缓存，保持使用相同的 key
  static String get key => DefaultCacheManager.key;

  static HttpClient httpClient() {
    final context = SecurityContext.defaultContext;
    context.allowLegacyUnsafeRenegotiation = true;
    return HttpClient(context: context);
  }

  static final CachedNetworkImageManager _instance = CachedNetworkImageManager._();

  factory CachedNetworkImageManager() {
    return _instance;
  }

  CachedNetworkImageManager._()
      : super(
          Config(
            key,
// 指定 httpClient
            fileService: HttpFileService(
              httpClient: IOClient(httpClient()),
            ),
          ),
        );
}
