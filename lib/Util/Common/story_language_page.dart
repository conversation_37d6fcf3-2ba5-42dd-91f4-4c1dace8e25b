import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/Util/Common/Animation/lottie_animation_view.dart';
import 'package:UrNovel/Util/ThirdSdkManger/third_manger.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../Launch&Login/Model/language_model.dart';
import '../Extensions/colorUtil.dart';
import '../tools.dart';
import 'CommonManager/common_manager.dart';

class StoryLanguagePage extends BaseFulWidget {
  StoryLanguagePage({super.key});

  @override
  State<StoryLanguagePage> createState() => _StoryLanguagePageState();
}

class _StoryLanguagePageState extends State<StoryLanguagePage> {
  late List<LanguageItem>? _dataList;
  late LanguageItem? _currentItem;
  late bool _isLoading;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    _isLoading = false;
    _getContentLanguage();
  }

  Future<void> _getContentLanguage() async {
    _dataList = CommonManager.instance.languageList;
    _currentItem = CommonManager.instance.contentLanguage;

    if (!isAvailable(_dataList)) {
      if (mounted) {
        setState(() {
          _isLoading = true;
        });
      }
      await CommonManager.instance.getContentLanguage();
      _dataList = CommonManager.instance.languageList;
      _currentItem = CommonManager.instance.contentLanguage;

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var bottom = mediaQuery.padding.bottom;
    var count = _dataList?.length ?? 0;
    return Scaffold(
        appBar: AppBar(
          backgroundColor: HexColor('#FFFFFF'),
          title: Text(
            "story_language".tr,
            style: TextStyle(
                color: HexColor('#000000'),
                fontSize: 18,
                fontWeight: FontWeight.bold),
          ),
        ),
        body: SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: Column(
              children: [
                SizedBox(height: 0.5),
                DecoratedBox(
                  decoration: BoxDecoration(
                    color: HexColor('#FFFFFF'),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.only(
                        top: 15, left: 14, right: 14, bottom: 18),
                    child: RichText(
                      text: TextSpan(
                          text: "${"set_story_language".tr}\n\n",
                          style: TextStyle(
                              color: HexColor('#888C94'),
                              fontSize: 12.sp,
                              fontWeight: FontWeight.bold),
                          children: [
                            TextSpan(
                                text: "change_ios_language".tr,
                                style: TextStyle(
                                    color: HexColor('#888C94'),
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.bold,
                                    height: 1.3)),
                            TextSpan(
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                    // 点击事件处理逻辑
                                    ThirdManger.instance.openLanguageSettings();
                                  },
                                text: ' ${"ios_language".tr}',
                                style: TextStyle(
                                    color: HexColor('#1B86FF'),
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.bold,
                                    height: 1.3))
                          ]),
                    ),
                  ),
                ),
                Padding(
                    padding: const EdgeInsets.only(
                        top: 10, left: 8, right: 8, bottom: 10),
                    child: DecoratedBox(
                        decoration: BoxDecoration(
                          color: HexColor('#FFFFFF'),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              vertical: 8, horizontal: 12),
                          child: RichText(
                              text: TextSpan(
                            text: "language_not_availabe".tr,
                            style: TextStyle(
                                color: HexColor('#1F1F2F'),
                                fontSize: 12.sp,
                                fontWeight: FontWeight.bold,
                                height: 1.3),
                          )),
                        ))),
                Expanded(
                  child: !_isLoading
                      ? DecoratedBox(
                          decoration: BoxDecoration(
                            color: HexColor("#FFFFFF"),
                          ),
                          child: ListView.builder(
                              itemCount: count,
                              itemBuilder: (context, index) {
                                if (index < count) {
                                  var item = _dataList?[index];
                                  return GestureDetector(
                                    onTap: () async {
                                      if (mounted) {
                                        setState(() {
                                          _currentItem = item;
                                        });
                                      }
                                      if (isAvailable(item)) {
                                        await CommonManager.instance
                                            .setCurrentItem(item!);
                                        eventBusFire(item);
                                        Get.back();
                                      }
                                    },
                                    behavior: HitTestBehavior.opaque,
                                    child: SizedBox(
                                      height: 68,
                                      width: double.infinity,
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          SizedBox(
                                            height: 67,
                                            child: Padding(
                                                padding: const EdgeInsets.only(
                                                    left: 30, right: 40),
                                                child: Row(
                                                  children: [
                                                    Expanded(
                                                        child: Row(
                                                      children: [
                                                        Text(item?.name ?? '',
                                                            style: TextStyle(
                                                                fontSize: 15,
                                                                color: HexColor(
                                                                    "#1F1F2F"),
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold)),
                                                        if (isAvailable(
                                                            item?.desc))
                                                          Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                    .only(
                                                                    left: 8),
                                                            child: Text(
                                                                '(${item?.desc ?? ""})',
                                                                style: TextStyle(
                                                                    fontSize:
                                                                        12,
                                                                    color: HexColor(
                                                                        "#7E839D"),
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold)),
                                                          )
                                                      ],
                                                    )),
                                                    if ((_currentItem?.code) ==
                                                        item?.code)
                                                      Image.asset(
                                                          "assets/images/sign/icon_choose.png",
                                                          width: 15,
                                                          height: 12,
                                                          fit: BoxFit.cover)
                                                  ],
                                                )),
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.only(
                                                left: 19, right: 19, bottom: 0),
                                            child: Divider(
                                                height: 1,
                                                color: ColorsUtil.hexColor(
                                                    0x7E839D,
                                                    alpha: 0.1)),
                                          )
                                        ],
                                      ),
                                    ),
                                  );
                                }
                                return SizedBox();
                              }))
                      : const LottieAnimationView(),
                ),
                SizedBox(height: bottom + 10),
              ],
            )));
  }
}
