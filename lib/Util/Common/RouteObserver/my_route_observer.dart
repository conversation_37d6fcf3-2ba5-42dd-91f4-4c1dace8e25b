import 'package:UrNovel/Util/Common/RouteObserver/Model/my_route_model.dart';
import 'package:UrNovel/Util/logUtil.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:flutter/material.dart';

import '../../StatusManagement/status_management.dart';
import '../../enum.dart';
import '../../genericUtil.dart';
import '../../global.dart';

class PageRouteObserver<R extends Route<dynamic>> extends RouteObserver<R> {
  final BottomBarController _bottomBarController = findGetXInstance(BottomBarController());

  @override
  void didPush(Route route, Route? previousRoute) {
    super.didPush(route, previousRoute);

    _dealWithCurrentRouteName(route.settings);
    if (isAvailable(route.settings.name)) {
      eventBusFire(_routeModel(route, previousRoute, MyRouteType.push));
    }
  }

  @override
  void didPop(Route route, Route? previousRoute) {
    super.didPop(route, previousRoute);

    _dealWithCurrentRouteName(previousRoute?.settings);
    if (isAvailable(route.settings.name)) {
      eventBusFire(_routeModel(route, previousRoute, MyRouteType.pop));
    }
  }

  void _dealWithCurrentRouteName(RouteSettings? settings) {
    if (settings?.name == 'bottomNavBarPage') {
      if (_bottomBarController.currentIndex.value == 0) {
        currentRouteName = 'readPage';
      } else if (_bottomBarController.currentIndex.value == 1) {
        currentRouteName = 'libraryPage';
      } else if (_bottomBarController.currentIndex.value == 2) {
        currentRouteName = 'genresTagsPage';
      } else {
        currentRouteName = 'profilePage';
      }
    } else {
      currentRouteName = settings?.name?.replaceFirst('/', '');
    }
  }

  @override
  void didReplace({Route? newRoute, Route? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    logP('Replaced: ${oldRoute?.settings.name} with ${newRoute?.settings.name}');
  }

  @override
  void didRemove(Route route, Route? previousRoute) {
    super.didRemove(route, previousRoute);
    logP('Removed: ${route.settings.name}');
  }

  @override
  void didStartUserGesture(Route route, Route? previousRoute) {
    super.didStartUserGesture(route, previousRoute);
    logP('Started gesture: ${route.settings.name}');
  }

  @override
  void didStopUserGesture() {
    super.didStopUserGesture();
    logP('Stopped gesture');
  }

  PageRouteModel _routeModel(Route route, Route? previousRoute, MyRouteType routeType) {
    logP(
        '${routeType.name}: current: ${route.settings.name}  previous: ${previousRoute?.settings.name}');
    _setCurrentRoutePage(route.settings.name, previousRoute?.settings.name, routeType);
    return PageRouteModel(
      settings: route.settings,
      previousSettings: previousRoute?.settings,
      routeType: routeType,
    );
  }

  void _setCurrentRoutePage(String? routeName, String? previousRouteName, MyRouteType routeType) {
    lastRoutePage = curRoutePage;

    if (routeType == MyRouteType.push) {
      switch (routeName) {
        case '/accountPage':
          curRoutePage = RoutePage.accountPage;
          break;
        case '/premiumPage':
          curRoutePage = RoutePage.premiumPage;
          break;
        case '/novelReadPage':
          curRoutePage = RoutePage.novelReadPage;
          break;
        case '/bookDetailPage':
          curRoutePage = RoutePage.bookDetailPage;
          break;
        default:
          curRoutePage = RoutePage.unKnown;
          break;
      }
    } else if (routeType == MyRouteType.pop) {
      switch (previousRouteName) {
        case '/accountPage':
          curRoutePage = RoutePage.accountPage;
          break;
        case '/premiumPage':
          curRoutePage = RoutePage.premiumPage;
          break;
        case '/novelReadPage':
          curRoutePage = RoutePage.novelReadPage;
          break;
        case '/bookDetailPage':
          curRoutePage = RoutePage.bookDetailPage;
          break;
        default:
          curRoutePage = RoutePage.unKnown;
          break;
      }
    }
  }
}
