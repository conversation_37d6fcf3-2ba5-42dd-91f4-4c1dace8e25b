import 'package:UrNovel/Launch&Login/Model/user_model.dart';
import 'package:UrNovel/Util/enum.dart';
import 'package:get/get.dart';

import 'Model/reward_item.dart';

class DataConfigUtil {
  static final DataConfigUtil instance = DataConfigUtil._internal();

  DataConfigUtil._internal();

  //TODO: 个人中心
  //获取个人中心列表数据
  List<Map<String, dynamic>> profileList(UserInfoModel? userInfo) {
    return [
      {
        'items': [
          {
            "assets": userInfo?.vipStatus == 1
                ? 'assets/images/profile/icon_premium_card.png'
                : 'assets/images/profile/icon_premium_trial_card.png',
            "title": "premium".tr
          },
        ]
      },
      {
        "items": [
          {
            "assets": 'assets/images/profile/icon_account.png',
            "title": "account".tr
          },
        ]
      },
      {
        "items": [
          {
            "assets": 'assets/images/profile/icon_writer.png',
            "title": "become_writer".tr
          },
          // {
          //   "assets": 'assets/images/profile/icon_redeem.png',
          //   "title": "redeem_code".tr
          // },
          {
            "assets": 'assets/images/profile/icon_language.png',
            "title": "language".tr
          },
          {
            "assets": 'assets/images/profile/icon_support.png',
            "title": "support".tr
          },
        ]
      },
      {
        "items": [
          {
            "assets": 'assets/images/bookDetails/icon_share_facebook.png',
            "title": "fb_follow".tr
          },
          {
            "assets": 'assets/images/bookDetails/icon_share_instagram.png',
            "title": "ins_follow".tr
          },
          {
            "assets": 'assets/images/bookDetails/icon_share_whatsApp.png',
            "title": "wa_follow".tr
          }
        ]
      },
    ];
  }

  //获取个人资料列表数据
  List<Map<String, dynamic>> personalList = [
    {
      "group": "",
      "items": [
        {"title": "nickname".tr},
        {"title": "gender".tr},
        {"title": "birthday".tr}
      ]
    },
    {
      "group": "Account Information",
      "items": [
        {"title": "phone_number".tr},
        {"title": "email_address".tr},
      ]
    },
    // {
    //   "group": "your_interests".tr,
    //   "items": [
    //   ]
    // }
  ];

  //Content Guidelines
  List<Map<String, dynamic>> contentGuidelines = [
    {"text": "· Original fiction only"},
    {"text": "· You must own full rights to your work"},
    {"text": "· Content must follow our community standards"}
  ];

  //礼物打赏列表数据
  List<RewardItem> rewardList = [
    RewardItem(
      title: "Like",
      coin: 10,
      assets: "assets/images/novelReading/reward/icon_like.png",
      type: SpineAnimationType.like,
    ),
    RewardItem(
      title: "Love",
      coin: 29,
      assets: "assets/images/novelReading/reward/icon_love.png",
      type: SpineAnimationType.love,
    ),
    RewardItem(
      title: "Bouquet",
      coin: 59,
      assets: "assets/images/novelReading/reward/icon_bouquet.png",
      type: SpineAnimationType.bouquet,
    ),
    RewardItem(
      title: "Ring",
      coin: 99,
      assets: "assets/images/novelReading/reward/icon_ring.png",
      type: SpineAnimationType.ring,
    ),
    RewardItem(
      title: "Balloon",
      coin: 199,
      assets: "assets/images/novelReading/reward/icon_balloon.png",
      type: SpineAnimationType.balloon,
    ),
    RewardItem(
      title: "Rocket",
      coin: 299,
      assets: "assets/images/novelReading/reward/icon_rocket.png",
      type: SpineAnimationType.rocket,
    ),
  ];
}
