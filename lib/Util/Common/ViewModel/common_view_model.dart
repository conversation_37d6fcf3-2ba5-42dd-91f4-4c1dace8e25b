import 'package:UrNovel/Util/Common/Model/normal_model.dart';

import '../../NetWorkManager/net_work_manager.dart';
import '../../api_config.dart';
import '../Model/share_channel_model.dart';
import '../Model/version_update_model.dart';

class CommonViewModel {
  //todo: 获取分享渠道链接列表
  static Future<ShareBookUrlModel?> getShareBookUrl(int? bookId) async {
    var response = await NetWorkManager.instance
        .post(apiGetShareBookUrl, parameters: {'bookId': bookId ?? 0});
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      ShareBookModel? shareBookModel = ShareBookModel.fromJson(response.data);

      return shareBookModel.result;
    }

    return null;
  }

  //todo:版本更新
  static Future<VersionInfoModel?> getVersionInfo() async {
    var response =
        await NetWorkManager.instance.get(apiVersionUpdate, params: {});
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      VersionUpdateModel versionUpdateModel =
          VersionUpdateModel.fromJson(response.data);

      return versionUpdateModel.versionInfo;
    }

    return null;
  }

  //TODO: 任务开关查询
  static Future<bool> getSwitchByCode(String code) async {
    var response = await NetWorkManager.instance
        .get(apiGetSwitchByCode, params: {"code" : code});
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      NormalModel model = NormalModel.fromJson(response.data);

      return model.result ?? false;
    }

    return false;
  }
}