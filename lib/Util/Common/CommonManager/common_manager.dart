import 'package:UrNovel/Util/genericUtil.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../../Launch&Login/Model/language_model.dart';
import '../../../Launch&Login/ViewModel/ViewModel.dart';
import '../../../MainPage/Library/ViewModel/ViewModel.dart';
import '../../../MainPage/NovelRead/ViewModel/ViewModel.dart';
import '../../Extensions/colorUtil.dart';
import '../../LanguageManager/MyTranslations.dart';
import '../../ShareManager/share_manager.dart';
import '../../SharedPreferences/shared_preferences.dart';
import '../../SheetAndAlter/bottom_sheet.dart';
import '../../StatusManagement/status_management.dart';
import '../../stringUtil.dart';
import '../Model/normal_model.dart';
import '../Model/share_channel_model.dart';
import '../Model/version_update_model.dart';
import '../ViewModel/common_view_model.dart';

class CommonManager extends GetxController {
  static CommonManager instance = CommonManager._internal();

  CommonManager._internal();

  ///本地语言
  final Locale? _deviceLocale = Get.deviceLocale;

  ///皮肤语言
  var _skinLanguage = Locale('en', 'US');

  ///app内容语言
  LanguageResultModel? _languageResultModel;
  var contentLanguageItem = LanguageItem(code: '', desc: '').obs;

  ///设备语言
  Locale? get deviceLocale => _deviceLocale;

  ///皮肤语言
  Locale get skinLanguage => _skinLanguage;

  LanguageItem get contentLanguage => contentLanguageItem.value;

  List<LanguageItem>? get languageList => _languageResultModel?.all;

  //todo：初始化配置数据
  Future<void> initConfigData() async {
    await _initSkinLanguage();
    await _initContentLanguage();
  }

  //TODO: 初始化皮肤语言环境
  Future<void> _initSkinLanguage() async {
    Locale? locale = _deviceLocale;
    if (isAvailable(locale?.languageCode)) {
      if (locale?.languageCode == 'nn' || locale?.languageCode == 'nb') {
        //如果设备语言环境为挪威语 特殊处理
        locale = Locale('no', 'Norsk');
      }

      if (MyTranslations.translations.keys.contains(locale?.languageCode)) {
        _skinLanguage = locale!;
      }
    }

    Get.updateLocale(_skinLanguage);
  }

  //TODO: 初始化内容语言环境
  Future<void> _initContentLanguage() async {
    await getContentLanguage();
  }

  /// 获取app内容语言列表
  Future<void> getContentLanguage() async {
    LanguageItem? item = await SpUtil.spGetContentLanguageItem();
    if (isAvailable(item)) {
      contentLanguageItem.value = item!;
    }

    _languageResultModel = await LoginViewModel.getContentLanguage();
    if (!isAvailable(item) && isAvailable(_languageResultModel?.current)) {
      await setCurrentItem(_languageResultModel!.current!);
    }
  }

  Future<void> setCurrentItem(LanguageItem item) async {
    contentLanguageItem.value = item;
    await SpUtil.spSetContentLanguageItem(contentLanguageItem.value);
  }

  ///是否是需要反转自定义的图标
  ///如果是需要反转，则返回true，否则返回false
  bool isReverse() {
    return _skinLanguage.languageCode == 'he' ||
        _skinLanguage.languageCode == 'fa' ||
        _skinLanguage.languageCode == 'ar';
  }

  ///仅仅适配内容
  bool isContentReverse() {
    return contentLanguage.code == 'he' ||
        contentLanguage.code == 'fa' ||
        contentLanguage.code == 'ar';
  }

  /// 获取分享渠道列表
  static Future<ShareBookUrlModel?> getShareBookUrl(int? bookId) async {
    ShareBookUrlModel? shareBookUrlModel = await CommonViewModel.getShareBookUrl(bookId);
    return shareBookUrlModel;
  }

  //TODO: 获取最新版本
  Future<void> getVersionInfo(
      BuildContext context, Function(bool isVerify) onResult, VoidCallback? onDismiss) async {
    //获取线上版本号
    VersionInfoModel? versionInfo = await CommonViewModel.getVersionInfo();
    if (isAvailable(versionInfo?.latestVersion) && versionInfo!.latestVersion!.contains('.')) {
      //获取本地版本号
      final info = await PackageInfo.fromPlatform();
      String localVersion = info.version;
      //进行版本比较
      bool isNeedUpdate = isVersionNeedUpdate(versionInfo.latestVersion!, localVersion);
      if (isNeedUpdate) {
        bool isForceUpdate = versionInfo.forceUpdate == true;
        //弹出升级提示
        showBookBottomSheet(
            context,
            VersionUpdateSheet(
                forceUpdate: isForceUpdate,
                onUpdateTap: () async {
                  //跳转到app市场
                  await ShareManager.instance.launchLinkUrl(appStoreAppUrl);
                }),
            HexColor("#F4F5F6"),
            isDismissible: false,
            enableDrag: false,
            onDismiss: onDismiss);
      }

      //是否是审核版本
      String buildNumber = info.buildNumber;
      bool isVerify = (versionInfo.verifyBuildCode == int.parse(buildNumber));
      await SpUtil.spSetVerifyStatus(isVerify);

      onResult(isVerify);
    } else {
      await SpUtil.spSetVerifyStatus(false);

      onResult(false);
    }
  }

  //TODO: 任务开关查询
  Future<void> getSwitchByCode() async {
    isSignInSwitchOn = await CommonViewModel.getSwitchByCode(taskSwitchSignIn);
  }

  ///添加到书架&收藏
  Future<void> addBookToLibrary(
      int? bookId, int dataType, Function(bool isSuccess) onResult) async {
    Map<String, dynamic> arguments = {"bookId": bookId ?? 0, "dataType": dataType};
    NormalModel? model = await LibraryViewModel.addBookToLibrary(arguments);
    if (model?.code == 200) {
      final NovelReadStatusController novelReadStatusController =
          findGetXInstance(NovelReadStatusController());
      if (dataType == 1) {
        novelReadStatusController.setAddLibrary(true);
      } else {
        if (dataType == 2) {
          novelReadStatusController.setLiked(true);
          novelReadStatusController.updateLikes(true);
        }
      }

      onResult(true);
    } else {
      onResult(false);
    }
  }

  ///从书架移除
  Future<void> cancelBookFromLibrary(int? bookId, int dataType, Function(bool isSuccess) onResult) async {
    Map<String, dynamic> arguments = {"bookId": bookId ?? 0, "dataType": dataType};
    NormalModel? model = await LibraryViewModel.cancelBookFromLibrary(arguments);
    if (model?.code == 200) {
      final NovelReadStatusController novelReadStatusController =
          findGetXInstance(NovelReadStatusController());

      if (dataType == 1) {
        novelReadStatusController.setAddLibrary(false);
      } else {
        if (dataType == 2) {
          novelReadStatusController.setLiked(false);
          novelReadStatusController.updateLikes(false);
        }
      }

      onResult(true);
    } else {
      onResult(false);
    }
  }
}
