import 'dart:ui';
import 'package:json_annotation/json_annotation.dart';
import '../enum.dart';
part 'novel_preferences_settings.g.dart';

@JsonSerializable()
class NovelPreferencesSettings {
  String key = readPreferenceKey; //阅读偏好设置的key
  String? themeColor; //主题色
  String? titleColor; //字体颜色
  double? fontSize; //字体大小
  double? lineSpace; //行间距
  double? horizontalPadding; //左右边距
  FontType? fontType; //字体类型 有衬线和无衬线
  String? fontFamily; //字体名称
  ReadingMode? readingMode; //阅读模式
  @JsonKey(includeFromJson: false, includeToJson: false)
  Size? illustrationSize; //插图尺寸

  NovelPreferencesSettings(
      {this.themeColor,
      this.titleColor,
      this.fontSize,
      this.lineSpace,
      this.horizontalPadding,
      this.fontType,
      this.fontFamily,
      this.readingMode,
      this.illustrationSize});

  factory NovelPreferencesSettings.fromJson(Map<String, dynamic> json) =>
      _$NovelPreferencesSettingsFromJson(json);

  Map<String, dynamic> toJson() => _$NovelPreferencesSettingsToJson(this);

  static const String readPreferenceKey = "read_preference_key";
}
