import 'package:realm/realm.dart';
part 'novel_read_chapter.realm.dart';

@RealmModel()
class $NovelReadChapterModel {
  @PrimaryKey()
  int? bookId; //书籍ID
  int? chapterId; //当前阅读章节ID
  int? chapterIndex;//章节索引
  int? pageIndex; //当前阅读页索引
}


@RealmModel()
class $NovelReadRecentlyModel {
  @PrimaryKey()
  int? bookId; //书籍ID
  int? chapterId; //当前阅读章节ID
  int? chapterIndex;//章节索引
  int? pageIndex; //当前阅读页索引
}