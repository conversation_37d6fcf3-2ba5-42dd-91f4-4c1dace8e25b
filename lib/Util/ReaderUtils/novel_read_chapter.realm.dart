// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'novel_read_chapter.dart';

// **************************************************************************
// RealmObjectGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
class NovelReadChapterModel extends $NovelReadChapterModel
    with RealmEntity, RealmObjectBase, RealmObject {
  NovelReadChapterModel(
    int? bookId, {
    int? chapterId,
    int? chapterIndex,
    int? pageIndex,
  }) {
    RealmObjectBase.set(this, 'bookId', bookId);
    RealmObjectBase.set(this, 'chapterId', chapterId);
    RealmObjectBase.set(this, 'chapterIndex', chapterIndex);
    RealmObjectBase.set(this, 'pageIndex', pageIndex);
  }

  NovelReadChapterModel._();

  @override
  int? get bookId => RealmObjectBase.get<int>(this, 'bookId') as int?;
  @override
  set bookId(int? value) => RealmObjectBase.set(this, 'bookId', value);

  @override
  int? get chapterId => RealmObjectBase.get<int>(this, 'chapterId') as int?;
  @override
  set chapterId(int? value) => RealmObjectBase.set(this, 'chapterId', value);

  @override
  int? get chapterIndex =>
      RealmObjectBase.get<int>(this, 'chapterIndex') as int?;
  @override
  set chapterIndex(int? value) =>
      RealmObjectBase.set(this, 'chapterIndex', value);

  @override
  int? get pageIndex => RealmObjectBase.get<int>(this, 'pageIndex') as int?;
  @override
  set pageIndex(int? value) => RealmObjectBase.set(this, 'pageIndex', value);

  @override
  Stream<RealmObjectChanges<NovelReadChapterModel>> get changes =>
      RealmObjectBase.getChanges<NovelReadChapterModel>(this);

  @override
  Stream<RealmObjectChanges<NovelReadChapterModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<NovelReadChapterModel>(this, keyPaths);

  @override
  NovelReadChapterModel freeze() =>
      RealmObjectBase.freezeObject<NovelReadChapterModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'bookId': bookId.toEJson(),
      'chapterId': chapterId.toEJson(),
      'chapterIndex': chapterIndex.toEJson(),
      'pageIndex': pageIndex.toEJson(),
    };
  }

  static EJsonValue _toEJson(NovelReadChapterModel value) => value.toEJson();
  static NovelReadChapterModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'bookId': EJsonValue bookId,
      } =>
        NovelReadChapterModel(
          fromEJson(ejson['bookId']),
          chapterId: fromEJson(ejson['chapterId']),
          chapterIndex: fromEJson(ejson['chapterIndex']),
          pageIndex: fromEJson(ejson['pageIndex']),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(NovelReadChapterModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(ObjectType.realmObject, NovelReadChapterModel,
        'NovelReadChapterModel', [
      SchemaProperty('bookId', RealmPropertyType.int,
          optional: true, primaryKey: true),
      SchemaProperty('chapterId', RealmPropertyType.int, optional: true),
      SchemaProperty('chapterIndex', RealmPropertyType.int, optional: true),
      SchemaProperty('pageIndex', RealmPropertyType.int, optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}

class NovelReadRecentlyModel extends $NovelReadRecentlyModel
    with RealmEntity, RealmObjectBase, RealmObject {
  NovelReadRecentlyModel(
    int? bookId, {
    int? chapterId,
    int? chapterIndex,
    int? pageIndex,
  }) {
    RealmObjectBase.set(this, 'bookId', bookId);
    RealmObjectBase.set(this, 'chapterId', chapterId);
    RealmObjectBase.set(this, 'chapterIndex', chapterIndex);
    RealmObjectBase.set(this, 'pageIndex', pageIndex);
  }

  NovelReadRecentlyModel._();

  @override
  int? get bookId => RealmObjectBase.get<int>(this, 'bookId') as int?;
  @override
  set bookId(int? value) => RealmObjectBase.set(this, 'bookId', value);

  @override
  int? get chapterId => RealmObjectBase.get<int>(this, 'chapterId') as int?;
  @override
  set chapterId(int? value) => RealmObjectBase.set(this, 'chapterId', value);

  @override
  int? get chapterIndex =>
      RealmObjectBase.get<int>(this, 'chapterIndex') as int?;
  @override
  set chapterIndex(int? value) =>
      RealmObjectBase.set(this, 'chapterIndex', value);

  @override
  int? get pageIndex => RealmObjectBase.get<int>(this, 'pageIndex') as int?;
  @override
  set pageIndex(int? value) => RealmObjectBase.set(this, 'pageIndex', value);

  @override
  Stream<RealmObjectChanges<NovelReadRecentlyModel>> get changes =>
      RealmObjectBase.getChanges<NovelReadRecentlyModel>(this);

  @override
  Stream<RealmObjectChanges<NovelReadRecentlyModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<NovelReadRecentlyModel>(this, keyPaths);

  @override
  NovelReadRecentlyModel freeze() =>
      RealmObjectBase.freezeObject<NovelReadRecentlyModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'bookId': bookId.toEJson(),
      'chapterId': chapterId.toEJson(),
      'chapterIndex': chapterIndex.toEJson(),
      'pageIndex': pageIndex.toEJson(),
    };
  }

  static EJsonValue _toEJson(NovelReadRecentlyModel value) => value.toEJson();
  static NovelReadRecentlyModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'bookId': EJsonValue bookId,
      } =>
        NovelReadRecentlyModel(
          fromEJson(ejson['bookId']),
          chapterId: fromEJson(ejson['chapterId']),
          chapterIndex: fromEJson(ejson['chapterIndex']),
          pageIndex: fromEJson(ejson['pageIndex']),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(NovelReadRecentlyModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(ObjectType.realmObject, NovelReadRecentlyModel,
        'NovelReadRecentlyModel', [
      SchemaProperty('bookId', RealmPropertyType.int,
          optional: true, primaryKey: true),
      SchemaProperty('chapterId', RealmPropertyType.int, optional: true),
      SchemaProperty('chapterIndex', RealmPropertyType.int, optional: true),
      SchemaProperty('pageIndex', RealmPropertyType.int, optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}
