import 'dart:ui';

import '../deviceScreenUtil.dart';
import '../enum.dart';
import '../stringUtil.dart';

class ReaderUtil {
  static ReaderUtil instance = ReaderUtil();

  //头部，底部设置菜单的高度
  static double menuHeight = 70;

  //阅读标题的高度
  static double titlePaddingTop = DeviceScreenUtil.instance.topSafeHeight + 40;
  static double titleHeight = 30;

  //阅读部分的padding
  static double pageTopOffset = 70;
  static double pageLeftOffset = 25;
  static double pageRightOffset = 25;
  static double pageBottomOffset =
      ReaderUtil.pageNumberPaddingBottom + ReaderUtil.pageNumberHeight + 10;

  //底部页码的高度
  static double pageNumberHeight = 20;
  static double pageNumberPaddingBottom = DeviceScreenUtil.instance.bottomSafeHeight + 10;

  //章节阅读完毕后的视图
  static double chapterEndViewHeight = 194;

  //todo:阅读页默认设置
  /// 阅读页背景主题颜色
  String themeColor = "#FFFFFF";

  /// 阅读页字体颜色
  String titleColor = "#000000";

  /// 阅读页字体大小
  double fontSize = 19;

  /// 阅读页字体粗细
  FontWeight fontWeight = FontWeight.normal;

  /// 阅读页行距
  double lineSpace = 1.4;

  /// 阅读页左右行距
  double horizontalPadding = 25.0;

  /// 阅读页字体名，默认无衬线字体
  FontType fontType = FontType.sansSerifed;

  /// 阅读页字体名
  String fontFamily = "NotoSans";

  /// 阅读页阅读模式
  ReadingMode readingMode = ReadingMode.verticalScroll;

  getReadingMode() {
    if (readingMode == ReadingMode.pageFlip) {
      return readModeFlip;
    } else if (readingMode == ReadingMode.verticalScroll) {
      return readModeVerticalScroll;
    } else {
      return readModeHorizontalScroll;
    }
  }
}
