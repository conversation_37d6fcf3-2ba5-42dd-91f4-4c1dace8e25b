import 'dart:convert';

import 'package:UrNovel/Util/Common/CommonManager/common_manager.dart';
import 'package:UrNovel/Util/ReaderUtils/readerUtil.dart';
import 'package:UrNovel/Util/logUtil.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

import '../../MainPage/NovelRead/Model/illustration_rule_model.dart';
import '../enum.dart';

List<Map<String, int>> pageConfig = [];

const String ruleString = r'#\{[^}]*\}#?';
double illustrationRatio = 0.75;
Size contentSize = Size(0, 0);

class NovelPageAgent {
  /// 分页逻辑
  /// content: 输入文本
  /// height: 页面高度
  /// width: 页面宽度
  /// textStyle: 文本样式
  /// firstShowLineCount: 首次显示的行数
  static Map<String, dynamic> paginateText(
      String content, double height, double width, TextStyle textStyle,
      {int? firstShowLineCount}) {
    contentSize = Size(width, height);

    return paginateTextWithWords(content, height, width, textStyle,
        firstShowLineCount: firstShowLineCount);
  }

  ///分页算法1 Offset分页(阿拉伯等rtl类型语言偶尔会出现比符号单独占一行的情况)
  static Map<String, dynamic> paginateTextWithOffset(
      String content, double height, double width, TextStyle textStyle,
      {int? firstShowLineCount}) {
    List<String> pages = [];
    TextPainter textPainter = TextPainter(
      textDirection:
          CommonManager.instance.isContentReverse() ? TextDirection.rtl : TextDirection.ltr,
      text: TextSpan(text: content, style: textStyle),
      textAlign: CommonManager.instance.isContentReverse() ? TextAlign.right : TextAlign.left,
    );

    // 将输入文本拆分为行
    textPainter.layout(maxWidth: width);
    int lineCount = textPainter.computeLineMetrics().length;

    // 分页逻辑
    double currentHeight = 0.0;
    String currentPage = '';
    double firstShowLineHeight = 0.0;

    for (var i = 0; i < lineCount; i++) {
      LineMetrics lineMetrics = textPainter.computeLineMetrics()[i];
      // 获取当前行的起始位置
      int start = textPainter.getPositionForOffset(Offset(0, lineMetrics.baseline)).offset;
      // 获取下一行的起始位置，或文本的结束位置
      int end = (i + 1 < lineCount)
          ? textPainter
              .getPositionForOffset(Offset(0, textPainter.computeLineMetrics()[i + 1].baseline))
              .offset
          : content.length; // 对于最后一行，使用整个文本的长度
      String lineText = content.substring(start, end);

      // 模拟将行添加到页面
      if (currentHeight + lineMetrics.height > height) {
        // 如果添加当前行会超出页面高度，将当前页添加到列表并重置
        pages.add(currentPage);

        // 如果当前章节是收费章节，则只展示一页
        if (isAvailable(firstShowLineCount) && 0 < firstShowLineCount!) {
          currentPage = "";
          break;
        } else {
          currentHeight = lineMetrics.height;
          currentPage = lineText; // 开始新一页
        }
      } else {
        // 如果当前章节是收费章节，获取前firstShowLineCount行的高度
        if (isAvailable(firstShowLineCount) && 0 < firstShowLineCount!) {
          if (i < firstShowLineCount) {
            currentPage += lineText; // 继续添加行
            currentHeight += lineMetrics.height; // 更新当前高度
            firstShowLineHeight = currentHeight;
          } else {
            break;
          }
        } else {
          currentPage += lineText; // 继续添加行
          currentHeight += lineMetrics.height; // 更新当前高度
        }
      }
    }

    // 添加最后一页
    if (isAvailable(currentPage)) {
      pages.add(currentPage);
      if (height - ReaderUtil.chapterEndViewHeight <= currentHeight) {
        pages.add("");
      }
    }

    return {
      "pages": pages,
      "firstShowLineHeight": 62.0 < firstShowLineHeight ? 62.0 : firstShowLineHeight
    };
  }

  /// 分页算法2 Word分页(适用于所有类型语言)
  static Map<String, dynamic> paginateTextWithWords(
      String content, double height, double width, TextStyle textStyle,
      {int? firstShowLineCount}) {
    List<String> pages = [];
    TextPainter textPainter = TextPainter(
      textDirection:
          CommonManager.instance.isContentReverse() ? TextDirection.rtl : TextDirection.ltr,
      text: TextSpan(text: content, style: textStyle),
      textAlign: CommonManager.instance.isContentReverse() ? TextAlign.right : TextAlign.left,
    );

    //解析出所有单词
    List<String> words = splitArabicWords(content);
    String currentPage = '';
    double firstShowLineHeight = 0.0;
    ReadPictureFitType fitType = ReadPictureFitType.none;

    for (int i = 0; i < words.length; i++) {
      String word = words[i];
      if (word != '\n') {
        word = "$word ";
      } else {
        if (!isAvailable(currentPage)) {
          continue;
        } else {
          //如果是换行符，则将最后一个空格去掉
          currentPage = currentPage.trimRight();
        }
      }

      if (isAvailable(getRuleString(word))) {
        //去除规则部分
        String text = currentPage.replaceFirst(getRuleString(currentPage), '');
        //去除换行符
        text = text.trimRight();
        textPainter.text = TextSpan(text: text, style: textStyle);
        textPainter.layout(maxWidth: width);

        IllustrationRuleModel? model = parseRuleString(word);
        if (isAvailable(model?.w) && isAvailable(model?.h)) {
          illustrationRatio = model!.w! / model.h!;
        }

        double imgHeight = width / illustrationRatio + 10;

        if (height < (i == 0 ? 0 : textPainter.height) + imgHeight) {
          //插图不够放，直接跳过当前单词
          fitType = ReadPictureFitType.noSpace;
        } else if (height == (i == 0 ? 0 : textPainter.height) + imgHeight) {
          //插图不够放，直接跳过当前单词
          fitType = ReadPictureFitType.onlyFit;
        } else {
          //插图可以放下，继续添加单词
          fitType = ReadPictureFitType.hasSpace;
        }
      } else {
        // 如果插图跟文字混排，需要将插图地址移除再算文字高度
        String ruleWord = getRuleString(currentPage);
        if (isAvailable(ruleWord)) {
          String text = currentPage.replaceFirst(ruleWord, '');
          textPainter.text = TextSpan(text: text + word, style: textStyle);
          textPainter.layout(maxWidth: width);

          IllustrationRuleModel? model = parseRuleString(ruleWord);
          if (isAvailable(model?.w) && isAvailable(model?.h)) {
            illustrationRatio = model!.w! / model.h!;
          }
          double imgHeight = width / illustrationRatio + 25;
          if (height < textPainter.height + imgHeight) {
            //插图不够放，直接跳过当前单词
            fitType = ReadPictureFitType.noSpace;
          } else if (height == textPainter.height + imgHeight) {
            //插图不够放，直接跳过当前单词
            fitType = ReadPictureFitType.onlyFit;
          } else {
            //插图可以放下，继续添加单词
            fitType = ReadPictureFitType.hasSpace;
          }
        }
      }

      String testPage = currentPage + word;
      if (fitType == ReadPictureFitType.none) {
        textPainter.text = TextSpan(text: testPage, style: textStyle);
        textPainter.layout(maxWidth: width);
      } else if (fitType == ReadPictureFitType.hasSpace) {
        String text = testPage.replaceFirst(getRuleString(testPage), '');
        textPainter.text = TextSpan(text: text, style: textStyle);
        textPainter.layout(maxWidth: width);
      }

      var lines = textPainter.computeLineMetrics();
      if (height + 20 < textPainter.height) {
        pages.add(currentPage);
        currentPage = (word == '\n' ? '' : word);

        // 如果当前章节是收费章节，则只展示一页
        if (isAvailable(firstShowLineCount) && 0 < firstShowLineCount!) {
          break;
        }
      } else {
        // 如果当前章节是收费章节，获取前firstShowLineCount行的高度
        if (isAvailable(firstShowLineCount) && 0 < firstShowLineCount!) {
          if (lines.length < firstShowLineCount) {
            currentPage = !isAvailable(testPage) ? '' : testPage; // 继续添加行
            firstShowLineHeight = textPainter.height;
          } else {
            break;
          }
        } else {
          // 处理插图
          if (fitType == ReadPictureFitType.onlyFit) {
            pages.add(currentPage);
            currentPage = (word == '\n' ? '' : word);

            //重置fitType状态
            fitType = ReadPictureFitType.none;
          } else if (fitType == ReadPictureFitType.hasSpace) {
            currentPage = !isAvailable(testPage) ? '' : testPage; // 继续添加行
          } else if (fitType == ReadPictureFitType.noSpace) {
            //判定是否第一个单词就是规则，特殊处理
            if (i == 0) {
              pages.add(word);
              currentPage = '';
            } else {
              pages.add(currentPage);
              currentPage = (word == '\n' ? '' : word);
            }

            //重置fitType状态
            fitType = ReadPictureFitType.none;
          } else {
            // 继续添加行
            currentPage = testPage;
          }
        }
      }
    }

    // 添加最后一页
    if (isAvailable(currentPage)) {
      pages.add(currentPage);
      String ruleWord = getRuleString(currentPage);
      if (isAvailable(ruleWord)) {
        String text = currentPage.replaceFirst(ruleWord, '');
        textPainter.text = TextSpan(text: text, style: textStyle);
        textPainter.layout(maxWidth: width);

        IllustrationRuleModel? model = parseRuleString(ruleWord);
        if (isAvailable(model?.w) && isAvailable(model?.h)) {
          illustrationRatio = model!.w! / model.h!;
        }
        double imgHeight = width / illustrationRatio + 25;

        if (height - ReaderUtil.chapterEndViewHeight - imgHeight <= textPainter.height) {
          //非解锁状态才添加最后空白页
          if (!isAvailable(firstShowLineCount) || firstShowLineCount! <= 0) {
            pages.add("");
          }
        }
      } else {
        if (height - ReaderUtil.chapterEndViewHeight <= textPainter.height) {
          pages.add("");
        }
      }
    } else {
      pages.add("");
    }

    //针对最后一页换行符的特殊处理
    String lastPage = pages.last.replaceAll('\n', '');
    if (!isAvailable(lastPage)) {
      pages.last = "";
    } else {
      lastPage = lastPage.replaceAll(getRuleString(lastPage), '');
      if (!isAvailable(lastPage)) {
        //最后一页只有一张图片
        pages.add("");
      }
    }

    return {
      "pages": pages,
      "firstShowLineHeight": 62.0 < firstShowLineHeight ? 62.0 : firstShowLineHeight
    };
  }

  //规则字符串处理
  static IllustrationRuleModel? parseRuleString(String word) {
    try {
      String ruleStr = word.substring(word.indexOf("#{") + 1, word.lastIndexOf("}#") + 1);
      Map<String, dynamic> map = jsonDecode(ruleStr);
      IllustrationRuleModel ruleModel = IllustrationRuleModel.fromJson(map);

      return ruleModel;
    } catch (e) {
      logP("解析规则字符串失败：$e");
      return null;
    }
  }

  ///words分割正则
  static List<String> splitArabicWords(String content) {
    //正则分割更精准，但是正则规则太复杂，太耗性能
    // final reg = RegExp(
    //   //匹配特殊结构，不再依赖于非贪婪模式的复杂行为，而是直接告诉引擎：“从 #{ 开始，一直匹配到你第一次遇到 } 为止”。这在处理复杂或长字符串时，比 .*? 更稳定，更不容易出错
    //   r'#\{[^}]*\}#?|'
    //   // //匹配单词
    //   // r'["“”«»]*[\p{L}\p{N}]+[.,!?،"؟!؛\n\r“”«»]*|'
    //   // //匹配分隔符 对分隔符的规则增加了“排除项” (遇到这些字符，不把它们当作普通的分隔符，直接跳过它们)
    //   // r'[^\p{L}\p{N}.,!?،"؟!؛\n\r“”«»#{}#?]+',
    //   r'["“”«»‘’《【]*[\p{L}\p{N}]+[.,!?،؟!؛:;·、“”«»#{}#?"”’»》】]*|' // 前后标点+单词整体
    //   r'\n\r|' // 单独的换行符
    //   r'\s+|' // 单独的空格
    //   r'[^\p{L}\p{N}\n\r\s+]', // 其它分隔符（不包括字母、数字、换行、空格）
    //   unicode: true,
    // );

    // return reg.allMatches(content).map((m) => m.group(0)!).toList();

    // 首先根据换行符分割字符串
    List<String> parts = content.split('\n');
    // 然后根据空格分割每个部分
    List<String> words = [];
    for (String part in parts) {
      words.addAll(part.split(' ').where((w) => w.isNotEmpty));
      words.add('\n');
    }

    return words;
  }

  static String getRuleString(String value) {
    final reg = RegExp(ruleString);
    List<String> regStr = reg.allMatches(value).map((m) => m.group(0)!).toList();
    if (!isAvailable(regStr)) {
      return "";
    }

    return regStr.first;
  }
}
