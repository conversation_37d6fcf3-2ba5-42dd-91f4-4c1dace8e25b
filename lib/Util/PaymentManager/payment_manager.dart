import 'dart:async';
import 'dart:io';

import 'package:UrNovel/Util/SharedPreferences/shared_preferences.dart';
import 'package:UrNovel/Util/SheetAndAlter/toast.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_android/in_app_purchase_android.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:in_app_purchase_storekit/store_kit_wrappers.dart';

import '../../Launch&Login/Model/user_model.dart';
import '../../MainPage/Profile/Model/goods_info_model.dart';
import '../../MainPage/Profile/Model/order_validate_model.dart';
import '../../MainPage/Profile/ViewModel/ViewModel.dart';
import '../Common/Model/normal_model.dart';
import '../DataReportManager/event_name_config.dart';
import '../DataReportManager/event_report_manager.dart';
import '../enum.dart';
import '../logUtil.dart';
import '../tools.dart';

class PaymentManager {
  // 定义一个类型为 Function 的回调
  static final PaymentManager instance = PaymentManager._internal();

  PaymentManager._internal();

  final InAppPurchase _inAppPurchase = InAppPurchase.instance;

  // 监听更新
  late StreamSubscription<List<PurchaseDetails>> _subscription;

  List<ProductDetails> _products = []; //内购的商品对象集合
  // 商品对象
  GoodsListItem? _productItem;

  //本地商品
  GoodsListItem? _localProductItem;

  // 商品类型
  GoodsType? _goodsType;

  // 订单类型
  OrderType? _orderType;

  VoidCallback? _onPurchaseCanceled;
  Function(SubscribeVerifyStatus status, Map<String, dynamic>? purchaseInfo)?
      _onPurchaseValidateCallback;

  //初始化购买组件
  Future<void> initializeInAppPurchase() async {
    //监听购买的事件
    final Stream<List<PurchaseDetails>> purchaseUpdated = _inAppPurchase.purchaseStream;
    _subscription = purchaseUpdated.listen((purchaseDetailsList) {
      _listenToPurchaseUpdated(purchaseDetailsList);
    }, onDone: () async {
      _onPurchaseCanceled?.call();
      await _onClearObject();
      onClose();
    }, onError: (error) async {
      showToast("Error: $error");
      _onPurchaseCanceled?.call();
      await _onClearObject();
      onClose();
    });

    _initStoreInfo();
  }

  Future<void> _initStoreInfo() async {
    final bool isAvailable = await _inAppPurchase.isAvailable();

    if (isAvailable) {
      if (Platform.isIOS) {
        var iosPlatformAddition =
            _inAppPurchase.getPlatformAddition<InAppPurchaseStoreKitPlatformAddition>();
        await iosPlatformAddition.showPriceConsentIfNeeded();
        await iosPlatformAddition.setDelegate(ExamplePaymentQueueDelegate());
      }
    }
  }

  /// 内购的购买更新监听
  void _listenToPurchaseUpdated(List<PurchaseDetails> purchaseDetailsList) async {
    if (purchaseDetailsList.isEmpty) {
      return;
    }

    try {
      for (PurchaseDetails purchaseDetails in purchaseDetailsList) {
        await _dealPurchaseDetails(purchaseDetails);
        await Future.delayed(const Duration(seconds: 10));
      }
    } catch (e) {
      logP('购买监听订单处理失败：$e');
    }
  }

  Future<void> _dealPurchaseDetails(PurchaseDetails purchase) async {
    if (purchase.status == PurchaseStatus.pending) {
      // 等待支付完成
      _handlePending(purchase);
    } else {
      if (purchase.status == PurchaseStatus.purchased ||
          purchase.status == PurchaseStatus.restored) {
        //完成购买, 到服务器验证
        if (Platform.isAndroid) {
          var googleDetail = purchase as GooglePlayPurchaseDetails;
          await checkAndroidPayInfo(googleDetail);
        } else if (Platform.isIOS) {
          var appstoreDetail = purchase as AppStorePurchaseDetails;
          await checkApplePayInfo(appstoreDetail);
        }
      } else if (purchase.status == PurchaseStatus.error) {
        // 购买失败
        _handleError(purchase);
      } else if (purchase.status == PurchaseStatus.canceled) {
        // 取消支付
        _handleCancel(purchase);
      }
    }
  }

  /// Android支付成功的校验
  Future<void> checkAndroidPayInfo(GooglePlayPurchaseDetails googleDetail) async {
    logP("安卓支付交易ID为${googleDetail.purchaseID}");
    //先确定当前商品类型
    _getGoodsType(googleDetail.productID);
    await _getLocalOrder(googleDetail);
    await serviceOrderValidate(
        googleDetail,
        _productItem ?? _localProductItem,
        googleDetail.billingClientPurchase.orderId,
        _goodsType,
        _orderType ?? OrderType.personalCenter);
  }

  /// Apple支付成功的校验
  Future<void> checkApplePayInfo(AppStorePurchaseDetails purchaseDetails) async {
    logP("Apple支付交易ID为 ${purchaseDetails.purchaseID}");
    //先确定当前商品类型
    _getGoodsType(purchaseDetails.productID);
    await _getLocalOrder(purchaseDetails);
    await serviceOrderValidate(
        purchaseDetails,
        _productItem ?? _localProductItem,
        purchaseDetails.skPaymentTransaction.payment.applicationUsername,
        _goodsType,
        _orderType ?? OrderType.personalCenter);
  }

  void _getGoodsType(String productId) {
    if (!isAvailable(_goodsType)) {
      if (productId.contains('coins')) {
        _goodsType = GoodsType.purchaseCoins;
      } else if (productId.contains('diamonds')) {
        _goodsType = GoodsType.purchaseDiamonds;
      } else {
        _goodsType = GoodsType.subscriptionMembership;
      }
    }
  }

  //获取老订单
  Future<GooglePlayPurchaseDetails?> _getOldSubscription() async {
    GooglePlayPurchaseDetails? oldSubscription;
    if (Platform.isAndroid) {
      final InAppPurchaseAndroidPlatformAddition androidAddition =
          InAppPurchase.instance.getPlatformAddition<InAppPurchaseAndroidPlatformAddition>();
      QueryPurchaseDetailsResponse oldPurchaseDetailsQuery =
          await androidAddition.queryPastPurchases();

      for (var element in oldPurchaseDetailsQuery.pastPurchases) {
        if (element.status == PurchaseStatus.purchased) {
          oldSubscription = element;
        }
      }
    }

    return oldSubscription;
  }

  //todo: 本地商品处理部分(用于中断后验签数据上报)
  ///存储商品
  Future<void> _saveLocalOrder(GoodsListItem item) async {
    try {
      List? goodsList = await SpUtil.spGetPurchaseRecord();
      if (isAvailable(item.appStorePid) && isAvailable(item.salesMoney)) {
        Map<String, dynamic> goodsMap = {
          'appStorePid': item.appStorePid,
          'salesMoney': item.salesMoney
        };
        if (isAvailable(goodsList)) {
          goodsList?.add(goodsMap);
        } else {
          goodsList = [goodsMap];
        }

        await SpUtil.spSavePurchaseGoods(goodsList);
      }
    } catch (e) {
      logP("存储商品失败：$e");
    }
  }

  ///匹配商品
  Future<void> _getLocalOrder(PurchaseDetails purchaseDetails) async {
    try {
      List? goodsList = await SpUtil.spGetPurchaseRecord();
      if (!isAvailable(_productItem)) {
        if (isAvailable(goodsList)) {
          Map? goodsMap = goodsList
              ?.firstWhere((element) => element?['appStorePid'] == purchaseDetails.productID);
          _localProductItem = GoodsListItem();
          _localProductItem?.appStorePid = goodsMap?['appStorePid'];
          _localProductItem?.salesMoney = goodsMap?['salesMoney'];
        }
      }
    } catch (e) {
      logP("获取存储商品失败：$e");
    }
  }

  ///删除商品
  Future<void> _deleteLocalOrder(String productId) async {
    try {
      List? goodsList = await SpUtil.spGetPurchaseRecord();
      if (isAvailable(goodsList)) {
        if (goodsList!.contains(productId)) {
          goodsList.remove(productId);
          await SpUtil.spSavePurchaseGoods(goodsList);
        }
      }
    } catch (e) {
      logP("删除存储商品失败：$e");
    }
  }

  // TODO: 服务创建订单
  Future<void> createShopOrder(GoodsListItem item,
      {required GoodsType goodsType,
      int? bookId,
      int? chapterId,
      required OrderType orderType,
      required Function() pullPurchaseCallback,
      required VoidCallback onPurchaseCanceled,
      required Function(SubscribeVerifyStatus status, Map<String, dynamic>? purchaseInfo)
          onPurchaseValidateCallback}) async {
    _productItem = item;
    _goodsType = goodsType;
    _orderType = orderType;
    _onPurchaseCanceled = onPurchaseCanceled;
    _onPurchaseValidateCallback = onPurchaseValidateCallback;
    clearPendingPurchases();
    PaymentGateway paymentGateway =
        Platform.isIOS ? PaymentGateway.applePay : PaymentGateway.googlePay;
    // TODO: 创建订单
    NormalModel? orderModel = await ProfileViewModel.createShopOrder(
        item, paymentGateway, goodsType, bookId, chapterId, orderType);
    if (orderModel?.code == 200) {
      try {
        isPaymentReminderNeedShow = true;
        //todo: 存储本地订单信息
        await _saveLocalOrder(item);
        // TODO: 开始支付
        await PaymentManager.instance.payment(item.appStorePid, orderModel?.result, callback: () {
          // TODO: 支付掉起回调
          pullPurchaseCallback.call();
        });
      } catch (e) {
        logP("payment error：$e");
        onClose();
      }
    } else {
      showToast(orderModel?.msg ?? "order create failed");
      onPurchaseCanceled.call();
    }
  }

  /// 加载全部的商品
  Future<void> payment(String? productId, String orderId, {required Function() callback}) async {
    logP("请求商品id $productId");
    if (!isAvailable(productId)) {
      onClose();
      return;
    }

    final bool available = await _inAppPurchase.isAvailable();
    if (!available) {
      onClose();
      callback();
      return;
    }

    //获取商品详情
    List<String> kIds = [productId!];
    List<ProductDetails>? products = await getProductDetails(kIds);
    if (!isAvailable(products)) {
      onClose();
      callback();
      return;
    }

    logP("全部商品加载完成了，可以启动购买了,总共商品数量为：${products?.length}");

    await startPurchase(productId, orderId, callback: callback);
  }

  // 获取商品详情
  Future<List<ProductDetails>?> getProductDetails(List<String> kIds) async {
    final ProductDetailsResponse response = await _inAppPurchase.queryProductDetails(kIds.toSet());

    // logP("商品获取结果  ${response.productDetails}");
    if (response.productDetails.isEmpty) {
      showToast("product_not_found".tr);
      _onPurchaseValidateCallback?.call(SubscribeVerifyStatus.canceled, null);

      return null;
    }

    // 处理查询到的商品列表
    _products = response.productDetails;

    return _products;
  }

  // 启动购买过程
  Future<void> startPurchase(String productId, String orderId, {Function()? callback}) async {
    logP("购买的商品id为$productId");
    ProductDetails? productDetails = getProduct(productId);
    if (isAvailable(productDetails)) {
      logP("开始购买商品：title: ${productDetails!.title}  desc:${productDetails.description} "
          "price:${productDetails.price}  currencyCode:${productDetails.currencyCode}  currencySymbol:${productDetails.currencySymbol}");
      final PurchaseParam purchaseParam =
          PurchaseParam(productDetails: productDetails, applicationUserName: orderId);
      try {
        if (_goodsType == GoodsType.subscriptionMembership) {
          await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
        } else {
          await _inAppPurchase.buyConsumable(purchaseParam: purchaseParam);
        }

        if (callback != null) {
          callback();
        }
      } catch (e) {
        logP('Error starting purchase: $e');
        showToast('coin_error'.tr);
        // showToast(e.toString(), duration: 3);
        _onPurchaseCanceled?.call();
        await _onClearObject();
        await onClose();
      }
    } else {
      //商品信息为空
      await _onClearObject();
      await onClose();
      _onPurchaseCanceled?.call();
    }
  }

  Future<void> clearPendingPurchases() async {
    var transactions = await SKPaymentQueueWrapper().transactions();
    for (var skPaymentTransactionWrapper in transactions) {
      SKPaymentQueueWrapper().finishTransaction(skPaymentTransactionWrapper);
      AppStorePurchaseDetails? purchaseDetails = AppStorePurchaseDetails(
        purchaseID: skPaymentTransactionWrapper.transactionIdentifier,
        productID: skPaymentTransactionWrapper.payment.productIdentifier,
        verificationData: PurchaseVerificationData(
          localVerificationData: '',
          serverVerificationData: '',
          source: '',
        ),
        transactionDate: null,
        skPaymentTransaction: SKPaymentTransactionWrapper(
          payment: skPaymentTransactionWrapper.payment,
          transactionState: skPaymentTransactionWrapper.transactionState,
        ),
        status: PurchaseStatus.pending,
      );
      await _inAppPurchase.completePurchase(purchaseDetails);
    }
  }

  // TODO: 服务验证订单
  Future<void> serviceOrderValidate(PurchaseDetails purchaseDetails, GoodsListItem? productItem,
      String? orderId, GoodsType? goodsType, OrderType? orderType) async {
    // TODO: 验证订单
    OrderValidateResultModel? resultModel = await ProfileViewModel.shopOrderValidate(
        purchaseDetails.verificationData.serverVerificationData,
        purchaseDetails.purchaseID,
        purchaseDetails.productID);

    ///订单购买状态，0-已购买，1-已取消，2-待定,3-过期，4-异常，5-匹配不到交易信息，6-退款，7-重复验签, 8-订单商品和验签商品不匹配, 9:同一笔三方订单正在验签中， 10:重复验签，无待处理订单
    if (resultModel?.purchaseState == 0) {
      // if (Platform.isAndroid) {
      //   //是用于处理消耗型商品的一个步骤。在应用内购买中，消耗型商品（如游戏中的金币、一次性道具等）可以被多次购买和使用。
      //   // 每次购买后，需要调用 consumePurchase 来标记该商品已被使用，以便用户可以再次购买
      //   if (purchaseDetails.productID == productItem?.appStorePid &&
      //       _goodsType != GoodsType.subscriptionMembership) {
      //     final InAppPurchaseAndroidPlatformAddition androidAddition =
      //         _inAppPurchase.getPlatformAddition<InAppPurchaseAndroidPlatformAddition>();
      //     await androidAddition.consumePurchase(purchaseDetails);
      //   }
      // }
      await _inAppPurchase.completePurchase(purchaseDetails);
      _onPurchaseValidateCallback?.call(SubscribeVerifyStatus.purchased,
          {'orderSnStr': orderId, 'salesMoney': productItem?.salesMoney});
      eventBusFire({'refreshProfile': true});
      await _deleteLocalOrder(purchaseDetails.productID);
      // TODO: 支付成功，上报数据
      await paymentSuccessReport(productItem, orderId, goodsType, orderType);

      await _onClearObject();
      await onClose();

      if (goodsType == GoodsType.purchaseCoins) {
        showToast("coin_success".tr);
      } else if (goodsType == GoodsType.purchaseDiamonds) {
        showToast('diamond_recharge'.tr);
      } else {
        showToast("already_subscribed".tr);
      }
    } else if (resultModel?.purchaseState != 9) {
      if (resultModel?.purchaseState == 10) {
        await _inAppPurchase.completePurchase(purchaseDetails);
        await _deleteLocalOrder(purchaseDetails.productID);
      }
      _onPurchaseValidateCallback?.call(SubscribeVerifyStatus.canceled, null);
      await _onClearObject();
      await onClose();
    }
  }

  //根据产品ID获取产品信息
  ProductDetails? getProduct(String productId) {
    if (isAvailable(_products)) {
      for (ProductDetails product in _products) {
        if (product.id == productId) {
          return product;
        }
      }
    }

    return null;
  }

  /// 恢复购买
  Future<void> restorePurchase(VoidCallback callBack) async {
    await _inAppPurchase.restorePurchases();
    callBack();
  }

  /// 购买失败
  Future<void> _handleError(PurchaseDetails purchase) async {
    String? orderId;
    if (purchase is AppStorePurchaseDetails) {
      orderId = purchase.skPaymentTransaction.payment.applicationUsername;
    } else if (purchase is GooglePlayPurchaseDetails) {
      orderId = purchase.billingClientPurchase.orderId;
    }

    try {
      dynamic details = purchase.error?.details;
      if (isAvailable(details) && details is Map) {
        int? code = details['NSUnderlyingError']?['code'];
        if (isAvailable(orderId) && purchase.status == PurchaseStatus.purchased) {
          if (code == 3532) {
            await _inAppPurchase.completePurchase(purchase);
            await _deleteLocalOrder(purchase.productID);
          } else {
            await serviceOrderValidate(purchase, _productItem, orderId, _goodsType, _orderType);
          }
        } else {
          await _deleteLocalOrder(purchase.productID);
        }
      } else {
        await _deleteLocalOrder(purchase.productID);
      }
      _onPurchaseCanceled?.call();
      await _onClearObject();
      await onClose();
    } catch (e) {
      logP("Error handling purchase: $e");
    }
  }

  /// 等待支付
  Future<void> _handlePending(PurchaseDetails purchase) async {
    logP("等待支付");
  }

  /// 支付错误
  Future<void> _handleCancel(PurchaseDetails purchase) async {
    try {
      showToast("cancel".tr);
      await _inAppPurchase.completePurchase(purchase);
      _onPurchaseCanceled?.call();
      await _deleteLocalOrder(purchase.productID);
      await _onClearObject();
      await onClose();
    } catch (e) {
      logP("Error handling purchase: $e");
    }
  }

  Future<void> disposeStore() async {
    await _onClearObject();

    if (isAvailable(_subscription)) {
      try {
        if (Platform.isIOS) {
          final InAppPurchaseStoreKitPlatformAddition iosPlatformAddition =
              _inAppPurchase.getPlatformAddition<InAppPurchaseStoreKitPlatformAddition>();
          iosPlatformAddition.setDelegate(null);
        }
        // 订阅已取消
        await _subscription.cancel();
      } on Exception catch (e) {
        logP("Error closing subscription: $e");
      }
    }
  }

  Future<void> onClose() async {
    isPaymentReminderNeedShow = false;
  }

  Future<void> _onClearObject() async {
    _productItem = null;
    _goodsType = null;
    _orderType = null;
    _products = [];
    _onPurchaseCanceled = null;
    _onPurchaseValidateCallback = null;
  }

  //TODO: 支付成功-数据上报
  Future<void> paymentSuccessReport(GoodsListItem? productItem, String? orderId,
      GoodsType? goodsType, OrderType? orderType) async {
    // 支付成功，上报数据
    UserInfoModel? userInfoModel = await SpUtil.spGetUserInfo();

    if (goodsType == GoodsType.purchaseCoins) {
      /// 购买金币
      if (userInfoModel?.newUser == true) {
        await EventReportManager.eventReportOfCommon(addToWishlist,
            parameters: {'af_currency': 'USD', 'af_revenue': productItem?.salesMoney ?? 0});
        await EventReportManager.logAddToWishlist(
            id: orderId ?? "", type: "coin", currency: 'USD', price: productItem?.salesMoney ?? 0);
      }

      await EventReportManager.eventReportOfCommon(checkout,
          parameters: {'af_currency': 'USD', 'af_revenue': productItem?.salesMoney ?? 0});
      await EventReportManager.logInitiatedCheckout(
          productItem?.salesMoney, 'USD', 'Coins', orderId, 1, true);
    } else {
      /// 订阅会员
      if (userInfoModel?.newUser == true) {
        await EventReportManager.eventReportOfCommon(spendCredit,
            parameters: {'af_currency': 'USD', 'af_revenue': productItem?.salesMoney ?? 0});
        await EventReportManager.eventReportOfFacebook(fbSpendCredit,
            parameters: {'content_type': 'subscription', 'content': productItem?.title ?? ""});
      }

      await EventReportManager.eventReportOfCommon(subscribe,
          parameters: {'af_currency': 'USD', 'af_revenue': productItem?.salesMoney ?? 0});
      await EventReportManager.logSubscribe(orderId: orderId ?? "");
    }

    if (userInfoModel?.newUser == true) {
      await EventReportManager.eventReportOfCommon(rate,
          parameters: {'af_currency': 'USD', 'af_revenue': productItem?.salesMoney ?? 0});
      await EventReportManager.logRated(valueToSum: productItem?.salesMoney ?? 0);
      await EventReportManager.eventReportOfAppsflyer(completeTutoria,
          parameters: {'af_currency': 'USD', 'af_revenue': productItem?.salesMoney ?? 0});
    }

    await EventReportManager.eventReportOfCommon(purchase,
        parameters: {'af_currency': 'USD', 'af_revenue': productItem?.salesMoney ?? 0});

    await EventReportManager.logPurchase(
      amount: productItem?.salesMoney ?? 0,
      currency: 'USD',
    );
  }
}

/// Example implementation of the
/// [`SKPaymentQueueDelegate`](https://developer.apple.com/documentation/storekit/skpaymentqueuedelegate?language=objc).
/// 设置的委托应实现SKPaymentQueueDelegateWrapper和handleshouldContinueTransaction和shouldShowPriceConsent。
/// 将shouldShowPriceConsent设置为false时，默认弹出窗口将不会显示，应用程序需要稍后显示。
class ExamplePaymentQueueDelegate implements SKPaymentQueueDelegateWrapper {
  @override
  bool shouldContinueTransaction(
      SKPaymentTransactionWrapper transaction, SKStorefrontWrapper storefront) {
    return true;
  }

  @override
  bool shouldShowPriceConsent() {
    return false;
  }
}
