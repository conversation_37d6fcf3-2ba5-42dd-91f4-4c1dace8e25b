import 'dart:async';
import 'dart:io';

import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';

import '../SheetAndAlter/toast.dart';
import '../StatusManagement/status_management.dart';
import '../ThirdSdkManger/third_manger.dart';
import '../logUtil.dart';

class PermissionManager {
  static final PermissionManager instance = PermissionManager._internal();

  PermissionManager._internal();

  // 网络状态
  final NetWorkStatusController _netWorkStatusController = Get.put(NetWorkStatusController());

  // 监听网络状态的变化
  late StreamSubscription<List<ConnectivityResult>> _connectivitySubscription;
  late ConnectivityResult _currentConnectivityResult;

  void startConnectivityListener() {
    _currentConnectivityResult = ConnectivityResult.none;
    Connectivity().onConnectivityChanged.listen(_updateConnectionStatus);
  }

  Future<void> stopConnectivityListener() async {
    await _connectivitySubscription.cancel();
  }

  // 网络状态改变时的回调方法
  void _updateConnectionStatus(ConnectivityResult result) {
    logP('网络状态改变: $result');
    _checkNetworkStatus(result);
  }

  //TODO: 网路权限申请
  Future<void> requestNetworkPermission() async {
    final ConnectivityResult connectivityResult = await (Connectivity().checkConnectivity());

    _checkNetworkStatus(connectivityResult);
  }

  void _checkNetworkStatus(ConnectivityResult connectivityResult) {
    if (connectivityResult == _currentConnectivityResult) {
      return;
    }
    _currentConnectivityResult = connectivityResult;

    if (connectivityResult == ConnectivityResult.mobile) {
      logP('current network is mobile');
    } else if (connectivityResult == ConnectivityResult.wifi) {
      logP('current network is wifi');
    } else if (connectivityResult == ConnectivityResult.ethernet) {
      logP('current network is ethernet');
    } else if (connectivityResult == ConnectivityResult.vpn) {
      logP('current network is vpn');
    } else if (connectivityResult == ConnectivityResult.bluetooth) {
      logP('current network is bluetooth');
    } else if (connectivityResult == ConnectivityResult.other) {
      showToast('no_network'.tr);
    } else if (connectivityResult == ConnectivityResult.none) {
      showToast('no_network'.tr);
    }

    _netWorkStatusController.updateConnectivityStatus(_currentConnectivityResult);
  }

  //TODO：请求通知权限
  static requestNotificationPermission({AuthorizationStatus? status}) async {
    await ThirdManger.requestNotificationPermission();
  }

  Future<AuthorizationStatus> getNotificationAuthorizationStatus() async {
    AuthorizationStatus status = await ThirdManger.getNotificationAuthorizationStatus();
    return status;
  }

  //TODO: 广告跟踪权限申请
  Future<TrackingStatus> requestTrackingTransparencyPermission() async {
    // 初始化请求的状态
    final trackingStatus = await AppTrackingTransparency.requestTrackingAuthorization();
    return trackingStatus;
  }

  Future<TrackingStatus> getTrackingAuthorizationStatus() async {
    final trackingStatus = await AppTrackingTransparency.trackingAuthorizationStatus;
    return trackingStatus;
  }

  //TODO: 前往设置中心
  Future<void> gotoSettingCenter() async {
    openAppSettings(); // 打开应用设置
  }

  /// 打开系统语言设置界面
  Future<void> openLanguageSettings() async {
    if (Platform.isAndroid) {
      const platform = MethodChannel('app_settings');
      try {
        await platform.invokeMethod('openLocaleSettings');
      } catch (e) {
        // fallback 方案
        await platform.invokeMethod('android.settings.LOCALE_SETTINGS');
      }
    } else if (Platform.isIOS) {
      if (await canLaunchUrl(Uri.parse('app-settings:'))) {
        await launchUrl(Uri.parse('app-settings:'));
      }
    }
  }
}
