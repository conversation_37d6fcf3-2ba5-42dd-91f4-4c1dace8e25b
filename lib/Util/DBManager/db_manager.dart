import 'package:get/get.dart';
import 'package:realm/realm.dart';
import 'package:UrNovel/Util/SheetAndAlter/toast.dart';

import 'package:UrNovel/Util/tools.dart';

import '../../MainPage/BookInfo/Model/BookDownLoad/download_book_detail_info_model.dart';
import '../../MainPage/NovelRead/Model/book_detailInfo_model.dart';
import '../../MainPage/NovelRead/Model/content_list_model.dart';
import '../ReaderUtils/novel_read_chapter.dart';
import '../logUtil.dart';

class DBManager {
  // TODO: Implement RealmDBManager
  // 单例实例
  static final DBManager instance = DBManager._internal();

  // 私有构造函数
  DBManager._internal() {
    init();
  }

  // Realm实例
  late final Realm realm;

  void init() {
    final Configuration config = Configuration.local(
      [
        NovelReadChapterModel.schema,
        DownloadBookDetailInfoModel.schema,
        DownloadChapterVoModel.schema,
        DownloadChapterVoiceModel.schema,
        DownloadContentModel.schema,
        NovelReadRecentlyModel.schema
      ],
      schemaVersion: 1, // 版本号逐步增加
      migrationCallback: (oldRealm, newRealm) {
        // 迁移逻辑
      },
    );
    realm = Realm(config);
  }

  //todo:阅读记录相关
  ///存储最近阅读的书籍信息
  void addRecentlyReadBook(NovelReadRecentlyModel model) {
    //移除旧的记录
    deleteAllObjects<NovelReadRecentlyModel>();
    //添加新的记录
    NovelReadRecentlyModel? result = addObject<NovelReadRecentlyModel>(model);
    if (isAvailable(result)) {
      logP("添加最近阅读章节记录成功");
    } else {
      logP("添加最近阅读章节记录失败");
    }
  }

  /// 获取最近阅读的书籍信息
  List<NovelReadRecentlyModel>? getRecentlyReadBook() {
    return getAllObjects<NovelReadRecentlyModel>();
  }

  ///添加阅读章节记录
  void addNovelReadBookChapter(NovelReadChapterModel model) {
    if (checkIsNovelExist<NovelReadChapterModel>(model.bookId)) {
      updateNovelReadBookChapter(model);
    } else {
      NovelReadChapterModel? result = addObject<NovelReadChapterModel>(model);
      if (isAvailable(result)) {
        logP("添加阅读章节记录成功");
      } else {
        logP("添加阅读章节记录失败");
      }
    }
  }

  ///通过书籍ID查找阅读章节记录
  NovelReadChapterModel? getNovelReadBookChapter(int? bookId) {
    if (isAvailable(bookId)) {
      return getObject<NovelReadChapterModel>(bookId!);
    }

    return null;
  }

  ///查询所有阅读章节记录
  List<NovelReadChapterModel>? getAllNovelReadBookChapter() {
    return getAllObjects<NovelReadChapterModel>();
  }

  ///更新阅读章节记录
  void updateNovelReadBookChapter(NovelReadChapterModel model) {
    NovelReadChapterModel? result = updateObject<NovelReadChapterModel>(model);
    if (isAvailable(result)) {
      logP("更新阅读章节记录成功");
    } else {
      logP("更新阅读章节记录失败");
    }
  }

  ///删除阅读章节记录
  void deleteNovelReadBookChapter(int bookId) {
    NovelReadChapterModel? model = getObject<NovelReadChapterModel>(bookId);
    if (isAvailable(model)) {
      deleteObject(model!);
    }
  }

  //todo:书籍下载相关
  ///书籍数据模型转换，转为Realm对象
  DownloadBookDetailInfoModel? novelDataTurnToRealmObject(
      BookDetailInfoResultModel? model) {
    DownloadBookDetailInfoModel chapterModel = DownloadBookDetailInfoModel(
        model?.bookId,
        nextBookId: model?.nextBookId,
        bookLangId: model?.bookLangId,
        authorName: model?.authorName,
        authorId: model?.authorId,
        authorCover: model?.authorCover,
        title: model?.title,
        cover: model?.cover,
        tagList: model?.tagList ?? [],
        reads: model?.reads,
        likes: model?.likes,
        time: model?.time,
        description: model?.description,
        library: model?.library,
        liked: model?.liked,
        bookGoldCount: model?.bookGoldCount,
        contentUrl: model?.contentUrl,
        lock: model?.lock,
        chapterVoList: model?.chapterVoList?.map((item) =>
                DownloadChapterVoModel(item?.id, //章节ID
                    bookId: item?.bookId,
                    content: item?.content,
                    chapterIndex: item?.chapterIndex,
                    title: item?.title,
                    lock: item?.lock,
                    cost: item?.cost,
                    prevId: item?.prevId,
                    nextId: item?.nextId,
                    lang: item?.lang,
                    chapterVoice: item?.chapterVoice?.map((item) =>
                            DownloadChapterVoiceModel(item?.id,
                                voiceName: item?.voiceName,
                                headImg: item?.headImg,
                                chapterId: item?.chapterId,
                                name: item?.name,
                                url: item?.url)) ??
                        [])) ??
            [],
        contentList: model?.contentList?.map((item) => DownloadContentModel(
                  item?.id,
                  chapterIndex: item?.chapterIndex,
                  content: item?.content,
                )) ??
            []);

    return chapterModel;
  }

  ///书籍数据模型转换，Realm转为普通对象
  BookDetailInfoResultModel? novelDataTurnToNormalObject(
      DownloadBookDetailInfoModel? chapterModel) {
    try {
      BookDetailInfoResultModel detailInfoModel = BookDetailInfoResultModel(
          bookId: chapterModel?.bookId,
          nextBookId: chapterModel?.nextBookId,
          bookLangId: chapterModel?.bookLangId,
          authorName: chapterModel?.authorName,
          authorId: chapterModel?.authorId,
          authorCover: chapterModel?.authorCover,
          title: chapterModel?.title,
          cover: chapterModel?.cover,
          tagList: chapterModel?.tagList,
          reads: chapterModel?.reads,
          likes: chapterModel?.likes,
          //点赞数
          time: chapterModel?.time,
          description: chapterModel?.description,
          library: chapterModel?.library,
          liked: chapterModel?.liked,
          bookGoldCount: chapterModel?.bookGoldCount,
          contentUrl: chapterModel?.contentUrl,
          lock: chapterModel?.lock,
          chapterVoList: chapterModel?.chapterVoList
              .map((item) => ChapterVoModel(
                  id: item.id,
                  //章节ID
                  bookId: item.bookId,
                  content: item.content,
                  chapterIndex: item.chapterIndex,
                  title: item.title,
                  lock: item.lock,
                  cost: item.cost,
                  prevId: item.prevId,
                  nextId: item.nextId,
                  lang: item.lang,
                  chapterVoice: item.chapterVoice
                      .map((item) => ChapterVoiceModel(
                          id: item.id,
                          voiceName: item.voiceName,
                          headImg: item.headImg,
                          chapterId: item.chapterId,
                          name: item.name))
                      .toList()))
              .toList(),
          contentList: chapterModel?.contentList
              .map((item) => ContentModel(
                  id: item.id,
                  chapterIndex: item.chapterIndex,
                  content: item.content))
              .toList());

      return detailInfoModel;
    } catch (e) {
      logP("书籍数据模型转换失败: $e");
      return null;
    }
  }

  ///章节数据模型转换，Realm转为普通对象
  ChapterVoModel? chapterDataTurnToNormalObject(
      DownloadChapterVoModel? chapterVoModel) {
    return ChapterVoModel(
        id: chapterVoModel?.id,
        bookId: chapterVoModel?.bookId,
        content: chapterVoModel?.content,
        chapterIndex: chapterVoModel?.chapterIndex,
        title: chapterVoModel?.title,
        lock: chapterVoModel?.lock,
        cost: chapterVoModel?.cost,
        prevId: chapterVoModel?.prevId,
        nextId: chapterVoModel?.nextId,
        lang: chapterVoModel?.lang,
        chapterVoice: chapterVoModel?.chapterVoice
            .map((item) => ChapterVoiceModel(
                  id: item.id,
                  voiceName: item.voiceName,
                  headImg: item.headImg,
                  chapterId: item.chapterId,
                  name: item.name,
                ))
            .toList());
  }

  ///添加下载书籍
  void addNovel(DownloadBookDetailInfoModel? model) {
    if (!isAvailable(model) ||
        checkIsNovelExist<DownloadBookDetailInfoModel>(model?.bookId)) {
      showToast(
          "The content of the novel is illegal or has already been downloaded.");
      return;
    }

    //添加书籍
    var result = addObject(model!);
    if (isAvailable(result)) {
      showToast("downloaded".tr);
    } else {
      showToast("Novel download failed");
    }
  }

  ///查询某一书籍
  DownloadBookDetailInfoModel? getNovel(int? bookId) {
    return getObject<DownloadBookDetailInfoModel>(bookId);
  }

  ///查询所有书籍
  List<DownloadBookDetailInfoModel>? getAllNovel() {
    return getAllObjects<DownloadBookDetailInfoModel>();
  }

  ///查询书籍所有章节
  List<DownloadChapterVoModel>? getNovelAllChapters(int? bookId) {
    if (isAvailable(bookId)) {
      return getObject<DownloadChapterVoModel>(bookId!)?.chapterVoList;
    }

    return null;
  }

  ///查询书籍某一章节
  DownloadChapterVoModel? getNovelChapter(int? chapterId) {
    try {
      if (isAvailable(chapterId)) {
        return getObject<DownloadChapterVoModel>(chapterId!);
      }
    } catch (e) {
      logP("查询书籍某一章节失败: $e");
    }

    return null;
  }

  ///todo:判断是否存在书籍
  bool checkIsNovelExist<T extends RealmObject>(int? bookId) {
    try {
      List<T>? novelList = getAllObjects<T>();
      if (isAvailable(novelList)) {
        for (T item in novelList!) {
          if (item is NovelReadChapterModel) {
            if (item.bookId == bookId) {
              return true;
            }
          } else if (item is DownloadBookDetailInfoModel) {
            if (item.bookId == bookId) {
              return true;
            }
          }
        }

        return false;
      }
    } catch (e) {
      logP("判断是否存在书籍失败: $e");
    }

    return false;
  }

  //todo:通用CRUD操作
  //todo：增加
  ///添加一个对象
  T addObject<T extends RealmObject>(T object) {
    return realm.write(() {
      return realm.add<T>(object);
    });
  }

  ///添加多个对象
  void addAllObjects<T extends RealmObject>(List<T> objects) {
    realm.write(() {
      realm.addAll<T>(objects);
    });
  }

  //todo:查询
  ///通过主节点查找对象
  getObject<T extends RealmObject>(dynamic key) {
    final model = realm.find<T>(key);

    return model;
  }

  ///查询所有对象
  List<T>? getAllObjects<T extends RealmObject>() {
    final results = realm.all<T>();

    if (isAvailable(results)) {
      return results.toList();
    }

    return null;
  }

  //todo:修改
  ///修改一个对象
  T? updateObject<T extends RealmObject>(T object) {
    return realm.write(() {
      return realm.add<T>(object, update: true);
    });
  }

  ///修改多个对象
  void updateObjects<T extends RealmObject>(List<T> objects) {
    realm.write(() {
      realm.addAll<T>(objects, update: true);
    });
  }

  //todo:删除
  ///删除单个对象
  void deleteObject<T extends RealmObject>(T object) {
    realm.write(() {
      realm.delete(object);
    });
  }

  ///删除多个对象
  void deleteManyObjects<T extends RealmObject>(List<T> objects) {
    realm.write(() {
      realm.deleteMany(objects);
    });
  }

  ///删除某一类型的所有对象
  void deleteAllObjects<T extends RealmObject>() {
    realm.write(() {
      realm.deleteAll<T>();
    });
  }

  //todo:重新打开数据库
  void reopen() {
    if (realm.isClosed) {
      init();
    }
  }

  //todo:刷新数据库
  void refresh() {
    if (realm.isClosed) {
      return;
    }

    realm.refresh();
  }

  //todo:关闭数据库
  void close() {
    if (realm.isClosed) {
      return;
    }

    realm.close();
  }
}
