import 'dart:io';

import 'package:UrNovel/Util/device.dart';
import 'package:UrNovel/Util/tools.dart';
import 'package:dio/dio.dart' as dio;
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../Launch&Login/Model/login_model.dart';
import '../Common/CommonManager/common_manager.dart';
import '../SharedPreferences/shared_preferences.dart';
import '../StatusManagement/status_management.dart';
import '../api_config.dart';
import '../enum.dart';
import '../genericUtil.dart';

typedef ProgressCallback = void Function(int count, int total);

class NetWorkManager extends GetxController {
  static final NetWorkManager _instance = NetWorkManager._internal();

  static NetWorkManager get instance => _instance;

  late dio.BaseOptions baseOptions;
  late dio.Dio _dio;
  late dio.CancelToken? _cancelToken;
  final BottomBarController bottomBarController =
      findGetXInstance(BottomBarController());

  // 定义 _internal 构造函数
  NetWorkManager._internal();

  // 初始化 Dio
  Future<void> initDio() async {
    baseOptions = await initBaseOptions();
    _dio = dio.Dio(baseOptions);
  }

  Future<dio.BaseOptions> initBaseOptions() async {
    dio.BaseOptions baseOptions = dio.BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 60),
      sendTimeout: const Duration(seconds: 60),
      receiveTimeout: const Duration(seconds: 60),
      headers: await getHeader(),
      // contentTypes: 'application/json; charset=utf-8',
      responseType: dio.ResponseType.json,
    );

    return baseOptions;
  }

  Future<void> refreshBaseOptions() async {
    baseOptions.headers = await getHeader();
  }

  static Future<Map<String, dynamic>> getHeader() async {
    LoginInfoModel? loginInfo = await SpUtil.spGetLoginInfo();
    String? deviceId = await Device.getDeviceId();
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    var headers = {
      "Content-Type": "application/json",
      "brandType": Platform.isIOS ? "UrNovel" : "UrNovel-Android",
      "timestamp": getCurrentTimeSeconds(),
      "SYSAGENTS": deviceId,
      "Authorization": "Bearer ${loginInfo?.token ?? ""}",
      "sign": "rewob312321khj",
      "language": isAvailable(CommonManager.instance.contentLanguage.code)
          ? CommonManager.instance.contentLanguage.code
          : "en",
      //内容语言
      "mobileLanguage":
          "${CommonManager.instance.deviceLocale?.languageCode}_${CommonManager.instance.deviceLocale?.countryCode}",
      //设备语言
      "skinLanguage":
          "${CommonManager.instance.skinLanguage.languageCode}_${CommonManager.instance.skinLanguage.countryCode}",
      //皮肤语言
      'version': packageInfo.version,
      'channel': Platform.isAndroid ? 1000 : 1001,
      //1000表示ANDROID  1001表示IOS
      'timeZone': DateTime.now().timeZoneName,
      //时区名
    };

    return headers;
  }

  // get 请求
  Future<dio.Response> get(String url,
      {required Map<String, dynamic> params,
      ProgressCallback? onReceiveProgress}) async {
    await refreshBaseOptions();
    try {
      return parseResponse(await _dio.get(url,
          queryParameters: params,
          cancelToken: _cancelToken = dio.CancelToken(),
          onReceiveProgress: onReceiveProgress));
    } catch (e) {
      return dio.Response(
          statusCode: 500,
          data: {'error': e.toString()},
          requestOptions: dio.RequestOptions(path: url));
    }
  }

  // post 请求
  Future<dio.Response> post(String url,
      {required Map<String, dynamic> parameters,
      ProgressCallback? onReceiveProgress}) async {
    await refreshBaseOptions();
    try {
      dio.Response response = await _dio.post(url,
          data: parameters,
          cancelToken: _cancelToken = dio.CancelToken(),
          onReceiveProgress: onReceiveProgress);
      if (url == apiDownloadS3File) {
        return response;
      } else {
        return parseResponse(response);
      }
    } catch (e) {
      return dio.Response(
          statusCode: 500,
          data: {'error': e.toString()},
          requestOptions: dio.RequestOptions(path: url));
    }
  }

  // put 请求
  Future<dio.Response> put(String url,
      {required Map<String, dynamic> data}) async {
    await refreshBaseOptions();
    try {
      return parseResponse(await _dio.put(url,
          data: data, cancelToken: _cancelToken = dio.CancelToken()));
    } catch (e) {
      return dio.Response(
          statusCode: 500,
          data: {'error': e.toString()},
          requestOptions: dio.RequestOptions(path: url));
    }
  }

  // delete 请求
  Future<dio.Response> delete(String url) async {
    await refreshBaseOptions();
    try {
      return parseResponse(await _dio.delete(url,
          cancelToken: _cancelToken = dio.CancelToken()));
    } catch (e) {
      return dio.Response(
          statusCode: 500,
          data: {'error': e.toString()},
          requestOptions: dio.RequestOptions(path: url));
    }
  }

  //下载json数据文件
  Future<dio.Response> downloadFile(
      String url, DownLoadResponseType responseType,
      {required ProgressCallback onReceiveProgress}) async {
    try {
      dio.ResponseType type = dio.ResponseType.json;
      if (responseType == DownLoadResponseType.json) {
        type = dio.ResponseType.json;
      } else if (responseType == DownLoadResponseType.stream) {
        type = dio.ResponseType.stream;
      } else if (responseType == DownLoadResponseType.plain) {
        type = dio.ResponseType.plain;
      } else if (responseType == DownLoadResponseType.bytes) {
        type = dio.ResponseType.bytes;
      }

      dio.Response response = await dio.Dio().get(url,
          options: dio.Options(responseType: type),
          cancelToken: _cancelToken = dio.CancelToken(),
          onReceiveProgress: onReceiveProgress);
      return response;
    } catch (e) {
      return dio.Response(
          statusCode: 500,
          data: {'error': e.toString()},
          requestOptions: dio.RequestOptions(path: url));
    }
  }

  // 取消请求
  void cancelRequests() {
    if (_cancelToken?.isCancelled == false) {
      _cancelToken?.cancel("request cancelled");
    }
  }

  dio.Response parseResponse(dio.Response response) {
    if (response.data['code'] == 401) {
      //todo: "refresh token"
      reLogin();
    } else if (response.data['code'] == 50007) {
      //todo: "login expired"
      reLogin();
    }

    return response;
  }

  //重新登陆
  Future<void> reLogin() async {
    bool isLogin = await SpUtil.isLogin();
    if (!isLogin) {
      return;
    }
    await SpUtil.clearLoginInfo();

    bottomBarController.changeIndex(0, false);
    Get.offAllNamed('/loginPage');
  }

  //刷新token
  Future<void> refreshToken() async {
    var loginInfo = await SpUtil.spGetLoginInfo();
    var params = {'refreshToken': loginInfo?.refreshToken};
    var response = await post(apiAuthRefreshToken, parameters: params);
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      LoginModel loginModel = LoginModel.fromJson(response.data);
      if (isAvailable(loginModel.loginInfo)) {
        // TODO: 登录成功后，保存token
        await SpUtil.spSetUserInfo(response.data);
      }
    } else {
      // TODO: 刷新token失败，重新登录
      reLogin();
    }
  }
}
