
//todo:appstore url这些········
//评分
const String appStoreRatingUrl     = 'https://itunes.apple.com/app/id6736648910?action=write-review';
const String appStoreAppUrl        = "https://apps.apple.com/cn/app/id6736648910";

//todo:google 登录配置
const String googleIosClientId     = "979084064090-arvjj65rsvnp4e44l62hd81uvhvsbpbe.apps.googleusercontent.com";
const String googleAndroidClientId = "979084064090-aruaouguns4c10jent6h3q80om6fjt5b.apps.googleusercontent.com";

//todo:Facebook
//App id
const String facebookAppId         = "564494579467840";
//Key
const String facebookKey           = "********************************";


//todo:链接
//隐私
const String privacyPolicyUrl      = "https://www.urnovel.com/privacy-policy";
//条款
const String termsOfServiceUrl     = "https://www.urnovel.com/terms-of-service";
//Apple用户协议
const String appleServicePolicyUrl = "https://www.apple.com/legal/internet-services/itunes/dev/stdeula";
//DMCA
const String dmca                  = "https://www.urnovel.com/dmca";
//follow instagram
const String followInstagramUrl    = "https://www.instagram.com/URNOVEL_app/";
//follow whatsapp
const String followWhatsappUrl     = "https://whatsapp.com/channel/0029VattxbiFCCoWnWLpcu1H";
//follow facebook
const String followFacebookUrl     = "https://www.facebook.com/profile.php?id=61567535649176";


//todo:排行榜类型
const String rankingTRENDING       = "TRENDING"; //热门榜
const String rankingNEWRELEASES    = "NEW_RELEASES"; //最新发布
const String rankingHOTSEARCHES    = "HOT_SEARCHES"; //热搜榜
const String rankingFAVORITES      = "FAVORITES"; //收藏榜
const String rankingDISCOVER       = "Discover"; //Discover栏目
const String rankingFEMALE         = "Female"; //Female栏目
const String rankingMALE           = "Male"; //Male栏目


//todo:本地文件存储
//首页banner数据
const String homeReadBannerFilePath = "homeReadBannerFilePath";
//首页书籍数据
const String homeReadBookFilePath   = "homeReadBookFilePath";


//todo:书籍详情页跳转类型
const String bookDetailJumpSearch   = "search"; //搜索页
const String bookDetailJumpOther    = ""; //其它


//todo:阅读偏好设置主键
const String readPreferenceKey        = "readPreferenceKey";
//todo:阅读模式
const String readModeFlip             = "pageFlip"; //单页翻页
const String readModeVerticalScroll   = "verticalScroll"; //竖向滚动
const String readModeHorizontalScroll = "horizontalScroll"; //横向滚动


//todo:分享渠道 facebook  instagram  whatsApp  copyLink
const String facebook      = "facebook";
const String instagram     = "instagram";
const String whatsApp      = "whatsApp";
const String copyLink      = "copyLink";


//todo:任务开关code
const String taskSwitchSignIn         = "sign_in_country";//签到