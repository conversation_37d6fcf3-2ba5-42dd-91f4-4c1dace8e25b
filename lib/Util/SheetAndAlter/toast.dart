import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

import '../Extensions/colorUtil.dart';
import '../logUtil.dart';
import '../deviceScreenUtil.dart';

void showToast(String msg, {int duration = 2, Color? textColor}) {
  try {
    Get.showSnackbar(
      GetSnackBar(
        messageText: Align(
            alignment: Alignment.center,
            child: Text(
              msg,
              style: TextStyle(
                  color: textColor ?? ColorsUtil.hexColor(0xFFFFFF),
                  fontSize: 17.0),
              textAlign: TextAlign.center,
            )),
        borderRadius: 12.0,
        backgroundColor: ColorsUtil.hexColor(0x000000, alpha: 0.7),
        duration: Duration(seconds: duration),
        margin:
            EdgeInsets.only(bottom: DeviceScreenUtil.instance.bottomSafeHeight + 80),
        padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 10.0),
        maxWidth: Get.width * 0.8,
        snackStyle: SnackStyle.FLOATING,
        snackPosition: SnackPosition.BOTTOM,
        titleText: null,
      ),
    );
  } catch (e) {
    logP(e.toString());
  }
}
