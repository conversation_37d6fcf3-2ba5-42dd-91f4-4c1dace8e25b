import 'package:UrNovel/BaseWidget/base_ful_widget.dart';
import 'package:UrNovel/BaseWidget/base_less_widget.dart';
import 'package:UrNovel/Util/Common/network_image_util.dart';
import 'package:UrNovel/Util/logUtil.dart';
import 'package:flutter/material.dart';
import 'package:flutter_card_swiper/flutter_card_swiper.dart';
import 'package:get/get.dart';

import '../../Launch&Login/Model/promotion_content_model.dart';
import '../../MainPage/Profile/Model/goods_info_model.dart';
import '../Common/Animation/number_animation.dart';
import '../DataReportManager/event_name_config.dart';
import '../DataReportManager/event_report_manager.dart';
import '../Extensions/colorUtil.dart';
import '../LocalNotificationManager/local_notification_manager.dart';
import '../StatusManagement/status_management.dart';
import '../enum.dart';
import '../genericUtil.dart';
import '../tools.dart';
import 'bottom_sheet.dart';

List<String> getReportList(ReportType type) {
  if (type == ReportType.bookDetail || type == ReportType.bookRead) {
    return [
      "spelling_errors".tr,
      "missing_content".tr,
      "disordered_chapters".tr,
      "poor_formatting".tr,
      "inappropriate_content".tr,
      "repeated_content".tr,
      "incorrect_book_info".tr,
      "technical_issues".tr,
      "copyright_concerns".tr,
      "translation_error".tr,
      "other_issues".tr,
    ];
  } else {
    return [
      "inappropriate_language".tr,
      "harassment_bullying".tr,
      "spam_advertising".tr,
      "offtopic_content".tr,
      "spoilers".tr,
      "hate_speech".tr,
      "personal_attacks".tr,
    ];
  }
}

late AnimationController _scaleController;
late Animation<double> _scaleAnimation;
late Animation<double> _buttonScaleAnimation;

void showAlter(Widget content,
    {Color backgroundColor = Colors.white,
    bool useSafeArea = true,
    Duration scaleDuration = const Duration(milliseconds: 500),
    VoidCallback? onDismiss,
    bool barrierDismissible = false}) {
  Get.dialog(
    Dialog(
      backgroundColor: backgroundColor,
      insetPadding: EdgeInsets.zero, // 去除对话框与屏幕边缘的间距// 保证内容不会溢出屏幕边缘
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12.0), // 应用与 Dialog 相同的圆角半径
        child: content,
      ), // 保证内容不会溢出屏幕边缘
    ),
    useSafeArea: useSafeArea, // 保证内容不会溢出屏幕边缘
    barrierDismissible: barrierDismissible, // 点击蒙层关闭对话框
  ).then((value) {
    onDismiss?.call();
  });
}

//todo: 搜索无结果反馈
class NoSearchResultAlter extends StatefulWidget {
  final Function(String content) onConfirm;

  const NoSearchResultAlter({super.key, required this.onConfirm});

  @override
  State<NoSearchResultAlter> createState() => _NoSearchResultAlterState();
}

class _NoSearchResultAlterState extends State<NoSearchResultAlter> {
  final TextEditingController controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    controller.text = "";
  }

  @override
  void dispose() {
    // TODO: implement dispose
    controller.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var mediaSize = MediaQuery.of(context).size;
    var alterWidth = mediaSize.width - 112;
    var contentHeight = (alterWidth - 28) / 1.46;
    return SizedBox(
        width: alterWidth,
        child: Column(
          mainAxisSize: MainAxisSize.min, // 设置主轴方向尺寸为最小,不设置会失控
          children: [
            const SizedBox(height: 35),
            Align(
              alignment: Alignment.center,
              child: Text(
                "describe_the_novel".tr,
                style: TextStyle(
                    fontSize: 18, color: HexColor('#000000'), fontWeight: FontWeight.bold),
              ),
            ),
            Container(
              height: contentHeight,
              margin: const EdgeInsets.only(top: 26, left: 14, right: 14),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10), color: HexColor('#ECEDF1')),
              child: TextField(
                // 设置为 null 以允许自动调整高度
                maxLines: null,
                controller: controller,
                decoration: InputDecoration(
                  hintText: 'enter_novel_detail'.tr,
                  hintStyle: TextStyle(fontSize: 11, color: HexColor('#787C87')),
                  labelStyle: TextStyle(fontSize: 11, color: HexColor('#000000')),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
                  // 设置左右边距
                  border: InputBorder.none, // 去掉边框
                ),
              ),
            ),
            Container(
              height: 48,
              margin: const EdgeInsets.only(top: 25),
              decoration: BoxDecoration(
                color: HexColor('#1B86FF'),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () {
                        Get.back(); // 关闭对话框
                      },
                      child: Text(
                        "cancel".tr,
                        style: TextStyle(fontSize: 16, color: HexColor('#FFFFFF')),
                      ),
                    ),
                  ),
                  Container(width: 0.5, height: 25, color: HexColor('#ECEDF1')),
                  Expanded(
                    child: TextButton(
                      onPressed: () {
                        Get.back(); // 关闭对话框
                        widget.onConfirm(controller.text);
                      },
                      child: Text(
                        "submit".tr,
                        style: TextStyle(fontSize: 16, color: HexColor('#FFFFFF')),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ));
  }
}

//todo: ReportError
class ReportErrorContent extends StatefulWidget {
  final ReportType reportType;
  final Function(List<dynamic> content) onConfirm;

  const ReportErrorContent({super.key, required this.reportType, required this.onConfirm});

  @override
  State<ReportErrorContent> createState() => _ReportContentState();
}

class _ReportContentState extends State<ReportErrorContent> {
  late double listHeight;
  final BookDetailReportController controller = findGetXInstance(BookDetailReportController());
  late List<String> reportList;

  @override
  initState() {
    super.initState();
    if (widget.reportType == ReportType.bookRead || widget.reportType == ReportType.bookDetail) {
      reportList = getReportList(widget.reportType);
    } else if (widget.reportType == ReportType.comment ||
        widget.reportType == ReportType.commentSecond) {
      reportList = getReportList(widget.reportType);
    }
    listHeight = reportList.length * 40.0;
  }

  @override
  void dispose() {
    // TODO: implement dispose
    controller.clearReport();
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var screenWidth = MediaQuery.of(context).size.width;
    return SizedBox(
      width: screenWidth - 84,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 24),
            child: Center(
              child: Text("report_error".tr,
                  style: TextStyle(
                      color: HexColor('#000000'), fontSize: 18.0, fontWeight: FontWeight.bold)),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 18, left: 14, right: 14),
            child: SizedBox(
              height: listHeight,
              child: ListView.builder(
                  itemCount: reportList.length,
                  itemBuilder: (context, index) {
                    return GestureDetector(
                      onTap: () {
                        if (mounted) {
                          setState(() {
                            controller.selected(reportList[index]);
                          });
                        }
                      },
                      child: Container(
                        height: 40,
                        width: double.infinity,
                        color: Colors.transparent,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(reportList[index],
                                style: TextStyle(
                                    fontSize: 13,
                                    color: HexColor("#000000"),
                                    fontWeight: FontWeight.bold)),
                            Image.asset(
                                controller.isSelected(reportList[index])
                                    ? 'assets/images/bookDetails/icon_choosed.png'
                                    : 'assets/images/bookDetails/icon_unchoose.png',
                                width: 16,
                                height: 16)
                          ],
                        ),
                      ),
                    );
                  }),
            ),
          ),
          const SizedBox(height: 15),
          Container(
            height: 55,
            decoration: BoxDecoration(color: HexColor("#1B86FF")),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                    child: TextButton(
                        onPressed: () {
                          Get.back();
                        },
                        child: Text("cancel".tr,
                            style: TextStyle(
                                fontSize: 16,
                                color: HexColor("#FFFFFF"),
                                fontWeight: FontWeight.bold)))),
                Container(width: 1, height: 25, color: HexColor("#E4E6E8")),
                Expanded(
                    child: TextButton(
                        onPressed: () {
                          Get.back();
                          widget.onConfirm(controller.reportList);
                        },
                        child: Text("submit".tr,
                            style: TextStyle(
                                fontSize: 16,
                                color: HexColor("#FFFFFF"),
                                fontWeight: FontWeight.bold)))),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

//todo: Redeem Code 兑换码
class RedeemCodeContent extends StatefulWidget {
  const RedeemCodeContent({super.key});

  @override
  State<RedeemCodeContent> createState() => _RedeemCodeContentState();
}

class _RedeemCodeContentState extends State<RedeemCodeContent> {
  late bool isRedeemable;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    isRedeemable = true;
  }

  @override
  Widget build(BuildContext context) {
    var screenWidth = MediaQuery.of(context).size.width;
    return SizedBox(
      width: screenWidth - 84,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(
            height: 18,
          ),
          Text("redeem_premium".tr,
              style:
                  TextStyle(fontSize: 18, color: HexColor("#8E6A31"), fontWeight: FontWeight.bold)),
          const SizedBox(
            height: 44,
          ),
          Container(
            height: 50,
            margin: const EdgeInsets.symmetric(horizontal: 15),
            decoration: BoxDecoration(
              color: HexColor("#F5F6F8"),
              borderRadius: BorderRadius.circular(10),
            ),
            child: TextField(
                style: TextStyle(
                    color: HexColor("#000000"), fontSize: 18, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
                decoration: InputDecoration(
                  hintText: "enter_code".tr,
                  hintStyle: TextStyle(color: HexColor("#787C87"), fontSize: 12),
                  border: InputBorder.none,
                  // 取消边框及下划线
                  focusedBorder: InputBorder.none,
                  // 取消聚焦时的边框及下划线
                  enabledBorder: InputBorder.none,
                  // 取消启用时的边框及下划线
                  errorBorder: InputBorder.none,
                  // 取消错误时的边框及下划线
                  disabledBorder: InputBorder.none, // 取消禁用时的边框及下划线
                )),
          ),
          SizedBox(height: isRedeemable ? 48 : 32),
          if (!isRedeemable)
            Text("Invalid or expired code",
                style: TextStyle(color: HexColor("#F42222"), fontSize: 14),
                textAlign: TextAlign.center),
          if (!isRedeemable) const SizedBox(height: 28),
          Container(
            width: double.infinity,
            height: 48,
            decoration: BoxDecoration(
              gradient: LinearGradient(colors: [HexColor("#ECCA95"), HexColor("#F4D8AC")]),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                    child: TextButton(
                        onPressed: () {
                          Get.back();
                        },
                        child: Text("cancel".tr,
                            style: TextStyle(
                                fontSize: 16,
                                color: HexColor("#704E1F"),
                                fontWeight: FontWeight.bold)))),
                Container(width: 1, height: 25, color: HexColor("#6F5127")),
                Expanded(
                    child: TextButton(
                        onPressed: () {
                          Get.back();
                        },
                        child: Text("redeem".tr,
                            style: TextStyle(
                                fontSize: 16,
                                color: HexColor("#704E1F"),
                                fontWeight: FontWeight.bold)))),
              ],
            ),
          )
        ],
      ),
    );
  }
}

//todo: 设置 删除账户
class DeleteAccountContent extends StatelessWidget {
  final VoidCallback onConfirm;

  const DeleteAccountContent({super.key, required this.onConfirm});

  @override
  Widget build(BuildContext context) {
    var screenWidth = MediaQuery.of(context).size.width;
    return SizedBox(
      width: screenWidth - 84,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(
            height: 20,
          ),
          Text("deletion_in_prograss".tr,
              style:
                  TextStyle(fontSize: 16, color: HexColor("#000000"), fontWeight: FontWeight.bold)),
          const SizedBox(
            height: 16,
          ),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                "scheduled_deleted".tr,
                style: TextStyle(color: HexColor("#000000"), fontSize: 13),
                maxLines: null,
              )),
          const SizedBox(height: 14),
          Divider(height: 1, color: HexColor('#E4E6E8')),
          SizedBox(
            width: double.infinity,
            height: 48,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                    child: TextButton(
                        onPressed: () {
                          onConfirm.call();
                          Get.back();
                        },
                        child: Text("no".tr,
                            style: TextStyle(
                                fontSize: 16,
                                color: HexColor("#1B86FF"),
                                fontWeight: FontWeight.bold)))),
                Container(width: 0.5, height: 47, color: HexColor("#E4E6E8")),
                Expanded(
                    child: TextButton(
                        onPressed: () {
                          Get.back();
                        },
                        child: Text("yes".tr,
                            style: TextStyle(
                                fontSize: 16,
                                color: HexColor("#1B86FF"),
                                fontWeight: FontWeight.bold)))),
              ],
            ),
          )
        ],
      ),
    );
  }
}

class StartNewChatAlert extends StatelessWidget {
  final VoidCallback onConfirm;

  const StartNewChatAlert({super.key, required this.onConfirm});

  @override
  Widget build(BuildContext context) {
    var screenWidth = MediaQuery.of(context).size.width;
    return SizedBox(
      width: screenWidth - 84,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(
            height: 20,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text("reset_confirm".tr,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  color: HexColor("#000000"),
                  // fontWeight: FontWeight.bold
                )),
          ),
          const SizedBox(
            height: 16,
          ),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                "reset_chat".tr,
                style: TextStyle(color: Color(0XFFFF0000), fontSize: 13),
                maxLines: null,
              )),
          const SizedBox(height: 14),
          Divider(height: 1, color: HexColor('#E4E6E8')),
          SizedBox(
            width: double.infinity,
            height: 48,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                    child: TextButton(
                        onPressed: () {
                          Get.back();
                          onConfirm.call();
                        },
                        child: Text("confirm".tr,
                            style: TextStyle(
                              fontSize: 16,
                              color: HexColor("#1B86FF"),
                              // fontWeight: FontWeight.bold
                            )))),
                Container(width: 0.5, height: 47, color: HexColor("#E4E6E8")),
                Expanded(
                    child: TextButton(
                        onPressed: () {
                          Get.back();
                        },
                        child: Text("cancel".tr,
                            style: TextStyle(
                              fontSize: 16,
                              color: HexColor("#4D4F56"),
                              // fontWeight: FontWeight.bold
                            )))),
              ],
            ),
          )
        ],
      ),
    );
  }
}

//todo: 设置 更改用户名
class ChangeProfileContent extends BaseFulWidget {
  final String title;
  final String? currentContent;
  final void Function(String content) onContentChanged;

  ChangeProfileContent(
      {super.key,
      required this.title,
      required this.currentContent,
      required this.onContentChanged});

  @override
  State<ChangeProfileContent> createState() => _ChangeProfileContentState();
}

class _ChangeProfileContentState extends State<ChangeProfileContent> {
  final TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _controller.text = widget.currentContent ?? "";
  }

  @override
  void dispose() {
    // TODO: implement dispose
    _controller.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var screenWidth = MediaQuery.of(context).size.width;
    return SizedBox(
      width: screenWidth - 84,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(
            height: 18,
          ),
          Text(widget.title,
              style:
                  TextStyle(fontSize: 18, color: HexColor("#000000"), fontWeight: FontWeight.bold)),
          const SizedBox(
            height: 44,
          ),
          Container(
            height: 50,
            margin: const EdgeInsets.symmetric(horizontal: 15),
            decoration: BoxDecoration(
              color: HexColor("#F5F6F8"),
              borderRadius: BorderRadius.circular(10),
            ),
            child: TextField(
                controller: _controller,
                style: TextStyle(
                    color: HexColor("#000000"), fontSize: 18, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
                decoration: InputDecoration(
                  hintText:
                      isAvailable(widget.currentContent) ? widget.currentContent : widget.title,
                  hintStyle: TextStyle(color: HexColor("#787C87"), fontSize: 12),
                  border: InputBorder.none,
                  // 取消边框及下划线
                  focusedBorder: InputBorder.none,
                  // 取消聚焦时的边框及下划线
                  enabledBorder: InputBorder.none,
                  // 取消启用时的边框及下划线
                  errorBorder: InputBorder.none,
                  // 取消错误时的边框及下划线
                  disabledBorder: InputBorder.none, // 取消禁用时的边框及下划线
                )),
          ),
          const SizedBox(height: 48),
          Container(
            width: double.infinity,
            height: 48,
            decoration: BoxDecoration(color: HexColor("#1B86FF")),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                    child: TextButton(
                        onPressed: () {
                          Get.back();
                        },
                        child: Text("cancel".tr,
                            style: TextStyle(
                                fontSize: 16,
                                color: HexColor("#FFFFFF"),
                                fontWeight: FontWeight.bold)))),
                Container(width: 1, height: 25, color: HexColor("#E4E6E8")),
                Expanded(
                    child: TextButton(
                        onPressed: () {
                          widget.onContentChanged(_controller.text);
                          Get.back();
                        },
                        child: Text("submit".tr,
                            style: TextStyle(
                                fontSize: 16,
                                color: HexColor("#FFFFFF"),
                                fontWeight: FontWeight.bold)))),
              ],
            ),
          )
        ],
      ),
    );
  }
}

//todo: 首页 read 书籍弹窗推荐
class ReadBookRecommendContent extends StatefulWidget {
  final bool isNormal;
  final List<PromotionContentItem>? contentList;

  const ReadBookRecommendContent({super.key, required this.isNormal, required this.contentList});

  @override
  State<ReadBookRecommendContent> createState() => _ReadBookRecommendContentState();
}

class _ReadBookRecommendContentState extends State<ReadBookRecommendContent> {
  late int _currentIndex;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    _currentIndex = 0;
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }

  Future<void> toNovelReadPage(PromotionContentItem? item) async {
    Get.back();
    // TODO: 跳转阅读页
    Map arguments = {"bookId": item?.bookId ?? 0, 'jumpType': BookDetailJumpType.other};

    //去往阅读页
    await LocalNotificationManager.toNovelReadPage(arguments);
    await EventReportManager.eventReportOfFirebase(clickPopup);
  }

  bool _onSwipe(
    int previousIndex,
    int? currentIndex,
    CardSwiperDirection direction,
  ) {
    setState(() {
      _currentIndex = currentIndex ?? 0;
    });

    return true;
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var width = mediaQuery.size.width * 0.675;
    var imgHeight = width / 0.75;
    var btHeight = width / 5.06;
    var count = widget.contentList?.length ?? 0;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: width,
          height: imgHeight + btHeight + 30,
          child: CardSwiper(
              padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
              numberOfCardsDisplayed: count,
              backCardOffset: const Offset(0, 20),
              onSwipe: _onSwipe,
              initialIndex: _currentIndex,
              cardsCount: count,
              cardBuilder: (context, index, percentThresholdX, percentThresholdY) {
                var item = widget.contentList?[index];
                return GestureDetector(
                  onTap: () {
                    toNovelReadPage(item);
                  },
                  child: Padding(
                      padding: EdgeInsets.only(top: 30),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: Column(
                          children: [
                            NetworkImageUtil(
                                imageUrl: item?.cover, width: width, height: imgHeight),
                            SizedBox(
                                width: width,
                                height: btHeight,
                                child: DecoratedBox(
                                  decoration: BoxDecoration(
                                      color: widget.isNormal ? HexColor("#FFFFFF") : null,
                                      gradient: !widget.isNormal
                                          ? LinearGradient(
                                              begin: Alignment.centerLeft,
                                              end: Alignment.centerRight,
                                              colors: [HexColor("#ECCA95"), HexColor("#F4D8AC")])
                                          : null),
                                  child: Center(
                                    child: Text("view_more".tr,
                                        style: TextStyle(
                                            fontSize: 16,
                                            color: widget.isNormal
                                                ? HexColor("#1B86FF")
                                                : HexColor("#704E1F"),
                                            fontWeight: FontWeight.bold)),
                                  ),
                                ))
                          ],
                        ),
                      )),
                );
              }),
        ),
        const SizedBox(height: 10),
        // 指示器
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
            count,
            (index) => Container(
              margin: EdgeInsets.symmetric(horizontal: 4),
              width: 10,
              height: 10,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: _currentIndex == index ? HexColor('#0087FB') : HexColor('#EBECEE'),
              ),
            ),
          ),
        ),
        const SizedBox(height: 26),
        IconButton(
            onPressed: () {
              Get.back();
            },
            icon: Image.asset('assets/images/common/icon_alter_close.png',
                width: 50, height: 50, fit: BoxFit.cover)),
      ],
    );
  }
}

//todo: 阅读 read 引导弹窗
class NovelReadingGuideContent extends BaseLessWidget {
  final VoidCallback onTap;

  NovelReadingGuideContent({super.key, required this.onTap});

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var topMargin = mediaQuery.size.height * 0.5 - 74;
    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: Row(
        children: [
          Expanded(
              flex: 1,
              child: Column(
                children: [
                  SizedBox(height: topMargin),
                  Image.asset('assets/images/novelReading/guide/icon_finger_left.png',
                      width: 44, height: 63),
                  const SizedBox(height: 45),
                  Text(
                    "previous_page".tr,
                    style: TextStyle(
                        fontSize: 18, color: HexColor("#FFFFFF"), fontWeight: FontWeight.bold),
                    maxLines: 10,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                  )
                ],
              )),
          Expanded(
              flex: 1,
              child: Container(
                height: double.infinity,
                color: ColorsUtil.hexColor(0x000000, alpha: 0.3),
                child: Column(
                  children: [
                    SizedBox(height: topMargin),
                    Image.asset('assets/images/novelReading/guide/icon_finger_midel.png',
                        width: 40, height: 70),
                    const SizedBox(height: 38),
                    Text("tap_for_option".tr,
                        style: TextStyle(
                            fontSize: 18, color: HexColor("#FFFFFF"), fontWeight: FontWeight.bold),
                        maxLines: 10,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center),
                    const SizedBox(height: 98),
                    SizedBox(
                        height: 35,
                        child: OutlinedButton(
                          onPressed: () {
                            Get.back();
                            onTap.call();
                          },
                          style: ButtonStyle(
                              backgroundColor: WidgetStateProperty.all(
                                ColorsUtil.hexColor(0xFFFFFF, alpha: 0.3),
                              ),
                              side: WidgetStateProperty.all(
                                const BorderSide(color: Colors.white, width: 1.5),
                              ),
                              shape: WidgetStateProperty.all(
                                RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(18),
                                ),
                              ),
                              padding: WidgetStateProperty.all(EdgeInsets.zero)),
                          child: Text("next".tr,
                              style: TextStyle(
                                  fontSize: 20,
                                  color: HexColor("#FFFFFF"),
                                  fontWeight: FontWeight.bold)),
                        ))
                  ],
                ),
              )),
          Expanded(
              flex: 1,
              child: Column(
                children: [
                  SizedBox(height: topMargin),
                  Image.asset('assets/images/novelReading/guide/icon_finger_left.png',
                      width: 44, height: 63),
                  const SizedBox(height: 45),
                  Text("next_page".tr,
                      style: TextStyle(
                          fontSize: 18, color: HexColor("#FFFFFF"), fontWeight: FontWeight.bold),
                      maxLines: 10,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center)
                ],
              ))
        ],
      ),
    );
  }
}

//todo: 阅读 read 工具栏引导弹窗
class NovelReadingMenuGuideContent extends StatelessWidget {
  const NovelReadingMenuGuideContent({super.key});

  Widget _topIntroduce(String text, bool isCurveLine,
      {required CrossAxisAlignment crossAxisAlignment,
      double lineHeight = 52,
      EdgeInsetsGeometry? edgeInsets}) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: crossAxisAlignment,
      children: [
        Image.asset(
            isCurveLine
                ? 'assets/images/novelReading/guide/icon_guide_curveLine.png'
                : 'assets/images/novelReading/guide/icon_guide_straightLine.png',
            width: isCurveLine ? 30 : 1,
            height: lineHeight,
            fit: isCurveLine ? BoxFit.contain : BoxFit.cover),
        const SizedBox(height: 4),
        Container(
            margin: isAvailable(edgeInsets) ? edgeInsets : null,
            padding: const EdgeInsets.symmetric(horizontal: 5),
            decoration:
                BoxDecoration(color: HexColor('#1B86FF'), borderRadius: BorderRadius.circular(5)),
            child: Text(text,
                style: TextStyle(color: HexColor("#FFFFFF"), fontSize: 13),
                textAlign: TextAlign.center)),
      ],
    );
  }

  Widget _bottomIntroduce(String text, {double lineHeight = 52}) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
            padding: const EdgeInsets.symmetric(horizontal: 5),
            decoration:
                BoxDecoration(color: HexColor('#1B86FF'), borderRadius: BorderRadius.circular(5)),
            child: Text(text,
                style: TextStyle(color: HexColor("#FFFFFF"), fontSize: 13),
                textAlign: TextAlign.center)),
        const SizedBox(height: 4),
        Image.asset('assets/images/novelReading/guide/icon_guide_straightLine.png',
            width: 1, height: lineHeight, fit: BoxFit.cover),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var lineSpace = (mediaQuery.size.width - 32 - 40 * 5) / 4;
    return SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: Stack(
          children: [
            Positioned(
                top: 13,
                left: 5,
                right: 5,
                height: 40,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Image.asset('assets/images/novelReading/guide/icon_guide_back.png'),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Image.asset('assets/images/novelReading/guide/icon_guide_download.png'),
                        const SizedBox(width: 5),
                        Image.asset('assets/images/novelReading/guide/icon_guide_share.png'),
                        const SizedBox(width: 5),
                        Image.asset('assets/images/novelReading/guide/icon_guide_report.png'),
                      ],
                    )
                  ],
                )),
            Positioned(
                top: 33,
                left: 50,
                width: 65,
                child: _topIntroduce('tips_back'.tr, true,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    edgeInsets: const EdgeInsets.only(left: 5))),
            Positioned(
                top: 58,
                right: 25 - 31,
                width: 62,
                child: _topIntroduce('tips_report'.tr, false,
                    crossAxisAlignment: CrossAxisAlignment.center)),
            Positioned(
                top: 58,
                right: 70 - 31,
                width: 62,
                child: _topIntroduce('tips_share'.tr, false,
                    crossAxisAlignment: CrossAxisAlignment.center, lineHeight: 102)),
            Positioned(
                top: 58,
                right: 115 - 40,
                width: 80,
                child: _topIntroduce('tips_download'.tr, false,
                    crossAxisAlignment: CrossAxisAlignment.center, lineHeight: 148)),

            ///中间部分
            Center(
                child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset('assets/images/novelReading/guide/icon_finger_midel.png',
                    width: 40, height: 70),
                const SizedBox(height: 22),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: SizedBox(
                    child: Text("tap_for_option".tr,
                        style: TextStyle(
                            fontSize: 18, color: HexColor("#FFFFFF"), fontWeight: FontWeight.bold),
                        maxLines: 5,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center),
                  ),
                ),
                const SizedBox(height: 60),
                SizedBox(
                    height: 35,
                    child: OutlinedButton(
                      onPressed: () {
                        Get.back();
                      },
                      style: ButtonStyle(
                          backgroundColor: WidgetStateProperty.all(
                            ColorsUtil.hexColor(0xFFFFFF, alpha: 0.2),
                          ),
                          side: WidgetStateProperty.all(
                            BorderSide(color: HexColor("#FFFFFF"), width: 1.5),
                          ),
                          shape: WidgetStateProperty.all(
                            RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(18),
                            ),
                          ),
                          padding: WidgetStateProperty.all(EdgeInsets.zero)),
                      child: Text('  ${"next".tr}  ',
                          style: TextStyle(
                              fontSize: 20,
                              color: HexColor("#FFFFFF"),
                              fontWeight: FontWeight.bold)),
                    ))
              ],
            )),

            Positioned(
                bottom: 8,
                left: 16,
                right: 16,
                height: 40,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Image.asset('assets/images/novelReading/guide/icon_guide_chapter.png'),
                    Image.asset('assets/images/novelReading/guide/icon_guide_library.png'),
                    Image.asset('assets/images/novelReading/guide/icon_guide_like.png'),
                    Image.asset('assets/images/novelReading/guide/icon_guide_light.png'),
                    Image.asset('assets/images/novelReading/guide/icon_guide_character.png'),
                  ],
                )),
            Positioned(
                left: 36 - 38,
                bottom: 53,
                width: 76,
                child: _bottomIntroduce('tips_chapter'.tr, lineHeight: 99)),
            Positioned(
                left: 76 + lineSpace - 49,
                bottom: 53,
                width: 98,
                child: _bottomIntroduce('library_add'.tr)),
            Positioned(
                left: 116 + lineSpace * 2 - 50,
                bottom: 53,
                width: 100,
                child: _bottomIntroduce('add_likes_title'.tr, lineHeight: 99)),
            Positioned(
                left: 156 + lineSpace * 3 - 47,
                bottom: 53,
                width: 94,
                child: _bottomIntroduce('tips_night_mode'.tr)),
            Positioned(
                left: 196 + lineSpace * 4 - 29,
                bottom: 53,
                width: 58,
                child: _bottomIntroduce('font'.tr, lineHeight: 99)),
          ],
        ));
  }
}

//todo: 支付中提醒弹窗
class PaymentReminderContent extends StatelessWidget {
  final Function(bool isContinue) onConfirm;

  const PaymentReminderContent({super.key, required this.onConfirm});

  @override
  Widget build(BuildContext context) {
    var screenWidth = MediaQuery.of(context).size.width;
    return SizedBox(
      width: screenWidth - 84,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(
            height: 30,
          ),
          Text("payment_processing".tr,
              style:
                  TextStyle(fontSize: 16, color: HexColor("#000000"), fontWeight: FontWeight.bold)),
          const SizedBox(
            height: 16,
          ),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                "payment_processing_desc".tr,
                style: TextStyle(color: HexColor("#000000"), fontSize: 14),
                maxLines: null,
              )),
          const SizedBox(height: 14),
          Container(
            width: double.infinity,
            height: 48,
            margin: const EdgeInsets.symmetric(horizontal: 20),
            decoration: BoxDecoration(
              color: ColorsUtil.hexColor(0x000000),
              borderRadius: const BorderRadius.all(Radius.circular(12)),
            ),
            child: TextButton(
                onPressed: () {
                  Get.back();
                  onConfirm.call(true);
                },
                child: Text("continue_Waiting".tr,
                    style: TextStyle(
                        fontSize: 16, color: HexColor("#FFFFFF"), fontWeight: FontWeight.bold))),
          ),
          const SizedBox(height: 10),
          Container(
            width: double.infinity,
            height: 48,
            margin: const EdgeInsets.symmetric(horizontal: 20),
            decoration: BoxDecoration(
              color: ColorsUtil.hexColor(0xEBECEE),
              borderRadius: const BorderRadius.all(Radius.circular(12)),
            ),
            child: TextButton(
                onPressed: () {
                  Get.back();
                  onConfirm.call(false);
                },
                child: Text("tips_back".tr,
                    style: TextStyle(
                        fontSize: 16, color: HexColor("#1F1F2F"), fontWeight: FontWeight.bold))),
          ),
          const SizedBox(
            height: 30,
          ),
        ],
      ),
    );
  }
}

//todo: 充值购买页面挽留弹窗
class PurchaseReminderContent extends BaseFulWidget {
  final GoodsListItem? goodItem;
  final VoidCallback onBack;
  final Function(GoodsListItem? goodItem)? onClaimNow;

  PurchaseReminderContent(
      {super.key, required this.goodItem, required this.onBack, required this.onClaimNow});

  @override
  State<PurchaseReminderContent> createState() => _PurchaseReminderContentState();
}

class _PurchaseReminderContentState extends State<PurchaseReminderContent>
    with TickerProviderStateMixin {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    _initAlterAnimation();
    _initButtonAnimation();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    try {
      _scaleController.dispose();
    } catch (e) {
      logD('_scaleController dispose error: $e');
    }
    super.dispose();
  }

  _initAlterAnimation() {
    // 缩放动画控制器
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // 创建复杂的缩放动画序列
    final List<TweenSequenceItem<double>> sequenceItems = [
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 1.3).chain(CurveTween(curve: Curves.easeOut)),
        weight: 25.0,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.3, end: 1.0).chain(CurveTween(curve: Curves.easeOut)),
        weight: 25.0,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 0.8).chain(CurveTween(curve: Curves.easeInOut)),
        weight: 25.0,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 0.8, end: 1.0).chain(CurveTween(curve: Curves.easeOut)),
        weight: 25.0,
      ),
    ];
    // 缩放动画序列
    _scaleAnimation = TweenSequence<double>(sequenceItems).animate(_scaleController);
    _scaleController.forward();
  }

  _initButtonAnimation() {
    // 缩放动画控制器
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // 创建复杂的缩放动画序列
    final List<TweenSequenceItem<double>> sequenceItems = [
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 1.1).chain(CurveTween(curve: Curves.easeOut)),
        weight: 25.0,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.1, end: 1.2).chain(CurveTween(curve: Curves.easeOut)),
        weight: 25.0,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.2, end: 1.1).chain(CurveTween(curve: Curves.easeInOut)),
        weight: 25.0,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.1, end: 1.0).chain(CurveTween(curve: Curves.easeOut)),
        weight: 25.0,
      ),
    ];
    // 缩放动画序列
    _buttonScaleAnimation = TweenSequence<double>(sequenceItems).animate(_scaleController);
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var alterWidth = mediaQuery.size.width;
    var alterHeight = alterWidth / 0.893;
    bool isFirstCharge = widget.goodItem?.activityType == 'First_Charge';
    bool isDiscount = widget.goodItem?.activityType == 'Discount';
    bool isOtherActivity = true;
    return ScaleTransition(
        scale: _scaleAnimation,
        child: SizedBox(
            width: alterWidth,
            height: alterHeight + 69,
            child: Stack(children: [
              Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 69,
                  child: DecoratedBox(
                      decoration: BoxDecoration(
                    image: DecorationImage(
                        image: AssetImage('assets/images/profile/purchase/icon_purchase_bg.png'),
                        fit: BoxFit.cover),
                  ))),
              Positioned(
                  top: alterHeight * 0.182,
                  left: alterWidth * 0.122,
                  right: alterWidth * 0.116,
                  height: alterHeight * 0.142,
                  child: Transform.rotate(
                    angle: 0.13, // 旋转角度（弧度，0.3≈17度）
                    child: Text(
                      'leave_benefits'.tr,
                      style: TextStyle(
                          color: HexColor("#FFFFFF"), fontSize: 24, fontWeight: FontWeight.bold),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                  )),
              Positioned(
                  top: alterHeight * 0.364,
                  left: alterWidth * 0.111,
                  right: alterWidth * 0.111,
                  child: Center(
                    child: Text(
                      'special_discount'.tr,
                      style: TextStyle(
                          color: HexColor("#DE5133"), fontSize: 22, fontWeight: FontWeight.bold),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                  )),
              Positioned(
                top: alterHeight * 0.438,
                left: alterWidth * 0.114,
                width: alterWidth * 0.288 + alterWidth * 0.025,
                height: alterWidth * 0.288 / 1.405 + alterHeight * 0.052,
                child: Stack(
                  children: [
                    Positioned(
                      top: alterHeight * 0.052,
                      left: alterWidth * 0.025,
                      right: 0,
                      bottom: 0,
                      child: DecoratedBox(
                        decoration: BoxDecoration(
                            image: DecorationImage(
                                image: AssetImage(
                                    'assets/images/profile/purchase/icon_purchase_regular_bg.png'),
                                fit: BoxFit.cover)),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            SizedBox(height: 20),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Image.asset(
                                  'assets/images/profile/purchase/icon_coin.png',
                                  width: 16,
                                  height: 16,
                                  fit: BoxFit.cover,
                                ),
                                SizedBox(width: 8),
                                Text(
                                    '${(widget.goodItem?.skuValue ?? 0) + (widget.goodItem?.coinsGift ?? 0)}',
                                    style: TextStyle(
                                        color: HexColor('#C79648'),
                                        fontSize: 19,
                                        fontWeight: FontWeight.bold,
                                        height: 1)),
                              ],
                            ),
                            SizedBox(height: 15),
                            FittedBox(
                              child: Text(
                                '${widget.goodItem?.currencySymbol}${(widget.goodItem!.rawPrice! * (widget.goodItem?.pricePercentage ?? 1)).toStringAsFixed(2)}',
                                style: TextStyle(
                                  color: HexColor("#7E8188"),
                                  fontSize: 21,
                                  fontWeight: FontWeight.bold,
                                  decoration: TextDecoration.lineThrough,
                                  decorationColor: HexColor("#7E8188"),
                                  decorationThickness: 1.5,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    if (isFirstCharge || isDiscount || isOtherActivity)
                      Positioned(
                          top: isFirstCharge
                              ? 0
                              : isDiscount
                                  ? 13
                                  : alterHeight * 0.052 + 2,
                          left: isFirstCharge || isDiscount ? 0 : alterWidth * 0.025 + 2,
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              if (isFirstCharge || isDiscount)
                                Image.asset(
                                    isFirstCharge
                                        ? 'assets/images/profile/purchase/icon_top_first_charge.png'
                                        : 'assets/images/profile/purchase/icon_top_discount.png',
                                    fit: BoxFit.cover,
                                    width: isFirstCharge ? 30 : 18,
                                    height: isFirstCharge ? 36 : 25),
                              Container(
                                height: isFirstCharge
                                    ? 24
                                    : isDiscount
                                        ? 21
                                        : 17,
                                decoration: isFirstCharge || isDiscount
                                    ? BoxDecoration(
                                        color: isFirstCharge
                                            ? HexColor('#FE6F0C')
                                            : HexColor('#404243'),
                                        borderRadius: BorderRadius.only(
                                            topRight: Radius.circular(8),
                                            bottomRight: Radius.circular(8)),
                                      )
                                    : BoxDecoration(
                                        gradient: LinearGradient(colors: [
                                          HexColor('#FF87C0'),
                                          HexColor('#FC348C'),
                                        ], begin: Alignment.topCenter, end: Alignment.bottomCenter),
                                        borderRadius: BorderRadius.only(
                                            topLeft: Radius.circular(12),
                                            topRight: Radius.circular(12),
                                            bottomRight: Radius.circular(12)),
                                      ),
                                child: Padding(
                                    padding: EdgeInsets.only(
                                        top: isFirstCharge || isDiscount ? 3 : 0,
                                        left: isFirstCharge ? 3 : 5,
                                        right: 5),
                                    child: Text(
                                        isFirstCharge
                                            ? '1st_top_up'.tr
                                            : isDiscount
                                                ? '${widget.goodItem?.activityName}'
                                                : 'best_deal'.tr,
                                        style: TextStyle(
                                            color: HexColor(isFirstCharge ? '#FFFB18' : '#F7DCB1'),
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        textAlign: TextAlign.center)),
                              )
                            ],
                          )),
                  ],
                ),
              ),
              Positioned(
                top: alterHeight * 0.475,
                right: alterWidth * 0.136,
                width: alterWidth * 0.383,
                height: alterWidth * 0.383 / 1.452,
                child: Stack(
                  children: [
                    Positioned.fill(
                      child: DecoratedBox(
                        decoration: BoxDecoration(
                            image: DecorationImage(
                                image: AssetImage(
                                    'assets/images/profile/purchase/icon_purchase_sale_bg.png'),
                                fit: BoxFit.fill)),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            SizedBox(height: 27),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Image.asset(
                                  'assets/images/profile/purchase/icon_coin.png',
                                  width: 22,
                                  height: 22,
                                  fit: BoxFit.cover,
                                ),
                                SizedBox(width: 11),
                                Text(
                                    '${(widget.goodItem?.skuValue ?? 0) + (widget.goodItem?.coinsGift ?? 0)}',
                                    style: TextStyle(
                                        color: HexColor("#C79648"),
                                        fontSize: 24,
                                        fontWeight: FontWeight.bold)),
                              ],
                            ),
                            SizedBox(height: 11),
                            if (isAvailable(widget.goodItem?.rawPrice))
                              NumberAnimated(
                                beginNumber: widget.goodItem!.rawPrice! *
                                    (widget.goodItem?.pricePercentage ?? 1),
                                endNumber: widget.goodItem!.rawPrice!,
                                delay: const Duration(milliseconds: 600),
                                symbol: widget.goodItem?.currencySymbol,
                                style: TextStyle(
                                    color: HexColor("#F52164"),
                                    fontSize: 28,
                                    fontWeight: FontWeight.bold),
                                animationCompleted: () {
                                  _scaleController.repeat();
                                },
                              ),
                          ],
                        ),
                      ),
                    ),
                    if (isAvailable(widget.goodItem?.countDownSecond))
                      Positioned(
                          top: 2,
                          right: 2,
                          height: 18,
                          child: Container(
                              padding: EdgeInsets.only(left: 9, right: 9),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [HexColor('#FD8413'), HexColor('#FE4101')],
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                ),
                                borderRadius: BorderRadius.only(
                                    topRight: Radius.circular(12), bottomLeft: Radius.circular(12)),
                              ),
                              child: CountDownCard(
                                  countDownSecond: widget.goodItem?.countDownSecond,
                                  onTimerEnd: () {}))),
                  ],
                ),
              ),
              Positioned(
                top: alterHeight * 0.546,
                left: alterWidth * 0.366,
                width: alterWidth * 0.191,
                height: alterWidth * 0.191 / 1.642,
                child: Image.asset('assets/images/novelReading/icon_unlock_arrow.png',
                    fit: BoxFit.cover),
              ),
              Positioned(
                  top: alterHeight * 0.869,
                  left: alterWidth * 0.333,
                  right: alterWidth * 0.333,
                  height: 40,
                  child: ScaleTransition(
                    scale: _buttonScaleAnimation,
                    child: DecoratedBox(
                        decoration: BoxDecoration(
                          borderRadius: const BorderRadius.all(Radius.circular(12)),
                          color: HexColor("#FFCA77"),
                        ),
                        child: TextButton(
                            style: TextButton.styleFrom(
                              padding: EdgeInsets.zero,
                            ),
                            onPressed: () {
                              Get.back();
                              widget.onClaimNow?.call(widget.goodItem);
                            },
                            child: Text(
                              "claim_now".tr,
                              style: TextStyle(
                                color: HexColor("#8C4F00"),
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ))),
                  )),
              Positioned(
                  width: alterWidth * 0.075,
                  height: alterHeight * 0.075,
                  left: alterWidth * 0.461,
                  bottom: 0,
                  child: IconButton(
                      onPressed: () {
                        Get.back();
                        widget.onBack.call();
                      },
                      style: IconButton.styleFrom(padding: EdgeInsets.zero),
                      icon: Image.asset('assets/images/novelReading/icon_unlock_close.png')))
            ])));
  }
}

//todo: 订阅购买页面挽留弹窗
class SubscribeReminderContent extends StatefulWidget {
  final GoodsListItem? goodItem;
  final VoidCallback onBack;
  final Function(GoodsListItem? goodItem)? onClaimNow;

  const SubscribeReminderContent(
      {super.key, required this.goodItem, required this.onBack, required this.onClaimNow});

  @override
  State<SubscribeReminderContent> createState() => _SubscribeReminderContentState();
}

class _SubscribeReminderContentState extends State<SubscribeReminderContent>
    with TickerProviderStateMixin {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    _initAlterAnimation();
    _initButtonAnimation();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    try {
      _scaleController.dispose();
    } catch (e) {
      logD('_scaleController dispose error: $e');
    }
    super.dispose();
  }

  _initAlterAnimation() {
    // 缩放动画控制器
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // 创建复杂的缩放动画序列
    final List<TweenSequenceItem<double>> sequenceItems = [
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 1.3).chain(CurveTween(curve: Curves.easeOut)),
        weight: 25.0,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.3, end: 1.0).chain(CurveTween(curve: Curves.easeOut)),
        weight: 25.0,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 0.8).chain(CurveTween(curve: Curves.easeInOut)),
        weight: 25.0,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 0.8, end: 1.0).chain(CurveTween(curve: Curves.easeOut)),
        weight: 25.0,
      ),
    ];
    // 缩放动画序列
    _scaleAnimation = TweenSequence<double>(sequenceItems).animate(_scaleController);
    _scaleController.forward();
  }

  _initButtonAnimation() {
    // 缩放动画控制器
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // 创建复杂的缩放动画序列
    final List<TweenSequenceItem<double>> sequenceItems = [
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 1.1).chain(CurveTween(curve: Curves.easeOut)),
        weight: 25.0,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.1, end: 1.2).chain(CurveTween(curve: Curves.easeOut)),
        weight: 25.0,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.2, end: 1.1).chain(CurveTween(curve: Curves.easeInOut)),
        weight: 25.0,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.1, end: 1.0).chain(CurveTween(curve: Curves.easeOut)),
        weight: 25.0,
      ),
    ];
    // 缩放动画序列
    _buttonScaleAnimation = TweenSequence<double>(sequenceItems).animate(_scaleController);
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var alterWidth = mediaQuery.size.width;
    var alterHeight = alterWidth / 0.913;
    bool isFirstCharge = widget.goodItem?.activityType == 'First_Charge';
    bool isDiscount = widget.goodItem?.activityType == 'Discount';
    bool isOtherActivity = widget.goodItem?.activityType == 'Best_Deal';
    return ScaleTransition(
        scale: _scaleAnimation,
        child: SizedBox(
            width: alterWidth,
            height: alterHeight,
            child: Stack(children: [
              Positioned.fill(
                  child: DecoratedBox(
                      decoration: BoxDecoration(
                image: DecorationImage(
                    image: AssetImage('assets/images/profile/purchase/icon_subscribe_bg.png'),
                    fit: BoxFit.cover),
              ))),
              Positioned(
                  top: alterHeight * 0.06,
                  left: alterWidth * 0.125,
                  right: alterWidth * 0.125,
                  child: Center(
                    child: Text(
                      'leave_benefits'.tr,
                      style: TextStyle(
                          color: HexColor("#FFFFFF"), fontSize: 21, fontWeight: FontWeight.bold),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                  )),
              Positioned(
                  top: alterHeight * 0.401,
                  left: alterWidth * 0.125,
                  right: alterWidth * 0.125,
                  height: alterHeight * 0.045,
                  child: Center(
                    child: Text(
                      'special_discount'.tr,
                      style: TextStyle(
                          color: HexColor("#DE5133"), fontSize: 18, fontWeight: FontWeight.bold),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                  )),
              Positioned(
                top: alterHeight * 0.461,
                left: alterWidth * 0.141,
                width: alterWidth * 0.291 + alterWidth * 0.014,
                height: alterWidth * 0.291 / 1.4 + alterHeight * 0.039,
                child: Stack(
                  children: [
                    Positioned(
                      top: alterHeight * 0.039,
                      left: alterWidth * 0.014,
                      right: 0,
                      bottom: 0,
                      child: DecoratedBox(
                          decoration: BoxDecoration(
                              image: DecorationImage(
                                  image: AssetImage(
                                      'assets/images/profile/purchase/icon_purchase_regular_bg.png'),
                                  fit: BoxFit.cover)),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              SizedBox(height: 20),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  FittedBox(
                                    child: Text(
                                      '${widget.goodItem?.currencySymbol}${(widget.goodItem!.rawPrice! * (widget.goodItem?.pricePercentage ?? 1)).toStringAsFixed(2)}',
                                      style: TextStyle(
                                        color: HexColor("#7E8188"),
                                        fontSize: 21,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 11),
                              Text(
                                'regular_price'.tr,
                                style: TextStyle(
                                    color: HexColor("#7E8188"),
                                    fontSize: 13,
                                    fontWeight: FontWeight.bold),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                textAlign: TextAlign.center,
                              ),
                            ],
                          )),
                    ),
                    if (isFirstCharge || isDiscount || isOtherActivity)
                      Positioned(
                          top: isFirstCharge
                              ? 0
                              : isDiscount
                                  ? 6
                                  : alterHeight * 0.039 + 2,
                          left: isFirstCharge || isDiscount ? 0 : alterWidth * 0.014 + 2,
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              if (isFirstCharge || isDiscount)
                                Image.asset(
                                    isFirstCharge
                                        ? 'assets/images/profile/purchase/icon_top_first_charge.png'
                                        : 'assets/images/profile/purchase/icon_top_discount.png',
                                    fit: BoxFit.cover,
                                    width: isFirstCharge ? 30 : 18,
                                    height: isFirstCharge ? 36 : 25),
                              Container(
                                height: isFirstCharge
                                    ? 24
                                    : isDiscount
                                        ? 21
                                        : 17,
                                decoration: isFirstCharge || isDiscount
                                    ? BoxDecoration(
                                        color: isFirstCharge
                                            ? HexColor('#FE6F0C')
                                            : HexColor('#404243'),
                                        borderRadius: BorderRadius.only(
                                            topRight: Radius.circular(8),
                                            bottomRight: Radius.circular(8)),
                                      )
                                    : BoxDecoration(
                                        gradient: LinearGradient(colors: [
                                          HexColor('#FF87C0'),
                                          HexColor('#FC348C'),
                                        ], begin: Alignment.topCenter, end: Alignment.bottomCenter),
                                        borderRadius: BorderRadius.only(
                                            topLeft: Radius.circular(12),
                                            topRight: Radius.circular(12),
                                            bottomRight: Radius.circular(12)),
                                      ),
                                child: Padding(
                                    padding: EdgeInsets.only(
                                        top: isFirstCharge || isDiscount ? 3 : 0,
                                        left: isFirstCharge ? 3 : 5,
                                        right: 5),
                                    child: Text(
                                        isFirstCharge
                                            ? '1st_top_up'.tr
                                            : isDiscount
                                                ? '${widget.goodItem?.activityName}'
                                                : 'best_deal'.tr,
                                        style: TextStyle(
                                            color: HexColor(isFirstCharge ? '#FFFB18' : '#F7DCB1'),
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        textAlign: TextAlign.center)),
                              )
                            ],
                          )),
                  ],
                ),
              ),
              Positioned(
                top: alterHeight * 0.487,
                right: alterWidth * 0.119,
                width: alterWidth * 0.377,
                height: alterWidth * 0.377 / 1.462,
                child: Stack(
                  children: [
                    Positioned.fill(
                      child: DecoratedBox(
                        decoration: BoxDecoration(
                            image: DecorationImage(
                                image: AssetImage(
                                    'assets/images/profile/purchase/icon_subscribe_sale_bg.png'),
                                fit: BoxFit.fill)),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            if (isAvailable(widget.goodItem?.rawPrice))
                              NumberAnimated(
                                beginNumber: widget.goodItem!.rawPrice! *
                                    (widget.goodItem?.pricePercentage ?? 1),
                                endNumber: widget.goodItem!.rawPrice!,
                                delay: const Duration(milliseconds: 600),
                                symbol: widget.goodItem?.currencySymbol,
                                style: TextStyle(
                                    color: HexColor("#F52164"),
                                    fontSize: 28,
                                    fontWeight: FontWeight.bold),
                                animationCompleted: () {
                                  _scaleController.repeat();
                                },
                              ),
                            SizedBox(height: 11),
                            Text(
                              'special_price'.tr,
                              style: TextStyle(
                                  color: HexColor("#E23225"),
                                  fontSize: 13,
                                  fontWeight: FontWeight.bold),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                    if (isAvailable(widget.goodItem?.countDownSecond))
                      Positioned(
                          top: 1,
                          right: 1,
                          height: 18,
                          child: Container(
                              padding: EdgeInsets.only(left: 9, right: 9),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [HexColor('#FD8413'), HexColor('#FE4101')],
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                ),
                                borderRadius: BorderRadius.only(
                                    topRight: Radius.circular(12), bottomLeft: Radius.circular(12)),
                              ),
                              child: CountDownCard(
                                  countDownSecond: widget.goodItem?.countDownSecond,
                                  onTimerEnd: () {}))),
                  ],
                ),
              ),
              Positioned(
                top: alterHeight * 0.546,
                left: alterWidth * 0.366,
                width: alterWidth * 0.191,
                height: alterWidth * 0.191 / 1.642,
                child: Image.asset('assets/images/novelReading/icon_unlock_arrow.png',
                    fit: BoxFit.cover),
              ),
              Positioned(
                  left: alterWidth * 0.161,
                  right: alterWidth * 0.113,
                  bottom: alterHeight * 0.058,
                  child: Row(children: [
                    Container(
                      width: alterWidth * 0.336,
                      height: alterHeight * 0.104,
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.all(Radius.circular(12)),
                        color: HexColor("#FF9688"),
                      ),
                      child: TextButton(
                          style: TextButton.styleFrom(
                            padding: EdgeInsets.zero,
                          ),
                          onPressed: () {
                            Get.back();
                            widget.onBack.call();
                          },
                          child: Text(
                            "miss_out".tr,
                            style: TextStyle(
                              color: HexColor("#FFE2DE"),
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          )),
                    ),
                    SizedBox(width: alterWidth * 0.044),
                    ScaleTransition(
                        scale: _buttonScaleAnimation,
                        child: Container(
                            width: alterWidth * 0.336,
                            height: alterHeight * 0.104,
                            decoration: BoxDecoration(
                              borderRadius: const BorderRadius.all(Radius.circular(12)),
                              color: HexColor("#FFCA77"),
                            ),
                            child: TextButton(
                                style: TextButton.styleFrom(
                                  padding: EdgeInsets.zero,
                                ),
                                onPressed: () {
                                  Get.back();
                                  widget.onClaimNow?.call(widget.goodItem);
                                },
                                child: Text(
                                  "claim_now".tr,
                                  style: TextStyle(
                                    color: HexColor("#8C4F00"),
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  textAlign: TextAlign.center,
                                ))))
                  ]))
            ])));
  }
}

//todo: 章节解锁商品弹窗
class NovelUnlockGoodsContent extends BaseFulWidget {
  final GoodsListItem? premiumGoodItem;
  final VoidCallback onClaimNow;
  final VoidCallback onClose;

  NovelUnlockGoodsContent(
      {super.key, required this.premiumGoodItem, required this.onClaimNow, required this.onClose});

  @override
  State<NovelUnlockGoodsContent> createState() => _NovelUnlockGoodsContentState();
}

class _NovelUnlockGoodsContentState extends State<NovelUnlockGoodsContent>
    with TickerProviderStateMixin {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    _initAlterAnimation();
    _initButtonAnimation();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    try {
      _scaleController.dispose();
    } catch (e) {
      logD('_scaleController dispose error: $e');
    }
    super.dispose();
  }

  _initAlterAnimation() {
    // 缩放动画控制器
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // 创建复杂的缩放动画序列
    final List<TweenSequenceItem<double>> sequenceItems = [
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 1.3).chain(CurveTween(curve: Curves.easeOut)),
        weight: 25.0,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.3, end: 1.0).chain(CurveTween(curve: Curves.easeOut)),
        weight: 25.0,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 0.8).chain(CurveTween(curve: Curves.easeInOut)),
        weight: 25.0,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 0.8, end: 1.0).chain(CurveTween(curve: Curves.easeOut)),
        weight: 25.0,
      ),
    ];
    // 缩放动画序列
    _scaleAnimation = TweenSequence<double>(sequenceItems).animate(_scaleController);
    _scaleController.forward();
  }

  _initButtonAnimation() {
    // 缩放动画控制器
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // 创建复杂的缩放动画序列
    final List<TweenSequenceItem<double>> sequenceItems = [
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 1.1).chain(CurveTween(curve: Curves.easeOut)),
        weight: 25.0,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.1, end: 1.2).chain(CurveTween(curve: Curves.easeOut)),
        weight: 25.0,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.2, end: 1.1).chain(CurveTween(curve: Curves.easeInOut)),
        weight: 25.0,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.1, end: 1.0).chain(CurveTween(curve: Curves.easeOut)),
        weight: 25.0,
      ),
    ];
    // 缩放动画序列
    _buttonScaleAnimation = TweenSequence<double>(sequenceItems).animate(_scaleController);
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var alterWidth = mediaQuery.size.width;
    var alterHeight = alterWidth / 0.87;
    bool isFirstCharge = widget.premiumGoodItem?.activityType == 'First_Charge';
    bool isDiscount = widget.premiumGoodItem?.activityType == 'Discount';
    bool isOtherActivity = widget.premiumGoodItem?.activityType == 'Best_Deal';

    return ScaleTransition(
        scale: _scaleAnimation,
        child: SizedBox(
            width: alterWidth,
            height: alterHeight + 54,
            child: Stack(children: [
              Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 54,
                  child: DecoratedBox(
                      decoration: BoxDecoration(
                    image: DecorationImage(
                        image: AssetImage('assets/images/novelReading/icon_unlock_goods_bg.png'),
                        fit: BoxFit.cover),
                  ))),
              Positioned(
                top: alterHeight * 0.276,
                left: alterWidth * 0.13,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Image.asset(
                      'assets/images/novelReading/icon_premium.png',
                      width: alterWidth * 0.08,
                      height: alterWidth * 0.08 / 1.61,
                      fit: BoxFit.contain,
                    ),
                    SizedBox(width: 4),
                    Padding(
                      padding: EdgeInsets.only(bottom: 4),
                      child: Text(
                        'premium'.tr,
                        style: TextStyle(
                            color: HexColor("#FFFFFF"), fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                    ),
                    SizedBox(width: 6),
                    SizedBox(
                      width: alterWidth * 0.266,
                      height: alterWidth * 0.266 / 4.571,
                      child: DecoratedBox(
                        decoration: BoxDecoration(
                          image: DecorationImage(
                              image: AssetImage('assets/images/novelReading/icon_time_card.png'),
                              fit: BoxFit.cover),
                        ),
                        child: Center(
                          child: Text(
                            getPremiumType(widget.premiumGoodItem?.productCode),
                            style: TextStyle(
                                color: HexColor("#FFFFFF"),
                                fontSize: 13,
                                fontWeight: FontWeight.bold),
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              ),
              Positioned(
                top: alterHeight * 0.341,
                left: alterWidth * 0.13,
                right: alterWidth * 0.13,
                child: Text(
                  'lowest_price'.tr,
                  style: TextStyle(
                      color: HexColor("#FFF000"), fontSize: 21, fontWeight: FontWeight.bold),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Positioned(
                top: alterHeight * 0.433,
                left: alterWidth * 0.138,
                width: alterWidth * 0.3,
                height: alterWidth * 0.3 / 1.38,
                child: DecoratedBox(
                  decoration: BoxDecoration(
                      image: DecorationImage(
                          image:
                              AssetImage('assets/images/novelReading/icon_unlock_regular_bg.png'),
                          fit: BoxFit.cover)),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      FittedBox(
                        child: Text(
                          '${widget.premiumGoodItem?.currencySymbol}${(widget.premiumGoodItem!.rawPrice! * (widget.premiumGoodItem?.pricePercentage ?? 1)).toStringAsFixed(2)}',
                          style: TextStyle(
                              color: HexColor("#7E8188"),
                              fontSize: 21,
                              fontWeight: FontWeight.bold),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.center,
                        ),
                      ),
                      SizedBox(height: 9),
                      Text(
                        'regular_price'.tr,
                        style: TextStyle(
                            color: HexColor("#7E8188"), fontSize: 13, fontWeight: FontWeight.bold),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
              Positioned(
                top: alterHeight * 0.421,
                right: alterWidth * 0.122,
                width: alterWidth * 0.394,
                height: alterWidth * 0.394 / 1.42,
                child: Stack(
                  children: [
                    Positioned.fill(
                      child: DecoratedBox(
                        decoration: BoxDecoration(
                            image: DecorationImage(
                                image: AssetImage(
                                    'assets/images/novelReading/icon_unlock_sale_bg.png'),
                                fit: BoxFit.fill)),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            if (isAvailable(widget.premiumGoodItem?.rawPrice))
                              NumberAnimated(
                                beginNumber: widget.premiumGoodItem!.rawPrice! *
                                    (widget.premiumGoodItem?.pricePercentage ?? 1),
                                endNumber: widget.premiumGoodItem!.rawPrice!,
                                delay: const Duration(milliseconds: 600),
                                symbol: widget.premiumGoodItem?.currencySymbol,
                                style: TextStyle(
                                    color: HexColor("#F52164"),
                                    fontSize: 28,
                                    fontWeight: FontWeight.bold),
                                animationCompleted: () {
                                  _scaleController.repeat();
                                },
                              ),
                            SizedBox(height: 11),
                            Text(
                              'special_price'.tr,
                              style: TextStyle(
                                  color: HexColor("#E23225"),
                                  fontSize: 13,
                                  fontWeight: FontWeight.bold),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                    if (isAvailable(widget.premiumGoodItem?.countDownSecond))
                      Positioned(
                          top: 4,
                          right: 6,
                          height: 18,
                          child: Container(
                              padding: EdgeInsets.only(left: 9, right: 9),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [HexColor('#FD8413'), HexColor('#FE4101')],
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                ),
                                borderRadius: BorderRadius.only(
                                    topRight: Radius.circular(12), bottomLeft: Radius.circular(12)),
                              ),
                              child: CountDownCard(
                                  countDownSecond: widget.premiumGoodItem?.countDownSecond,
                                  onTimerEnd: () {}))),
                  ],
                ),
              ),
              Positioned(
                top: alterHeight * 0.492,
                left: alterWidth * 0.372,
                width: alterWidth * 0.191,
                height: alterWidth * 0.191 / 1.642,
                child: Image.asset('assets/images/novelReading/icon_unlock_arrow.png',
                    fit: BoxFit.cover),
              ),
              Positioned(
                  top: alterHeight * 0.704,
                  left: alterWidth * 0.180,
                  right: alterWidth * 0.180,
                  height: alterHeight * 0.154,
                  child: ScaleTransition(
                    scale: _buttonScaleAnimation,
                    child: Stack(
                      children: [
                        Positioned(
                          top: alterHeight * 0.059,
                          left: 10,
                          right: 10,
                          bottom: 0,
                          child: DecoratedBox(
                              decoration: BoxDecoration(
                                gradient: LinearGradient(colors: [
                                  HexColor("#7832F8"),
                                  HexColor("#E479F0"),
                                ], begin: Alignment.centerLeft, end: Alignment.centerRight),
                                borderRadius: const BorderRadius.all(Radius.circular(12)),
                              ),
                              child: TextButton(
                                style: TextButton.styleFrom(
                                  padding: EdgeInsets.zero,
                                ),
                                onPressed: () {
                                  Get.back();
                                  widget.onClaimNow.call();
                                },
                                child: Text(
                                  'claim_now'.tr,
                                  style: TextStyle(
                                    color: HexColor("#FFFFFF"),
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  textAlign: TextAlign.center,
                                ),
                              )),
                        ),
                        if (isFirstCharge || isDiscount || isOtherActivity)
                          Positioned(
                              top: isFirstCharge
                                  ? 0
                                  : isDiscount
                                      ? 8
                                      : alterHeight * 0.059,
                              left: isFirstCharge || isDiscount ? 0 : 10,
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  if (isFirstCharge || isDiscount)
                                    Image.asset(
                                        isFirstCharge
                                            ? 'assets/images/profile/purchase/icon_top_first_charge.png'
                                            : 'assets/images/profile/purchase/icon_top_discount.png',
                                        fit: BoxFit.cover,
                                        width: isFirstCharge ? 34 : 25,
                                        height: isFirstCharge ? 40 : 32),
                                  Container(
                                    height: isOtherActivity ? 18 : 27,
                                    decoration: isFirstCharge || isDiscount
                                        ? BoxDecoration(
                                            color: isFirstCharge
                                                ? HexColor('#FE6F0C')
                                                : HexColor('#404243'),
                                            borderRadius: BorderRadius.only(
                                                topRight: Radius.circular(8),
                                                bottomRight: Radius.circular(8)),
                                          )
                                        : BoxDecoration(
                                            gradient: LinearGradient(
                                                colors: [
                                                  HexColor('#FF87C0'),
                                                  HexColor('#FC348C'),
                                                ],
                                                begin: Alignment.topCenter,
                                                end: Alignment.bottomCenter),
                                            borderRadius: BorderRadius.only(
                                                topLeft: Radius.circular(12),
                                                topRight: Radius.circular(12),
                                                bottomRight: Radius.circular(12)),
                                          ),
                                    child: Padding(
                                        padding: EdgeInsets.only(
                                            top: isFirstCharge || isDiscount ? 7 : 3,
                                            left: isOtherActivity ? 10 : 5,
                                            right: 10),
                                        child: Text(
                                            isFirstCharge
                                                ? getPremiumSubTitle(
                                                    widget.premiumGoodItem?.numberOfPeriods,
                                                    widget.premiumGoodItem?.productCode,
                                                    isBilled: false)
                                                : isDiscount
                                                    ? '${widget.premiumGoodItem?.activityName}'
                                                    : 'best_deal'.tr,
                                            style: TextStyle(
                                                color:
                                                    HexColor(isFirstCharge ? '#FFFB18' : '#F7DCB1'),
                                                fontSize: 12,
                                                fontWeight: FontWeight.bold),
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                            textAlign: TextAlign.center)),
                                  )
                                ],
                              )),
                      ],
                    ),
                  )),
              Positioned(
                  width: alterWidth * 0.075,
                  height: alterHeight * 0.075,
                  left: alterWidth * 0.461,
                  bottom: 0,
                  child: IconButton(
                      onPressed: widget.onClose,
                      style: IconButton.styleFrom(padding: EdgeInsets.zero),
                      icon: Image.asset('assets/images/novelReading/icon_unlock_close.png')))
            ])));
  }
}
