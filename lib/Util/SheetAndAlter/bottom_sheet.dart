import 'dart:async';
import 'dart:math' as math;

import 'package:UrNovel/BaseWidget/base_less_widget.dart';
import 'package:UrNovel/Launch&Login/Model/user_model.dart';
import 'package:UrNovel/Util/Common/Animation/lottie_animation_view.dart';
import 'package:UrNovel/Util/Common/CommonManager/common_manager.dart';
import 'package:UrNovel/Util/Common/network_image_util.dart';
import 'package:UrNovel/Util/Common/no_load_view.dart';
import 'package:UrNovel/Util/DataReportManager/event_report_manager.dart';
import 'package:UrNovel/Util/ReaderUtils/readerUtil.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../MainPage/BookInfo/Model/book_author_info.dart';
import '../../MainPage/NovelRead/Model/book_detailInfo_model.dart';
import '../../MainPage/NovelRead/Model/speed_option.dart';
import '../../MainPage/NovelRead/Widget/NovelReminder/recommend_card.dart';
import '../../MainPage/Profile/Model/goods_info_model.dart';
import '../../MainPage/Read/Model/ForYou/home_read_model.dart';
import '../../MainPage/Read/ViewModel/ViewModel.dart';
import '../Common/Model/reward_item.dart';
import '../Common/data_config_util.dart';
import '../DataReportManager/event_name_config.dart';
import '../Extensions/colorUtil.dart';
import '../ReaderUtils/novel_page_agent.dart';
import '../StatusManagement/status_management.dart';
import '../deviceScreenUtil.dart';
import '../enum.dart';
import '../genericUtil.dart';
import '../logUtil.dart';
import '../tools.dart';

Future<void> showBookBottomSheet(BuildContext context, Widget child, dynamic backgroundColor,
    {bool isDismissible = true,
    Color? barrierColor,
    bool isScrollControlled = true,
    bool enableDrag = true,
    VoidCallback? onDismiss}) async {
  try {
    await showModalBottomSheet(
      context: context,
      backgroundColor: backgroundColor is Color ? backgroundColor : Colors.transparent,
      isDismissible: isDismissible,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(17),
          topRight: Radius.circular(17),
        ),
      ),
      barrierColor: barrierColor is Color ? barrierColor : null,
      isScrollControlled: isScrollControlled,
      enableDrag: enableDrag,
      // 设置背景蒙层为透明
      builder: (BuildContext context) {
        return child;
      },
    ).then((value) {
      // 当模态底部表单关闭时，这里的代码会被调用
      onDismiss?.call();
    });
  } catch (e) {
    logP(e.toString());
  }
}

//todo:高度计算
double getBottomSheetHeight(BuildContext context, int line, double itemHeight, int padding) {
  var mediaQuery = MediaQuery.of(context);
  var height = (line * itemHeight) + (line - 1) * padding;
  if (mediaQuery.size.height * 0.7 < height) {
    height = mediaQuery.size.height * 0.7;
  }

  return height;
}

//todo:版本更新弹窗
class VersionUpdateSheet extends StatelessWidget {
  final bool forceUpdate;
  final VoidCallback onUpdateTap;

  const VersionUpdateSheet({super.key, required this.forceUpdate, required this.onUpdateTap});

  @override
  Widget build(BuildContext context) {
    return Column(mainAxisSize: MainAxisSize.min, children: [
      const SizedBox(height: 11),
      Container(
        width: 34,
        height: 4,
        decoration:
            BoxDecoration(color: HexColor('#000000'), borderRadius: BorderRadius.circular(2)),
      ),
      const SizedBox(height: 18),
      Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('update_title'.trParams({'param': 'UrNovel'}),
              style:
                  TextStyle(fontSize: 17, color: HexColor('#1F1F2F'), fontWeight: FontWeight.bold),
              textAlign: TextAlign.left)
        ],
      ),
      const SizedBox(height: 8),
      Padding(
          padding: const EdgeInsets.symmetric(horizontal: 40),
          child: Text("update_desc".tr,
              style: TextStyle(color: HexColor('#7E839D'), fontSize: 12),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center)),
      const SizedBox(height: 36),
      SizedBox(
        width: 140,
        height: 102,
        child: Image.asset(
          'assets/images/common/img_update.png',
          fit: BoxFit.cover,
        ),
      ),
      const SizedBox(height: 40),
      Container(
          width: double.infinity,
          height: 44,
          margin: const EdgeInsets.symmetric(horizontal: 18),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: [HexColor('#0087FB'), HexColor('#0099F8')]),
          ),
          child: TextButton(
            onPressed: () {
              onUpdateTap();
            },
            child: Text('update_now'.tr,
                style: TextStyle(
                    color: HexColor('#FFFFFF'), fontSize: 16, fontWeight: FontWeight.bold)),
          )),
      if (!forceUpdate) const SizedBox(height: 15),
      if (!forceUpdate)
        Container(
            width: double.infinity,
            height: 44,
            margin: const EdgeInsets.symmetric(horizontal: 18),
            decoration:
                BoxDecoration(borderRadius: BorderRadius.circular(12), color: HexColor("#EBECEE")),
            child: TextButton(
              onPressed: () {
                Get.back();
              },
              child: Text('cancel'.tr,
                  style: TextStyle(
                      color: HexColor('#1F1F2F'), fontSize: 16, fontWeight: FontWeight.bold)),
            )),
      SizedBox(height: DeviceScreenUtil.instance.bottomSafeHeight + 30),
    ]);
  }
}

//todo:通知&跟踪权限弹窗
class NotificationTrackingPermissionSheet extends StatelessWidget {
  final bool isNotificationType;
  final VoidCallback onTurnOnTap;

  const NotificationTrackingPermissionSheet(
      {super.key, required this.isNotificationType, required this.onTurnOnTap});

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var width = mediaQuery.size.width * 0.763;
    var height = width / 0.677 + 10;
    var accessTopPadding = height * 0.294;
    var accessLeftPadding = width * 0.116;
    var accessWidth = width * 0.552;
    var titleTopPadding = height * 0.532;
    return SizedBox(
      width: width + 10,
      height: height,
      child: Stack(
        children: [
          Positioned(
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              child: Image.asset(
                isNotificationType
                    ? 'assets/images/common/icon_back_notifications.png'
                    : 'assets/images/common/icon_back_tracking.png',
                fit: BoxFit.cover,
              )),
          Positioned(
              top: accessTopPadding,
              left: accessLeftPadding,
              width: accessWidth,
              height: height * 0.117,
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(isNotificationType ? "allow_notifications".tr : 'tracking_access'.tr,
                    style: TextStyle(
                        color: HexColor('#555A65'), fontSize: 14, fontWeight: FontWeight.bold),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis),
              )),
          Positioned(
              top: titleTopPadding,
              left: 0,
              right: 0,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                      isNotificationType
                          ? "notification_access_title".tr
                          : 'tracking_access_title'.tr,
                      style: TextStyle(
                          color: HexColor('#1F1F2F'), fontSize: 15, fontWeight: FontWeight.bold),
                      maxLines: 1),
                  const SizedBox(height: 18),
                  Padding(
                      padding: EdgeInsets.symmetric(horizontal: 22),
                      child: Text(
                          isNotificationType
                              ? "notification_access_desc".tr
                              : 'tracking_access_desc'.tr,
                          style: TextStyle(color: HexColor('#7E839D'), fontSize: 12),
                          maxLines: null)),
                  const SizedBox(height: 25),
                  Container(
                      width: double.infinity,
                      height: 44,
                      margin: const EdgeInsets.symmetric(horizontal: 12),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        gradient: LinearGradient(
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                            colors: [HexColor('#0087FB'), HexColor('#0099F8')]),
                      ),
                      child: TextButton(
                        onPressed: () {
                          Get.back();
                          onTurnOnTap();
                        },
                        child: Text('turn_on'.tr,
                            style: TextStyle(
                                color: HexColor('#FFFFFF'),
                                fontSize: 16,
                                fontWeight: FontWeight.bold)),
                      )),
                  const SizedBox(height: 15),
                  Container(
                      width: double.infinity,
                      height: 44,
                      margin: const EdgeInsets.symmetric(horizontal: 12),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12), color: HexColor("#EBECEE")),
                      child: TextButton(
                        onPressed: () {
                          Get.back();
                        },
                        child: Text('cancel'.tr,
                            style: TextStyle(
                                color: HexColor('#1F1F2F'),
                                fontSize: 16,
                                fontWeight: FontWeight.bold)),
                      )),
                  SizedBox(height: 10),
                ],
              ))
        ],
      ),
    );
  }
}

//todo:书籍详情下载弹窗
class BookDownloadPaySheet extends StatelessWidget {
  final UserInfoModel? userInfoModel;
  final int? bookGoldCount;
  final String? title;
  final String? authorName;
  final VoidCallback onRecharge;

  const BookDownloadPaySheet(
      {super.key,
      required this.bookGoldCount,
      required this.title,
      required this.authorName,
      required this.userInfoModel,
      required this.onRecharge});

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var width = mediaQuery.size.width;
    var height = mediaQuery.size.width / 0.798 + 10;
    var buyButtonTitle =
        (bookGoldCount ?? 0) <= (userInfoModel?.goldCoin ?? 0) ? 'download_button'.tr : 'buy'.tr;
    return SizedBox(
      width: width,
      height: height,
      child: Stack(
        children: [
          Positioned(
              top: 10,
              left: 0,
              right: 0,
              bottom: 0,
              child: DecoratedBox(
                  decoration: BoxDecoration(
                      image: DecorationImage(
                          image: AssetImage('assets/images/novelReading/icon_download_bg.png'))))),
          Positioned(
              top: 140,
              left: 0,
              right: 0,
              bottom: 0,
              child: Column(
                mainAxisSize: MainAxisSize.min, // 控制高度
                children: [
                  // const SizedBox(height: 30),
                  Text("coins_count".trParams({"param": "${bookGoldCount ?? 0}"}),
                      style: TextStyle(
                          fontSize: 26,
                          color: HexColor('#1F1F2F'),
                          fontWeight: FontWeight.bold,
                          height: 1),
                      textAlign: TextAlign.center),
                  const SizedBox(height: 20),
                  Text('coin_count'.trParams({"param": "${userInfoModel?.goldCoin ?? 0}"}),
                      style: TextStyle(fontSize: 11, color: HexColor('#F42222'), height: 1),
                      textAlign: TextAlign.center),
                  const SizedBox(height: 32),
                  Text('novel'.tr,
                      style: TextStyle(fontSize: 13, color: HexColor('#7E839D'), height: 1),
                      textAlign: TextAlign.center),
                  const SizedBox(height: 12),
                  Text("\"${title ?? ''}\"",
                      style: TextStyle(
                          fontSize: 15,
                          color: HexColor('#1F1F2F'),
                          fontWeight: FontWeight.bold,
                          height: 1),
                      textAlign: TextAlign.center),
                  const SizedBox(height: 32),
                  Text('author'.tr,
                      style: TextStyle(fontSize: 13, color: HexColor('#7E839D'), height: 1),
                      textAlign: TextAlign.center),
                  const SizedBox(height: 12),
                  Text(authorName ?? '',
                      style: TextStyle(
                          fontSize: 15,
                          color: HexColor('#1F1F2F'),
                          fontWeight: FontWeight.bold,
                          height: 1),
                      textAlign: TextAlign.center),
                  SizedBox(
                      width: double.infinity,
                      child: Padding(
                          padding: EdgeInsets.only(top: 40, left: 18, right: 18),
                          child: DecoratedBox(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              gradient: LinearGradient(
                                  colors: [HexColor('#0087FB'), HexColor('#0099F8')]),
                            ),
                            child: TextButton(
                              onPressed: () {
                                Get.back();
                                onRecharge();
                              },
                              child: Text(buyButtonTitle,
                                  style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold)),
                            ),
                          ))),
                  SizedBox(
                      width: double.infinity,
                      child: Padding(
                        padding: EdgeInsets.only(top: 14, left: 18, right: 18),
                        child: TextButton(
                          onPressed: () {
                            Get.back();
                          },
                          child: Text('cancel'.tr,
                              style: TextStyle(
                                  color: HexColor('#4D4F56'),
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold)),
                        ),
                      )),
                ],
              )),
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Row(
              children: [
                Expanded(
                    child: Padding(
                        padding: EdgeInsets.only(left: 15, right: 15),
                        child: Text.rich(
                          TextSpan(
                              text: 'Download for Permanent Access',
                              style: TextStyle(
                                  color: HexColor('#0081EF'),
                                  fontSize: 22,
                                  fontWeight: FontWeight.bold)),
                          textAlign: TextAlign.center,
                        ))),
                Padding(
                    padding: EdgeInsets.only(top: 0, right: 0),
                    child: Image.asset(
                      'assets/images/novelReading/icon_download_book.png',
                      width: 140,
                      height: 107,
                    ))
              ],
            ),
          )
        ],
      ),
    );
  }
}

//todo:书籍分享
class BookShareSheet extends StatelessWidget {
  final Function(ShareType) onShareTap;

  const BookShareSheet({super.key, required this.onShareTap});

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    return SizedBox(
      width: double.infinity,
      child: Column(
        mainAxisSize: MainAxisSize.min, // 控制高度
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 26, left: 27, right: 27),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                //Facebook
                GestureDetector(
                  onTap: () {
                    Get.back();
                    onShareTap(ShareType.facebook);
                  },
                  child: Column(
                    children: [
                      Image.asset('assets/images/bookDetails/icon_share_facebook.png',
                          width: 50, height: 50, fit: BoxFit.cover),
                      const SizedBox(height: 10),
                      Text('Facebook',
                          style: TextStyle(
                              color: HexColor('#787C87'),
                              fontSize: 11,
                              fontWeight: FontWeight.bold),
                          textAlign: TextAlign.center),
                    ],
                  ),
                ),

                //Instagram
                GestureDetector(
                  onTap: () {
                    Get.back();
                    onShareTap(ShareType.instagram);
                  },
                  child: Column(
                    children: [
                      Image.asset('assets/images/bookDetails/icon_share_instagram.png',
                          width: 50, height: 50, fit: BoxFit.cover),
                      const SizedBox(height: 10),
                      Text('Instagram',
                          style: TextStyle(
                              color: HexColor('#787C87'),
                              fontSize: 11,
                              fontWeight: FontWeight.bold),
                          textAlign: TextAlign.center),
                    ],
                  ),
                ),

                //WhatsApp
                GestureDetector(
                  onTap: () {
                    Get.back();
                    onShareTap(ShareType.whatsApp);
                  },
                  child: Column(
                    children: [
                      Image.asset('assets/images/bookDetails/icon_share_whatsApp.png',
                          width: 50, height: 50, fit: BoxFit.cover),
                      const SizedBox(height: 10),
                      Text('WhatsApp',
                          style: TextStyle(
                              color: HexColor('#787C87'),
                              fontSize: 11,
                              fontWeight: FontWeight.bold),
                          textAlign: TextAlign.center),
                    ],
                  ),
                ),

                //TikTok
                GestureDetector(
                  onTap: () {
                    Get.back();
                    onShareTap(ShareType.tiktok);
                  },
                  child: Column(
                    children: [
                      Image.asset('assets/images/bookDetails/icon_share_tiktok.png',
                          width: 50, height: 50, fit: BoxFit.cover),
                      const SizedBox(height: 10),
                      Text('TikTok',
                          style: TextStyle(
                              color: HexColor('#787C87'),
                              fontSize: 11,
                              fontWeight: FontWeight.bold),
                          textAlign: TextAlign.center),
                    ],
                  ),
                ),

                //Copy Link
                GestureDetector(
                  onTap: () {
                    Get.back();
                    onShareTap(ShareType.copyLink);
                  },
                  child: Column(
                    children: [
                      Image.asset('assets/images/bookDetails/icon_share_link.png',
                          width: 50, height: 50, fit: BoxFit.cover),
                      const SizedBox(height: 10),
                      Text('copy_link'.tr,
                          style: TextStyle(
                              color: HexColor('#787C87'),
                              fontSize: 11,
                              fontWeight: FontWeight.bold),
                          textAlign: TextAlign.center),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(
            height: 25,
          ),
          Divider(height: 1, color: ColorsUtil.hexColor(0x444750, alpha: 0.06)),
          SizedBox(
            width: double.infinity,
            height: 50,
            child: TextButton(
                onPressed: () {
                  Get.back();
                },
                child: Text('cancel'.tr,
                    style: TextStyle(
                        color: HexColor('#4D4F56'), fontSize: 17, fontWeight: FontWeight.bold))),
          ),
          SizedBox(height: mediaQuery.padding.bottom + 10)
        ],
      ),
    );
  }
}

//todo:设置登出弹窗
class SettingsLoginOutSheet extends StatelessWidget {
  final VoidCallback onConfirm;

  const SettingsLoginOutSheet({super.key, required this.onConfirm});

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    return SizedBox(
      width: double.infinity,
      child: Column(
        mainAxisSize: MainAxisSize.min, // 控制高度
        children: [
          const SizedBox(
            height: 32,
          ),
          Text('sign_out_pop'.tr,
              style: TextStyle(
                  fontSize: 15,
                  color: HexColor('#7E839D'),
                  fontWeight: FontWeight.bold,
                  height: 1)),
          const SizedBox(
            height: 26,
          ),
          Divider(height: 1, color: ColorsUtil.hexColor(0x444750, alpha: 0.06)),
          SizedBox(
            height: 50,
            width: double.infinity,
            child: TextButton(
              onPressed: () {
                Get.back();
                onConfirm.call();
              },
              child: Text('sign_out'.tr,
                  style: TextStyle(
                      color: HexColor('#F42222'), fontSize: 17, fontWeight: FontWeight.bold)),
            ),
          ),
          Divider(height: 1, color: ColorsUtil.hexColor(0x444750, alpha: 0.06)),
          SizedBox(
            height: 50,
            width: double.infinity,
            child: TextButton(
              onPressed: () {
                Get.back();
              },
              child: Text('cancel'.tr,
                  style: TextStyle(
                      color: HexColor('#4D4F56'), fontSize: 17, fontWeight: FontWeight.bold)),
            ),
          ),
          SizedBox(height: mediaQuery.padding.bottom + 20)
        ],
      ),
    );
  }
}

//todo:设置Gender
class SettingsGenderSheet extends StatefulWidget {
  final String gender;
  final Function(String) onGenderChanged;

  const SettingsGenderSheet({super.key, required this.gender, required this.onGenderChanged});

  @override
  State<SettingsGenderSheet> createState() => _SettingsGenderSheetState();
}

class _SettingsGenderSheetState extends State<SettingsGenderSheet> {
  late final List genderList;
  int selectedIndex = 2;
  late double listHeight;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    genderList = ["gender_male".tr, "gender_female".tr, "gender_other".tr];
    selectedIndex = genderList.indexOf(widget.gender);
    listHeight = genderList.length * 63.0;
  }

  @override
  Widget build(BuildContext context) {
    return Column(mainAxisSize: MainAxisSize.min, children: [
      const SizedBox(height: 11),
      Container(
        width: 34,
        height: 4,
        decoration:
            BoxDecoration(color: HexColor('#000000'), borderRadius: BorderRadius.circular(2)),
      ),
      const SizedBox(height: 10),
      Padding(
          padding: const EdgeInsets.only(left: 19),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text('gender'.tr,
                  style: TextStyle(
                      fontSize: 18, color: HexColor('#1F1F2F'), fontWeight: FontWeight.bold),
                  textAlign: TextAlign.left)
            ],
          )),
      const SizedBox(height: 20),
      Container(
        height: listHeight,
        margin: const EdgeInsets.only(left: 11, bottom: 11, right: 11),
        decoration: BoxDecoration(
          color: HexColor('#FFFFFF'),
          borderRadius: BorderRadius.circular(12),
        ),
        child: ListView.builder(
            itemCount: genderList.length,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              return GestureDetector(
                onTap: () {
                  if (mounted) {
                    setState(() {
                      selectedIndex = index;
                    });
                  }
                  widget.onGenderChanged(genderList[index]);
                },
                child: Container(
                  height: 62,
                  width: double.infinity,
                  color: Colors.transparent,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Padding(
                          padding: const EdgeInsets.only(left: 36, right: 36),
                          child: SizedBox(
                              height: 61,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Text(genderList[index],
                                      style: TextStyle(
                                          fontSize: 15,
                                          color: HexColor("#1F1F2F"),
                                          fontWeight: FontWeight.bold)),
                                  Image.asset(
                                      selectedIndex == index
                                          ? 'assets/images/bookDetails/icon_choosed.png'
                                          : 'assets/images/bookDetails/icon_unchoose.png',
                                      width: 20,
                                      height: 20),
                                ],
                              ))),
                      if (index != genderList.length - 1)
                        Padding(
                            padding: const EdgeInsets.only(left: 19, right: 10),
                            child: Divider(
                                height: 1, color: ColorsUtil.hexColor(0x444750, alpha: 0.06)))
                    ],
                  ),
                ),
              );
            }),
      ),
      SizedBox(height: MediaQuery.of(context).padding.bottom)
    ]);
  }
}

//todo:account 充值
class AccountPurchaseSheet extends StatelessWidget {
  final List<GoodsListItem?>? goodList;
  final bool isSheetType;
  final GoodsType goodsType;
  final Function(GoodsListItem? item) onProductTap;

  const AccountPurchaseSheet(
      {super.key,
      required this.goodList,
      this.isSheetType = true,
      this.goodsType = GoodsType.purchaseCoins,
      required this.onProductTap});

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);

    List<GoodsListItem> firstChargeList = [];
    List<GoodsListItem> otherActivityList = [];
    List<GoodsListItem> normalList = [];
    goodList?.forEach((item) {
      if (item?.activityPricePower == true) {
        //活动类型 ("First_Charge", "首充活动"), ("Discount", "额外赠送&折扣活动"), ("Best_Deal", "最佳交易"),
        if (item?.activityType == 'First_Charge') {
          firstChargeList.add(item!);
        } else if (item?.activityType == 'Discount' || item?.activityType == 'Best_Deal') {
          otherActivityList.add(item!);
        } else {
          normalList.add(item!);
        }
      } else {
        normalList.add(item!);
      }
    });
    var count = firstChargeList.length +
        (otherActivityList.isNotEmpty ? 1 : 0) +
        (normalList.isNotEmpty ? 1 : 0);

    return SizedBox(
      width: double.infinity,
      height: mediaQuery.size.height * 0.85,
      child: Stack(
        children: [
          Positioned(
              top: isSheetType ? 43 : 0,
              left: 0,
              right: 0,
              bottom: 0,
              child: DecoratedBox(
                decoration: BoxDecoration(
                    color: goodsType == GoodsType.purchaseDiamonds
                        ? HexColor("#000000")
                        : HexColor("#F4F5F6"),
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12), topRight: Radius.circular(12))),
                child: Column(
                  mainAxisSize: MainAxisSize.min, // 控制高
                  children: [
                    if (isSheetType)
                      Container(
                        margin: const EdgeInsets.only(top: 11),
                        height: 4,
                        width: 34,
                        decoration: BoxDecoration(
                            color: HexColor('#D4D5D7'), borderRadius: BorderRadius.circular(2)),
                      ),
                    if (isSheetType) const SizedBox(height: 22),
                    if (isSheetType)
                      Text('buy_coins'.tr,
                          style: TextStyle(
                              fontSize: 26,
                              color: HexColor('#1F1F2F'),
                              fontWeight: FontWeight.bold,
                              height: 1),
                          textAlign: TextAlign.center),
                    if (isSheetType) const SizedBox(height: 23),
                    0 < count
                        ? Expanded(
                            child: ListView.builder(
                                itemCount: count,
                                itemBuilder: (context, index) {
                                  if (firstChargeList.isNotEmpty &&
                                      index < firstChargeList.length) {
                                    ///首冲商品
                                    var item = firstChargeList[index];
                                    return FirstChargeCard(
                                        item: item,
                                        goodsType: goodsType,
                                        onProductTap: (item) {
                                          if (isSheetType) {
                                            Get.back();
                                          }
                                          onProductTap(item);
                                        });
                                  } else if (otherActivityList.isNotEmpty &&
                                      index == firstChargeList.length) {
                                    ///其他活动商品
                                    return Padding(
                                        padding: EdgeInsets.only(top: 8),
                                        child: OtherActivityCard(
                                            otherActivityList: otherActivityList,
                                            goodsType: goodsType,
                                            onProductTap: (item) {
                                              if (isSheetType) {
                                                Get.back();
                                              }
                                              onProductTap(item);
                                            }));
                                  } else {
                                    ///其他商品
                                    return Padding(
                                        padding: EdgeInsets.only(top: 9, bottom: 9),
                                        child: NormalCard(
                                            normalList: normalList,
                                            goodsType: goodsType,
                                            onProductTap: (item) {
                                              if (isSheetType) {
                                                Get.back();
                                              }
                                              onProductTap(item);
                                            }));
                                  }
                                }))
                        : Expanded(child: NoLoadView()),
                  ],
                ),
              )),
          if (isSheetType)
            Positioned(
                top: 0,
                left: 2,
                width: 112,
                height: 92,
                child: Image.asset('assets/images/profile/purchase/icon_coin_top.png'))
        ],
      ),
    );
  }
}

///首冲商品
class FirstChargeCard extends StatefulWidget {
  final GoodsListItem? item;
  final bool isUnLock;
  final GoodsType goodsType;
  final Function(GoodsListItem? item) onProductTap;

  const FirstChargeCard(
      {super.key,
      required this.item,
      this.isUnLock = false,
      required this.goodsType,
      required this.onProductTap});

  @override
  State<FirstChargeCard> createState() => _FirstChargeCardState();
}

class _FirstChargeCardState extends State<FirstChargeCard> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var width = mediaQuery.size.width;
    int coinsGift = widget.item?.coinsGift ?? 0;

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        widget.onProductTap(widget.item);
      },
      child: SizedBox(
        width: width,
        height: 100,
        child: Stack(
          children: [
            Positioned(
                top: 16,
                left: widget.isUnLock ? 4 : 18,
                right: widget.isUnLock ? 0 : 18,
                bottom: 0,
                child: Container(
                  padding: EdgeInsets.only(top: 13, left: 36, right: 50),
                  decoration: BoxDecoration(
                      color: widget.goodsType == GoodsType.purchaseDiamonds
                          ? HexColor('#2D2837')
                          : HexColor('#FBF6E6'),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: HexColor('#C7781B'), width: 1)),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Row(
                            children: [
                              Image.asset(
                                widget.goodsType == GoodsType.purchaseDiamonds
                                    ? 'assets/images/chat/chat_icon_diamond.png'
                                    : 'assets/images/profile/purchase/icon_coin.png',
                                width: widget.goodsType == GoodsType.purchaseDiamonds ? 27 : 23,
                                height: widget.goodsType == GoodsType.purchaseDiamonds ? 27 : 23,
                              ),
                              SizedBox(width: 11),
                              Text('${widget.item?.skuValue ?? ""}',
                                  style: TextStyle(
                                      color: widget.goodsType == GoodsType.purchaseDiamonds
                                          ? HexColor('#FFFFFF')
                                          : HexColor('#C79648'),
                                      fontSize: 22,
                                      fontWeight: FontWeight.bold,
                                      height: 1)),
                            ],
                          ),
                          const SizedBox(height: 11),
                          if (0 < coinsGift)
                            Text("bonus_count".trParams({'param': coinsGift.toString()}),
                                style: TextStyle(
                                    color: widget.goodsType == GoodsType.purchaseDiamonds
                                        ? HexColor('#D535B4')
                                        : HexColor('#FE4D5E'),
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.bold,
                                    height: 1)),
                        ],
                      ),
                      if (widget.item?.activityPricePower == true &&
                          isAvailable(widget.item?.introductoryPrice))
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text("${widget.item?.currencySymbol} ${widget.item?.introductoryPrice}",
                                style: TextStyle(
                                    color: widget.goodsType == GoodsType.purchaseDiamonds
                                        ? HexColor('#FFFFFF')
                                        : HexColor('#1F1F2F'),
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    height: 1)),
                            SizedBox(height: 4),
                            RichText(
                              text: TextSpan(children: [
                                TextSpan(
                                    text: '(',
                                    style: TextStyle(
                                      color: widget.goodsType == GoodsType.purchaseDiamonds
                                          ? HexColor('#818181')
                                          : HexColor('#5D6884'),
                                      fontSize: 12,
                                    )),
                                TextSpan(
                                    text: "${widget.item?.showPrice}",
                                    style: TextStyle(
                                      color: widget.goodsType == GoodsType.purchaseDiamonds
                                          ? HexColor('#818181')
                                          : HexColor('#5D6884'),
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                      height: 1,
                                      decoration: TextDecoration.lineThrough,
                                      decorationColor:
                                          widget.goodsType == GoodsType.purchaseDiamonds
                                              ? HexColor('#818181')
                                              : HexColor('#1F233B'),
                                      decorationStyle: TextDecorationStyle.solid,
                                      decorationThickness: 2,
                                    )),
                                TextSpan(
                                    text: ')',
                                    style: TextStyle(
                                      color: widget.goodsType == GoodsType.purchaseDiamonds
                                          ? HexColor('#818181')
                                          : HexColor('#5D6884'),
                                      fontSize: 12,
                                    )),
                              ]),
                            ),
                          ],
                        )
                      else
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text("${widget.item?.showPrice}",
                                style: TextStyle(
                                    color: widget.goodsType == GoodsType.purchaseDiamonds
                                        ? HexColor('#FFFFFF')
                                        : HexColor('#1F1F2F'),
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    height: 1)),
                          ],
                        )
                    ],
                  ),
                )),
            Positioned(
                top: 0,
                left: widget.isUnLock ? 0 : 14,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Image.asset('assets/images/profile/purchase/icon_top_first_charge.png',
                        fit: BoxFit.cover, width: 22, height: 28),
                    Container(
                      height: 19,
                      decoration: BoxDecoration(
                        color: HexColor('#FE6F0C'),
                        borderRadius: BorderRadius.only(
                            topRight: Radius.circular(8), bottomRight: Radius.circular(8)),
                      ),
                      child: Padding(
                          padding: EdgeInsets.only(left: 5, right: 10),
                          child: Align(
                            alignment: Alignment.centerLeft,
                            child: Text('1st_top_up'.tr,
                                style: TextStyle(
                                    color: HexColor('#FFFB18'),
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold),
                                textAlign: TextAlign.center),
                          )),
                    )
                  ],
                )),
            if (isAvailable(widget.item?.countDownSecond))
              Positioned(
                  top: 17,
                  right: widget.isUnLock ? 1 : 19,
                  height: 18,
                  child: Container(
                      padding: EdgeInsets.only(left: 22, right: 22),
                      decoration: BoxDecoration(
                        color: HexColor('#F0CB94'),
                        borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(12), topRight: Radius.circular(12)),
                      ),
                      child: CountDownCard(countDownSecond: widget.item?.countDownSecond))),
            Positioned(
                bottom: 0,
                right: 19,
                width: 71,
                height: 62,
                child: DecoratedBox(
                    decoration: BoxDecoration(
                        image: DecorationImage(
                            image:
                                AssetImage('assets/images/profile/purchase/icon_first_charge.png'),
                            fit: BoxFit.cover)))),
          ],
        ),
      ),
    );
  }
}

///其他活动商品
class OtherActivityCard extends StatelessWidget {
  final List<GoodsListItem>? otherActivityList;
  final GoodsType goodsType;
  final Function(GoodsListItem? item) onProductTap;

  const OtherActivityCard(
      {super.key, this.otherActivityList, required this.goodsType, required this.onProductTap});

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var width = mediaQuery.size.width;
    var cardWidth = (width - 14) / 2.0;
    var cardHeight = 115;
    var count = otherActivityList?.length ?? 0;
    var totalHeight = 0.0;
    if (count == 1) {
      totalHeight = cardHeight + 8;
    } else {
      totalHeight = (cardHeight + 8) * count / 2 - 8 + (count % 2) * 8;
    }

    return SizedBox(
      width: width,
      height: totalHeight,
      child: GridView.builder(
          physics: NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: cardWidth / cardHeight,
            mainAxisSpacing: 8,
            crossAxisSpacing: 14,
          ),
          itemCount: count,
          itemBuilder: (context, index) {
            if (index < count) {
              var item = otherActivityList?[index];
              int coinsGift = item?.coinsGift ?? 0;
              bool isDiscount = item?.activityType == 'Discount';
              return GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  onProductTap(item);
                },
                child: Stack(
                  children: [
                    if (isAvailable(item?.countDownSecond))
                      Positioned(
                          left: index % 2 == 0 ? 18 : 0,
                          right: index % 2 == 0 ? 0 : 18,
                          bottom: 0,
                          height: 36,
                          child: DecoratedBox(
                              decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                      begin: Alignment.centerLeft,
                                      end: Alignment.centerRight,
                                      colors: item?.activityType == 'Discount'
                                          ? [HexColor('#404243'), HexColor('#404243')]
                                          : [HexColor('#FF87C0'), HexColor('#FC348C')]),
                                  borderRadius: BorderRadius.only(
                                      bottomLeft: Radius.circular(12),
                                      bottomRight: Radius.circular(12))),
                              child: Padding(
                                  padding: EdgeInsets.only(top: 16),
                                  child: CountDownCard(countDownSecond: item?.countDownSecond)))),
                    Positioned(
                        top: 12,
                        left: index % 2 == 0 ? 18 : 0,
                        right: index % 2 == 0 ? 0 : 18,
                        bottom: 20,
                        child: Container(
                          padding: EdgeInsets.only(top: 11),
                          decoration: BoxDecoration(
                            color: HexColor('#FFFFFF'),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const SizedBox(height: 4),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Image.asset(
                                    goodsType == GoodsType.purchaseDiamonds
                                        ? 'assets/images/chat/chat_icon_diamond.png'
                                        : 'assets/images/profile/purchase/icon_coin.png',
                                    width: 23,
                                    height: 23,
                                  ),
                                  SizedBox(width: 11),
                                  Text('${item?.skuValue ?? ""}',
                                      style: TextStyle(
                                          color: goodsType == GoodsType.purchaseDiamonds
                                              ? HexColor('#FFFFFF')
                                              : HexColor('#C79648'),
                                          fontSize: 22,
                                          fontWeight: FontWeight.bold,
                                          height: 1)),
                                ],
                              ),
                              const SizedBox(height: 4),
                              if (0 < coinsGift)
                                Text("bonus_count".trParams({'param': coinsGift.toString()}),
                                    style: TextStyle(
                                        color: goodsType == GoodsType.purchaseDiamonds
                                            ? HexColor('#D535B4')
                                            : HexColor('#C79648'),
                                        fontSize: 12.sp,
                                        height: 1)),
                              if (0 < coinsGift) const SizedBox(height: 8),
                              if (item?.activityPricePower == true &&
                                  isAvailable(item?.introductoryPrice))
                                Text.rich(TextSpan(
                                    text: "${item?.currencySymbol} ${item?.introductoryPrice}",
                                    style: TextStyle(
                                        color: goodsType == GoodsType.purchaseDiamonds
                                            ? HexColor('#FFFFFF')
                                            : HexColor('#1F1F2F'),
                                        fontSize: 15,
                                        fontWeight: FontWeight.bold,
                                        height: 1),
                                    children: [
                                      TextSpan(
                                          text: '(',
                                          style: TextStyle(
                                            color: goodsType == GoodsType.purchaseDiamonds
                                                ? HexColor('#818181')
                                                : HexColor('#5D6884'),
                                            fontSize: 12,
                                          )),
                                      TextSpan(
                                          text: "${item?.showPrice}",
                                          style: TextStyle(
                                            color: goodsType == GoodsType.purchaseDiamonds
                                                ? HexColor('#818181')
                                                : HexColor('#5D6884'),
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                            height: 1,
                                            decoration: TextDecoration.lineThrough,
                                            decorationColor: goodsType == GoodsType.purchaseDiamonds
                                                ? HexColor('#818181')
                                                : HexColor('#1F233B'),
                                            decorationStyle: TextDecorationStyle.solid,
                                            decorationThickness: 2,
                                          )),
                                      TextSpan(
                                          text: ')',
                                          style: TextStyle(
                                            color: goodsType == GoodsType.purchaseDiamonds
                                                ? HexColor('#818181')
                                                : HexColor('#5D6884'),
                                            fontSize: 12,
                                          )),
                                    ]))
                              else
                                Text(item?.showPrice ?? "",
                                    style: TextStyle(
                                        color: goodsType == GoodsType.purchaseDiamonds
                                            ? HexColor('#FFFFFF')
                                            : HexColor('#1F1F2F'),
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        height: 1)),
                              const SizedBox(height: 4),
                            ],
                          ),
                        )),
                    Positioned(
                        top: 0,
                        left: isDiscount
                            ? index % 2 == 0
                                ? 14
                                : 0
                            : count == 1 || index % 2 == 0
                                ? 18
                                : 0,
                        height: isDiscount ? null : 22,
                        child: isDiscount
                            ? Row(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Image.asset(
                                      'assets/images/profile/purchase/icon_top_discount.png',
                                      fit: BoxFit.cover,
                                      width: 25,
                                      height: 24),
                                  Container(
                                      height: 19,
                                      decoration: BoxDecoration(
                                        color: HexColor('#404243'),
                                        borderRadius: BorderRadius.only(
                                            topRight: Radius.circular(8),
                                            bottomRight: Radius.circular(8)),
                                      ),
                                      child: Padding(
                                        padding: EdgeInsets.only(left: 5, right: 10),
                                        child: Align(
                                            alignment: Alignment.centerLeft,
                                            child: Text('${item?.activityName}',
                                                style: TextStyle(
                                                    color: HexColor('#F7DCB1'),
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.bold),
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                                textAlign: TextAlign.center)),
                                      ))
                                ],
                              )
                            : DecoratedBox(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                      begin: Alignment.topCenter,
                                      end: Alignment.bottomCenter,
                                      colors: [HexColor('#FF87C0'), HexColor('#FC348C')]),
                                  borderRadius: BorderRadius.only(
                                      topLeft: Radius.circular(8),
                                      topRight: Radius.circular(8),
                                      bottomRight: Radius.circular(8)),
                                ),
                                child: Padding(
                                  padding: EdgeInsets.only(top: 2, left: 15, right: 15, bottom: 2),
                                  child: Align(
                                      alignment: Alignment.centerLeft,
                                      child: Text(
                                        'best_deal'.tr,
                                        style: TextStyle(
                                            color: HexColor('#FFFFFF'),
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      )),
                                ))),
                    Positioned(
                        bottom: 20,
                        right: index % 2 == 0 ? 0 : 18,
                        width: 50,
                        height: 50,
                        child: DecoratedBox(
                            decoration: BoxDecoration(
                                image: DecorationImage(
                                    image: AssetImage(item?.activityType == 'Discount'
                                        ? 'assets/images/profile/purchase/icon_bonus.png'
                                        : 'assets/images/profile/purchase/icon_best_deal.png'),
                                    fit: BoxFit.cover)))),
                  ],
                ),
              );
            }
            return Container();
          }),
    );
  }
}

///普通商品
class NormalCard extends StatelessWidget {
  final List<GoodsListItem>? normalList;
  final GoodsType goodsType;
  final Function(GoodsListItem? item) onProductTap;

  const NormalCard(
      {super.key, required this.normalList, required this.goodsType, required this.onProductTap});

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var width = mediaQuery.size.width;
    var cardWidth = (width - 50) / 2.0;
    var cardHeight = 84.0;
    var count = normalList?.length ?? 0;
    var height = count / 2 * (cardHeight + 9) + (count % 2) * cardHeight;

    return SizedBox(
      width: width,
      height: height,
      child: GridView.builder(
          physics: NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.only(left: 18, right: 18),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: cardWidth / cardHeight,
            mainAxisSpacing: 9,
            crossAxisSpacing: 14,
          ),
          itemCount: count,
          itemBuilder: (context, index) {
            if (index < count) {
              var item = normalList?[index];
              int coinsGift = item?.coinsGift ?? 0;
              return DecoratedBox(
                decoration: BoxDecoration(
                  color: goodsType == GoodsType.purchaseDiamonds
                      ? HexColor('#2D2837')
                      : HexColor('#FFFFFF'),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: TextButton(
                    onPressed: () {
                      onProductTap(item);
                    },
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Image.asset(
                              goodsType == GoodsType.purchaseDiamonds
                                  ? 'assets/images/chat/chat_icon_diamond.png'
                                  : 'assets/images/profile/purchase/icon_coin.png',
                              width: 23,
                              height: 23,
                            ),
                            SizedBox(width: 11),
                            Text('${item?.skuValue ?? ""}',
                                style: TextStyle(
                                    color: goodsType == GoodsType.purchaseDiamonds
                                        ? HexColor('#FFFFFF')
                                        : HexColor('#C79648'),
                                    fontSize: 22,
                                    fontWeight: FontWeight.bold,
                                    height: 1)),
                          ],
                        ),
                        const SizedBox(height: 8),
                        if (0 < coinsGift)
                          Text("bonus_count".trParams({'param': coinsGift.toString()}),
                              style: TextStyle(
                                  color: goodsType == GoodsType.purchaseDiamonds
                                      ? HexColor('#D535B4')
                                      : HexColor('#C79648'),
                                  fontSize: 12.sp,
                                  height: 1)),
                        if (0 < coinsGift) const SizedBox(height: 8),
                        Text(item?.showPrice ?? "",
                            style: TextStyle(
                                color: goodsType == GoodsType.purchaseDiamonds
                                    ? HexColor('#FFFFFF')
                                    : HexColor('#1F1F2F'),
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                height: 1)),
                      ],
                    )),
              );
            }
            return Container();
          }),
    );
  }
}

//todo:倒计时
class CountDownCard extends StatefulWidget {
  final int? countDownSecond;
  final bool isSmallFont;
  final VoidCallback? onTimerEnd;

  const CountDownCard(
      {super.key, required this.countDownSecond, this.isSmallFont = false, this.onTimerEnd});

  @override
  State<CountDownCard> createState() => CountDownCardState();
}

class CountDownCardState extends State<CountDownCard> with AutomaticKeepAliveClientMixin {
  late Timer? timer;
  late String countDownText;

  @override
  void initState() {
    super.initState();

    startTimer();
  }

  @override
  void dispose() {
    timer?.cancel();
    super.dispose();
  }

  startTimer() {
    int countDownSecond = widget.countDownSecond ?? 0;
    if (mounted && 0 < countDownSecond) {
      countDownText = getActivityDataStr(countDownSecond);

      timer = timerStart(1, countDownSecond, (dateStr, isEnd) {
        setState(() {
          countDownText = dateStr;
        });
        if (isEnd) {
          widget.onTimerEnd?.call();
        }
      });
    }
  }

  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // 调用父类的build方法
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Image.asset(
          'assets/images/profile/purchase/icon_timer.png',
          width: widget.isSmallFont ? 9 : 11,
          height: widget.isSmallFont ? 11 : 13,
        ),
        const SizedBox(width: 6),
        Text(
          countDownText,
          style: TextStyle(
              color: HexColor('#FFFFFF'),
              fontSize: widget.isSmallFont ? 8 : 12,
              fontWeight: FontWeight.bold),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        )
      ],
    );
  }
}

//todo:会员弹窗
class PremiumBuyNowSheet extends StatelessWidget {
  final PremiumType premiumType;
  final Function(bool isBuyNow) onTap;

  const PremiumBuyNowSheet({super.key, required this.premiumType, required this.onTap});

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(begin: Alignment.topCenter, end: Alignment.bottomCenter, colors: [
          HexColor('#20243D'),
          HexColor('#2B304D'),
        ]),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min, // 控制高
        children: [
          Container(
            margin: const EdgeInsets.only(top: 11),
            height: 4,
            width: 34,
            decoration: BoxDecoration(
                color: ColorsUtil.hexColor(0xFFFFFF, alpha: 0.1),
                borderRadius: BorderRadius.circular(2)),
          ),
          const SizedBox(height: 22),
          Image.asset("assets/images/profile/icon_premium_packages.png", width: 30, height: 30),
          const SizedBox(height: 12),
          Text(
              premiumType == PremiumType.buyNow
                  ? 'limited_offer'.tr
                  : premiumType == PremiumType.expiringSoon
                      ? 'expiring_soon_title'.tr
                      : 'expird_title'.tr,
              style: TextStyle(
                  fontSize: 18, color: HexColor('#F6D88F'), fontWeight: FontWeight.bold, height: 1),
              textAlign: TextAlign.center),
          const SizedBox(height: 15),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 18),
              child: Text(
                  premiumType == PremiumType.buyNow
                      ? 'offer_popup'.tr
                      : premiumType == PremiumType.expiringSoon
                          ? 'expiring_soon_desc'.tr
                          : 'expird_desc'.tr,
                  style: TextStyle(
                      fontSize: 13,
                      color: HexColor('#CFB693'),
                      fontWeight: FontWeight.bold,
                      height: 1),
                  textAlign: TextAlign.center)),
          const SizedBox(height: 22),
          Container(
            width: double.infinity,
            height: 44,
            margin: const EdgeInsets.symmetric(horizontal: 18),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              gradient:
                  LinearGradient(begin: Alignment.centerLeft, end: Alignment.centerRight, colors: [
                HexColor('#ECCA95'),
                HexColor('#F4D8AC'),
              ]),
            ),
            child: TextButton(
              onPressed: () {
                Get.back();
                onTap(true);
              },
              child: Text(premiumType == PremiumType.buyNow ? "buy_now".tr : "renew_now".tr,
                  style: TextStyle(
                      color: HexColor('#704E1F'), fontSize: 16, fontWeight: FontWeight.bold)),
            ),
          ),
          const SizedBox(height: 15),
          Container(
            width: double.infinity,
            height: 44,
            margin: const EdgeInsets.symmetric(horizontal: 18),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: ColorsUtil.hexColor(0xFFFFFF, alpha: 0.1),
            ),
            child: TextButton(
              onPressed: () {
                Get.back();
                onTap(false);
              },
              child: Text("cancel".tr,
                  style: TextStyle(
                      color: HexColor('#F6D88F'), fontSize: 16, fontWeight: FontWeight.bold)),
            ),
          ),
          SizedBox(height: mediaQuery.padding.bottom + 20),
        ],
      ),
    );
  }
}

//todo:书籍详情分享阅读经验
class UpdateBookReadingExperience extends StatelessWidget {
  final bool isShowCancel;

  const UpdateBookReadingExperience({super.key, this.isShowCancel = true});

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    return SizedBox(
      width: double.infinity,
      child: Column(
        mainAxisSize: MainAxisSize.min, // 控制高度
        children: [
          Container(
            margin: const EdgeInsets.only(top: 11),
            height: 4,
            width: 34,
            decoration:
                BoxDecoration(color: HexColor("#D4D5D7"), borderRadius: BorderRadius.circular(2)),
          ),
          const SizedBox(height: 18),
          Text('update_title'.tr,
              style: TextStyle(
                  fontSize: 17,
                  color: HexColor('#1F1F2F'),
                  fontWeight: FontWeight.bold,
                  height: 1)),
          const SizedBox(height: 8),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 44),
              child: Text('update_desc'.tr,
                  style: TextStyle(
                      fontSize: 13, color: HexColor('#7E839D'), fontWeight: FontWeight.bold),
                  textAlign: TextAlign.center)),
          const SizedBox(height: 36),
          Image.asset('assets/images/bookDetails/img_update_readingExperience.png',
              width: 140, height: 102, fit: BoxFit.cover),
          const SizedBox(height: 40),
          Container(
            height: 44,
            width: double.infinity,
            margin: const EdgeInsets.symmetric(horizontal: 18),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: LinearGradient(
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                    colors: [
                      HexColor("#0087FB"),
                      HexColor("#0099F8"),
                    ])),
            child: TextButton(
              onPressed: () {
                Get.back();
              },
              child: Text('update_now'.tr,
                  style: TextStyle(
                      color: HexColor('#FFFFFF'), fontSize: 16, fontWeight: FontWeight.bold)),
            ),
          ),
          if (isShowCancel) const SizedBox(height: 15),
          if (isShowCancel)
            Container(
              height: 44,
              width: double.infinity,
              margin: const EdgeInsets.symmetric(horizontal: 18),
              decoration: BoxDecoration(
                color: HexColor("#EBECEE"),
                borderRadius: BorderRadius.circular(12),
              ),
              child: TextButton(
                onPressed: () {
                  Get.back();
                },
                child: Text('cancel'.tr,
                    style: TextStyle(
                        color: HexColor('#1F1F2F'), fontSize: 16, fontWeight: FontWeight.bold)),
              ),
            ),
          SizedBox(height: mediaQuery.padding.bottom)
        ],
      ),
    );
  }
}

//todo:书籍添加到like
class AddBookToLikeSheet extends StatelessWidget {
  const AddBookToLikeSheet({super.key});

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    return SizedBox(
      width: double.infinity,
      child: Column(
        mainAxisSize: MainAxisSize.min, // 控制高度
        children: [
          Container(
            margin: const EdgeInsets.only(top: 11),
            height: 4,
            width: 34,
            decoration:
                BoxDecoration(color: HexColor("#D4D5D7"), borderRadius: BorderRadius.circular(2)),
          ),
          const SizedBox(height: 18),
          Text('add_likes_title'.tr,
              style: TextStyle(
                  fontSize: 17,
                  color: HexColor('#1F1F2F'),
                  fontWeight: FontWeight.bold,
                  height: 1)),
          const SizedBox(height: 12),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 44),
              child: Text('add_likes_desc'.tr,
                  style: TextStyle(
                      fontSize: 13, color: HexColor('#7E839D'), fontWeight: FontWeight.bold),
                  textAlign: TextAlign.center)),
          const SizedBox(height: 18),
          Container(
            height: 44,
            width: double.infinity,
            margin: const EdgeInsets.symmetric(horizontal: 18),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: LinearGradient(
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                    colors: [
                      HexColor("#0087FB"),
                      HexColor("#0099F8"),
                    ])),
            child: TextButton(
              onPressed: () {
                Get.back();
              },
              child: Text('add'.tr,
                  style: TextStyle(
                      color: HexColor('#FFFFFF'), fontSize: 16, fontWeight: FontWeight.bold)),
            ),
          ),
          const SizedBox(height: 15),
          Container(
            height: 44,
            width: double.infinity,
            margin: const EdgeInsets.symmetric(horizontal: 18),
            decoration: BoxDecoration(
              color: HexColor("#EBECEE"),
              borderRadius: BorderRadius.circular(12),
            ),
            child: TextButton(
              onPressed: () {
                Get.back();
              },
              child: Text('cancel'.tr,
                  style: TextStyle(
                      color: HexColor('#1F1F2F'), fontSize: 16, fontWeight: FontWeight.bold)),
            ),
          ),
          SizedBox(height: mediaQuery.padding.bottom)
        ],
      ),
    );
  }
}

//todo:书籍阅读章节Chapters选择
class NovelChaptersSheet extends BaseLessWidget {
  final BookDetailInfoResultModel? detailInfoResultModel;
  final int? currentChapterId;
  final bool isFromVoce;
  final VoidCallback onBookHeaderTap;
  final Function(ChapterVoModel? item) onChaptersTap;

  NovelChaptersSheet(
      {super.key,
      required this.detailInfoResultModel,
      required this.currentChapterId,
      this.isFromVoce = false,
      required this.onBookHeaderTap,
      required this.onChaptersTap});

  static final ScrollController _controller = ScrollController();

  _seekToCurrentChapter() {
    Future.delayed(Duration(milliseconds: 100), () {
      if (isAvailable(detailInfoResultModel?.chapterVoList) && isAvailable(currentChapterId)) {
        for (var i = 0; i < detailInfoResultModel!.chapterVoList!.length; i++) {
          ChapterVoModel? model = detailInfoResultModel!.chapterVoList![i];
          if (model?.id == currentChapterId) {
            _controller.jumpTo(i * 48.0 - 144);
            break;
          }
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    _seekToCurrentChapter();
    var menuHeight = isFromVoce
        ? DeviceScreenUtil.instance.bottomSafeHeight
        : DeviceScreenUtil.instance.bottomSafeHeight + ReaderUtil.menuHeight;
    int count = detailInfoResultModel?.chapterVoList?.length ?? 0;
    String? cover = detailInfoResultModel?.cover;
    String? title = detailInfoResultModel?.title;

    return Container(
        width: double.infinity,
        height: getBottomSheetHeight(context, count + 3, 48.0, 0),
        margin: EdgeInsets.only(bottom: menuHeight),
        child: Column(
          children: [
            Container(
              margin: const EdgeInsets.only(top: 11),
              height: 4,
              width: 34,
              decoration:
                  BoxDecoration(color: HexColor("#D4D5D7"), borderRadius: BorderRadius.circular(2)),
            ),
            const SizedBox(height: 18),
            GestureDetector(
              onTap: onBookHeaderTap,
              child: Container(
                padding: const EdgeInsets.only(left: 22, top: 12, right: 22),
                color: Colors.transparent,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    NetworkImageUtil(imageUrl: cover ?? "", width: 45, height: 60),
                    const SizedBox(width: 18),
                    Expanded(
                        child: Text(title ?? "",
                            style: TextStyle(
                                fontSize: 17,
                                color: isFromVoce ? HexColor("#FFFFFF") : HexColor("#1F1F2F"),
                                fontWeight: FontWeight.bold),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis)),
                    const SizedBox(width: 18),
                    Transform.rotate(
                      angle: CommonManager.instance.isReverse() ? math.pi : 0,
                      child: Image.asset(
                          isFromVoce
                              ? "assets/images/common/icon_more_white.png"
                              : "assets/images/common/icon_arrow_black.png",
                          width: 7,
                          height: 14,
                          fit: BoxFit.cover),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 13),
            Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Divider(
                    height: 0.5,
                    color: isFromVoce
                        ? ColorsUtil.hexColor(0x737373, alpha: 0.2)
                        : HexColor('#EBEBEC'))),
            Expanded(
                child: ListView.builder(
                    controller: _controller,
                    itemCount: count,
                    itemBuilder: (context, index) {
                      if (index < count) {
                        ChapterVoModel? item = detailInfoResultModel?.chapterVoList?[index];
                        return Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 23),
                            child: GestureDetector(
                              onTap: () {
                                Get.back();
                                onChaptersTap(item);
                              },
                              child: SizedBox(
                                height: 48,
                                width: double.infinity,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Expanded(
                                        flex: 9,
                                        child: Row(
                                          children: [
                                            Expanded(
                                                child: Text(item?.title ?? "",
                                                    style: TextStyle(
                                                        fontSize: 15,
                                                        color: item?.id == currentChapterId
                                                            ? HexColor("#176DE4")
                                                            : isFromVoce
                                                                ? HexColor("#FFFFFF")
                                                                : HexColor("#1F1F2F"),
                                                        fontWeight: FontWeight.bold))),
                                            const SizedBox(width: 10),
                                            //章节解锁状态，0免费，1-试看，需要购买，2-已购买
                                            if (item?.lock == 1)
                                              Image.asset(
                                                  "assets/images/novelReading/icon_lock.png",
                                                  width: 10,
                                                  height: 10,
                                                  fit: BoxFit.cover)
                                          ],
                                        )),
                                    Expanded(
                                        flex: 1,
                                        child: Divider(
                                            height: 0.5,
                                            color: isFromVoce
                                                ? ColorsUtil.hexColor(0x737373, alpha: 0.3)
                                                : HexColor('#EBEBEC')))
                                  ],
                                ),
                              ),
                            ));
                      }

                      return Container();
                    }))
          ],
        ));
  }
}

//todo:书籍阅读亮度luminance选择
class NovelLuminanceSheet extends StatefulWidget {
  final Function(String? color, int index) onBgColorElementOnTap;

  const NovelLuminanceSheet({super.key, required this.onBgColorElementOnTap});

  @override
  State<NovelLuminanceSheet> createState() => _NovelLuminanceSheetState();
}

class _NovelLuminanceSheetState extends State<NovelLuminanceSheet> {
  late double _brightness; // 初始亮度
  final double trackHeight = 33; // 滑条高度
  final double thumbRadius = 33 / 2.0; // 滑快半径
  final NovelReadStatusController novelReadStatusController =
      findGetXInstance(NovelReadStatusController());

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    _brightness = novelReadStatusController.brightness.value;
  }

  void _setBrightness(double value) {
    novelReadStatusController.setBrightness(value);
    if (mounted) {
      setState(() {
        _brightness = novelReadStatusController.brightness.value;
      });
    }

    EventReportManager.eventReportOfFirebase(clickReadLight);
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var bottom = mediaQuery.padding.bottom + 55;
    var elementSize =
        Size((mediaQuery.size.width - 100) / 4.0, (mediaQuery.size.width - 100) / 4.0 / 2.21);
    return Container(
        width: double.infinity,
        margin: EdgeInsets.only(bottom: bottom),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: trackHeight,
              margin: const EdgeInsets.only(top: 58, left: 20, right: 20),
              child: Row(
                children: [
                  Expanded(
                      flex: 1,
                      child: Container(
                        decoration: BoxDecoration(
                          color: HexColor("#ECECEE"),
                          borderRadius: CommonManager.instance.isReverse()
                              ? const BorderRadius.only(
                                  topRight: Radius.circular(16), bottomRight: Radius.circular(16))
                              : const BorderRadius.only(
                                  topLeft: Radius.circular(16), bottomLeft: Radius.circular(16)),
                        ),
                        padding: const EdgeInsets.only(top: 7, left: 10, bottom: 7),
                        child: Image.asset("assets/images/novelReading/icon_luminance_weak.png",
                            width: 18, height: 18, fit: BoxFit.contain),
                      )),
                  Expanded(
                      flex: 7,
                      child: Stack(
                        children: [
                          Positioned(
                              top: 0,
                              left: CommonManager.instance.isReverse() ? null : 0,
                              right: CommonManager.instance.isReverse() ? 0 : null,
                              bottom: 0,
                              child: Container(
                                height: trackHeight,
                                width: thumbRadius,
                                color: HexColor("#ECECEE"),
                              )),
                          Positioned(
                              top: 0,
                              left: CommonManager.instance.isReverse() ? 0 : null,
                              right: CommonManager.instance.isReverse() ? null : 0,
                              bottom: 0,
                              child: Container(
                                height: trackHeight,
                                width: thumbRadius,
                                color: HexColor("#F8F8F8"),
                              )),
                          Positioned(
                              top: 0,
                              left: 0,
                              bottom: 0,
                              right: 0,
                              child: SliderTheme(
                                data: SliderTheme.of(context).copyWith(
                                  activeTrackColor: HexColor("#ECECEE"),
                                  // 滑动部分的颜色
                                  inactiveTrackColor: HexColor("#F8F8F8"),
                                  // 非活动部分的颜色
                                  thumbColor: HexColor("#FFFFFF"),
                                  // 触摸点的颜色
                                  trackHeight: trackHeight,
                                  // 滑动轨道的高度
                                  thumbShape:
                                      RoundSliderThumbShape(enabledThumbRadius: thumbRadius),
                                  overlayShape: const RoundSliderOverlayShape(overlayRadius: 0.0),
                                  trackShape: const RectangularSliderTrackShape(),
                                  tickMarkShape: const RoundSliderTickMarkShape(),
                                ),
                                child: Slider(
                                  min: 0,
                                  max: 1,
                                  value: _brightness,
                                  onChanged: (value) {
                                    _setBrightness(value);
                                  },
                                ),
                              ))
                        ],
                      )),
                  Expanded(
                      flex: 1,
                      child: Container(
                        decoration: BoxDecoration(
                          color: HexColor("#F8F8F8"),
                          borderRadius: CommonManager.instance.isReverse()
                              ? const BorderRadius.only(
                                  topLeft: Radius.circular(16), bottomLeft: Radius.circular(16))
                              : const BorderRadius.only(
                                  topRight: Radius.circular(16), bottomRight: Radius.circular(16)),
                        ),
                        padding: const EdgeInsets.only(top: 7, right: 10, bottom: 7),
                        child: Image.asset("assets/images/novelReading/icon_luminance_strong.png",
                            width: 25, height: 25, fit: BoxFit.contain),
                      )),
                ],
              ),
            ),
            const SizedBox(height: 32),
            Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Text("color".tr,
                    style: TextStyle(
                        fontSize: 12, color: HexColor("#8C8F95"), fontWeight: FontWeight.bold))),
            const SizedBox(height: 15),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  getThemItem(
                      "assets/images/novelReading/icon_element_one.png", elementSize, "#ffffff", 0),
                  getThemItem(
                      "assets/images/novelReading/icon_element_two.png", elementSize, "#f6f0e0", 1),
                  getThemItem("assets/images/novelReading/icon_element_three.png", elementSize,
                      "#d4eed1", 2),
                  getThemItem("assets/images/novelReading/icon_element_four.png", elementSize,
                      "#010101", 3),
                ],
              ),
            ),
            const SizedBox(height: 30),
          ],
        ));
  }

  GestureDetector getThemItem(String imgSrc, Size size, String colorHex, int index) {
    return GestureDetector(
      onTap: () {
        widget.onBgColorElementOnTap(colorHex, index);
        EventReportManager.eventReportOfFirebase(clickReadColor);
      },
      child: Image.asset(imgSrc, width: size.width, height: size.height, fit: BoxFit.cover),
    );
  }
}

//todo:书籍阅读字体Character设置
class NovelCharacterSheet extends StatefulWidget {
  final NovelPreferencesSettingController preferencesController;
  final VoidCallback? onReadingModelChanged;

  const NovelCharacterSheet(
      {super.key, required this.preferencesController, required this.onReadingModelChanged});

  @override
  State<NovelCharacterSheet> createState() => NovelCharacterSheetState();
}

class NovelCharacterSheetState extends State<NovelCharacterSheet> {
  final double trackHeight = 33; // 滑条高度
  final double thumbRadius = 33 / 2.0; // 滑快半径
  final fontTypeKey = GlobalKey<NovelFontTypeSettingSheetState>();
  final readingModelKey = GlobalKey<NovelReadingModelSettingSheetState>();
  late double _fontSlideValue;
  late double _horizontalPaddingSlideValue;
  late double _lineHeightSlideValue;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    _fontSlideValue = getSliderValue(widget.preferencesController.fontSize.value);
    _horizontalPaddingSlideValue =
        getHorizontalSliderValue(widget.preferencesController.horizontalPadding.value);
    _lineHeightSlideValue = getLineSpacingSliderValue(widget.preferencesController.lineSpace.value);
  }

  @override
  void dispose() {
    super.dispose();
  }

  bool getIsOtherSheetShow() {
    if (fontTypeKey.currentState != null) {
      return fontTypeKey.currentState?.isSheetShow ?? false;
    } else if (readingModelKey.currentState != null) {
      return readingModelKey.currentState?.isSheetShow ?? false;
    }

    return false;
  }

  ///字体设置
  setFontSize(double value) {
    if (mounted) {
      setState(() {
        _fontSlideValue = value;
      });
    }
    widget.preferencesController.setFontSize(getFontSize(value));

    EventReportManager.eventReportOfFirebase(clickReadSize);
  }

  ///边距设置
  setHorizontalPadding(double value) {
    if (mounted) {
      setState(() {
        _horizontalPaddingSlideValue = value;
      });
    }
    widget.preferencesController.setHorizontalPadding(getHorizontalPadding(value));

    EventReportManager.eventReportOfFirebase(clickReadMargin);
  }

  ///行距设置
  setLineSpacing(double value) {
    setState(() {
      _lineHeightSlideValue = value;
    });
    widget.preferencesController.setLineSpace(getLineSpacing(value));

    EventReportManager.eventReportOfFirebase(clickReadSpace);
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var bottom = mediaQuery.padding.bottom + 55;
    var spacingWidth = mediaQuery.size.width - 60 / 2.0;
    return Container(
        width: double.infinity,
        margin: EdgeInsets.only(bottom: bottom),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: trackHeight,
              margin: const EdgeInsets.only(top: 32, left: 20, right: 20),
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.all(
                  Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                      flex: 1,
                      child: Container(
                        decoration: BoxDecoration(
                          color: HexColor("#ECECEE"),
                          borderRadius: CommonManager.instance.isReverse()
                              ? const BorderRadius.only(
                                  topRight: Radius.circular(16), bottomRight: Radius.circular(16))
                              : const BorderRadius.only(
                                  topLeft: Radius.circular(16), bottomLeft: Radius.circular(16)),
                        ),
                        alignment: Alignment.center,
                        child: Text("A",
                            style: TextStyle(
                                fontSize: 14,
                                color: HexColor("#1F1F2F"),
                                fontWeight: FontWeight.bold,
                                height: 1)),
                      )),
                  Expanded(
                      flex: 8,
                      child: Stack(
                        children: [
                          Positioned(
                              top: 0,
                              left: CommonManager.instance.isReverse() ? null : 0,
                              right: CommonManager.instance.isReverse() ? 0 : null,
                              bottom: 0,
                              child: Container(
                                height: trackHeight,
                                width: thumbRadius,
                                color: HexColor("#ECECEE"),
                              )),
                          Positioned(
                              top: 0,
                              left: CommonManager.instance.isReverse() ? 0 : null,
                              right: CommonManager.instance.isReverse() ? null : 0,
                              bottom: 0,
                              child: Container(
                                height: trackHeight,
                                width: thumbRadius,
                                color: HexColor("#F8F8F8"),
                              )),
                          Positioned(
                            top: 0,
                            left: 0,
                            bottom: 0,
                            right: 0,
                            child: SliderTheme(
                              data: SliderTheme.of(context).copyWith(
                                activeTrackColor: HexColor("#ECECEE"),
                                // 滑动部分的颜色
                                inactiveTrackColor: HexColor("#F8F8F8"),
                                // 非活动部分的颜色
                                thumbColor: HexColor("#FFFFFF"),
                                // 触摸点的颜色
                                trackHeight: trackHeight,
                                // 滑动轨道的高度
                                thumbShape: RoundSliderThumbShape(enabledThumbRadius: thumbRadius),
                                overlayShape: const RoundSliderOverlayShape(overlayRadius: 0.0),
                                trackShape: const RectangularSliderTrackShape(),
                                tickMarkShape: const RoundSliderTickMarkShape(),
                              ),
                              child: Slider(
                                min: 0,
                                max: 100,
                                value: _fontSlideValue.floorToDouble(),
                                onChanged: (value) {
                                  setFontSize(value);
                                },
                                label: widget.preferencesController.fontSize.floor().toString(),
                                divisions: 10, // 可选：设置分割成的区间数量，以便更好地显示
                              ),
                            ),
                          )
                        ],
                      )),
                  Expanded(
                      flex: 1,
                      child: Container(
                          decoration: BoxDecoration(
                            color: HexColor("#F8F8F8"),
                            borderRadius: CommonManager.instance.isReverse()
                                ? const BorderRadius.only(
                                    topLeft: Radius.circular(16), bottomLeft: Radius.circular(16))
                                : const BorderRadius.only(
                                    topRight: Radius.circular(16),
                                    bottomRight: Radius.circular(16)),
                          ),
                          alignment: Alignment.center,
                          child: Text(
                            "A",
                            style: TextStyle(
                                fontSize: 19,
                                color: HexColor("#1F1F2F"),
                                fontWeight: FontWeight.bold,
                                height: 1),
                          ))),
                ],
              ),
            ),
            const SizedBox(height: 40),
            Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  children: [
                    Expanded(
                        child: Container(
                      width: spacingWidth,
                      height: trackHeight,
                      decoration: BoxDecoration(
                        color: HexColor("#ECECEE"),
                        borderRadius: const BorderRadius.all(Radius.circular(16)),
                      ),
                      child: Row(
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              color: HexColor("#ECECEE"),
                              borderRadius: CommonManager.instance.isReverse()
                                  ? const BorderRadius.only(
                                      topRight: Radius.circular(16),
                                      bottomRight: Radius.circular(16))
                                  : const BorderRadius.only(
                                      topLeft: Radius.circular(16),
                                      bottomLeft: Radius.circular(16)),
                            ),
                            padding: CommonManager.instance.isReverse()
                                ? const EdgeInsets.only(right: 14)
                                : const EdgeInsets.only(left: 14),
                            child: Image.asset("assets/images/novelReading/icon_margin_left.png",
                                width: 12, height: 12, fit: BoxFit.cover),
                          ),
                          Expanded(
                              flex: 8,
                              child: Stack(
                                children: [
                                  Positioned(
                                      top: 0,
                                      left: CommonManager.instance.isReverse() ? null : 0,
                                      right: CommonManager.instance.isReverse() ? 0 : null,
                                      bottom: 0,
                                      child: Container(
                                        height: trackHeight,
                                        width: thumbRadius,
                                        color: HexColor("#ECECEE"),
                                      )),
                                  Positioned(
                                      top: 0,
                                      left: CommonManager.instance.isReverse() ? 0 : null,
                                      right: CommonManager.instance.isReverse() ? null : 0,
                                      bottom: 0,
                                      child: Container(
                                        height: trackHeight,
                                        width: thumbRadius,
                                        color: HexColor("#F8F8F8"),
                                      )),
                                  Positioned(
                                      top: 0,
                                      left: 0,
                                      bottom: 0,
                                      right: 0,
                                      child: SliderTheme(
                                        data: SliderTheme.of(context).copyWith(
                                          activeTrackColor: HexColor("#ECECEE"),
                                          // 滑动部分的颜色
                                          inactiveTrackColor: HexColor("#F8F8F8"),
                                          // 非活动部分的颜色
                                          thumbColor: HexColor("#FFFFFF"),
                                          // 触摸点的颜色
                                          trackHeight: trackHeight,
                                          // 滑动轨道的高度
                                          thumbShape: RoundSliderThumbShape(
                                              enabledThumbRadius: thumbRadius),
                                          overlayShape:
                                              const RoundSliderOverlayShape(overlayRadius: 0.0),
                                          trackShape: const RectangularSliderTrackShape(),
                                          tickMarkShape: const RoundSliderTickMarkShape(),
                                        ),
                                        child: Slider(
                                          min: 0,
                                          max: 100,
                                          value: _horizontalPaddingSlideValue.floorToDouble(),
                                          onChanged: (value) {
                                            setHorizontalPadding(value);
                                          },
                                          label: widget.preferencesController.horizontalPadding
                                              .toStringAsFixed(0),
                                          divisions: 7, // 可选：设置分割成的区间数量，以便更好地显示
                                        ),
                                      ))
                                ],
                              )),
                          Container(
                            height: trackHeight,
                            decoration: BoxDecoration(
                              color: HexColor("#F8F8F8"),
                              borderRadius: CommonManager.instance.isReverse()
                                  ? const BorderRadius.only(
                                      topLeft: Radius.circular(16), bottomLeft: Radius.circular(16))
                                  : const BorderRadius.only(
                                      topRight: Radius.circular(16),
                                      bottomRight: Radius.circular(16)),
                            ),
                            padding: CommonManager.instance.isReverse()
                                ? const EdgeInsets.only(left: 14)
                                : const EdgeInsets.only(right: 14),
                            child: Image.asset("assets/images/novelReading/icon_margin_right.png",
                                width: 12, height: 12, fit: BoxFit.contain),
                          ),
                        ],
                      ),
                    )),
                    const SizedBox(width: 20),
                    Expanded(
                        child: Container(
                      width: spacingWidth,
                      height: trackHeight,
                      decoration: BoxDecoration(
                        color: HexColor("#ECECEE"),
                        borderRadius: const BorderRadius.all(Radius.circular(16)),
                      ),
                      child: Row(
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              color: HexColor("#ECECEE"),
                              borderRadius: CommonManager.instance.isReverse()
                                  ? const BorderRadius.only(
                                      topRight: Radius.circular(16),
                                      bottomRight: Radius.circular(16))
                                  : const BorderRadius.only(
                                      topLeft: Radius.circular(16),
                                      bottomLeft: Radius.circular(16)),
                            ),
                            padding: CommonManager.instance.isReverse()
                                ? const EdgeInsets.only(right: 14)
                                : const EdgeInsets.only(left: 14),
                            child: Image.asset("assets/images/novelReading/icon_spacing_left.png",
                                width: 18, height: 12, fit: BoxFit.cover),
                          ),
                          Expanded(
                              flex: 8,
                              child: Stack(
                                children: [
                                  Positioned(
                                      top: 0,
                                      left: CommonManager.instance.isReverse() ? null : 0,
                                      right: CommonManager.instance.isReverse() ? 0 : null,
                                      bottom: 0,
                                      child: Container(
                                        height: trackHeight,
                                        width: thumbRadius,
                                        color: HexColor("#ECECEE"),
                                      )),
                                  Positioned(
                                      top: 0,
                                      left: CommonManager.instance.isReverse() ? 0 : null,
                                      right: CommonManager.instance.isReverse() ? null : 0,
                                      bottom: 0,
                                      child: Container(
                                        height: trackHeight,
                                        width: thumbRadius,
                                        color: HexColor("#F8F8F8"),
                                      )),
                                  Positioned(
                                      top: 0,
                                      left: 0,
                                      bottom: 0,
                                      right: 0,
                                      child: SliderTheme(
                                        data: SliderTheme.of(context).copyWith(
                                          activeTrackColor: HexColor("#ECECEE"),
                                          // 滑动部分的颜色
                                          inactiveTrackColor: HexColor("#F8F8F8"),
                                          // 非活动部分的颜色
                                          thumbColor: HexColor("#FFFFFF"),
                                          // 触摸点的颜色
                                          trackHeight: trackHeight,
                                          // 滑动轨道的高度
                                          thumbShape: RoundSliderThumbShape(
                                              enabledThumbRadius: thumbRadius),
                                          overlayShape:
                                              const RoundSliderOverlayShape(overlayRadius: 0.0),
                                          trackShape: const RectangularSliderTrackShape(),
                                          tickMarkShape: const RoundSliderTickMarkShape(),
                                        ),
                                        child: Slider(
                                          min: 0,
                                          max: 100,
                                          value: _lineHeightSlideValue.floorToDouble(),
                                          onChanged: (value) {
                                            setLineSpacing(value);
                                          },
                                          label: widget.preferencesController.lineSpace.toString(),
                                          divisions: 7, // 可选：设置分割成的区间数量，以便更好地显示
                                        ),
                                      ))
                                ],
                              )),
                          Container(
                            height: trackHeight,
                            decoration: BoxDecoration(
                              color: HexColor("#F8F8F8"),
                              borderRadius: CommonManager.instance.isReverse()
                                  ? const BorderRadius.only(
                                      topLeft: Radius.circular(16), bottomLeft: Radius.circular(16))
                                  : const BorderRadius.only(
                                      topRight: Radius.circular(16),
                                      bottomRight: Radius.circular(16)),
                            ),
                            padding: CommonManager.instance.isReverse()
                                ? const EdgeInsets.only(left: 14)
                                : const EdgeInsets.only(right: 14),
                            child: Image.asset("assets/images/novelReading/icon_spacing_right.png",
                                width: 18, height: 12, fit: BoxFit.contain),
                          ),
                        ],
                      ),
                    )),
                  ],
                )),
            const SizedBox(height: 40),
            Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  children: [
                    Expanded(
                        child: GestureDetector(
                      onTap: () {
                        //todo：字体
                        showBookBottomSheet(
                            context,
                            NovelFontTypeSettingSheet(
                                key: fontTypeKey,
                                preferencesSettingController: widget.preferencesController),
                            HexColor("#FFFFFF"),
                            barrierColor: ColorsUtil.hexColor(0x000000, alpha: 0.1),
                            isDismissible: true);
                      },
                      child: Container(
                        width: spacingWidth,
                        height: trackHeight,
                        decoration: BoxDecoration(
                          color: HexColor("#F3F3F5"),
                          borderRadius: const BorderRadius.all(Radius.circular(16)),
                        ),
                        child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 14),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text("font".tr,
                                    style: TextStyle(
                                        fontSize: 14,
                                        color: HexColor("#1F1F2F"),
                                        fontWeight: FontWeight.bold)),
                                Transform.rotate(
                                  angle: CommonManager.instance.isReverse() ? math.pi : 0,
                                  child: Image.asset(
                                    'assets/images/common/icon_arrow_gray.png',
                                    width: 6,
                                    height: 8,
                                    fit: BoxFit.contain,
                                  ),
                                )
                              ],
                            )),
                      ),
                    )),
                    const SizedBox(width: 20),
                    Expanded(
                        child: GestureDetector(
                      onTap: () {
                        //todo：阅读model
                        showBookBottomSheet(
                            context,
                            NovelReadingModelSettingSheet(
                              key: readingModelKey,
                              preferencesSettingController: widget.preferencesController,
                              onReadingModelChanged: () {
                                Get.back();
                                widget.onReadingModelChanged?.call();
                              },
                            ),
                            HexColor("#FFFFFF"),
                            barrierColor: ColorsUtil.hexColor(0x000000, alpha: 0.1),
                            isDismissible: true);
                      },
                      child: Container(
                        width: spacingWidth,
                        height: trackHeight,
                        decoration: BoxDecoration(
                          color: HexColor("#F3F3F5"),
                          borderRadius: const BorderRadius.all(Radius.circular(16)),
                        ),
                        child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 14),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text("reading_mode".tr,
                                    style: TextStyle(
                                        fontSize: 14,
                                        color: HexColor("#1F1F2F"),
                                        fontWeight: FontWeight.bold)),
                                Transform.rotate(
                                  angle: CommonManager.instance.isReverse() ? math.pi : 0,
                                  child: Image.asset(
                                    'assets/images/common/icon_arrow_gray.png',
                                    width: 6,
                                    height: 8,
                                    fit: BoxFit.contain,
                                  ),
                                )
                              ],
                            )),
                      ),
                    )),
                  ],
                )),
            const SizedBox(height: 30),
          ],
        ));
  }
}

//todo:书籍阅读字体设置弹窗
class NovelFontTypeSettingSheet extends StatefulWidget {
  final NovelPreferencesSettingController preferencesSettingController;

  const NovelFontTypeSettingSheet({super.key, required this.preferencesSettingController});

  @override
  State<NovelFontTypeSettingSheet> createState() => NovelFontTypeSettingSheetState();
}

class NovelFontTypeSettingSheetState extends State<NovelFontTypeSettingSheet> {
  late bool isSheetShow; //sheet是否展示标志位

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    isSheetShow = true;
  }

  _setFontType(FontType fontType) {
    widget.preferencesSettingController.setFontType(fontType);
    widget.preferencesSettingController.setFontFamily();

    EventReportManager.eventReportOfFirebase(clickReadFont);
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var bottom = mediaQuery.padding.bottom + 55;
    var width = (mediaQuery.size.width - 38) / 2.0;
    return Container(
        width: double.infinity,
        padding: EdgeInsets.only(bottom: bottom),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 18),
            Row(
              children: [
                const SizedBox(width: 17),
                SizedBox(
                  width: 26,
                  height: 26,
                  child: IconButton(
                    onPressed: () {
                      isSheetShow = false;
                      Get.back();
                    },
                    icon: Image.asset("assets/images/common/icon_arrow_down_black.png",
                        width: 16, height: 9, fit: BoxFit.cover),
                  ),
                ),
                const SizedBox(width: 17),
                Expanded(
                    child: Text(
                  "font".tr,
                  style: TextStyle(
                      fontSize: 15, color: HexColor("#1F1F2F"), fontWeight: FontWeight.bold),
                  textAlign: TextAlign.center,
                )),
                const SizedBox(width: 60),
              ],
            ),
            const SizedBox(height: 21),
            Divider(height: 0.5, color: HexColor("#EBEBEC")),
            const SizedBox(height: 25),
            Padding(
                padding: const EdgeInsets.symmetric(horizontal: 13),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    GestureDetector(
                      onTap: () {
                        _setFontType(FontType.serifed);
                      },
                      child: Obx(() => Container(
                            width: width,
                            height: 50,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: widget.preferencesSettingController.fontType.value ==
                                      FontType.serifed
                                  ? HexColor("#F1F8FF")
                                  : HexColor("#F8F8F8"),
                              borderRadius: const BorderRadius.all(Radius.circular(12)),
                              border: widget.preferencesSettingController.fontType.value ==
                                      FontType.serifed
                                  ? Border.all(color: HexColor("#1B86FF"), width: 1)
                                  : null,
                            ),
                            child: Text("Noto Serif",
                                style: TextStyle(
                                    fontSize: 17,
                                    color: HexColor("#1F1F2F"),
                                    fontWeight: FontWeight.bold)),
                          )),
                    ),
                    GestureDetector(
                      onTap: () {
                        _setFontType(FontType.sansSerifed);
                      },
                      child: Obx(() => Container(
                            width: width,
                            height: 50,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: widget.preferencesSettingController.fontType.value ==
                                      FontType.sansSerifed
                                  ? HexColor("#F1F8FF")
                                  : HexColor("#F8F8F8"),
                              borderRadius: const BorderRadius.all(Radius.circular(12)),
                              border: widget.preferencesSettingController.fontType.value ==
                                      FontType.sansSerifed
                                  ? Border.all(color: HexColor("#1B86FF"), width: 1)
                                  : null,
                            ),
                            child: Text("Noto Sans",
                                style: TextStyle(
                                    fontSize: 17,
                                    color: HexColor("#1F1F2F"),
                                    fontWeight: FontWeight.bold)),
                          )),
                    ),
                  ],
                )),
            const SizedBox(height: 37),
          ],
        ));
  }
}

//todo:书籍阅读翻页方式弹窗
class NovelReadingModelSettingSheet extends StatefulWidget {
  final NovelPreferencesSettingController preferencesSettingController;
  final VoidCallback? onReadingModelChanged;

  const NovelReadingModelSettingSheet(
      {super.key, required this.preferencesSettingController, required this.onReadingModelChanged});

  @override
  State<NovelReadingModelSettingSheet> createState() => NovelReadingModelSettingSheetState();
}

class NovelReadingModelSettingSheetState extends State<NovelReadingModelSettingSheet> {
  final elementPageFlip = "page_flip".tr;
  final elementVerticalScroll = "vertical _scroll".tr;
  final elementHorizontalSwipe = "horizontal_swipe".tr;
  late bool isSheetShow; //sheet是否展示标志位

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    isSheetShow = true;
  }

  void _setReadingModel(ReadingMode model) {
    widget.preferencesSettingController.setReadingMode(model);
    Get.back();
    widget.onReadingModelChanged?.call();
    EventReportManager.eventReportOfFirebase(clickReadMode);
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var bottom = mediaQuery.padding.bottom + 55;

    return Container(
        width: double.infinity,
        padding: EdgeInsets.only(bottom: bottom),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 18),
            Row(
              children: [
                const SizedBox(width: 17),
                SizedBox(
                  width: 26,
                  height: 26,
                  child: IconButton(
                    onPressed: () {
                      isSheetShow = false;
                      Get.back();
                    },
                    icon: Image.asset("assets/images/common/icon_arrow_down_black.png",
                        width: 16, height: 9, fit: BoxFit.cover),
                  ),
                ),
                const SizedBox(width: 17),
                Expanded(
                    child: Text(
                  "reading_mode".tr,
                  style: TextStyle(
                      fontSize: 15, color: HexColor("#1F1F2F"), fontWeight: FontWeight.bold),
                  textAlign: TextAlign.center,
                )),
                const SizedBox(width: 60),
              ],
            ),
            const SizedBox(height: 18),
            Divider(height: 0.5, color: HexColor("#EBEBEC")),
            const SizedBox(height: 20),
            Padding(
              padding: const EdgeInsets.only(left: 20, right: 16),
              child: GestureDetector(
                onTap: () {
                  _setReadingModel(ReadingMode.verticalScroll);
                },
                child: Container(
                    width: double.infinity,
                    height: 55,
                    color: Colors.transparent, // 使其可点击
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Obx(() => Text(elementVerticalScroll,
                            style: TextStyle(
                                fontSize: 15,
                                color: widget.preferencesSettingController.readingMode.value ==
                                        ReadingMode.verticalScroll
                                    ? HexColor("#1B86FF")
                                    : HexColor("#1F1F2F"),
                                fontWeight: FontWeight.bold))),
                        Obx(() => widget.preferencesSettingController.readingMode.value ==
                                ReadingMode.verticalScroll
                            ? Image.asset("assets/images/common/icon_tick.png",
                                width: 12, height: 9, fit: BoxFit.cover)
                            : Container())
                      ],
                    )),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 20, right: 16, bottom: 20),
              child: GestureDetector(
                onTap: () {
                  _setReadingModel(ReadingMode.horizontalScroll);
                },
                child: Container(
                    width: double.infinity,
                    height: 55,
                    color: Colors.transparent, // 使其可点击
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Obx(() => Text(elementHorizontalSwipe,
                            style: TextStyle(
                                fontSize: 15,
                                color: widget.preferencesSettingController.readingMode.value ==
                                        ReadingMode.horizontalScroll
                                    ? HexColor("#1B86FF")
                                    : HexColor("#1F1F2F"),
                                fontWeight: FontWeight.bold))),
                        Obx(() => widget.preferencesSettingController.readingMode.value ==
                                ReadingMode.horizontalScroll
                            ? Image.asset("assets/images/common/icon_tick.png",
                                width: 12, height: 9, fit: BoxFit.cover)
                            : Container())
                      ],
                    )),
              ),
            )
          ],
        ));
  }
}

//todo:有声声音类型弹窗
class ListeningBookVoiceToneSheet extends StatelessWidget {
  final ChapterVoiceModel? currentVoiceModel;
  final List<ChapterVoiceModel?>? chapterVoiceList;
  final Function(ChapterVoiceModel? item) onChapterVoiceSelected;

  const ListeningBookVoiceToneSheet(
      {super.key,
      required this.currentVoiceModel,
      required this.chapterVoiceList,
      required this.onChapterVoiceSelected});

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var itemHeight = 66.0;
    var height =
        (chapterVoiceList?.length ?? 0) * itemHeight + itemHeight + mediaQuery.padding.bottom + 10;
    return Container(
      width: double.infinity,
      height: height,
      padding: EdgeInsets.only(left: 16, right: 16, bottom: 0),
      decoration: BoxDecoration(
        color: const Color.fromRGBO(46, 46, 46, 1),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'voice_tone'.tr,
                style: TextStyle(
                  fontSize: 20,
                  // fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              IconButton(
                icon: Icon(Icons.close, color: Colors.white),
                onPressed: () {
                  Get.back();
                },
              ),
            ],
          ),
          const SizedBox(height: 5),
          Expanded(
              child: ListView.builder(
                  itemCount: chapterVoiceList?.length ?? 0,
                  itemBuilder: (context, index) {
                    if (index < chapterVoiceList!.length) {
                      ChapterVoiceModel? voiceItem = chapterVoiceList?[index];
                      final isSelected = currentVoiceModel?.id == voiceItem?.id;
                      return InkWell(
                        onTap: () {
                          onChapterVoiceSelected(voiceItem);
                          Get.back();
                        },
                        child: SizedBox(
                            width: double.infinity,
                            height: itemHeight,
                            child: Column(
                              children: [
                                Expanded(
                                    flex: 9,
                                    child: Row(
                                      children: [
                                        Container(
                                          width: 50,
                                          height: 50,
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(25),
                                            border: Border.all(
                                              color: isSelected
                                                  ? const Color.fromRGBO(27, 134, 255, 1)
                                                  : const Color.fromRGBO(136, 140, 146, 1),
                                              width: 1,
                                            ),
                                          ),
                                          clipBehavior: Clip.antiAlias,
                                          // child: Image.asset(imagePath, fit: BoxFit.cover),
                                          // child: NetImage(imageUrl: headImg),
                                          child: NetworkImageUtil(imageUrl: voiceItem?.headImg),
                                        ),
                                        const SizedBox(width: 16),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                voiceItem?.voiceName ?? '',
                                                style: TextStyle(
                                                  fontSize: 18,
                                                  // fontWeight: FontWeight.w500,
                                                  color: isSelected
                                                      ? const Color.fromRGBO(27, 134, 255, 1)
                                                      : Colors.white,
                                                ),
                                              ),
                                              const SizedBox(height: 4),
                                              Text(
                                                voiceItem?.name ?? '',
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  color: isSelected
                                                      ? const Color.fromRGBO(27, 134, 255, 1)
                                                      : const Color.fromRGBO(136, 140, 146, 1),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        isSelected
                                            ? Container(
                                                width: 20,
                                                height: 20,
                                                decoration: BoxDecoration(
                                                  borderRadius: BorderRadius.circular(15),
                                                ),
                                                child: Image.asset(
                                                    "assets/images/listening_book/Listening_icon_choosed.png"),
                                              )
                                            : Container(
                                                width: 20,
                                                height: 20,
                                                decoration: BoxDecoration(
                                                  // color: Colors.transparent,
                                                  borderRadius: BorderRadius.circular(15),
                                                ),
                                                child: Image.asset(
                                                    "assets/images/listening_book/Listening_icon_choose.png"),
                                              ),
                                      ],
                                    )),
                                Expanded(
                                    flex: 1,
                                    child: Divider(
                                        height: 0.5,
                                        color: ColorsUtil.hexColor(0x737373, alpha: 0.3)))
                              ],
                            )),
                      );
                    }

                    return Container();
                  })),
        ],
      ),
    );
  }
}

//todo:有声播速弹窗
class ListeningBookSpeedBottomSheet extends StatelessWidget {
  final List<SpeedOption>? speedOptions;
  final SpeedOption? currOption;
  final Function(SpeedOption?)? onSpeedSelected;

  const ListeningBookSpeedBottomSheet({
    super.key,
    required this.speedOptions,
    required this.currOption,
    required this.onSpeedSelected,
  });

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var count = speedOptions?.length ?? 0;
    double horizontalPadding = 30;
    if (0 < count) {
      var itemWidth = (mediaQuery.size.width - 20) / count;
      horizontalPadding = 10 + itemWidth / 2.0;
    }

    return Container(
      // padding: const EdgeInsets.symmetric(vertical: 24),
      padding: const EdgeInsets.only(
        top: 2, // 减小顶部padding
        bottom: 40,
        left: 0,
        right: 0,
      ),
      decoration: const BoxDecoration(
        // color: Color.fromRGBO(46, 46, 46, 1),
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(mainAxisSize: MainAxisSize.min, children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'speed_option'.tr,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  // fontWeight: FontWeight.w600,
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close, color: Colors.white),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
        ),
        const SizedBox(height: 24),
        SizedBox(
          width: double.infinity,
          child: Stack(
            children: [
              // 添加背景线条
              Positioned(
                left: horizontalPadding,
                top: 3,
                right: horizontalPadding,
                child: SizedBox(
                    height: 6, // 线条高度
                    child: DecoratedBox(
                        decoration: BoxDecoration(color: Color.fromRGBO(53, 53, 53, 1)))),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: audioBookController.speedOptions.map((option) {
                  bool isSelected = option.speed == currOption?.speed;
                  return Expanded(
                      child: GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      onSpeedSelected?.call(option);
                      Get.back();
                    },
                    child: Padding(
                      padding: EdgeInsets.only(
                          left: option.speed == 0.5 ? 10 : 0, right: option.speed == 1.5 ? 10 : 0),
                      child: Column(
                        children: [
                          Container(
                            width: 12,
                            height: 12,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: isSelected
                                  ? const Color.fromRGBO(27, 134, 255, 1)
                                  : const Color.fromRGBO(82, 82, 82, 1),
                            ),
                          ),
                          const SizedBox(height: 25),
                          Text(
                            option.name,
                            style: TextStyle(
                              color:
                                  isSelected ? const Color.fromRGBO(27, 134, 255, 1) : Colors.white,
                              fontSize: 12,
                            ),
                            maxLines: 3,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ));
                }).toList(),
              ),
            ],
          ),
        ),
      ]),
    );
  }
}

//todo:作者打赏弹窗
class AuthorRewardSheet extends StatefulWidget {
  final BookAuthorResultModel? bookAuthorInfo;
  final Function(RewardItem item, SpineAnimationType animationType) onRewardSelected;

  const AuthorRewardSheet(
      {super.key, required this.bookAuthorInfo, required this.onRewardSelected});

  @override
  State<AuthorRewardSheet> createState() => AuthorRewardSheetState();
}

class AuthorRewardSheetState extends State<AuthorRewardSheet> {
  late List<RewardItem> _rewardList;
  late RewardItem _currentRewardItem;
  late bool _isLoading;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    _rewardList = DataConfigUtil.instance.rewardList;
    _currentRewardItem = _rewardList.first;
    _isLoading = false;
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var cardWidth = (mediaQuery.size.width - 40) / 3.0;
    var cardHeight = cardWidth / 0.717;
    var assetsPadding = cardWidth * 0.066;
    var line = _rewardList.length / 3;
    var cardTotalHeight = cardHeight * line + (line - 1) * 15;
    return Stack(
      children: [
        SizedBox(
            width: double.infinity,
            child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const SizedBox(height: 11),
                  SizedBox(
                    width: 34,
                    height: 4,
                    child: DecoratedBox(
                      decoration: BoxDecoration(
                        color: HexColor("#D4D5D7"),
                        borderRadius: BorderRadius.all(
                          Radius.circular(2),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 15),
                  Text('tips'.tr,
                      style: TextStyle(
                          fontSize: 19, color: HexColor("#1F1F2F"), fontWeight: FontWeight.bold)),
                  const SizedBox(height: 33),
                  GestureDetector(
                    onTap: () async {
                      EventReportManager.eventReportOfFirebase(openAuthor);
                      await Get.toNamed('/bookAuthorPage',
                          arguments: {"authorId": widget.bookAuthorInfo?.id});
                    },
                    behavior: HitTestBehavior.opaque,
                    child: SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: Row(
                        children: [
                          const SizedBox(width: 10),
                          NetworkImageUtil(
                            imageUrl: widget.bookAuthorInfo?.cover,
                            w: 100,
                            h: 100,
                            width: 50,
                            height: 50,
                            isCircle: true,
                          ),
                          const SizedBox(width: 10),
                          Expanded(
                              child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(widget.bookAuthorInfo?.name ?? '',
                                  style: TextStyle(
                                      fontSize: 14,
                                      color: HexColor("#1F1F2F"),
                                      fontWeight: FontWeight.bold),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis),
                              const SizedBox(height: 10),
                              Expanded(
                                  child: Text(widget.bookAuthorInfo?.description ?? '--',
                                      style: TextStyle(
                                          fontSize: 13,
                                          color: HexColor("#7E839D"),
                                          fontWeight: FontWeight.bold),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis)),
                            ],
                          )),
                          const SizedBox(width: 10),
                        ],
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 15, bottom: 15, left: 10, right: 10),
                    child: SizedBox(
                      width: double.infinity,
                      height: cardTotalHeight,
                      child: GridView.builder(
                        scrollDirection: Axis.vertical,
                        physics: const NeverScrollableScrollPhysics(),
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 3,
                          childAspectRatio: cardWidth / cardHeight,
                          mainAxisSpacing: 15,
                          crossAxisSpacing: 10,
                        ),
                        itemCount: _rewardList.length,
                        itemBuilder: (context, index) {
                          if (index < _rewardList.length) {
                            var item = _rewardList[index];
                            return GestureDetector(
                              onTap: () {
                                if (mounted) {
                                  setState(() {
                                    _currentRewardItem = item;
                                  });
                                }
                              },
                              child: DecoratedBox(
                                decoration: BoxDecoration(
                                  color: item.title == _currentRewardItem.title
                                      ? HexColor('#F6EBD6')
                                      : HexColor("#F4F5F6"),
                                  border: Border.all(
                                      width: 1,
                                      color: item.title == _currentRewardItem.title
                                          ? HexColor('#F6CD8D')
                                          : HexColor("#D4D5D7")),
                                  borderRadius: BorderRadius.all(Radius.circular(18)),
                                ),
                                child: Column(
                                  children: [
                                    Padding(
                                      padding: EdgeInsets.all(assetsPadding),
                                      child: Image.asset(item.assets, fit: BoxFit.cover),
                                    ),
                                    Text(item.title,
                                        style: TextStyle(
                                            fontSize: 14,
                                            color: HexColor("#1F1F2F"),
                                            fontWeight: FontWeight.bold)),
                                    const SizedBox(height: 8),
                                    Text('coins_count'.trParams({'param': '${item.coin}'}),
                                        style: TextStyle(
                                            fontSize: 11,
                                            color: HexColor("#1F1F2F"),
                                            fontWeight: FontWeight.bold)),
                                  ],
                                ),
                              ),
                            );
                          }
                          return SizedBox();
                        },
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 15, bottom: 10, left: 10, right: 10),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Row(
                            children: [
                              Text('${"balance".tr}:',
                                  style: TextStyle(
                                      fontSize: 13,
                                      color: HexColor("#7E839D"),
                                      fontWeight: FontWeight.bold)),
                              const SizedBox(width: 10),
                              Expanded(
                                child: Text(
                                    'coins_count'.trParams({'param': '${_currentRewardItem.coin}'}),
                                    style: TextStyle(
                                        fontSize: 16,
                                        color: HexColor("#1F1F2F"),
                                        fontWeight: FontWeight.bold)),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 10),
                        SizedBox(
                          width: 162,
                          height: 40,
                          child: DecoratedBox(
                              decoration: BoxDecoration(
                                color: HexColor("#008CFB"),
                                borderRadius: BorderRadius.all(Radius.circular(10)),
                              ),
                              child: TextButton(
                                onPressed: () {
                                  if (mounted) {
                                    setState(() {
                                      _isLoading = true;
                                    });
                                  }
                                  widget.onRewardSelected(
                                      _currentRewardItem, _currentRewardItem.type);
                                },
                                child: Text(
                                  'send'.tr,
                                  style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 17,
                                      fontWeight: FontWeight.bold),
                                ),
                              )),
                        )
                      ],
                    ),
                  ),
                  SizedBox(height: mediaQuery.padding.bottom),
                ])),
        if (_isLoading)
          Positioned(
            left: 0,
            right: 0,
            top: cardTotalHeight / 2 - 20,
            child: LottieAnimationView(),
          )
      ],
    );
  }
}

//todo: 正常阅读中 reading 挽留弹窗
class NovelReadingReminderContent extends StatefulWidget {
  final Function(HomeReadBookItem?) onBookClick;
  final VoidCallback onBack;

  const NovelReadingReminderContent({super.key, required this.onBookClick, required this.onBack});

  @override
  State<NovelReadingReminderContent> createState() => _NovelReadingReminderContentState();
}

class _NovelReadingReminderContentState extends State<NovelReadingReminderContent> {
  late List<HomeReadBookItem>? bookList;
  late bool isLoading;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    bookList = [];
    isLoading = true;

    getTopPicksForYouData();
  }

  Future<void> getTopPicksForYouData() async {
    List<HomeReadBookListModel>? list = await HomeReadViewModel.loadBookData(
        homeDataColumnCode: 'Top_Picks_For_You', pageSize: 1, secondaryList: false);
    if (isAvailable(list)) {
      HomeReadBookListModel? model = list!.first;
      if (isAvailable(model.bookList)) {
        for (var i = 0; i < model.bookList!.length; i++) {
          if (i < 3) {
            bookList?.add(model.bookList![i]);
          } else {
            break;
          }
        }
      }
    }

    setState(() {
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var width = mediaQuery.size.width;
    var height = mediaQuery.size.height;
    var sheetHeight = width / 0.681 + mediaQuery.padding.bottom + 30;
    var listImgWidth = width * 0.18;
    var listImgHeight = listImgWidth / 0.755;
    var count = bookList?.length ?? 0;
    var listHeight = listImgHeight * 3 + 9 * (3 + 1);
    return SizedBox(
        width: width,
        height: sheetHeight,
        child: Stack(children: [
          Positioned.fill(
              child: DecoratedBox(
                  decoration: BoxDecoration(
                      gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                        Colors.transparent,
                        Colors.transparent,
                        Colors.white,
                        Colors.white,
                        Colors.white,
                      ])),
                  child: Image.asset(
                    'assets/images/novelReading/icon_reading_reminder_bg.png',
                  ))),
          Positioned(
              top: height * 0.066 + 20,
              left: 20,
              width: width * 0.505,
              child: Text('${"exit_3_books_1".tr}\n${"exit_3_books_2".tr}',
                  style: TextStyle(
                      fontSize: 22, color: HexColor("#EF2B67"), fontWeight: FontWeight.bold),
                  textAlign: TextAlign.center)),
          Positioned(
              top: height * 0.2,
              left: 0,
              right: 0,
              bottom: 0,
              child: Column(
                children: [
                  Container(
                    width: double.infinity,
                    height: listHeight,
                    margin: EdgeInsets.symmetric(horizontal: 20),
                    padding: const EdgeInsets.symmetric(vertical: 9),
                    decoration: BoxDecoration(
                      color: HexColor("#F5F6F8"),
                      borderRadius: const BorderRadius.all(Radius.circular(10)),
                    ),
                    child: isLoading
                        ? LottieAnimationView()
                        : ListView.builder(
                            itemCount: count,
                            physics: NeverScrollableScrollPhysics(),
                            itemBuilder: (context, index) {
                              var item = bookList![index];
                              return SizedBox(
                                  width: double.infinity,
                                  height: listImgHeight + 9,
                                  child: Padding(
                                      padding: EdgeInsets.only(left: 9, bottom: 9, right: 9),
                                      child: GestureDetector(
                                        onTap: () {
                                          Get.back();
                                          widget.onBookClick.call(item);
                                        },
                                        child: RecommendCard(
                                            imgSize: Size(listImgWidth, listImgHeight),
                                            bookItem: item),
                                      )));
                            }),
                  ),
                  Spacer(),
                  GestureDetector(
                    onTap: () {
                      Get.back();
                    },
                    behavior: HitTestBehavior.opaque,
                    child: Container(
                      width: double.infinity,
                      height: height * 0.061,
                      margin: const EdgeInsets.symmetric(horizontal: 18),
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.all(Radius.circular(12)),
                        gradient: LinearGradient(colors: [
                          HexColor("#0087FB"),
                          HexColor("#0099F8"),
                        ], begin: Alignment.centerLeft, end: Alignment.centerRight),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            "continue_reading".tr,
                            style: TextStyle(
                                color: HexColor("#FFFFFF"),
                                fontSize: 16,
                                fontWeight: FontWeight.bold),
                          ),
                          SizedBox(width: 10),
                          Transform.rotate(
                            angle: CommonManager.instance.isReverse() ? math.pi : 0,
                            child: Image.asset('assets/images/common/icon_more_white.png',
                                width: 7, height: 12, fit: BoxFit.contain),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: 5),
                  SizedBox(
                      width: double.infinity,
                      height: height * 0.061,
                      child: TextButton(
                        style: TextButton.styleFrom(
                          padding: EdgeInsets.zero,
                        ),
                        onPressed: () {
                          Get.back();
                          widget.onBack.call();
                        },
                        child: Text(
                          "not_now".tr,
                          style: TextStyle(
                              color: HexColor("#4D4F56"),
                              fontSize: 16,
                              fontWeight: FontWeight.bold),
                        ),
                      )),
                  SizedBox(height: 20),
                ],
              )),
        ]));
  }
}

//todo: 章节解锁 unlock 挽留弹窗
class NovelUnLockReminderContent extends StatefulWidget {
  final Function(HomeReadBookItem?) onBookClick;
  final VoidCallback onFreeSecondPage;

  const NovelUnLockReminderContent(
      {super.key, required this.onBookClick, required this.onFreeSecondPage});

  @override
  State<NovelUnLockReminderContent> createState() => _NovelUnLockReminderContentState();
}

class _NovelUnLockReminderContentState extends State<NovelUnLockReminderContent> {
  late HomeReadBookItem? bookItem;
  late bool isLoading;
  late String? description;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    bookItem = null;
    isLoading = true;
    description = null;

    getFreeForNowData();
  }

  Future<void> getFreeForNowData() async {
    List<HomeReadBookListModel>? list = await HomeReadViewModel.loadBookData(
        homeDataColumnCode: 'Hurry_Free_for_Now', pageSize: 1, secondaryList: false);
    if (isAvailable(list)) {
      HomeReadBookListModel? model = list!.first;
      if (isAvailable(model.bookList)) {
        bookItem = model.bookList!.first;
        if (isAvailable(bookItem?.description)) {
          Map<String, dynamic> lines = NovelPageAgent.paginateText(
              bookItem!.description!,
              DeviceScreenUtil.instance.height * 0.138,
              DeviceScreenUtil.instance.width * 0.922,
              TextStyle(color: HexColor("#BBBBBB"), fontSize: 16),
              firstShowLineCount: 4);
          List? descList = lines['pages'];
          if (isAvailable(descList?.first)) {
            description = descList?.first;
          }
        }
      }
    }

    setState(() {
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var width = mediaQuery.size.width;

    return SizedBox(
        width: double.infinity,
        height: 555,
        child: Stack(
          children: [
            Positioned(
                top: 9,
                left: 0,
                right: 0,
                bottom: 0,
                child: isLoading
                    ? DecoratedBox(
                        decoration: BoxDecoration(
                          color: Colors.white,
                        ),
                        child: LottieAnimationView())
                    : NetworkImageUtil(
                        imageUrl: bookItem?.cover,
                        width: width,
                        height: 35,
                      )),
            Positioned(
              top: 0,
              left: 30,
              right: 30,
              height: 35,
              child: Container(
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  image: DecorationImage(
                      image: AssetImage('assets/images/novelReading/icon_reading_reminder_top.png'),
                      fit: BoxFit.cover),
                ),
                child: Text("exit_1_free".tr,
                    style: TextStyle(
                        color: HexColor("#FFFFFF"), fontSize: 15, fontWeight: FontWeight.bold),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center),
              ),
            ),
            Positioned(
                top: 10 + 35 / 3,
                left: 0,
                right: 0,
                bottom: 0,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        ColorsUtil.hexColor(0x000000, alpha: 0.0), // 完全透明
                        ColorsUtil.hexColor(0x000000, alpha: 0.5), // 逐渐变得50%透明
                        ColorsUtil.hexColor(0x000000, alpha: 0.8), // 逐渐变得20%透明
                        ColorsUtil.hexColor(0x000000, alpha: 0.9), // 0%透明
                        ColorsUtil.hexColor(0x000000, alpha: 1.0), // 0%透明
                      ],
                    ),
                  ),
                )),
            Positioned(
                top: 188,
                left: 0,
                right: 0,
                bottom: 0,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Padding(
                        padding: EdgeInsets.symmetric(horizontal: width * 0.105),
                        child: Text(bookItem?.title ?? '',
                            style: TextStyle(
                                color: HexColor("#FFFFFF"),
                                fontSize: 20,
                                fontWeight: FontWeight.bold),
                            maxLines: 3,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.center)),
                    SizedBox(height: 12),
                    if (!isLoading)
                      Align(
                        alignment: Alignment.center,
                        child: DecoratedBox(
                          decoration: BoxDecoration(
                            borderRadius: const BorderRadius.all(Radius.circular(12)),
                            gradient: LinearGradient(colors: [
                              HexColor('#FF87AC'),
                              HexColor('#FC346A'),
                            ], begin: Alignment.topCenter, end: Alignment.bottomCenter),
                          ),
                          child: Padding(
                              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              child: Text(
                                'limited_free'.tr,
                                style: TextStyle(
                                    color: HexColor("#FFFFFF"),
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold),
                              )),
                        ),
                      ),
                    SizedBox(height: 22),
                    if (isAvailable(description))
                      Padding(
                          padding: EdgeInsets.symmetric(horizontal: width * 0.038),
                          child: Text(
                            '${description!}...',
                            style: TextStyle(color: HexColor("#BBBBBB"), fontSize: 16),
                            maxLines: 4,
                            overflow: TextOverflow.ellipsis,
                          )),
                    if (isAvailable(description))
                      GestureDetector(
                        onTap: () {
                          Get.back();
                          widget.onBookClick.call(bookItem);
                        },
                        behavior: HitTestBehavior.opaque,
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 20),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Text('read_now'.tr,
                                  style: TextStyle(
                                      color: HexColor("#FFFFFF"),
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold)),
                              SizedBox(width: 10),
                              Transform.rotate(
                                angle: CommonManager.instance.isReverse() ? math.pi : 0,
                                child: Image.asset('assets/images/common/icon_more_white.png',
                                    width: 7, height: 12, fit: BoxFit.cover),
                              ),
                            ],
                          ),
                        ),
                      ),
                    Spacer(),
                    GestureDetector(
                      onTap: () {
                        Get.back();
                        widget.onFreeSecondPage.call();
                      },
                      behavior: HitTestBehavior.opaque,
                      child: Container(
                        width: double.infinity,
                        height: 44,
                        margin: const EdgeInsets.symmetric(horizontal: 18),
                        decoration: BoxDecoration(
                          borderRadius: const BorderRadius.all(Radius.circular(12)),
                          gradient: LinearGradient(colors: [
                            HexColor("#0087FB"),
                            HexColor("#0099F8"),
                          ], begin: Alignment.centerLeft, end: Alignment.centerRight),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "explore_free".tr,
                              style: TextStyle(
                                  color: HexColor("#FFFFFF"),
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold),
                            ),
                            SizedBox(width: 20),
                            Transform.rotate(
                              angle: CommonManager.instance.isReverse() ? math.pi : 0,
                              child: Image.asset('assets/images/common/icon_more_white.png',
                                  width: 7, height: 12, fit: BoxFit.contain),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(height: mediaQuery.padding.bottom + 20),
                  ],
                )),
          ],
        ));
  }
}
