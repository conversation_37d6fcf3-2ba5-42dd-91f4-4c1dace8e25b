import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../Extensions/colorUtil.dart';

class RefreshLoadUtil extends StatefulWidget {
  final VoidCallback onRefresh;
  final VoidCallback onLoading;
  final RefreshController controller;
  final bool enablePullDown;
  final bool enablePullUp;
  final Widget child;

  const RefreshLoadUtil(
      {super.key,
      required this.onRefresh,
      required this.onLoading,
      required this.controller,
      this.enablePullDown = true,
      this.enablePullUp = true,
      required this.child});

  @override
  State<RefreshLoadUtil> createState() => _RefreshLoadUtilState();
}

class _RefreshLoadUtilState extends State<RefreshLoadUtil> {
  @override
  Widget build(BuildContext context) {
    return RefreshConfiguration(
      footerTriggerDistance: 80,
      hideFooterWhenNotFull: true,
      headerBuilder: () =>
          WaterDropMaterialHeader(backgroundColor: HexColor("0099F8")),
      footerBuilder: () => const ClassicFooter(),
      child: SmartRefresher(
        physics: BouncingScrollPhysics(),
        dragStartBehavior: DragStartBehavior.down,
        enablePullDown: widget.enablePullDown,
        // 下拉刷新
        enablePullUp: widget.enablePullUp,
        // 上拉加载数据
        controller: widget.controller,
        onRefresh: widget.onRefresh,
        onLoading: widget.onLoading,
        child: widget.child,
      ),
    );
  }
}