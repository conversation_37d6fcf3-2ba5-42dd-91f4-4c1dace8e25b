import 'dart:async';
import 'dart:io';

import 'package:UrNovel/Util/tools.dart';
import 'package:live_activities/live_activities.dart';
import 'package:path_provider/path_provider.dart';

import '../NetWorkManager/net_work_manager.dart';
import '../enum.dart';
import '../logUtil.dart';
import 'Model/novel_activities_model.dart';

const appGroupId = "group.urnovel.story.books";

class LiveActivitiesManager {
  static final LiveActivitiesManager instance = LiveActivitiesManager._();

  LiveActivitiesManager._();

  final _liveActivitiesPlugin = LiveActivities();
  bool _isActivitiesEnabled = false;
  String? _latestActivityId;
  String? _curCoverPath;

  get curCoverPath => _curCoverPath;

  Future<void> initActivity() async {
    _liveActivitiesPlugin.init(appGroupId: appGroupId, urlScheme: "UrNovel");
    _isActivitiesEnabled = await _liveActivitiesPlugin.areActivitiesEnabled();

    if (Platform.isIOS) {
      _liveActivitiesPlugin.activityUpdateStream.listen((event) {
        logP('Activity update: $event');
      });
    }
  }

  Future<void> createLiveActivity(NovelActivitiesModel? model) async {
    if (!_isActivitiesEnabled) {
      return;
    }

    if (!isAvailable(model)) {
      return;
    }

    await endAllActivities();
    _latestActivityId = await _liveActivitiesPlugin.createActivity(
      DateTime.now().millisecondsSinceEpoch.toString(),
      model!.toMap(),
    );
  }

  Future<void> updateLiveActivity(NovelActivitiesModel? model) async {
    if (!_isActivitiesEnabled) {
      return;
    }

    if (!isAvailable(model)) {
      return;
    }

    if (isAvailable(_latestActivityId)) {
      _liveActivitiesPlugin.updateActivity(
        _latestActivityId!,
        {"chapterIndex": model?.chapterIndex},
      );
    } else {
      await createLiveActivity(model);
    }
  }

  Future<void> endAllActivities() async {
    _latestActivityId = null;
    _curCoverPath = null;

    if (!_isActivitiesEnabled) {
      return;
    }
    await _liveActivitiesPlugin.endAllActivities();
  }

  // 从网络下载小说封面图并保存到相册，带有重试和缓存机制
  Future<String?> downloadAndSaveCover(String? imageUrl, int w, int h) async {
    try {
      // 检查URL有效性
      if (!isAvailable(imageUrl)) {
        return null;
      }

      // 获取处理过的URL (可能需要重定向等处理)
      final processedUrl = getRedirectImageUrl(imageUrl!, w: w, h: h);

      final response = await NetWorkManager.instance.downloadFile(
          processedUrl, DownLoadResponseType.bytes,
          onReceiveProgress: (int count, int total) {});

      if (response.statusCode != 200) {
        throw Exception("下载封面图失败: ${response.statusCode}");
      }

      final tempDir = await getTemporaryDirectory();
      final fileName = "novel_cover_thumbnail.png";
      final filePath = '${tempDir.path}/$fileName';
      final file = File(filePath);
      await file.writeAsBytes(response.data);

      return _curCoverPath = file.path;
    } catch (e) {
      logP("下载封面图失败: $e");
      return null;
    }
  }

  ///本地图片处理用image插件
  // Future<String> resizeImageFile(File file, int w, int h) async {
  //   var image = img.decodeImage(file.readAsBytesSync());
  //   var thumbnail = img.copyResize(image!, width: w, height: h);
  //
  //   final buffer = img.encodeJpg(thumbnail, quality: 100); // 使用100%的质量来保持质量不变，但减小文件大小
  //
  //   await file.writeAsBytes(buffer);
  //
  //   return file.path;
  // }

  dispose() {
    _liveActivitiesPlugin.dispose();
  }
}
