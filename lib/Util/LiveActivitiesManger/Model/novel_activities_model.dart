import 'package:live_activities/models/live_activity_file.dart';

class NovelActivitiesModel {
  int? bookId;
  LiveActivityFileFromAsset? coverUrl;
  String? bookName;
  String? chapterTitle;
  int? chapterIndex;
  String? continueTitle;

  NovelActivitiesModel(
      {this.bookId,
      this.coverUrl,
      this.bookName,
      this.chapterTitle,
      this.chapterIndex,
      this.continueTitle});

  Map<String, dynamic> toMap() {
    final map = {
      'bookId': bookId,
      'coverUrl': coverUrl,
      'bookName': bookName,
      'chapterTitle': chapterTitle,
      'chapterIndex': chapterIndex,
      'continueTitle': continueTitle,
    };

    return map;
  }

  NovelActivitiesModel copyWith(
      {int? bookId,
      LiveActivityFileFromAsset? coverUrl,
      LiveActivityFileFromAsset? coverPlaceholder,
      String? bookName,
      String? chapterTitle,
      int? chapterIndex,
      String? continueTitle}) {
    return NovelActivitiesModel(
        bookId: bookId ?? this.bookId,
        coverUrl: coverUrl ?? this.coverUrl,
        bookName: bookName ?? this.bookName,
        chapterTitle: chapterTitle ?? this.chapterTitle,
        chapterIndex: chapterIndex ?? this.chapterIndex,
        continueTitle: continueTitle ?? this.continueTitle);
  }
}
