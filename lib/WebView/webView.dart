import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

import '../Util/logUtil.dart';


class WebViewPage extends StatefulWidget {
  final Map arguments;

  const WebViewPage({super.key, required this.arguments});

  @override
  State<StatefulWidget> createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> {
  // late WebViewController controller;
  late InAppWebViewController webViewController;
  double loadingProgress = 0;
  late String? _title;
  late String? _url;

  @override
  void initState() {
    super.initState();

    _title = widget.arguments['title'];
    _url = widget.arguments['url'];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(_title ?? '')),
      body: Column(
        children: [
          LinearProgressIndicator(value: loadingProgress),
          Expanded(
            child: InAppWebView(
              initialUrlRequest:
              URLRequest(url: WebUri.uri(Uri.parse(_url ?? ''))),
              onWebViewCreated: (InAppWebViewController controller) {
                webViewController = controller;
              },
              onLoadStart: (controller, url) {
                logP("Loading started: $url");
              },
              onLoadStop: (controller, url) async {
                logP("Loading stopped: $url");
                if (mounted) {
                  setState(() {
                    loadingProgress = 0;
                  });
                }
              },
              onUpdateVisitedHistory: (controller, url, androidIsReload) {
                logP("Visited: $url");
              },
              onReceivedError: (controller, request, error) {
                logP("Error: $error");
              },
              onProgressChanged: (controller, progress) {
               if (mounted) {
                 setState(() {
                   loadingProgress = progress / 100;
                 });
               }
              },
            ),
          ),
        ],
      ),
    );
  }

// void _handleBackForbid() {
//   controller.canGoBack().then((value) {
//     if (value) {
//       controller.goBack();
//     } else {
//       //禁止返回
//     }
//   });
// }
//
// void initWebViewController() {
//   controller = WebViewController()
//     ..setJavaScriptMode(JavaScriptMode.unrestricted)
//     ..setBackgroundColor(const Color(0x00000000))
//     ..setNavigationDelegate(
//       NavigationDelegate(
//         onProgress: (int progress) {
//           // Update loading bar.
//         },
//         onPageStarted: (String url) {},
//         onPageFinished: (String url) {
//           //页面加载完成后才能执行js
//           _handleBackForbid();
//         },
//         onWebResourceError: (WebResourceError error) {},
//         onNavigationRequest: (NavigationRequest request) {
//           return NavigationDecision.navigate;
//         },
//       ),
//     )
//     ..loadRequest(Uri.parse(url ?? ''));
// }
}
