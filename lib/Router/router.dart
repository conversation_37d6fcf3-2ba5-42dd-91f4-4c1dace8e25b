import 'package:UrNovel/MainPage/Profile/Page/BecomeAWriter/become_a_writer.dart';
import 'package:flutter/material.dart';

import '../BottomBar/bottom_nav_bar.dart';
import '../Launch&Login/Page/ad_launch_page.dart';
import '../Launch&Login/Page/login_page.dart';
import '../Launch&Login/Page/welcome_info_page.dart';
import '../MainPage/BookInfo/page/BookAuthor/book_author_page.dart';
import '../MainPage/BookInfo/page/RatingComment/rating_comment_page.dart';
import '../MainPage/BookInfo/page/book_details_page.dart';
import '../MainPage/Chat/Page/ChatInfo/chat_detail_page.dart';
import '../MainPage/Chat/Page/ChatInfo/chat_page.dart';
import '../MainPage/Chat/Page/chat_navbar_page.dart';
import '../MainPage/Genres/Page/genres_tags_page.dart';
import '../MainPage/Library/Page/library_page.dart';
import '../MainPage/NovelRead/ListeningBook/Page/listening_book_page.dart';
import '../MainPage/NovelRead/Page/novel_read_page.dart';
import '../MainPage/Profile/Page/AccountPage/PurchaseHistoryPage/purchase_history_page.dart';
import '../MainPage/Profile/Page/AccountPage/PurchasedNovelsPage/purchased_novels_page.dart';
import '../MainPage/Profile/Page/AccountPage/TransactionHistoryPage/transaction_history_page.dart';
import '../MainPage/Profile/Page/AccountPage/account_page.dart';
import '../MainPage/Profile/Page/PremiumPage/premium_page.dart';
import '../MainPage/Profile/Page/PremiumPage/premium_rule_page.dart';
import '../MainPage/Profile/Page/ProfileEditPage/HometownPage/hometown_page.dart';
import '../MainPage/Profile/Page/ProfileEditPage/PhoneNumberPage/phone_number_page.dart';
import '../MainPage/Profile/Page/ProfileEditPage/profile_edit_page.dart';
import '../MainPage/Profile/Page/Setting/AccountDeletionScheduledPage/account_deletion_scheduled_page.dart';
import '../MainPage/Profile/Page/Setting/DeleteAccountPage/delete_account_page.dart';
import '../MainPage/Profile/Page/Setting/setttings_page.dart';
import '../MainPage/Profile/Page/profile_page.dart';
import '../MainPage/Rankings/Page/rankings_page.dart';
import '../MainPage/Read/PAGE/read_page.dart';
import '../MainPage/Search/Page/search_page.dart';
import '../MainPage/Secondary/Page/comment_list_page.dart';
import '../MainPage/Secondary/Page/secondary_page.dart';
import '../Util/Common/story_language_page.dart';
import '../Util/logUtil.dart';
import '../WebView/webView.dart';

final Map<String, dynamic> routes = {
  '/bottomNavBarPage': (context) => BottomNavBarPage(),
  '/libraryPage': (context) => const LibraryPage(),
  '/genresTagsPage': (context, arguments) =>
      GenresTagsPage(arguments: arguments),
  '/profilePage': (context) => ProfilePage(),
  '/loginPage': (context) => const LoginPage(),
  '/adLaunchPage': (context) => const AdLaunchPage(),
  '/welcomeInfoPage': (context) => WelcomeInfoPage(),
  '/readPage': (context) => ReadPage(),
  '/webViewPage': (context, arguments) => WebViewPage(arguments: arguments),
  '/searchPage': (context, arguments) => SearchPage(arguments: arguments),
  '/rankingPage': (context, arguments) => RankingsPage(arguments: arguments),
  '/bookDetailPage': (context, arguments) =>
      BookDetailsPage(arguments: arguments),
  '/ratingCommentPage': (context, arguments) =>
      RatingCommentPage(arguments: arguments),
  '/bookAuthorPage': (context, arguments) =>
      BookAuthorPage(arguments: arguments),
  '/settingsPage': (context) => SettingsPage(),
  '/deleteAccountPage': (context) => DeleteAccountPage(),
  '/deletionScheduledPage': (context) => const AccountDeletionScheduledPage(),
  '/profileEditPage': (context, arguments) =>
      ProfileEditPage(arguments: arguments),
  '/phoneNumberPage': (context, arguments) =>
      PhoneNumberPage(arguments: arguments),
  '/hometownPage': (context) => HometownPage(),
  '/accountPage': (context, arguments) => AccountPage(arguments: arguments),
  '/purchasedNovelsPage': (context) => PurchasedNovelsPage(),
  '/transactionHistoryPage': (context) => TransactionHistoryPage(),
  '/purchaseHistoryPage': (context) => PurchaseHistoryPage(),
  '/premiumPage': (context, arguments) => PremiumPage(arguments: arguments),
  '/novelReadPage': (context, arguments) => NovelReadPage(arguments: arguments),
  '/premiumRulePage': (context, arguments) =>
      PremiumRulePage(arguments: arguments),
  '/secondaryListPage': (context, arguments) =>
      SecondaryPage(arguments: arguments),
  '/becomeWriterPage': (context) => BecomeAWriterPage(),
  '/commentListPage': (context, arguments) =>
      CommentListPage(arguments: arguments),
  '/chatNavBarPage': (context, arguments) =>
      ChatNavBarPage(arguments: arguments),
  '/chatPage': (context, arguments) => ChatPage(arguments: arguments),
  '/chatDetailPage': (context, arguments) =>
      ChatDetailPage(arguments: arguments),
  '/listeningBookPage': (context) => ListeningBookPage(),
  '/storyLanguagePage': (context) => StoryLanguagePage(),
};

//TODO: 配置onGenerateRoute 固定写法
var onGenerateRoute = (RouteSettings settings) {
  logP("onGenerateRoute: ${settings.name}");
  final String? name = settings.name;
  final Function? pageContentBuilder = routes[name];
  if (pageContentBuilder != null) {
    if (settings.arguments != null) {
      return MaterialPageRoute(
          builder: (context) => pageContentBuilder(context, settings.arguments),
          settings: settings);
    } else {
      return MaterialPageRoute(
          builder: (context) => pageContentBuilder(context),
          settings: settings);
    }
  } else {
    return null;
  }
};
