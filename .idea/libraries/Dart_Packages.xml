<component name="libraryTable">
  <library name="Dart Packages" type="DartPackagesLibraryType">
    <properties>
      <option name="packageNameToDirsMap">
        <entry key="_fe_analyzer_shared">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/_fe_analyzer_shared-85.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="_flutterfire_internals">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/_flutterfire_internals-1.3.56/lib" />
            </list>
          </value>
        </entry>
        <entry key="analyzer">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/analyzer-7.5.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="app_settings">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/app_settings-6.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="app_tracking_transparency">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/app_tracking_transparency-2.0.6+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="appinio_social_share">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/appinio_social_share-0.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="appsflyer_sdk">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/appsflyer_sdk-6.16.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="archive">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/archive-4.0.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="args">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/args-2.7.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="asn1lib">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/asn1lib-1.6.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="async">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/async-2.13.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="audio_service">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/audio_service-0.18.18/lib" />
            </list>
          </value>
        </entry>
        <entry key="audio_service_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/audio_service_platform_interface-0.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="audio_service_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/audio_service_web-0.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="audio_session">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/audio_session-0.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="boolean_selector">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/boolean_selector-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="build">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build-2.5.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_cli_annotations">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_cli_annotations-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_config">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_config-1.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_daemon">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_daemon-4.0.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_resolvers">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_resolvers-2.5.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_runner">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_runner-2.5.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_runner_core">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_runner_core-9.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="built_collection">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/built_collection-5.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="built_value">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/built_value-8.10.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="cached_network_image">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cached_network_image-3.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="cached_network_image_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cached_network_image_platform_interface-4.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="cached_network_image_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cached_network_image_web-1.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="cancellation_token">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cancellation_token-2.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="characters">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/characters-1.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="checked_yaml">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/checked_yaml-2.0.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="clock">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/clock-1.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="code_builder">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/code_builder-4.10.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="collection">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/collection-1.19.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="connectivity_plus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus-5.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="connectivity_plus_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus_platform_interface-1.2.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="convert">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/convert-3.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="country_code_picker">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/country_code_picker-3.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="cross_file">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cross_file-0.3.4+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="crypto">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/crypto-3.0.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="dart_style">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/dart_style-3.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="dbus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/dbus-0.7.11/lib" />
            </list>
          </value>
        </entry>
        <entry key="decimal">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/decimal-3.2.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="device_info_plus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-11.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="device_info_plus_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/device_info_plus_platform_interface-7.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="diacritic">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/diacritic-0.1.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="dio">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/dio-5.8.0+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="dio_web_adapter">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/dio_web_adapter-2.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="ejson">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/ejson-0.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="ejson_annotation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/ejson_annotation-0.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="encrypt">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/encrypt-5.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="facebook_app_events">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/facebook_app_events-0.19.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="facebook_audience_network">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/facebook_audience_network-1.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="facebook_auth_desktop">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/facebook_auth_desktop-2.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="fake_async">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/fake_async-1.3.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="ffi">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/ffi-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="file">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/file-7.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_analytics">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_analytics-11.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_analytics_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_analytics_platform_interface-4.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_analytics_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_analytics_web-0.5.10+13/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_auth">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_auth-5.6.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_auth_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_auth_platform_interface-7.7.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_auth_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_auth_web-5.15.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_core">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_core-3.14.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_core_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_core_platform_interface-5.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_core_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_core_web-2.23.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_crashlytics">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_crashlytics-4.3.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_crashlytics_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_crashlytics_platform_interface-3.8.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_messaging">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_messaging-15.2.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_messaging_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_messaging_platform_interface-4.6.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="firebase_messaging_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_messaging_web-3.10.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="fixnum">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/fixnum-1.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flame">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flame-1.29.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flame_spine">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flame_spine-0.2.2+12/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter">
          <value>
            <list>
              <option value="$USER_HOME$/flutter/packages/flutter/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_app_group_directory">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_app_group_directory-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_cache_manager">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_cache_manager-3.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_card_swiper">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_card_swiper-7.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_easyloading">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_easyloading-3.0.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_facebook_auth">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_facebook_auth-7.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_facebook_auth_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_facebook_auth_platform_interface-6.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_facebook_auth_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_facebook_auth_web-6.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_inappwebview">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview-6.1.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_inappwebview_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_android-1.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_inappwebview_internal_annotations">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_internal_annotations-1.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_inappwebview_ios">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_inappwebview_macos">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_macos-1.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_inappwebview_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_platform_interface-1.3.0+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_inappwebview_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_web-1.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_inappwebview_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_windows-0.6.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_lints">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_lints-6.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_local_notifications">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_local_notifications-19.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_local_notifications_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_local_notifications_linux-6.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_local_notifications_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_local_notifications_platform_interface-9.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_local_notifications_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_local_notifications_windows-1.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_localizations">
          <value>
            <list>
              <option value="$USER_HOME$/flutter/packages/flutter_localizations/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_rating_bar">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_rating_bar-4.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_screenutil">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_screenutil-5.9.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_secure_storage">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_secure_storage-9.2.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_secure_storage_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_secure_storage_linux-1.2.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_secure_storage_macos">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_secure_storage_macos-3.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_secure_storage_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_secure_storage_platform_interface-1.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_secure_storage_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_secure_storage_web-1.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_secure_storage_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_secure_storage_windows-3.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_spinkit">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_spinkit-5.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_staggered_grid_view">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_staggered_grid_view-0.7.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_test">
          <value>
            <list>
              <option value="$USER_HOME$/flutter/packages/flutter_test/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_timezone">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_timezone-4.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_web_plugins">
          <value>
            <list>
              <option value="$USER_HOME$/flutter/packages/flutter_web_plugins/lib" />
            </list>
          </value>
        </entry>
        <entry key="freshchat_sdk">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/freshchat_sdk-0.10.25/lib" />
            </list>
          </value>
        </entry>
        <entry key="frontend_server_client">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/frontend_server_client-4.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="get">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/get-4.7.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="glob">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/glob-2.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_identity_services_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/google_identity_services_web-0.3.3+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_sign_in">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/google_sign_in-6.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_sign_in_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/google_sign_in_android-6.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_sign_in_ios">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/google_sign_in_ios-5.9.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_sign_in_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/google_sign_in_platform_interface-2.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_sign_in_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/google_sign_in_web-0.12.4+4/lib" />
            </list>
          </value>
        </entry>
        <entry key="graphs">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/graphs-2.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="http">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/http-1.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="http_multi_server">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/http_multi_server-3.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="http_parser">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/http_parser-4.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="image">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image-4.5.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="in_app_purchase">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/in_app_purchase-3.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="in_app_purchase_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/in_app_purchase_android-0.4.0+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="in_app_purchase_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/in_app_purchase_platform_interface-1.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="in_app_purchase_storekit">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/in_app_purchase_storekit-0.3.22+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="inject_js">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/inject_js-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="intl">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/intl-0.20.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="io">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/io-1.0.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="js">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/js-0.6.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="json_annotation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/json_annotation-4.9.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="json_serializable">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/json_serializable-6.9.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="just_audio">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/just_audio-0.10.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="just_audio_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/just_audio_platform_interface-4.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="just_audio_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/just_audio_web-0.4.16/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker-10.0.9/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker_flutter_testing">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker_flutter_testing-3.0.9/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker_testing">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker_testing-3.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="lecle_social_share">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/lecle_social_share-0.5.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="lints">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/lints-6.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="live_activities">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/live_activities-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="logging">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/logging-1.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="lottie">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/lottie-3.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="matcher">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/matcher-0.12.17/lib" />
            </list>
          </value>
        </entry>
        <entry key="material_color_utilities">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/material_color_utilities-0.11.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="meta">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/meta-1.16.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="mime">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/mime-2.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="nested">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/nested-1.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="nm">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/nm-0.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="objectid">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/objectid-4.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="octo_image">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/octo_image-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="ordered_set">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/ordered_set-8.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="package_config">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/package_config-2.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="package_info_plus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="package_info_plus_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/package_info_plus_platform_interface-3.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="path">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path-1.9.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider-2.1.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_android-2.2.17/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_foundation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_linux-2.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_platform_interface-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_windows-2.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler-12.0.0+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_android-13.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_apple">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_html">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_html-0.1.3+5/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_platform_interface-4.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_windows-0.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="petitparser">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/petitparser-6.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="photo_manager">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="photo_view">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/photo_view-0.15.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="platform">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/platform-3.1.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="plugin_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/plugin_platform_interface-2.1.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="pointycastle">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/pointycastle-3.9.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="pool">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/pool-1.5.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="posix">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/posix-6.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="provider">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/provider-6.1.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="pub_semver">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/pub_semver-2.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="pubspec_parse">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/pubspec_parse-1.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="pull_to_refresh">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/pull_to_refresh-2.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="rational">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/rational-2.2.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="realm">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/realm-20.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="realm_common">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/realm_common-20.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="realm_dart">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/realm_dart-20.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="realm_generator">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/realm_generator-20.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="rxdart">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/rxdart-0.28.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="sane_uuid">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sane_uuid-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="screen_brightness">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/screen_brightness-2.1.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="screen_brightness_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/screen_brightness_android-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="screen_brightness_ios">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/screen_brightness_ios-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="screen_brightness_macos">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/screen_brightness_macos-2.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="screen_brightness_ohos">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/screen_brightness_ohos-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="screen_brightness_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/screen_brightness_platform_interface-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="screen_brightness_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/screen_brightness_windows-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="screen_protector">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/screen_protector-1.4.2+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="scrollable_positioned_list">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/scrollable_positioned_list-0.3.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="share_plus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/share_plus-11.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="share_plus_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/share_plus_platform_interface-6.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences-2.5.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_android-2.4.10/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_foundation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_linux-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_platform_interface-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_web-2.4.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_windows-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="shelf">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shelf-1.4.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="shelf_web_socket">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shelf_web_socket-3.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="sign_in_with_apple">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sign_in_with_apple-7.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="sign_in_with_apple_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sign_in_with_apple_platform_interface-2.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="sign_in_with_apple_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sign_in_with_apple_web-3.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="sky_engine">
          <value>
            <list>
              <option value="$USER_HOME$/flutter/bin/cache/pkg/sky_engine/lib" />
            </list>
          </value>
        </entry>
        <entry key="source_gen">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/source_gen-2.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="source_helper">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/source_helper-1.3.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="source_span">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/source_span-1.10.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="spine_flutter">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/spine_flutter-4.2.36/lib" />
            </list>
          </value>
        </entry>
        <entry key="sprintf">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sprintf-7.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqflite">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite-2.4.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqflite_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite_android-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqflite_common">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite_common-2.5.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqflite_darwin">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqflite_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite_platform_interface-2.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="stack_trace">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/stack_trace-1.12.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="stream_channel">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/stream_channel-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="stream_transform">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/stream_transform-2.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="string_scanner">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/string_scanner-1.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="synchronized">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/synchronized-3.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="tar">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/tar-2.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="term_glyph">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/term_glyph-1.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="test_api">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/test_api-0.7.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="timezone">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/timezone-0.10.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="timing">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/timing-1.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="type_plus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/type_plus-2.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="typed_data">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/typed_data-1.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher-6.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_android-6.3.16/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_ios">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_linux-3.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_macos">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_macos-3.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_platform_interface-2.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_web-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_windows-3.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="uuid">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/uuid-4.5.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_math">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/vector_math-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="visibility_detector">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/visibility_detector-0.4.0+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="vm_service">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/vm_service-15.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="vpn_check">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/vpn_check-0.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="watcher">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/watcher-1.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/web-1.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="web_ffi_fork">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/web_ffi_fork-0.7.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="web_socket">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/web_socket-1.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="web_socket_channel">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/web_socket_channel-3.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="win32">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/win32-5.14.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="win32_registry">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/win32_registry-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="xdg_directories">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/xdg_directories-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="xml">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/xml-6.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="yaml">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/yaml-3.1.3/lib" />
            </list>
          </value>
        </entry>
      </option>
    </properties>
    <CLASSES>
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/_fe_analyzer_shared-85.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/_flutterfire_internals-1.3.56/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/analyzer-7.5.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/app_settings-6.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/app_tracking_transparency-2.0.6+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/appinio_social_share-0.3.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/appsflyer_sdk-6.16.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/archive-4.0.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/args-2.7.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/asn1lib-1.6.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/async-2.13.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/audio_service-0.18.18/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/audio_service_platform_interface-0.1.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/audio_service_web-0.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/audio_session-0.2.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/boolean_selector-2.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build-2.5.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_cli_annotations-2.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_config-1.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_daemon-4.0.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_resolvers-2.5.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_runner-2.5.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_runner_core-9.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/built_collection-5.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/built_value-8.10.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cached_network_image-3.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cached_network_image_platform_interface-4.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cached_network_image_web-1.3.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cancellation_token-2.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/characters-1.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/checked_yaml-2.0.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/clock-1.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/code_builder-4.10.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/collection-1.19.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus-5.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus_platform_interface-1.2.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/convert-3.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/country_code_picker-3.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cross_file-0.3.4+2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/crypto-3.0.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/dart_style-3.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/dbus-0.7.11/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/decimal-3.2.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-11.5.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/device_info_plus_platform_interface-7.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/diacritic-0.1.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/dio-5.8.0+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/dio_web_adapter-2.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/ejson-0.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/ejson_annotation-0.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/encrypt-5.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/facebook_app_events-0.19.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/facebook_audience_network-1.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/facebook_auth_desktop-2.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/fake_async-1.3.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/ffi-2.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/file-7.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_analytics-11.5.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_analytics_platform_interface-4.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_analytics_web-0.5.10+13/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_auth-5.6.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_auth_platform_interface-7.7.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_auth_web-5.15.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_core-3.14.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_core_platform_interface-5.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_core_web-2.23.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_crashlytics-4.3.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_crashlytics_platform_interface-3.8.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_messaging-15.2.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_messaging_platform_interface-4.6.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_messaging_web-3.10.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/fixnum-1.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flame-1.29.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flame_spine-0.2.2+12/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_app_group_directory-1.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_cache_manager-3.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_card_swiper-7.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_easyloading-3.0.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_facebook_auth-7.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_facebook_auth_platform_interface-6.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_facebook_auth_web-6.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview-6.1.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_android-1.1.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_internal_annotations-1.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_macos-1.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_platform_interface-1.3.0+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_web-1.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_windows-0.6.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_lints-6.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_local_notifications-19.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_local_notifications_linux-6.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_local_notifications_platform_interface-9.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_local_notifications_windows-1.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_rating_bar-4.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_screenutil-5.9.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_secure_storage-9.2.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_secure_storage_linux-1.2.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_secure_storage_macos-3.1.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_secure_storage_platform_interface-1.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_secure_storage_web-1.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_secure_storage_windows-3.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_spinkit-5.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_staggered_grid_view-0.7.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_timezone-4.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/freshchat_sdk-0.10.25/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/frontend_server_client-4.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/get-4.7.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/glob-2.1.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/google_identity_services_web-0.3.3+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/google_sign_in-6.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/google_sign_in_android-6.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/google_sign_in_ios-5.9.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/google_sign_in_platform_interface-2.5.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/google_sign_in_web-0.12.4+4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/graphs-2.3.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/http-1.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/http_multi_server-3.2.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/http_parser-4.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image-4.5.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/in_app_purchase-3.2.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/in_app_purchase_android-0.4.0+2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/in_app_purchase_platform_interface-1.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/in_app_purchase_storekit-0.3.22+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/inject_js-2.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/intl-0.20.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/io-1.0.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/js-0.6.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/json_annotation-4.9.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/json_serializable-6.9.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/just_audio-0.10.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/just_audio_platform_interface-4.5.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/just_audio_web-0.4.16/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker-10.0.9/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker_flutter_testing-3.0.9/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker_testing-3.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/lecle_social_share-0.5.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/lints-6.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/live_activities-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/logging-1.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/lottie-3.3.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/matcher-0.12.17/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/material_color_utilities-0.11.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/meta-1.16.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/mime-2.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/nested-1.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/nm-0.5.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/objectid-4.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/octo_image-2.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/ordered_set-8.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/package_config-2.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/package_info_plus_platform_interface-3.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path-1.9.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider-2.1.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_android-2.2.17/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_linux-2.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_platform_interface-2.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_windows-2.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler-12.0.0+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_android-13.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_html-0.1.3+5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_platform_interface-4.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_windows-0.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/petitparser-6.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/photo_view-0.15.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/platform-3.1.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/plugin_platform_interface-2.1.8/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/pointycastle-3.9.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/pool-1.5.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/posix-6.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/provider-6.1.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/pub_semver-2.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/pubspec_parse-1.5.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/pull_to_refresh-2.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/rational-2.2.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/realm-20.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/realm_common-20.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/realm_dart-20.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/realm_generator-20.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/rxdart-0.28.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sane_uuid-1.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/screen_brightness-2.1.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/screen_brightness_android-2.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/screen_brightness_ios-2.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/screen_brightness_macos-2.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/screen_brightness_ohos-2.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/screen_brightness_platform_interface-2.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/screen_brightness_windows-2.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/screen_protector-1.4.2+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/scrollable_positioned_list-0.3.8/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/share_plus-11.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/share_plus_platform_interface-6.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences-2.5.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_android-2.4.10/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_linux-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_platform_interface-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_web-2.4.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_windows-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shelf-1.4.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shelf_web_socket-3.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sign_in_with_apple-7.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sign_in_with_apple_platform_interface-2.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sign_in_with_apple_web-3.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/source_gen-2.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/source_helper-1.3.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/source_span-1.10.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/spine_flutter-4.2.36/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sprintf-7.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite-2.4.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite_android-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite_common-2.5.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite_platform_interface-2.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/stack_trace-1.12.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/stream_channel-2.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/stream_transform-2.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/string_scanner-1.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/synchronized-3.3.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/tar-2.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/term_glyph-1.2.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/test_api-0.7.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/timezone-0.10.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/timing-1.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/type_plus-2.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/typed_data-1.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher-6.3.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_android-6.3.16/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_linux-3.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_macos-3.2.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_platform_interface-2.3.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_web-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_windows-3.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/uuid-4.5.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/vector_math-2.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/visibility_detector-0.4.0+2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/vm_service-15.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/vpn_check-0.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/watcher-1.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/web-1.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/web_ffi_fork-0.7.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/web_socket-1.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/web_socket_channel-3.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/win32-5.14.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/win32_registry-2.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/xdg_directories-1.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/xml-6.5.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/yaml-3.1.3/lib" />
      <root url="file://$USER_HOME$/flutter/bin/cache/pkg/sky_engine/lib" />
      <root url="file://$USER_HOME$/flutter/packages/flutter/lib" />
      <root url="file://$USER_HOME$/flutter/packages/flutter_localizations/lib" />
      <root url="file://$USER_HOME$/flutter/packages/flutter_test/lib" />
      <root url="file://$USER_HOME$/flutter/packages/flutter_web_plugins/lib" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>