<component name="libraryTable">
  <library name="Flutter Plugins" type="FlutterPluginsLibraryType">
    <CLASSES>
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_secure_storage-9.2.4" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/google_sign_in-6.3.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/google_sign_in_web-0.12.4+4" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/appsflyer_sdk-6.16.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/app_tracking_transparency-2.0.6+1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_facebook_auth_web-6.1.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/screen_protector-1.4.2+1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/audio_session-0.2.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/in_app_purchase_android-0.4.0+2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_secure_storage_windows-3.1.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider-2.1.5" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/facebook_audience_network-1.0.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus-5.0.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/audio_service_web-0.1.4" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_auth_web-5.15.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/share_plus-11.0.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_web-2.4.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_android-6.3.16" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_auth-5.6.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/app_settings-6.1.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_windows-0.6.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/live_activities-2.4.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite_android-2.4.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_analytics-11.5.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_android-2.4.10" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite-2.4.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences-2.5.3" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/facebook_auth_desktop-2.1.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_web-1.1.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_html-0.1.3+5" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_linux-2.2.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/spine_flutter-4.2.36" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_app_group_directory-1.1.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/google_sign_in_ios-5.9.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_timezone-4.1.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/audio_service-0.18.18" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_web-2.4.3" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_linux-2.4.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_secure_storage_web-1.2.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_android-1.1.3" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sign_in_with_apple_web-3.0.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_messaging_web-3.10.7" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_facebook_auth-7.1.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_macos-3.2.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler-12.0.0+1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/vpn_check-0.3.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview-6.1.5" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_core_web-2.23.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_core-3.14.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_windows-3.1.4" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/freshchat_sdk-0.10.25" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/just_audio_web-0.4.16" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_local_notifications-19.3.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/realm-20.1.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_android-2.2.17" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/appinio_social_share-0.3.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/screen_brightness-2.1.5" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_local_notifications_windows-1.0.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/lecle_social_share-0.5.3" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_macos-1.1.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/google_sign_in_android-6.2.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-11.5.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_windows-2.4.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_crashlytics-4.3.7" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_linux-3.2.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_local_notifications_linux-6.0.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/facebook_app_events-0.19.7" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_analytics_web-0.5.10+13" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/just_audio-0.10.4" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_windows-2.3.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_messaging-15.2.7" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sign_in_with_apple-7.0.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.4" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher-6.3.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/in_app_purchase-3.2.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/in_app_purchase_storekit-0.3.22+1" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>