name: UrNovel
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.6+35

environment:
  sdk: '>=3.3.0-273.0.dev <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  get: ^4.7.2
  #本地存储
  shared_preferences: ^2.5.3
  #webview浏览器
  flutter_inappwebview: ^6.1.5
  #谷歌登录
  google_sign_in: ^6.2.2
  #苹果登录
  sign_in_with_apple: ^7.0.1
  #评分
  flutter_rating_bar: ^4.0.1
  #用于为用户提供选择国家代码的功能
  country_code_picker: ^3.3.0
  #用于获取有关应用程序的信息，例如版本号、构建号、应用名称等
  package_info_plus: ^8.3.0
  #设备亮度
  screen_brightness: ^2.1.5
  #网络请求
  dio: ^5.8.0+1
  #用于在 Flutter 应用中显示加载指示器（loading indicator）的插件
  flutter_easyloading: ^3.0.5
  #网络图片加载缓存
  cached_network_image: ^3.4.1
  #json模型转换
  build_runner: ^2.5.4 #用于生成代码
  json_serializable: ^6.9.5 #用于标记类，表示这个类需要生成序列化代码。
  json_annotation: ^4.9.0 #用于为数据模型类添加注解，以便 build_runner 和 json_serializable 生成必要的序列化代码。
  #状态管理
  provider: ^6.1.5
  #刷新
  pull_to_refresh: ^2.0.0
  #国际化
  flutter_localizations:
    sdk: flutter
  #设备信息
  device_info_plus: ^11.5.0
  #firebase 认证，打点，推b送，崩溃
  firebase_auth: ^5.6.0
  firebase_core: ^3.14.0
  firebase_analytics: ^11.5.0
  firebase_messaging: ^15.2.7
  firebase_crashlytics: ^4.3.7
  #AES加解密
  encrypt: ^5.0.3
  convert: ^3.1.2
  #json动画
  lottie: ^3.3.1
  #数据库
  realm: ^20.0.0
  #支付
  in_app_purchase: ^3.2.1
  #The iOS and macOS implementation of in_app_purchase
  in_app_purchase_storekit: ^0.3.21
  #The Android implementation of in_app_purchase.
  in_app_purchase_android: ^0.4.0
  #路由跳转
  url_launcher: ^6.3.1
  #缓存管理
  path_provider: ^2.1.5
  # 隐私权限管理
  permission_handler: ^12.0.0+1
  #广告跟踪权限
  app_tracking_transparency: ^2.0.6+1
  #网络
  connectivity_plus: ^5.0.2
  #系统分享
  share_plus: ^11.0.0
  #打点
  appsflyer_sdk: ^6.16.2
  #facebook登录
  flutter_facebook_auth: ^7.1.2
  #facebook打点
  facebook_app_events: ^0.19.7
  #facebook广告 展示横幅广告、插页广告、奖励视频广告和原生广告
  facebook_audience_network: ^1.0.1
  #这个库允许你在 iOS 和 Android 平台上安全地存储键值对
  flutter_secure_storage: ^9.2.4
  #事件格式，本地化等设置
  intl: ^0.19.0
  #屏幕保护
  screen_protector: ^1.4.2+1
  #FreshChat客服
  freshchat_sdk: ^0.10.25
  #本地通知
  flutter_local_notifications: ^19.3.0
  #允许应用通过动态更新通知展示实时信息
  live_activities: ^2.4.1
  #瀑布流
  flutter_staggered_grid_view: ^0.7.0
  #卡片组合切换
  flutter_card_swiper: ^7.0.2
  scrollable_positioned_list: ^0.3.8
  #缓存
  flutter_cache_manager: ^3.4.1
  #音频播放
  just_audio: ^0.10.4
  audio_session: ^0.2.2
  audio_service: ^0.18.18
  #分享
  appinio_social_share: ^0.3.2
  lecle_social_share: ^0.5.3
  photo_manager: ^3.7.1
  flutter_timezone: ^4.1.1
  #列表可视
  visibility_detector: ^0.4.0+2
  flutter_screenutil: ^5.9.3
  app_settings: ^6.1.1
  #spine动画
  flame_spine: ^0.2.2+12
  flame: ^1.29.0
  #vpn检测
  vpn_check: ^0.3.0
  #手势缩放、拖拽等高级功能
  photo_view: ^0.15.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  analyzer: ^6.11.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/images/
    - assets/images/common/
    - assets/images/launch/
    - assets/images/bottomNavBar/
    - assets/images/read/
    - assets/images/library/
    - assets/images/rankings/
    - assets/images/bookDetails/
    - assets/images/bookDetails/ratingCommend/
    - assets/images/profile/
    - assets/images/profile/becomeAWriter/
    - assets/images/profile/purchase/
    - assets/images/novelReading/
    - assets/images/novelReading/guide/
    - assets/images/novelReading/reward/
    - assets/images/sign/
    - assets/images/welcome/
    - assets/images/chat/
    - assets/images/listening_book/
    - assets/images/activity/
    - assets/images/activity/signin/
    - assets/json/
    - assets/gif/
    - assets/gif/rankings/
    - assets/spine/
    - assets/spine/balloon/
    - assets/spine/bouquet/
    - assets/spine/like/
    - assets/spine/love/
    - assets/spine/ring/
    - assets/spine/rocket/
    - assets/spine/thanks/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    #Serif 有衬线字体
    - family: NotoNaskhArabic
      fonts:
        - asset: assets/fonts/Serif/NotoNaskhArabic-VariableFont_wght.ttf
    - family: NotoSerif
      fonts:
        - asset: assets/fonts/Serif/NotoSerif-VariableFont_wdth_wght.ttf
    - family: NotoSerifKhmer
      fonts:
        - asset: assets/fonts/Serif/NotoSerifKhmer-VariableFont_wdth_wght.ttf
    - family: NotoSerifThai
      fonts:
        - asset: assets/fonts/Serif/NotoSerifThai-VariableFont_wdth_wght.ttf
    #Sans Serif 无衬线字体，默认使用
    - family: NotoNaskhArabic
      fonts:
        - asset: assets/fonts/sansSerif/NotoNaskhArabic-VariableFont_wght.ttf
    - family: NotoSans
      fonts:
        - asset: assets/fonts/sansSerif/NotoSans-VariableFont_wdth_wght.ttf
    - family: NotoSansKhmer
      fonts:
        - asset: assets/fonts/sansSerif/NotoSansKhmer-VariableFont_wdth_wght.ttf
    - family: NotoSansThai
      fonts:
        - asset: assets/fonts/sansSerif/NotoSansThai-VariableFont_wdth_wght.ttf
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

