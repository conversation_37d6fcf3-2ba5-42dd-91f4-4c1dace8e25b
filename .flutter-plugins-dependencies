{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "app_settings", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/app_settings-6.1.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "app_tracking_transparency", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/app_tracking_transparency-2.0.6+1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "appinio_social_share", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/appinio_social_share-0.3.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "appsflyer_sdk", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/appsflyer_sdk-6.16.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "audio_service", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/audio_service-0.18.18/", "shared_darwin_source": true, "native_build": true, "dependencies": ["audio_session"], "dev_dependency": false}, {"name": "audio_session", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/audio_session-0.2.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus-5.0.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-11.5.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "facebook_app_events", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/facebook_app_events-0.19.7/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "facebook_audience_network", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/facebook_audience_network-1.0.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_analytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/firebase_analytics-11.5.0/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/firebase_auth-5.6.0/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/firebase_core-3.14.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_crashlytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/firebase_crashlytics-4.3.7/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_messaging", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/firebase_messaging-15.2.7/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_app_group_directory", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_app_group_directory-1.1.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_facebook_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_facebook_auth-7.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_inappwebview_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_local_notifications-19.3.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_secure_storage-9.2.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_timezone", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_timezone-4.1.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "freshchat_sdk", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/freshchat_sdk-0.10.25/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_sign_in_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/google_sign_in_ios-5.9.0/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "in_app_purchase_storekit", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/in_app_purchase_storekit-0.3.22+1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "just_audio", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/just_audio-0.10.4/", "shared_darwin_source": true, "native_build": true, "dependencies": ["audio_session"], "dev_dependency": false}, {"name": "lecle_social_share", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/lecle_social_share-0.5.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "live_activities", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/live_activities-2.4.1/", "native_build": true, "dependencies": ["flutter_app_group_directory"], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "photo_manager", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "realm", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/realm-20.1.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "screen_brightness_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/screen_brightness_ios-2.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "screen_protector", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/screen_protector-1.4.2+1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/share_plus-11.0.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sign_in_with_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/sign_in_with_apple-7.0.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "spine_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/spine_flutter-4.2.36/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "vpn_check", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/vpn_check-0.3.0/", "native_build": true, "dependencies": ["connectivity_plus"], "dev_dependency": false}], "android": [{"name": "app_settings", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/app_settings-6.1.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "appinio_social_share", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/appinio_social_share-0.3.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "appsflyer_sdk", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/appsflyer_sdk-6.16.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "audio_service", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/audio_service-0.18.18/", "native_build": true, "dependencies": ["audio_session"], "dev_dependency": false}, {"name": "audio_session", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/audio_session-0.2.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus-5.0.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-11.5.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "facebook_app_events", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/facebook_app_events-0.19.7/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "facebook_audience_network", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/facebook_audience_network-1.0.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_analytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/firebase_analytics-11.5.0/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/firebase_auth-5.6.0/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/firebase_core-3.14.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_crashlytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/firebase_crashlytics-4.3.7/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_messaging", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/firebase_messaging-15.2.7/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_facebook_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_facebook_auth-7.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_inappwebview_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_android-1.1.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_local_notifications-19.3.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_secure_storage-9.2.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_timezone", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_timezone-4.1.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "freshchat_sdk", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/freshchat_sdk-0.10.25/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_sign_in_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/google_sign_in_android-6.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "in_app_purchase_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/in_app_purchase_android-0.4.0+2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "just_audio", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/just_audio-0.10.4/", "native_build": true, "dependencies": ["audio_session"], "dev_dependency": false}, {"name": "lecle_social_share", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/lecle_social_share-0.5.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "live_activities", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/live_activities-2.4.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/path_provider_android-2.2.17/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_android-13.0.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "photo_manager", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "realm", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/realm-20.1.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "screen_brightness_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/screen_brightness_android-2.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "screen_protector", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/screen_protector-1.4.2+1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/share_plus-11.0.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_android-2.4.10/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sign_in_with_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/sign_in_with_apple-7.0.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "spine_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/spine_flutter-4.2.36/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/sqflite_android-2.4.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_android-6.3.16/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "vpn_check", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/vpn_check-0.3.0/", "native_build": true, "dependencies": ["connectivity_plus"], "dev_dependency": false}], "macos": [{"name": "app_settings", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/app_settings-6.1.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "audio_service", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/audio_service-0.18.18/", "shared_darwin_source": true, "native_build": true, "dependencies": ["audio_session"], "dev_dependency": false}, {"name": "audio_session", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/audio_session-0.2.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus-5.0.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-11.5.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "facebook_auth_desktop", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/facebook_auth_desktop-2.1.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_analytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/firebase_analytics-11.5.0/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/firebase_auth-5.6.0/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/firebase_core-3.14.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_crashlytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/firebase_crashlytics-4.3.7/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_messaging", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/firebase_messaging-15.2.7/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_app_group_directory", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_app_group_directory-1.1.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_inappwebview_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_macos-1.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_local_notifications-19.3.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_secure_storage_macos-3.1.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_timezone", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_timezone-4.1.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_sign_in_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/google_sign_in_ios-5.9.0/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "in_app_purchase_storekit", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/in_app_purchase_storekit-0.3.22+1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "just_audio", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/just_audio-0.10.4/", "shared_darwin_source": true, "native_build": true, "dependencies": ["audio_session"], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "photo_manager", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "realm", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/realm-20.1.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "screen_brightness_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/screen_brightness_macos-2.1.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/share_plus-11.0.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sign_in_with_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/sign_in_with_apple-7.0.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "spine_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/spine_flutter-4.2.36/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_macos-3.2.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "vpn_check", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/vpn_check-0.3.0/", "native_build": true, "dependencies": ["connectivity_plus"], "dev_dependency": false}], "linux": [{"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus-5.0.2/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-11.5.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "flutter_local_notifications_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_local_notifications_linux-6.0.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_secure_storage_linux-1.2.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_timezone", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_timezone-4.1.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/path_provider_linux-2.2.1/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "realm", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/realm-20.1.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/share_plus-11.0.0/", "native_build": false, "dependencies": ["url_launcher_linux"], "dev_dependency": false}, {"name": "shared_preferences_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_linux-2.4.1/", "native_build": false, "dependencies": ["path_provider_linux"], "dev_dependency": false}, {"name": "spine_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/spine_flutter-4.2.36/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_linux-3.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}], "windows": [{"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus-5.0.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-11.5.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/firebase_auth-5.6.0/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/firebase_core-3.14.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_inappwebview_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_windows-0.6.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_local_notifications_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_local_notifications_windows-1.0.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_secure_storage_windows-3.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_timezone", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_timezone-4.1.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/path_provider_windows-2.3.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_windows-0.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "realm", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/realm-20.1.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "screen_brightness_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/screen_brightness_windows-2.1.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/share_plus-11.0.0/", "native_build": true, "dependencies": ["url_launcher_windows"], "dev_dependency": false}, {"name": "shared_preferences_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_windows-2.4.1/", "native_build": false, "dependencies": ["path_provider_windows"], "dev_dependency": false}, {"name": "spine_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/spine_flutter-4.2.36/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_windows-3.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}], "web": [{"name": "audio_service_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/audio_service_web-0.1.4/", "dependencies": [], "dev_dependency": false}, {"name": "audio_session", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/audio_session-0.2.2/", "dependencies": [], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus-5.0.2/", "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-11.5.0/", "dependencies": [], "dev_dependency": false}, {"name": "firebase_analytics_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/firebase_analytics_web-0.5.10+13/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_auth_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/firebase_auth_web-5.15.0/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_core_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/firebase_core_web-2.23.0/", "dependencies": [], "dev_dependency": false}, {"name": "firebase_messaging_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/firebase_messaging_web-3.10.7/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "flutter_facebook_auth_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_facebook_auth_web-6.1.2/", "dependencies": [], "dev_dependency": false}, {"name": "flutter_inappwebview_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_web-1.1.2/", "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_secure_storage_web-1.2.1/", "dependencies": [], "dev_dependency": false}, {"name": "flutter_timezone", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_timezone-4.1.1/", "dependencies": [], "dev_dependency": false}, {"name": "google_sign_in_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/google_sign_in_web-0.12.4+4/", "dependencies": [], "dev_dependency": false}, {"name": "just_audio_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/just_audio_web-0.4.16/", "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/", "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_html", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_html-0.1.3+5/", "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/share_plus-11.0.0/", "dependencies": ["url_launcher_web"], "dev_dependency": false}, {"name": "shared_preferences_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_web-2.4.3/", "dependencies": [], "dev_dependency": false}, {"name": "sign_in_with_apple_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/sign_in_with_apple_web-3.0.0/", "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_web-2.4.1/", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "app_settings", "dependencies": []}, {"name": "app_tracking_transparency", "dependencies": []}, {"name": "appinio_social_share", "dependencies": []}, {"name": "appsflyer_sdk", "dependencies": []}, {"name": "audio_service", "dependencies": ["audio_service_web", "audio_session"]}, {"name": "audio_service_web", "dependencies": []}, {"name": "audio_session", "dependencies": []}, {"name": "connectivity_plus", "dependencies": []}, {"name": "device_info_plus", "dependencies": []}, {"name": "facebook_app_events", "dependencies": []}, {"name": "facebook_audience_network", "dependencies": []}, {"name": "facebook_auth_desktop", "dependencies": ["flutter_secure_storage"]}, {"name": "firebase_analytics", "dependencies": ["firebase_analytics_web", "firebase_core"]}, {"name": "firebase_analytics_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_auth", "dependencies": ["firebase_auth_web", "firebase_core"]}, {"name": "firebase_auth_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}, {"name": "firebase_crashlytics", "dependencies": ["firebase_core"]}, {"name": "firebase_messaging", "dependencies": ["firebase_core", "firebase_messaging_web"]}, {"name": "firebase_messaging_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "flutter_app_group_directory", "dependencies": []}, {"name": "flutter_facebook_auth", "dependencies": ["flutter_facebook_auth_web", "facebook_auth_desktop"]}, {"name": "flutter_facebook_auth_web", "dependencies": []}, {"name": "flutter_inappwebview", "dependencies": ["flutter_inappwebview_android", "flutter_inappwebview_ios", "flutter_inappwebview_macos", "flutter_inappwebview_web", "flutter_inappwebview_windows"]}, {"name": "flutter_inappwebview_android", "dependencies": []}, {"name": "flutter_inappwebview_ios", "dependencies": []}, {"name": "flutter_inappwebview_macos", "dependencies": []}, {"name": "flutter_inappwebview_web", "dependencies": []}, {"name": "flutter_inappwebview_windows", "dependencies": []}, {"name": "flutter_local_notifications", "dependencies": ["flutter_local_notifications_linux", "flutter_local_notifications_windows"]}, {"name": "flutter_local_notifications_linux", "dependencies": []}, {"name": "flutter_local_notifications_windows", "dependencies": []}, {"name": "flutter_secure_storage", "dependencies": ["flutter_secure_storage_linux", "flutter_secure_storage_macos", "flutter_secure_storage_web", "flutter_secure_storage_windows"]}, {"name": "flutter_secure_storage_linux", "dependencies": []}, {"name": "flutter_secure_storage_macos", "dependencies": []}, {"name": "flutter_secure_storage_web", "dependencies": []}, {"name": "flutter_secure_storage_windows", "dependencies": ["path_provider"]}, {"name": "flutter_timezone", "dependencies": []}, {"name": "freshchat_sdk", "dependencies": []}, {"name": "google_sign_in", "dependencies": ["google_sign_in_android", "google_sign_in_ios", "google_sign_in_web"]}, {"name": "google_sign_in_android", "dependencies": []}, {"name": "google_sign_in_ios", "dependencies": []}, {"name": "google_sign_in_web", "dependencies": []}, {"name": "in_app_purchase", "dependencies": ["in_app_purchase_android", "in_app_purchase_storekit"]}, {"name": "in_app_purchase_android", "dependencies": []}, {"name": "in_app_purchase_storekit", "dependencies": []}, {"name": "just_audio", "dependencies": ["just_audio_web", "audio_session", "path_provider"]}, {"name": "just_audio_web", "dependencies": []}, {"name": "lecle_social_share", "dependencies": []}, {"name": "live_activities", "dependencies": ["flutter_app_group_directory", "path_provider"]}, {"name": "package_info_plus", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_html", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "photo_manager", "dependencies": []}, {"name": "realm", "dependencies": []}, {"name": "screen_brightness", "dependencies": ["screen_brightness_android", "screen_brightness_ios", "screen_brightness_macos", "screen_brightness_windows", "screen_brightness_ohos"]}, {"name": "screen_brightness_android", "dependencies": []}, {"name": "screen_brightness_ios", "dependencies": []}, {"name": "screen_brightness_macos", "dependencies": []}, {"name": "screen_brightness_ohos", "dependencies": []}, {"name": "screen_brightness_windows", "dependencies": []}, {"name": "screen_protector", "dependencies": []}, {"name": "share_plus", "dependencies": ["url_launcher_web", "url_launcher_windows", "url_launcher_linux"]}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "sign_in_with_apple", "dependencies": ["sign_in_with_apple_web"]}, {"name": "sign_in_with_apple_web", "dependencies": []}, {"name": "spine_flutter", "dependencies": []}, {"name": "sqflite", "dependencies": ["sqflite_android", "sqflite_darwin"]}, {"name": "sqflite_android", "dependencies": []}, {"name": "sqflite_darwin", "dependencies": []}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}, {"name": "vpn_check", "dependencies": ["connectivity_plus"]}], "date_created": "2025-07-10 16:43:20.449649", "version": "3.32.2", "swift_package_manager_enabled": {"ios": false, "macos": false}}