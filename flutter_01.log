Flutter crash report.
Please report a bug at https://github.com/flutter/flutter/issues.

## command

flutter pub get --no-example

## exception

FileSystemException: FileSystemException: Deletion failed, path = '/Users/<USER>/Desktop/Project/UrNovel/windows/flutter/ephemeral/.plugin_symlinks' (OS Error: Directory not empty, errno = 66)

```
#0      _Directory._deleteSync (dart:io/directory_impl.dart:226:7)
#1      FileSystemEntity.deleteSync (dart:io/file_system_entity.dart:425:7)
#2      ForwardingFileSystemEntity.deleteSync (package:file/src/forwarding/forwarding_file_system_entity.dart:70:16)
#3      ErrorHandlingDirectory.deleteSync.<anonymous closure> (package:flutter_tools/src/base/error_handling_io.dart:448:22)
#4      _runSync (package:flutter_tools/src/base/error_handling_io.dart:549:14)
#5      ErrorHandlingDirectory.deleteSync (package:flutter_tools/src/base/error_handling_io.dart:447:12)
#6      ErrorHandlingFileSystem.deleteIfExists (package:flutter_tools/src/base/error_handling_io.dart:90:14)
#7      _createPlatformPluginSymlinks (package:flutter_tools/src/flutter_plugins.dart:1118:29)
#8      createPluginSymlinks (package:flutter_tools/src/flutter_plugins.dart:1048:5)
#9      refreshPluginsList (package:flutter_tools/src/flutter_plugins.dart:1189:5)
<asynchronous suspension>
#10     FlutterProject.ensureReadyForPlatformSpecificTooling (package:flutter_tools/src/project.dart:379:5)
<asynchronous suspension>
#11     PackagesGetCommand.runCommand (package:flutter_tools/src/commands/packages.dart:383:7)
<asynchronous suspension>
#12     FlutterCommand.run.<anonymous closure> (package:flutter_tools/src/runner/flutter_command.dart:1563:27)
<asynchronous suspension>
#13     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
<asynchronous suspension>
#14     CommandRunner.runCommand (package:args/command_runner.dart:212:13)
<asynchronous suspension>
#15     FlutterCommandRunner.runCommand.<anonymous closure> (package:flutter_tools/src/runner/flutter_command_runner.dart:494:9)
<asynchronous suspension>
#16     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
<asynchronous suspension>
#17     FlutterCommandRunner.runCommand (package:flutter_tools/src/runner/flutter_command_runner.dart:431:5)
<asynchronous suspension>
#18     run.<anonymous closure>.<anonymous closure> (package:flutter_tools/runner.dart:98:11)
<asynchronous suspension>
#19     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
<asynchronous suspension>
#20     main (package:flutter_tools/executable.dart:102:3)
<asynchronous suspension>
```

## flutter doctor

```
[✓] Flutter (Channel stable, 3.32.2, on macOS 14.6.1 23G93 darwin-arm64, locale zh-Hans-IN) [906ms]
    • Flutter version 3.32.2 on channel stable at /Users/<USER>/flutter
    • Upstream repository https://github.com/flutter/flutter.git
    • Framework revision 8defaa71a7 (3 weeks ago), 2025-06-04 11:02:51 -0700
    • Engine revision 1091508939
    • Dart version 3.8.1
    • DevTools version 2.45.1
    • Pub download mirror https://pub.flutter-io.cn
    • Flutter download mirror https://storage.flutter-io.cn

[✓] Android toolchain - develop for Android devices (Android SDK version 36.0.0-rc3) [4.3s]
    • Android SDK at /Users/<USER>/Library/Android/sdk
    • Platform android-35, build-tools 36.0.0-rc3
    • Java binary at: /Applications/Android Studio.app/Contents/jbr/Contents/Home/bin/java
      This is the JDK bundled with the latest Android Studio installation on this machine.
      To manually set the JDK path, use: `flutter config --jdk-dir="path/to/jdk"`.
    • Java version OpenJDK Runtime Environment (build 17.0.11+0-17.0.11b1207.24-11852314)
    • All Android licenses accepted.

[✓] Xcode - develop for iOS and macOS (Xcode 16.2) [3.9s]
    • Xcode at /Applications/Xcode.app/Contents/Developer
    • Build 16C5032a
    • CocoaPods version 1.16.2

[✓] Chrome - develop for the web [13ms]
    • Chrome at /Applications/Google Chrome.app/Contents/MacOS/Google Chrome

[✓] Android Studio (version 2024.1) [12ms]
    • Android Studio at /Applications/Android Studio.app/Contents
    • Flutter plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/9212-flutter
    • Dart plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/6351-dart
    • Java version OpenJDK Runtime Environment (build 17.0.11+0-17.0.11b1207.24-11852314)

[✓] VS Code (version 1.96.3) [7ms]
    • VS Code at /Applications/Visual Studio Code.app/Contents
    • Flutter extension version 3.102.0

[✓] Connected device (4 available) [10.8s]
    • Wendy-14 (wireless) (mobile) • 00008110-0016315C1AB9401E                • ios            • iOS 18.5 22F76
    • test-iPhone (mobile)         • 40a9e15d38795543a7f9069c1a37652567dfd3dd • ios            • iOS 16.7.11 20H360
    • macOS (desktop)              • macos                                    • darwin-arm64   • macOS 14.6.1 23G93 darwin-arm64
    • Chrome (web)                 • chrome                                   • web-javascript • Google Chrome 125.0.6422.142
    ! Error: Browsing on the local area network for Wendy-13. Ensure the device is unlocked and attached with a cable or associated with the same local area network as this Mac.
      The device must be opted into Developer Mode to connect wirelessly. (code -27)
    ! Error: Browsing on the local area network for Test-iPad. Ensure the device is unlocked and attached with a cable or associated with the same local area network as this Mac.
      The device must be opted into Developer Mode to connect wirelessly. (code -27)
    ! Error: Browsing on the local area network for iPhone带手机壳的. Ensure the device is unlocked and attached with a cable or associated with the same local area network as this Mac.
      The device must be opted into Developer Mode to connect wirelessly. (code -27)
    ! Error: Browsing on the local area network for iPad. Ensure the device is unlocked and attached with a cable or associated with the same local area network as this Mac.
      The device must be opted into Developer Mode to connect wirelessly. (code -27)

[✓] Network resources [766ms]
    • All expected network resources are available.

• No issues found!
```
