<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="GradleMigrationSettings" migrationVersion="1" />
  <component name="GradleSettings">
    <option name="linkedExternalProjectsSettings">
      <GradleProjectSettings>
        <compositeConfiguration>
          <compositeBuild compositeDefinitionSource="SCRIPT">
            <builds>
              <build path="$USER_HOME$/flutter/packages/flutter_tools/gradle" name="gradle">
                <projects>
                  <project path="$USER_HOME$/flutter/packages/flutter_tools/gradle" />
                </projects>
              </build>
            </builds>
          </compositeBuild>
        </compositeConfiguration>
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="gradleJvm" value="#GRADLE_LOCAL_JAVA_HOME" />
        <option name="modules">
          <set>
            <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/appsflyer_sdk-6.15.2/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus-6.1.1/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-11.2.0/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/facebook_app_events-0.19.5/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/facebook_audience_network-1.0.1/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_analytics-11.4.0/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_auth-5.4.0/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_core-3.10.0/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_crashlytics-4.3.0/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/firebase_messaging-15.2.0/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_facebook_auth-7.1.1/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_android-1.1.3/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_local_notifications-18.0.1/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_secure_storage-9.2.4/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/freshchat_sdk-0.10.23/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/google_sign_in_android-6.1.34/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/in_app_purchase_android-0.3.6+13/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/live_activities-2.3.0/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.1.2/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_android-2.2.15/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_android-12.0.13/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/realm-20.0.1/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/screen_brightness_android-2.0.0/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/screen_protector-1.4.2+1/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/share_plus-10.1.3/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_android-2.4.1/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sign_in_with_apple-6.1.4/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite_android-2.4.0/android" />
            <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_android-6.3.14/android" />
            <option value="$PROJECT_DIR$" />
            <option value="$PROJECT_DIR$/app" />
            <option value="$USER_HOME$/flutter/packages/flutter_tools/gradle" />
          </set>
        </option>
        <option name="resolveExternalAnnotations" value="false" />
      </GradleProjectSettings>
    </option>
  </component>
</project>