<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AndroidLayouts">
    <shared>
      <config />
    </shared>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="36354d53-6a03-49c6-89a5-7d6d841e7d3d" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/../.dart_tool/package_config.json" beforeDir="false" afterPath="$PROJECT_DIR$/../.dart_tool/package_config.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.dart_tool/package_config_subset" beforeDir="false" afterPath="$PROJECT_DIR$/../.dart_tool/package_config_subset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.flutter-plugins" beforeDir="false" afterPath="$PROJECT_DIR$/../.flutter-plugins" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.flutter-plugins-dependencies" beforeDir="false" afterPath="$PROJECT_DIR$/../.flutter-plugins-dependencies" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.idea/UrNovel.iml" beforeDir="false" afterPath="$PROJECT_DIR$/../.idea/UrNovel.iml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.idea/libraries/Dart_Packages.xml" beforeDir="false" afterPath="$PROJECT_DIR$/../.idea/libraries/Dart_Packages.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.idea/libraries/Flutter_Plugins.xml" beforeDir="false" afterPath="$PROJECT_DIR$/../.idea/libraries/Flutter_Plugins.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../ios/Podfile.lock" beforeDir="false" afterPath="$PROJECT_DIR$/../ios/Podfile.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../lib/Launch&amp;Login/Model/language_model.dart" beforeDir="false" afterPath="$PROJECT_DIR$/../lib/Launch&amp;Login/Model/language_model.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../lib/Launch&amp;Login/Model/language_model.g.dart" beforeDir="false" afterPath="$PROJECT_DIR$/../lib/Launch&amp;Login/Model/language_model.g.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../lib/Launch&amp;Login/Page/login_page.dart" beforeDir="false" afterPath="$PROJECT_DIR$/../lib/Launch&amp;Login/Page/login_page.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../lib/Launch&amp;Login/ViewModel/ViewModel.dart" beforeDir="false" afterPath="$PROJECT_DIR$/../lib/Launch&amp;Login/ViewModel/ViewModel.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../lib/MainPage/NovelRead/Page/novel_read_page.dart" beforeDir="false" afterPath="$PROJECT_DIR$/../lib/MainPage/NovelRead/Page/novel_read_page.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../lib/Util/Common/singleton.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../lib/Util/Common/story_language_page.dart" beforeDir="false" afterPath="$PROJECT_DIR$/../lib/Util/Common/story_language_page.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../lib/Util/PaymentManager/payment_manager.dart" beforeDir="false" afterPath="$PROJECT_DIR$/../lib/Util/PaymentManager/payment_manager.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../lib/Util/PermissionManager/permission_manager.dart" beforeDir="false" afterPath="$PROJECT_DIR$/../lib/Util/PermissionManager/permission_manager.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../lib/Util/StatusManagement/status_management.dart" beforeDir="false" afterPath="$PROJECT_DIR$/../lib/Util/StatusManagement/status_management.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../lib/Util/ThirdSdkManger/third_manger.dart" beforeDir="false" afterPath="$PROJECT_DIR$/../lib/Util/ThirdSdkManger/third_manger.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../lib/Util/enum.dart" beforeDir="false" afterPath="$PROJECT_DIR$/../lib/Util/enum.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../lib/Util/tools.dart" beforeDir="false" afterPath="$PROJECT_DIR$/../lib/Util/tools.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../lib/main.dart" beforeDir="false" afterPath="$PROJECT_DIR$/../lib/main.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../pubspec.lock" beforeDir="false" afterPath="$PROJECT_DIR$/../pubspec.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../pubspec.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/../pubspec.yaml" afterDir="false" />
      <change beforePath="$USER_HOME$/flutter/packages/flutter/lib/src/painting/text_style.dart" beforeDir="false" afterPath="$USER_HOME$/flutter/packages/flutter/lib/src/painting/text_style.dart" afterDir="false" />
      <change beforePath="$USER_HOME$/flutter/packages/flutter/lib/src/widgets/text.dart" beforeDir="false" afterPath="$USER_HOME$/flutter/packages/flutter/lib/src/widgets/text.dart" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="device_and_snapshot_combo_box_target[DeviceId(pluginId=PhysicalDevice, isTemplate=false, identifier=serial=5b347cd6)]" />
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <task path="$PROJECT_DIR$/app">
          <activation />
        </task>
        <task path="$PROJECT_DIR$">
          <activation />
        </task>
        <projects_view>
          <tree_state>
            <expand />
            <select />
          </tree_state>
        </projects_view>
      </state>
    </system>
  </component>
  <component name="GenerateSignedApkSettings">
    <option name="KEY_STORE_PATH" value="$PROJECT_DIR$/app/my-release-key.jks" />
    <option name="KEY_ALIAS" value="my-key-alias" />
    <option name="REMEMBER_PASSWORDS" value="true" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="GitLabMergeRequestFiltersHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPENED&quot;,
    &quot;assignee&quot;: {
      &quot;type&quot;: &quot;org.jetbrains.plugins.gitlab.mergerequest.ui.filters.GitLabMergeRequestsFiltersValue.MergeRequestsMemberFilterValue.MergeRequestsAssigneeFilterValue&quot;,
      &quot;username&quot;: &quot;songlucheng&quot;,
      &quot;fullname&quot;: &quot;路成 宋&quot;
    }
  }
}</component>
  <component name="GitLabMergeRequestsSettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;first&quot;: &quot;https://doc-git.hmserver.club/wendyai/novel-client-urnovel.git&quot;,
    &quot;second&quot;: &quot;5dea5de9-7957-451f-914a-cc52b8edc449&quot;
  }
}</component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/settings.gradle" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2rNdCFwRJ6Q1AukXhGhErfLfimT" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Android App.app.executor&quot;: &quot;Debug&quot;,
    &quot;BundleExportedModule&quot;: &quot;android.app&quot;,
    &quot;ExportBundle.BundlePathForandroid.app&quot;: &quot;/Users/<USER>/Desktop/Project/UrNovel/build/app/outputs&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.readMode.enableVisualFormatting&quot;: &quot;true&quot;,
    &quot;android.gradle.sync.needed&quot;: &quot;true&quot;,
    &quot;cf.first.check.clang-format&quot;: &quot;false&quot;,
    &quot;cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;com.google.services.firebase.aqiPopupShown&quot;: &quot;true&quot;,
    &quot;dart.analysis.tool.window.visible&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;v1.1.7&quot;,
    &quot;last_opened_file_path&quot;: &quot;/Users/<USER>/Desktop/Project/UrNovel/android&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.17&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;AndroidSdkUpdater&quot;,
    &quot;show.migrate.to.gradle.popup&quot;: &quot;false&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;ExportApk.BuildVariants&quot;: [
      &quot;release&quot;
    ],
    &quot;kotlin-gradle-user-dirs&quot;: [
      &quot;/Users/<USER>/.gradle&quot;
    ]
  }
}</component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/app" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="app" type="AndroidRunConfigurationType" factoryName="Android App">
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SwiftWorkspaceSettings">
    <option name="detectedToolchain" value="true" />
    <option name="toolchainPathValue" value="/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="36354d53-6a03-49c6-89a5-7d6d841e7d3d" name="Changes" comment="" />
      <created>1736403336729</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1736403336729</updated>
    </task>
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>