plugins {
    id "com.android.application"
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
    // END: FlutterFire Configuration
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

dependencies {
    // Import the Firebase BoM
    implementation(platform("com.google.firebase:firebase-bom:33.7.0"))

    // TODO: Add the dependencies for Firebase products you want to use
    // When using the BoM, don't specify versions in Firebase dependencies
    implementation("com.google.firebase:firebase-analytics")

    // Add the dependencies for any other desired Firebase products
    // https://firebase.google.com/docs/android/setup#available-libraries

    //  Google Play 结算库依赖项
    def billing_version = "7.0.0"
    implementation "com.android.billingclient:billing-ktx:$billing_version"
}

android {
    namespace = "com.example.books"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "urnovel.story.books.wattpad"
        ndk {
            debugSymbolLevel 'FULL'  // 为默认配置设置符号文件级别
        }
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = flutter.minSdkVersion
        targetSdk = 35
        minSdkVersion 23
        //28.0.12674087 rc2
        //28.0.12916984 rc3
        ndkVersion "28.0.12674087"
        versionName = '1.0.0'
        versionCode = 4
    }

    signingConfigs {
        release {
            storeFile file('my-release-key.jks')
            storePassword '123456'
            keyAlias 'my-key-alias'
            keyPassword '123456'
        }
        debug {
            storeFile file('my-release-key.jks')
            storePassword '123456'
            keyAlias 'my-key-alias'
            keyPassword '123456'
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.release
            //用于指定是否启用 zipalign 工具来对 APK 进行对齐
            zipAlignEnabled true
            //用于在Android项目中启用代码混淆和压缩的设置。
            minifyEnabled true
            //用于在构建过程中移除未使用的资源文件，从而减小 APK 的大小
            shrinkResources true
            //用来指定ProGuard规则文件
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            ndk {
                debugSymbolLevel 'FULL'  // 为发布版本设置符号文件级别
            }
        }
        debug {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.debug
            //用于在Android项目中启用代码混淆和压缩的设置。
            minifyEnabled true
            //用于在构建过程中移除未使用的资源文件，从而减小 APK 的大小
            shrinkResources true
            //用来指定ProGuard规则文件
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            ndk {
                debugSymbolLevel 'FULL'  // 为调试版本设置符号文件级别
            }
        }
    }
}

flutter {
    source = "../.."
}