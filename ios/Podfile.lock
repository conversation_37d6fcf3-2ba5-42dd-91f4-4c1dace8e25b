PODS:
  - app_settings (5.1.1):
    - Flutter
  - app_tracking_transparency (0.0.1):
    - Flutter
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - AppCheckCore (11.2.0):
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - appinio_social_share (0.0.1):
    - FBSDKCoreKit (= 17.1.0)
    - FBSDKShareKit (= 17.1.0)
    - Flutter
  - appsflyer_sdk (6.16.2):
    - AppsFlyerFramework (= 6.16.2)
    - Flutter
  - AppsFlyerFramework (6.16.2):
    - AppsFlyerFramework/Main (= 6.16.2)
  - AppsFlyerFramework/Main (6.16.2)
  - audio_service (0.0.1):
    - Flutter
    - FlutterMacOS
  - audio_session (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - device_info_plus (0.0.1):
    - Flutter
  - facebook_app_events (0.0.1):
    - FBAudienceNetwork (= 6.16)
    - FBSDKCoreKit (~> 17.1.0)
    - Flutter
  - facebook_audience_network (0.0.1):
    - FBAudienceNetwork (~> 6.3)
    - Flutter
  - FBAEMKit (17.1.0):
    - FBSDKCoreKit_Basics (= 17.1.0)
  - FBAudienceNetwork (6.16.0)
  - FBSDKCoreKit (17.1.0):
    - FBAEMKit (= 17.1.0)
    - FBSDKCoreKit_Basics (= 17.1.0)
  - FBSDKCoreKit_Basics (17.1.0)
  - FBSDKLoginKit (17.1.0):
    - FBSDKCoreKit (= 17.1.0)
  - FBSDKShareKit (17.1.0):
    - FBSDKCoreKit (= 17.1.0)
  - Firebase/Analytics (11.13.0):
    - Firebase/Core
  - Firebase/Auth (11.13.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 11.13.0)
  - Firebase/Core (11.13.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.13.0)
  - Firebase/CoreOnly (11.13.0):
    - FirebaseCore (~> 11.13.0)
  - Firebase/Crashlytics (11.13.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.13.0)
  - Firebase/Messaging (11.13.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.13.0)
  - firebase_analytics (11.5.0):
    - Firebase/Analytics (= 11.13.0)
    - firebase_core
    - Flutter
  - firebase_auth (5.6.0):
    - Firebase/Auth (= 11.13.0)
    - firebase_core
    - Flutter
  - firebase_core (3.14.0):
    - Firebase/CoreOnly (= 11.13.0)
    - Flutter
  - firebase_crashlytics (4.3.7):
    - Firebase/Crashlytics (= 11.13.0)
    - firebase_core
    - Flutter
  - firebase_messaging (15.2.7):
    - Firebase/Messaging (= 11.13.0)
    - firebase_core
    - Flutter
  - FirebaseAnalytics (11.13.0):
    - FirebaseAnalytics/AdIdSupport (= 11.13.0)
    - FirebaseCore (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.13.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - FirebaseAppCheckInterop (11.15.0)
  - FirebaseAuth (11.13.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.13.0)
    - FirebaseCoreExtension (~> 11.13.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
    - RecaptchaInterop (~> 101.0)
  - FirebaseAuthInterop (11.15.0)
  - FirebaseCore (11.13.0):
    - FirebaseCoreInternal (~> 11.13.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreExtension (11.13.0):
    - FirebaseCore (~> 11.13.0)
  - FirebaseCoreInternal (11.13.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseCrashlytics (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.1)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseInstallations (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Reachability (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
  - FirebaseRemoteConfigInterop (11.15.0)
  - FirebaseSessions (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - FirebaseCoreExtension (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - Flutter (1.0.0)
  - flutter_app_group_directory (0.0.1):
    - Flutter
  - flutter_facebook_auth (7.1.2):
    - FBSDKLoginKit (~> 17.1.0)
    - Flutter
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - flutter_timezone (0.0.1):
    - Flutter
  - freshchat_sdk (0.10.25):
    - Flutter
    - FreshchatSDK (= 6.3.7)
  - FreshchatSDK (6.3.7)
  - google_sign_in_ios (0.0.1):
    - AppAuth (>= 1.7.4)
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 8.0)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleAppMeasurement (11.13.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.13.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.13.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.13.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.13.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleSignIn (8.0.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - AppCheckCore (~> 11.0)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - in_app_purchase_storekit (0.0.1):
    - Flutter
    - FlutterMacOS
  - just_audio (0.0.1):
    - Flutter
    - FlutterMacOS
  - lecle_social_share (0.0.1):
    - FBSDKCoreKit (= 17.1.0)
    - FBSDKShareKit (= 17.1.0)
    - Flutter
    - TikTokOpenSDK (~> 5.0.15)
    - TwitterKit5 (= 5.2.0)
  - live_activities (0.0.1):
    - Flutter
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - OrderedSet (6.0.3)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - photo_manager (3.7.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - ReachabilitySwift (5.2.4)
  - realm (20.1.1):
    - Flutter
  - RecaptchaInterop (101.0.0)
  - screen_brightness_ios (0.1.0):
    - Flutter
  - screen_protector (1.2.1):
    - Flutter
    - ScreenProtectorKit (~> 1.3.1)
  - ScreenProtectorKit (1.3.1)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - Flutter
  - spine_flutter (0.0.1):
    - Flutter
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - TikTokOpenAuthSDK (2.5.0):
    - TikTokOpenAuthSDK/Auth (= 2.5.0)
    - TikTokOpenSDKCore (= 2.5.0)
  - TikTokOpenAuthSDK/Auth (2.5.0):
    - TikTokOpenSDKCore (= 2.5.0)
  - TikTokOpenSDK (5.0.15)
  - TikTokOpenSDKCore (2.5.0):
    - TikTokOpenSDKCore/Core (= 2.5.0)
  - TikTokOpenSDKCore/Core (2.5.0)
  - TikTokOpenShareSDK (2.5.0):
    - TikTokOpenSDKCore (= 2.5.0)
    - TikTokOpenShareSDK/Share (= 2.5.0)
  - TikTokOpenShareSDK/Share (2.5.0):
    - TikTokOpenSDKCore (= 2.5.0)
  - TwitterCore (3.2.0)
  - TwitterKit5 (5.2.0):
    - TwitterCore (>= 3.1.0)
  - url_launcher_ios (0.0.1):
    - Flutter
  - vpn_check (0.0.1):
    - Flutter

DEPENDENCIES:
  - app_settings (from `.symlinks/plugins/app_settings/ios`)
  - app_tracking_transparency (from `.symlinks/plugins/app_tracking_transparency/ios`)
  - appinio_social_share (from `.symlinks/plugins/appinio_social_share/ios`)
  - appsflyer_sdk (from `.symlinks/plugins/appsflyer_sdk/ios`)
  - audio_service (from `.symlinks/plugins/audio_service/darwin`)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - facebook_app_events (from `.symlinks/plugins/facebook_app_events/ios`)
  - facebook_audience_network (from `.symlinks/plugins/facebook_audience_network/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_app_group_directory (from `.symlinks/plugins/flutter_app_group_directory/ios`)
  - flutter_facebook_auth (from `.symlinks/plugins/flutter_facebook_auth/ios`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - flutter_timezone (from `.symlinks/plugins/flutter_timezone/ios`)
  - freshchat_sdk (from `.symlinks/plugins/freshchat_sdk/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - in_app_purchase_storekit (from `.symlinks/plugins/in_app_purchase_storekit/darwin`)
  - just_audio (from `.symlinks/plugins/just_audio/darwin`)
  - lecle_social_share (from `.symlinks/plugins/lecle_social_share/ios`)
  - live_activities (from `.symlinks/plugins/live_activities/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - photo_manager (from `.symlinks/plugins/photo_manager/ios`)
  - realm (from `.symlinks/plugins/realm/ios`)
  - screen_brightness_ios (from `.symlinks/plugins/screen_brightness_ios/ios`)
  - screen_protector (from `.symlinks/plugins/screen_protector/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - spine_flutter (from `.symlinks/plugins/spine_flutter/ios`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - TikTokOpenAuthSDK
  - TikTokOpenSDKCore
  - TikTokOpenShareSDK
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - vpn_check (from `.symlinks/plugins/vpn_check/ios`)

SPEC REPOS:
  trunk:
    - AppAuth
    - AppCheckCore
    - AppsFlyerFramework
    - FBAEMKit
    - FBAudienceNetwork
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKLoginKit
    - FBSDKShareKit
    - Firebase
    - FirebaseAnalytics
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - FreshchatSDK
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - nanopb
    - OrderedSet
    - PromisesObjC
    - PromisesSwift
    - ReachabilitySwift
    - RecaptchaInterop
    - ScreenProtectorKit
    - TikTokOpenAuthSDK
    - TikTokOpenSDK
    - TikTokOpenSDKCore
    - TikTokOpenShareSDK
    - TwitterCore
    - TwitterKit5

EXTERNAL SOURCES:
  app_settings:
    :path: ".symlinks/plugins/app_settings/ios"
  app_tracking_transparency:
    :path: ".symlinks/plugins/app_tracking_transparency/ios"
  appinio_social_share:
    :path: ".symlinks/plugins/appinio_social_share/ios"
  appsflyer_sdk:
    :path: ".symlinks/plugins/appsflyer_sdk/ios"
  audio_service:
    :path: ".symlinks/plugins/audio_service/darwin"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  facebook_app_events:
    :path: ".symlinks/plugins/facebook_app_events/ios"
  facebook_audience_network:
    :path: ".symlinks/plugins/facebook_audience_network/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_app_group_directory:
    :path: ".symlinks/plugins/flutter_app_group_directory/ios"
  flutter_facebook_auth:
    :path: ".symlinks/plugins/flutter_facebook_auth/ios"
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  flutter_timezone:
    :path: ".symlinks/plugins/flutter_timezone/ios"
  freshchat_sdk:
    :path: ".symlinks/plugins/freshchat_sdk/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  in_app_purchase_storekit:
    :path: ".symlinks/plugins/in_app_purchase_storekit/darwin"
  just_audio:
    :path: ".symlinks/plugins/just_audio/darwin"
  lecle_social_share:
    :path: ".symlinks/plugins/lecle_social_share/ios"
  live_activities:
    :path: ".symlinks/plugins/live_activities/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  photo_manager:
    :path: ".symlinks/plugins/photo_manager/ios"
  realm:
    :path: ".symlinks/plugins/realm/ios"
  screen_brightness_ios:
    :path: ".symlinks/plugins/screen_brightness_ios/ios"
  screen_protector:
    :path: ".symlinks/plugins/screen_protector/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  spine_flutter:
    :path: ".symlinks/plugins/spine_flutter/ios"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  vpn_check:
    :path: ".symlinks/plugins/vpn_check/ios"

SPEC CHECKSUMS:
  app_settings: 58017cd26b604ae98c3e65acbdd8ba173703cc82
  app_tracking_transparency: e169b653478da7bb15a6c61209015378ca73e375
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  AppCheckCore: cc8fd0a3a230ddd401f326489c99990b013f0c4f
  appinio_social_share: a7e26517d6cc90a611d6ef9704d837f537c349c2
  appsflyer_sdk: b8bf0ef73cadbebbad020a719ebc9bb42fe20526
  AppsFlyerFramework: fe5303bffcdfd941d5f570c2d21eaaea982e7bdc
  audio_service: cab6c1a0eaf01b5a35b567e11fa67d3cc1956910
  audio_session: 19e9480dbdd4e5f6c4543826b2e8b0e4ab6145fe
  connectivity_plus: bf0076dd84a130856aa636df1c71ccaff908fa1d
  device_info_plus: bf2e3232933866d73fe290f2942f2156cdd10342
  facebook_app_events: f76842b9381a587798e399864ac8be2d32af5bf4
  facebook_audience_network: bb22bdda913d419d8c3247c9570bda4563302417
  FBAEMKit: cb719c53575a3be86ea873279f30d6a2c4e15881
  FBAudienceNetwork: d1670939884e3a2e0ad98dca98d7e0c841417228
  FBSDKCoreKit: ecdb980a24633ccb012700299ceb16d0235e14d2
  FBSDKCoreKit_Basics: 045101c4a9ef10c845347424d73a29aae02c3e43
  FBSDKLoginKit: 69eb59b2f839aba635616df6e422acd0ca88030a
  FBSDKShareKit: be9db7d121cbc3d6700c1b712a61183cac407f64
  Firebase: 3435bc66b4d494c2f22c79fd3aae4c1db6662327
  firebase_analytics: 9bc12534b28e9955a6d02fac0478e92e027aed26
  firebase_auth: a9855fe79286576c5225b28aafed65bc374ac19a
  firebase_core: a861be150c0e7c6aecedde077968eb92cbf790b9
  firebase_crashlytics: aa26a226ac9a7047f729701fd8a60028a21f84ac
  firebase_messaging: 462da0f5171a325bf7834f9e49ac3d044bda9563
  FirebaseAnalytics: 630349facf4a114a0977e5d7570e104261973287
  FirebaseAppCheckInterop: 06fe5a3799278ae4667e6c432edd86b1030fa3df
  FirebaseAuth: 175cb5503dfdb52191b8ff81cdd52c1d9dee9ac9
  FirebaseAuthInterop: 7087d7a4ee4bc4de019b2d0c240974ed5d89e2fd
  FirebaseCore: c692c7f1c75305ab6aff2b367f25e11d73aa8bd0
  FirebaseCoreExtension: c048485c347616dba6165358dbef765c5197597b
  FirebaseCoreInternal: 29d7b3af4aaf0b8f3ed20b568c13df399b06f68c
  FirebaseCrashlytics: 8281e577b6f85a08ea7aeb8b66f95e1ae430c943
  FirebaseInstallations: 0ee9074f2c1e86561ace168ee1470dc67aabaf02
  FirebaseMessaging: 195bbdb73e6ca1dbc76cd46e73f3552c084ef6e4
  FirebaseRemoteConfigInterop: 1c6135e8a094cc6368949f5faeeca7ee8948b8aa
  FirebaseSessions: eaa8ec037e7793769defe4201c20bd4d976f9677
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_app_group_directory: d2c3337f424828558953172f9378d00df9b7756d
  flutter_facebook_auth: 3bae06a009563f4df9170b0e56f50c514f5d0014
  flutter_inappwebview_ios: 6f63631e2c62a7c350263b13fa5427aedefe81d4
  flutter_local_notifications: ff50f8405aaa0ccdc7dcfb9022ca192e8ad9688f
  flutter_secure_storage: d33dac7ae2ea08509be337e775f6b59f1ff45f12
  flutter_timezone: ac3da59ac941ff1c98a2e1f0293420e020120282
  freshchat_sdk: 4078d62c19b5f0163c2398b6e27600d6654ba7b9
  FreshchatSDK: 52a5fbda55f5892cc96bc0f2cd80ffe188fa106c
  google_sign_in_ios: 7411fab6948df90490dc4620ecbcabdc3ca04017
  GoogleAppMeasurement: 0dfca1a4b534d123de3945e28f77869d10d0d600
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleSignIn: ce8c89bb9b37fb624b92e7514cc67335d1e277e4
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  in_app_purchase_storekit: a1ce04056e23eecc666b086040239da7619cd783
  just_audio: a42c63806f16995daf5b219ae1d679deb76e6a79
  lecle_social_share: 36ce1284f5ecd5f05f97698438f303cab298ec76
  live_activities: 5a5313d5bf801bbbbe80d51eab80aed6d6df376b
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  photo_manager: 81954a1bf804b6e882d0453b3b6bc7fad7b47d3d
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  realm: 6368bd008c293320dc6eefd475b538d02ca69153
  RecaptchaInterop: 11e0b637842dfb48308d242afc3f448062325aba
  screen_brightness_ios: 6a6f7794b67f07c4f1e24f6374b2d8ad367ffb39
  screen_protector: 6f92086bd2f2f4b54f54913289b9d1310610140b
  ScreenProtectorKit: 83a6281b02c7a5902ee6eac4f5045f674e902ae4
  share_plus: 8b6f8b3447e494cca5317c8c3073de39b3600d1f
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sign_in_with_apple: f3bf75217ea4c2c8b91823f225d70230119b8440
  spine_flutter: 029584e4272785b636238b7ce1a475f85d9ada08
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  TikTokOpenAuthSDK: 35d99f5778b9635ab983bb25c4acf6ccad4404a9
  TikTokOpenSDK: dc1199ba9c6d7815aee0eaca6ec80ad13f7d9c1f
  TikTokOpenSDKCore: e6f34e48bd6e85e4d94f9c04782c13d5defafb55
  TikTokOpenShareSDK: a7da017bc66c28d0aefea9342c0cfcc7e52ea2b7
  TwitterCore: 8cbc9ad34d91c63a0035ea05bfbfc0d7ca72a28c
  TwitterKit5: 64095dfefcf39be2355711ef27112d55e340d90e
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  vpn_check: 6a654c609762ddf964f2ba78edeffe71b490ae44

PODFILE CHECKSUM: e00d162323976b4a0f62c6f00a7bf5a2381e804d

COCOAPODS: 1.16.2
