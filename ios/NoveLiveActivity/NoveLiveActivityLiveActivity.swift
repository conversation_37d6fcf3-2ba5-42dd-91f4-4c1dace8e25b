//
//  NoveLiveActivityLiveActivity.swift
//  NoveLiveActivity
//
//  Created by MACBOOK on 11/06/25.
//

import ActivityKit
import WidgetKit
import SwiftUI

struct LiveActivitiesAppAttributes: ActivityAttributes, Identifiable {
    typealias LiveDeliveryData = ContentState
    
    struct ContentState: Codable, Hashable { }
    
    var id = UUID()
}


extension LiveActivitiesAppAttributes {
  func prefixedKey(_ key: String) -> String {
      print(key)
    return "\(id)_\(key)"
  }
}

//struct RoundedCorner: Shape {
//    var radius: CGFloat = .infinity
//    var corners: UIRectCorner = .allCorners
//
//    func path(in rect: CGRect) -> Path {
//        let path = UIBezierPath(
//            roundedRect: rect,
//            byRoundingCorners: corners,
//            cornerRadii: CGSize(width: radius, height: radius)
//        )
//        return Path(path.cgPath)
//    }
//}
//
//
//extension View {
//    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
//        clipShape(RoundedCorner(radius: radius, corners: corners))
//    }
//}

