//
//  extension_example.swift
//  extension-example
//
//  Created by <PERSON> on 28/09/2022.
//

import WidgetKit
import SwiftUI
import ActivityKit


// Create shared default with custom group
let sharedDefault = UserDefaults(suiteName: "group.urnovel.story.books")!

@available(iOSApplicationExtension 16.1, *)
struct NoveLiveActivity: Widget {
    var body: some WidgetConfiguration {
    ActivityConfiguration(for: LiveActivitiesAppAttributes.self) { context in
        let bookId = sharedDefault.string(forKey: context.attributes.prefixedKey("bookId"))!
        let coverUrl = sharedDefault.string(forKey: context.attributes.prefixedKey("coverUrl"))!
        let bookName = sharedDefault.string(forKey: context.attributes.prefixedKey("bookName"))!
        let chapterTitle = sharedDefault.string(forKey: context.attributes.prefixedKey("chapterTitle"))!
        let chapterIndex = sharedDefault.string(forKey: context.attributes.prefixedKey("chapterIndex"))!
        let continueTitle = sharedDefault.string(forKey: context.attributes.prefixedKey("continueTitle"))!

        let image = UIImage(contentsOfFile: coverUrl)

//      let rule = (try? String(contentsOfFile: ruleFile, encoding: .utf8)) ?? ""
//      let matchStartDate = Date(timeIntervalSince1970: sharedDefault.double(forKey: context.attributes.prefixedKey("matchStartDate")) / 1000)
//      let matchEndDate = Date(timeIntervalSince1970: sharedDefault.double(forKey: context.attributes.prefixedKey("matchEndDate")) / 1000)
//      let matchRemainingTime = matchStartDate...matchEndDate
        
        Link(destination: URL(string: "UrNovel://read?bookId=\(bookId)&chapterIndex=\(chapterIndex)&jumpType=liveActivity")!){
            ZStack{
                HStack(alignment: .top) {
                    VStack(alignment: .leading) {
                        Text(bookName)
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(Color(red: 0, green: 1/255, blue: 11/255))
                            .lineLimit(2)
                        
                        Spacer()
                        
                        HStack {
                            Text(chapterTitle)
                                .font(.subheadline)
                                .foregroundColor(Color(red: 0, green: 1/255, blue: 11/255))
                            Text("\(chapterIndex)")
                                .font(.subheadline)
                                .foregroundColor(Color(red: 0, green: 146/255, blue: 249/255))
                        }
                        
                        Spacer()
                        
                        HStack {
                            Spacer()
                            
                            Text(continueTitle)
                                .font(.headline)
                                .foregroundColor(.white)
                            
                            Spacer()
                            
                        }.frame(width: .infinity, height: 37)
                            .background(LinearGradient(colors: [Color(red: 0, green: 135/255, blue: 251/255), Color(red: 0, green: 153/255, blue: 248/255)], startPoint: .leading, endPoint: .trailing))
                            .cornerRadius(20)
                            .clipped()
                    }.frame(width: .infinity, height: 133)
                    
                    Spacer(minLength: 18)
               
                    if (image != nil)
                    {
                        Spacer()
                        
                        Image(uiImage: image!)
                            .resizable() // 使图片可调整大小
                            .scaledToFit()
                            .frame(width: 100, height: 133)
                            .cornerRadius(10)
                            .clipped()
                    }
                }
                .padding(EdgeInsets.init(top: 18, leading: 18, bottom: 18, trailing: 18))
            }
            .background(.white)
        }
    } dynamicIsland: { context in
      return DynamicIsland {
          DynamicIslandExpandedRegion(.leading) {
          }
          
          DynamicIslandExpandedRegion(.center) {
          }
      } compactLeading: {
        
      } compactTrailing: {
        
      } minimal: {
        
      }
    }
  }
}
