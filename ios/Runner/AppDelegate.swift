import Flutter
import UIKit
import T<PERSON><PERSON>okOpenAuthSDK
import T<PERSON>T<PERSON><PERSON>penSDKCore
import FBSDKCoreKit
import Tik<PERSON>okOpenShareSDK
import Photos

@main
@objc class AppDelegate: FlutterAppDelegate {
    private let CHANNEL = "native_bridge_channel"
    private var methodChannel: FlutterMethodChannel?
    
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        // 保留原有代码
        GeneratedPluginRegistrant.register(with: self)
        
        if #available(iOS 10.0, *) {
            UNUserNotificationCenter.current().delegate = self as? UNUserNotificationCenterDelegate
        }
        
        // 新增Flutter与iOS通信的代码
        setupMethodChannel()
        
        // Put these lines in the application function
        FBSDKCoreKit.ApplicationDelegate.shared.application(
                application,
                didFinishLaunchingWithOptions: launchOptions
        )
        
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
    
    override func application(_ app: UIApplication,open url: URL,
                         options: [UIApplication.OpenURLOptionsKey: Any] = [:]) -> Bool {
//        print("Native: ", url.absoluteString);
            if (TikTokURLHandler.handleOpenURL(url)) {
                return true
            } else if url.scheme == "UrNovel" {
                self.callFlutterMethod(funcName: "liveActivityNovel", args: url.absoluteString)
                return true
            }
        
            return false
        }
    
    func queryParameters(from url: URL) -> [String: String] {
        guard let components = URLComponents(url: url, resolvingAgainstBaseURL: false),
              let queryItems = components.queryItems else {
            return [:]
        }
        
        var parameters: [String: String] = [:]
        for item in queryItems {
            parameters[item.name] = item.value ?? ""
        }
        return parameters
    }
        
    override func application(_ application: UIApplication,
                              continue userActivity: NSUserActivity,
                              restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void) -> Bool {
//        print("Native111: ", userActivity.webpageURL?.absoluteString ?? "No URL")
        
        // 解析查询参数
        var dict: [String: String] = [:]
        if let url = userActivity.webpageURL {
            dict = queryParameters(from: url)
        }
        
        if dict["from_platform"] == "tiktokopensdk" {
            let errorCode = dict["error_code", default: "999"]
            let code = dict["code", default: ""]
            let fromPlatform = dict["from_platform", default: ""]
            let requestId = dict["request_id", default: ""]
            let responseId = dict["response_id", default: ""]
            let scopes = dict["scopes", default: ""]
            
            
            if errorCode == "0" {
                // 创建字典并转换为JSON
                let authData: [String: String] = [
                    "code": code,
                    "codeVerifier": self.codeVerifier
                ]
                
                if let jsonData = try? JSONSerialization.data(withJSONObject: authData),
                   let jsonString = String(data: jsonData, encoding: .utf8) {
                    self.callFlutterMethod(funcName: "tiktokLoginSuc", args: jsonString)
                } else {
                    // 如果JSON转换失败，仍然传递原始code
                    self.callFlutterMethod(funcName: "tiktokLoginSuc", args: code)
                }
            } else {
                self.callFlutterMethod(funcName: "tiktokLoginFailed", args: errorCode)
            }
        }
        
        if let webpageURL = userActivity.webpageURL, TikTokURLHandler.handleOpenURL(webpageURL) {
//            print("Native222: ", webpageURL)
            return true
        }
        
        return false
    }
    
    // 设置MethodChannel
    private func setupMethodChannel() {
        let controller = window?.rootViewController as! FlutterViewController
        
        // 初始化MethodChannel
        methodChannel = FlutterMethodChannel(
            name: CHANNEL,
            binaryMessenger: controller.binaryMessenger
        )
        
        // 设置处理Flutter调用的方法
        setupMethodCallHandler()
    }
    
    // 设置处理Flutter调用的方法处理器
    private func setupMethodCallHandler() {
        methodChannel?.setMethodCallHandler { [weak self] (call, result) in
//            print("Flutter调用iOS: \(call.method)")
            
            switch call.method {
            case "methodName":
                // 获取参数
                let params = call.arguments as? [String: Any]
                let param1 = params?["param1"] as? String
                let param2 = params?["param2"] as? String
                
//                print("参数1: \(param1 ?? "nil"), 参数2: \(param2 ?? "nil")")
                
                // 处理逻辑
                // ...
                
                // 调用Flutter端方法
                self?.callFlutterMethod(funcName: "fromeiOS", args: "fromeiOSData")
//                self?.tiktokLogin();
                // 返回结果
                result("Success from iOS")
            
            case "tiktokLogin":
                self?.tiktokLogin();
            case "tiktokShare":
                let params = call.arguments as? [String: Any]
                let param1 = params?["path"] as? String
                self?.tiktokShare(path: param1!);
            default:
                result(FlutterMethodNotImplemented)
            }
        }
    }
    
    // 调用Flutter端方法
    private func callFlutterMethod(funcName : String, args: String) {
        print("iOS调用Flutter: \(funcName) \(args)")
        methodChannel?.invokeMethod(funcName, arguments: args) { (result) in
            if let error = result as? FlutterError {
//                print("错误: \(error.message ?? "unknown error")")
            } else if let resultData = result {
//                print("结果: \(resultData)")
            }
        }
    }
    
    public var codeVerifier: String = ""
    private func tiktokLogin() {
        let authRequest = TikTokAuthRequest(scopes: ["user.info.basic"],
                                           redirectURI: "https://share.urnovel.com")
        codeVerifier = authRequest.pkce.codeVerifier
        /* Step 2 */
        authRequest.send { [weak self] response in
            /* Step 3 */
            guard let self = self, let authResponse = response as? TikTokAuthResponse else { return }
            if authResponse.errorCode == .noError {
//                print("Auth code: \(authResponse.code)")
//                print("Auth code: \(authResponse.authCode)")
//                print("Auth code: \(authResponse.responseID)")
//                print("Auth code: \(authResponse.requestID)")
//                print("Auth code: \(authResponse.errorDescription)")
//                print("Auth code: \(authResponse.error)")
//                print("Auth code: \(authResponse.authCode)")
//                print("Auth code: \(authResponse.errorCode)")
//                print("Auth code: \(authResponse.state)")
                
            } else {
//                print("Authorization Failed! Error: \(authResponse.error ?? "") Error Description: \(authResponse.errorDescription ?? "")")
            }
        }
    }
    
    private func tiktokShare(path:String){
//        print("开始分享TikTok，localIdentifier: \(path)")
        
        // 直接使用Flutter传入的localIdentifier，无需再次保存到相册
        let shareRequest = TikTokShareRequest(localIdentifiers: [path],
                                              mediaType: .image,
                                              redirectURI: "https://share.urnovel.com")
        
        shareRequest.send { response in
            guard let shareResponse = response as? TikTokShareResponse else { return }
            if shareResponse.errorCode == .noError {
//                print("分享成功!")
                self.callFlutterMethod(funcName: "tiktokShareSuc", args: "")
            } else {
//                print("分享失败! 错误码: \(shareResponse.errorCode.rawValue) 错误信息: \(shareResponse.errorDescription ?? "") 分享状态: \(shareResponse.shareState.rawValue)")
                self.callFlutterMethod(funcName: "tiktokShareFailed", args: "\(shareResponse.errorDescription ?? "")")
            }
        }
    }
    
    // 保存图片到相册并返回本地标识符方法已移除，由Flutter端实现
}
