<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AppID</key>
	<string>ID6736648910</string>
	<key>AppsFlyerDevKey</key>
	<string>Bht5UeN6PmWTKKDeUVKG4e</string>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>UrNovel</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>UrNovel</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string></string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb564494579467840</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>UrNovel</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>UrNovel</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.979084064090-arvjj65rsvnp4e44l62hd81uvhvsbpbe</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>awzsjtidnigac5bj</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>FacebookAdvertiserIDCollectionEnabled</key>
	<true/>
	<key>FacebookAppID</key>
	<string>564494579467840</string>
	<key>FacebookAutoLogAppEventsEnabled</key>
	<false/>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>FacebookDisplayName</key>
	<string>UrNovel</string>
	<key>FirebaseAppDelegateProxyEnabled</key>
	<true/>
	<key>FirebaseAutomaticScreenReportingEnabled</key>
	<false/>
	<key>GIDClientID</key>
	<string>4798685303-f19uuihms9t151dh2kf5v9174c70cal4.apps.googleusercontent.com</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>fb</string>
		<string>fb-messenger</string>
		<string>facebook-stories</string>
		<string>facebook-reels</string>
		<string>UrNovel</string>
		<string>instagram</string>
		<string>whatsapp</string>
		<string>fbapi</string>
		<string>fb-messenger-api</string>
		<string>fb-messenger-share-api</string>
		<string>fbauth2</string>
		<string>fbshareextension</string>
		<string>tiktokopensdk</string>
		<string>tiktoksharesdk</string>
		<string>snssdk1180</string>
		<string>snssdk1233</string>
		<string>twitter</string>
		<string>twitterauth</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAdvertisingAttributionReportEndpoint</key>
	<string>https://appsflyer-skadnetwork.com/</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>A secure and convenient way to sign in while protecting your privacy</string>
	<key>NSFaceIDUsageDescription</key>
	<string>Securely access your account with Face ID</string>
	<key>NSLocalNetworkUsageDescription</key>
	<string>Connect to local network for full functionality</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>We need to obtain your microphone permission for conversation or recording operations</string>
	<key>NSPasteboardUsageDescription</key>
	<string>We need access to the clipboard to provide a better service experience</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>wants to use your photo album for saving novel covers</string>
	<key>NSSupportsLiveActivities</key>
	<true/>
	<key>NSUserActivityTypes</key>
	<array>
		<string>UrNovelLiveActivity</string>
	</array>
	<key>NSUserNotificationsUsageDescription</key>
	<string>Stay updated with new content</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>Enable to receive content recommendations tailored to your interests</string>
	<key>SKAdNetworkItems</key>
	<array>
		<dict>
			<key>SKAdNetworkIdentifier</key>
			<string>v9wttpbfk9.skadnetwork</string>
		</dict>
		<dict>
			<key>SKAdNetworkIdentifier</key>
			<string>n38lu8286q.skadnetwork</string>
		</dict>
		<dict>
			<key>SKAdNetworkIdentifier</key>
			<string>mj797d8u6f.skadnetwork</string>
		</dict>
		<dict>
			<key>SKAdNetworkIdentifier</key>
			<string>22mmun2rn5.skadnetwork</string>
		</dict>
	</array>
	<key>TikTokClientKey</key>
	<string>awzsjtidnigac5bj</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
		<string>audio</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIRequiresFullScreen</key>
	<false/>
	<key>UIStatusBarStyle</key>
	<string></string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>io.flutter.embedded_views_preview</key>
	<true/>
</dict>
</plist>
